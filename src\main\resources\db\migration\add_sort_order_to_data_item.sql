-- 为DataItem表添加sortOrder字段
-- 执行时间：2024-12-19

-- 添加sortOrder列
ALTER TABLE data_item ADD COLUMN sort_order INTEGER NOT NULL DEFAULT 0;

-- 为现有数据设置默认排序值（按创建时间或ID排序）
UPDATE data_item SET sort_order = 
    (SELECT ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY id) - 1 
     FROM (SELECT id, device_id FROM data_item) AS sub 
     WHERE sub.id = data_item.id);

-- 添加索引以提高查询性能
CREATE INDEX idx_data_item_device_sort ON data_item(device_id, sort_order);

-- 添加注释
COMMENT ON COLUMN data_item.sort_order IS '监控项在设备中的排序顺序，数值越小越靠前';
