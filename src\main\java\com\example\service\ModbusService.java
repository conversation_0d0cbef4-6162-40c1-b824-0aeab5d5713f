package com.example.service;

import com.example.model.Device;
import com.example.model.DataItem;
import com.example.repository.DeviceRepository;
import com.example.repository.DataItemRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.ip.IpParameters;
import com.serotonin.modbus4j.msg.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class ModbusService {
    private final MqttService mqttService;
    private final ObjectMapper objectMapper;
    private final Map<String, ModbusMaster> connections = new ConcurrentHashMap<>();
    private static final int DEFAULT_UNIT_ID = 1;
    private final ModbusFactory modbusFactory;
    private final DeviceRepository deviceRepository;
    private final DataItemRepository dataItemRepository;

    public ModbusService(MqttService mqttService, ObjectMapper objectMapper,
                        DeviceRepository deviceRepository, DataItemRepository dataItemRepository) {
        this.mqttService = mqttService;
        this.objectMapper = objectMapper;
        this.modbusFactory = new ModbusFactory();
        this.deviceRepository = deviceRepository;
        this.dataItemRepository = dataItemRepository;
        
        // 应用启动时初始化连接
        initializeConnections();
    }

    /**
     * 初始化所有已连接设备的连接
     */
    private void initializeConnections() {
        try {
            List<Device> connectedDevices = deviceRepository.findByConnectedTrue();
            if (!connectedDevices.isEmpty()) {
                log.info("Found {} connected devices, attempting to restore connections...", connectedDevices.size());
                
                for (Device device : connectedDevices) {
                    try {
                        // 创建TCP参数
                        IpParameters params = new IpParameters();
                        params.setHost(device.getAddress());
                        params.setPort(device.getPort());
                        params.setEncapsulated(false);
                        
                        // 创建TCP主站
                        ModbusMaster master = modbusFactory.createTcpMaster(params, true);
                        master.setTimeout(3000);
                        master.setRetries(1);
                        
                        // 启动主站
                        master.init();
                        
                        // 测试连接
                        ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(DEFAULT_UNIT_ID, 0, 1);
                        ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
                        if (response == null) {
                            throw new Exception("设备未响应");
                        }
                        
                        // 保存连接
                        connections.put(device.getId(), master);
                        log.info("Successfully restored connection to device: {} ({}:{})", 
                            device.getName(), device.getAddress(), device.getPort());
                    } catch (Exception e) {
                        // 如果重连失败，更新设备状态为未连接
                        device.setConnected(false);
                        deviceRepository.save(device);
                        log.error("Failed to restore connection to device: {} ({}:{}): {}", 
                            device.getName(), device.getAddress(), device.getPort(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error initializing device connections", e);
        }
    }

    /**
     * 转换Modbus地址
     * 40001 -> 0 (保持寄存器)
     * 30001 -> 0 (输入寄存器)
     * 
     * 支持两种格式的大地址:
     * 1. 直接偏移格式: 如 10000、65535 等
     * 2. Modbus Slave格式: 如 410001，将自动转换成相同的偏移量 10000
     */
    private ModbusRegisterInfo convertModbusAddress(String address) throws IllegalArgumentException {
        if (address == null || address.trim().isEmpty()) {
            throw new IllegalArgumentException("地址不能为空");
        }
        
        try {
            int addr = Integer.parseInt(address.trim());
            ModbusRegisterInfo info = new ModbusRegisterInfo();
            
            // 特殊处理Modbus Slave工具风格的地址格式
            // 例如：410001应该等同于输入10000
            if (addr >= 400000) {
                // 对于形如410001的地址，直接减去400000得到实际偏移量
                int offset = addr - 400000;
                log.debug("处理Modbus Slave格式地址: {} -> 偏移量: {}", addr, offset);
                
                // 确保偏移量不超过协议限制
                if (offset > 65535) {
                    offset = offset & 0xFFFF; // 取低16位
                    log.debug("偏移量超过限制，截取低16位: {}", offset);
                }
                
                info.setAddress(offset);
                info.setType(ModbusRegisterType.HOLDING);
                return info;
            }
            
            // 处理形如310001的地址
            if (addr >= 300000) {
                // 对于形如310001的地址，直接减去300000得到实际偏移量
                int offset = addr - 300000;
                log.debug("处理Modbus Slave格式地址: {} -> 偏移量: {}", addr, offset);
                
                // 确保偏移量不超过协议限制
                if (offset > 65535) {
                    offset = offset & 0xFFFF; // 取低16位
                    log.debug("偏移量超过限制，截取低16位: {}", offset);
                }
                
                info.setAddress(offset);
                info.setType(ModbusRegisterType.INPUT);
                return info;
            }
            
            // 处理4xxxx格式的地址（保持寄存器）
            if (addr >= 40001) {
                // 计算实际偏移地址
                int offset = addr - 40001;
                log.debug("处理标准4xxxx格式地址: {} -> 偏移量: {}", addr, offset);
                
                // Modbus协议限制单个请求最大地址为65535
                // 处理超大地址：如果地址大于65535，则取低16位
                if (offset > 65535) {
                    offset = offset & 0xFFFF; // 取低16位
                    log.debug("偏移量超过限制，截取低16位: {}", offset);
                }
                
                info.setAddress(offset);
                info.setType(ModbusRegisterType.HOLDING);
                return info;
            }
            
            // 处理3xxxx格式的地址（输入寄存器）
            if (addr >= 30001) {
                // 计算实际偏移地址
                int offset = addr - 30001;
                log.debug("处理标准3xxxx格式地址: {} -> 偏移量: {}", addr, offset);
                
                // 处理超大地址：如果地址大于65535，则取低16位
                if (offset > 65535) {
                    offset = offset & 0xFFFF; // 取低16位
                    log.debug("偏移量超过限制，截取低16位: {}", offset);
                }
                
                info.setAddress(offset);
                info.setType(ModbusRegisterType.INPUT);
                return info;
            }
            
            // 处理正常的偏移地址（默认为保持寄存器）
            if (addr >= 0) {
                // 处理超大地址：如果地址大于65535，则取低16位
                int offset = addr;
                log.debug("处理直接偏移地址: {}", addr);
                
                if (offset > 65535) {
                    offset = offset & 0xFFFF; // 取低16位
                    log.debug("偏移量超过限制，截取低16位: {}", offset);
                }
                
                info.setAddress(offset);
                info.setType(ModbusRegisterType.HOLDING);
                return info;
            }
            
            throw new IllegalArgumentException("地址格式不正确，地址必须是非负数");
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("地址必须是数字");
        }
    }

    @Transactional
    public void connect(String deviceId, String host, int port) throws Exception {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("PLC设备IP地址不能为空");
        }
        if (port <= 0 || port > 65535) {
            throw new IllegalArgumentException("端口号必须在1-65535之间");
        }

        try {
            // 创建TCP参数
            IpParameters params = new IpParameters();
            params.setHost(host);
            params.setPort(port);
            params.setEncapsulated(false);
            
            // 创建TCP主站
            ModbusMaster master = modbusFactory.createTcpMaster(params, true);
            master.setTimeout(3000);
            master.setRetries(1);
            
            // 启动主站
            master.init();
            
            // 测试连接
            ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(DEFAULT_UNIT_ID, 0, 1);
            ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
            if (response == null) {
                throw new Exception("设备未响应");
            }
            
            // 如果已存在连接，先断开
            disconnect(deviceId);
            
            // 保存连接
            connections.put(deviceId, master);
            
            // 更新数据库中的设备连接状态
            Device device = deviceRepository.findById(deviceId)
                .orElseThrow(() -> new Exception("设备不存在"));
            device.setConnected(true);
            deviceRepository.save(device);
            
            log.info("Connected to Modbus device {} at {}:{}", deviceId, host, port);
        } catch (Exception e) {
            log.error("Failed to connect to Modbus device {}: {}", deviceId, e.getMessage());
            throw new Exception("连接PLC设备失败: " + e.getMessage());
        }
    }

    @Transactional
    public void disconnect(String deviceId) {
        if (deviceId == null) {
            return;
        }
        
        ModbusMaster master = connections.get(deviceId);
        if (master != null) {
            try {
                master.destroy();
                connections.remove(deviceId);
                
                // 更新数据库中的设备连接状态
                deviceRepository.findById(deviceId).ifPresent(device -> {
                    device.setConnected(false);
                    deviceRepository.save(device);
                });
                
                log.info("Disconnected from Modbus device {}", deviceId);
            } catch (Exception e) {
                log.error("Error while disconnecting device {}: {}", deviceId, e.getMessage());
            }
        }
    }

    @Transactional
    public Map<String, Object> readRegister(String deviceId, String address) throws Exception {
        try {
            ModbusMaster master = getMaster(deviceId);
            if (master == null) {
                throw new Exception("设备未连接");
            }
            
            // 转换地址格式
            ModbusRegisterInfo registerInfo = convertModbusAddress(address);
            
            // 读取单个寄存器
            int value;
            if (registerInfo.getType() == ModbusRegisterType.INPUT) {
                ReadInputRegistersRequest request = new ReadInputRegistersRequest(
                    DEFAULT_UNIT_ID, registerInfo.getAddress(), 1);
                ReadInputRegistersResponse response = (ReadInputRegistersResponse) master.send(request);
                value = response.getShortData()[0];
            } else {
                ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(
                    DEFAULT_UNIT_ID, registerInfo.getAddress(), 1);
                ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
                value = response.getShortData()[0];
            }
            
            // 更新数据项的最新值
            dataItemRepository.findByDeviceId(deviceId).stream()
                .filter(item -> item.getAddress().equals(address))
                .forEach(item -> {
                    item.setLatestValue(value);
                    dataItemRepository.save(item);
                });
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("deviceId", deviceId);
            result.put("address", address);
            result.put("value", value);
            result.put("type", registerInfo.getType().toString());
            
            // 发布到MQTT（如果已连接）
            if (mqttService.isConnected()) {
                try {
                    String topic = String.format("modbus/device/%s/register/%s", deviceId, address);
                    String message = objectMapper.writeValueAsString(result);
                    mqttService.publish(topic, message);
                    log.debug("Published register value to MQTT - Topic: {}, Message: {}", topic, message);
                } catch (Exception e) {
                    log.debug("MQTT发布失败: {}", e.getMessage());
                }
            }
            
            return result;
        } catch (IllegalArgumentException e) {
            throw new Exception("地址格式错误: " + e.getMessage());
        }
    }

    public int[] batchReadRegisters(String deviceId, String startAddress, int count) throws Exception {
        if (count <= 0 || count > 100) {
            throw new Exception("读取数量必须在1-100之间");
        }

        ModbusMaster master = getMaster(deviceId);
        if (master == null) {
            throw new Exception("设备未连接");
        }

        ModbusRegisterInfo registerInfo = convertModbusAddress(startAddress);
        
        try {
            // 读取多个寄存器
            int[] values;
            if (registerInfo.getType() == ModbusRegisterType.INPUT) {
                ReadInputRegistersRequest request = new ReadInputRegistersRequest(
                    DEFAULT_UNIT_ID, registerInfo.getAddress(), count);
                ReadInputRegistersResponse response = (ReadInputRegistersResponse) master.send(request);
                if (response == null) {
                    throw new Exception("设备未响应");
                }
                short[] shortData = response.getShortData();
                values = new int[shortData.length];
                for (int i = 0; i < shortData.length; i++) {
                    values[i] = shortData[i] & 0xFFFF;  // 转换为无符号整数
                }
            } else {
                ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(
                    DEFAULT_UNIT_ID, registerInfo.getAddress(), count);
                ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
                if (response == null) {
                    throw new Exception("设备未响应");
                }
                short[] shortData = response.getShortData();
                values = new int[shortData.length];
                for (int i = 0; i < shortData.length; i++) {
                    values[i] = shortData[i] & 0xFFFF;  // 转换为无符号整数
                }
            }
            
            // 发布到MQTT（如果已连接）
            if (mqttService.isConnected()) {
                try {
                    Map<String, Object> result = new HashMap<>();
                    result.put("deviceId", deviceId);
                    result.put("startAddress", startAddress);
                    result.put("count", count);
                    result.put("values", values);
                    result.put("type", registerInfo.getType().toString());
                    
                    String topic = String.format("modbus/device/%s/batch-read/%s/%d", deviceId, startAddress, count);
                    String message = objectMapper.writeValueAsString(result);
                    mqttService.publish(topic, message);
                    log.debug("Published batch read values to MQTT - Topic: {}, Message: {}", topic, message);
                } catch (Exception e) {
                    log.debug("MQTT发布失败: {}", e.getMessage());
                }
            }
            
            return values;
        } catch (Exception e) {
            log.error("Failed to batch read registers for device {}: {}", deviceId, e.getMessage());
            throw new Exception("批量读取寄存器失败: " + e.getMessage());
        }
    }

    private ModbusMaster getMaster(String deviceId) throws Exception {
        if (deviceId == null || !connections.containsKey(deviceId)) {
            throw new Exception("未连接到PLC设备，请先建立连接");
        }
        
        return connections.get(deviceId);
    }

    public void writeRegister(String deviceId, String address, int[] values) throws Exception {
        try {
            ModbusMaster master = getMaster(deviceId);
            
            // 转换地址格式
            ModbusRegisterInfo registerInfo = convertModbusAddress(address);
            
            // 只允许写入保持寄存器
            if (registerInfo.getType() == ModbusRegisterType.INPUT) {
                throw new IllegalArgumentException("输入寄存器是只读的，不能写入");
            }
            
            // 将int[]转换为short[]
            short[] shortValues = new short[values.length];
            for (int i = 0; i < values.length; i++) {
                if (values[i] > Short.MAX_VALUE || values[i] < Short.MIN_VALUE) {
                    throw new IllegalArgumentException("写入的值超出范围（-32768 到 32767）");
                }
                shortValues[i] = (short) values[i];
            }
            
            // 写入寄存器
            WriteRegistersRequest request = new WriteRegistersRequest(
                DEFAULT_UNIT_ID, registerInfo.getAddress(), shortValues);
            WriteRegistersResponse response = (WriteRegistersResponse) master.send(request);
            
            if (response == null) {
                throw new Exception("写入失败：设备未响应");
            }
            
            // 发布到MQTT（如果已连接）
            if (mqttService.isConnected()) {
                try {
                    Map<String, Object> result = new HashMap<>();
                    result.put("deviceId", deviceId);
                    result.put("address", address);
                    result.put("values", values);
                    result.put("type", registerInfo.getType().toString());
                    result.put("operation", "write");
                    
                    String topic = String.format("modbus/device/%s/register/%s/write", deviceId, address);
                    String message = objectMapper.writeValueAsString(result);
                    mqttService.publish(topic, message);
                    log.debug("Published write operation to MQTT - Topic: {}, Message: {}", topic, message);
                } catch (Exception e) {
                    log.debug("MQTT发布失败: {}", e.getMessage());
                }
            }
        } catch (IllegalArgumentException e) {
            throw new Exception("地址格式错误或数据范围错误: " + e.getMessage());
        }
    }

    private enum ModbusRegisterType {
        HOLDING,    // 保持寄存器 (4x)
        INPUT       // 输入寄存器 (3x)
    }

    private static class ModbusRegisterInfo {
        private int address;
        private ModbusRegisterType type;

        public int getAddress() {
            return address;
        }

        public void setAddress(int address) {
            this.address = address;
        }

        public ModbusRegisterType getType() {
            return type;
        }

        public void setType(ModbusRegisterType type) {
            this.type = type;
        }
    }
} 