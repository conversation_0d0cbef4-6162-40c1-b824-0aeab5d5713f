package com.example.repository;

import com.example.model.DeviceCondition;
import com.example.model.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OperationLogRepository extends JpaRepository<OperationLog, Long> {
    List<OperationLog> findByCondition(DeviceCondition condition);
    
    Page<OperationLog> findByConditionOrderByCreatedAtDesc(DeviceCondition condition, Pageable pageable);
    
    List<OperationLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT ol FROM OperationLog ol WHERE ol.condition.device.id = :deviceId ORDER BY ol.createdAt DESC")
    Page<OperationLog> findByDeviceIdOrderByCreatedAtDesc(@Param("deviceId") String deviceId, Pageable pageable);
    
    @Query("SELECT ol FROM OperationLog ol WHERE ol.condition.device.id = :deviceId AND ol.createdAt BETWEEN :start AND :end ORDER BY ol.createdAt DESC")
    Page<OperationLog> findByDeviceIdAndCreatedAtBetweenOrderByCreatedAtDesc(
        @Param("deviceId") String deviceId,
        @Param("start") LocalDateTime start,
        @Param("end") LocalDateTime end,
        Pageable pageable);
    
    @Modifying
    @Query("DELETE FROM OperationLog ol WHERE ol.condition.id = :conditionId")
    void deleteByConditionId(@Param("conditionId") Long conditionId);
} 