# 上下文
文件名：设备管理页面开发任务
创建于：2024-12-19
创建者：用户
Yolo模式：RIPER-5协议

# 任务描述
在导航条添加一个设备管理按钮，点进后进入设备管理页面，页面显示已添加的设备标签，设备标签下方显示连接状态和已添加的实时监控项名称和数值，在实时监控项数据旁边有一个修改按钮，点击后可以通过输入数值，将数值写入对应设备的实时监控项地址。数据来源应该参考当前项目中已有数据进行读取调用，而不是重复创建数据，页面应该美观，该功能要以增量更新的方式，避免影响项目原有功能。

# 项目概述
这是一个基于Spring Boot的Modbus-MQTT Web管理系统，主要功能包括：
- 设备管理（Device）：管理Modbus设备的连接信息
- 数据项管理（DataItem）：管理设备的监控项，包括地址、刷新间隔、最新值等
- 实时数据采集和MQTT发布
- 设备状态监控和预警系统
- 组态图管理和文件管理

⚠️ 警告：切勿修改此部分 ⚠️
[RIPER-5协议核心规则：
1. 必须按照RESEARCH->INNOVATE->PLAN->EXECUTE->REVIEW的顺序执行
2. 在EXECUTE模式中必须100%忠实执行计划
3. 在REVIEW模式中必须标记任何偏差
4. 使用增量更新方式，避免影响原有功能
5. 复用现有数据源和API，不重复创建数据]
⚠️ 警告：切勿修改此部分 ⚠️

# 分析
## 现有架构分析
1. **实体模型**：
   - Device：设备实体，包含id、name、address、port、connected状态
   - DataItem：数据项实体，包含id、name、address、refreshInterval、latestValue，关联Device

2. **服务层**：
   - DeviceService：设备管理服务，提供设备CRUD操作
   - ModbusService：Modbus通信服务，提供读写寄存器功能
   - DeviceStatusService：设备状态广播服务，定时推送设备状态

3. **控制器**：
   - ModbusMqttController：提供设备和数据项的REST API
   - DataItemController：提供数据项相关API
   - DeviceStatusController：设备状态页面控制器

4. **现有API**：
   - GET /api/devices - 获取所有设备
   - GET /api/data-items/device/{deviceId} - 获取设备的数据项
   - POST /modbus/write - 写入Modbus寄存器
   - GET /api/data-items/{id}/latest-value - 获取数据项最新值

5. **前端模板**：
   - 使用Thymeleaf模板引擎
   - Bootstrap 5 UI框架
   - 导航条片段：fragments/navbar.html
   - 现有设备状态页面：device/status.html（主要用于预警状态显示）

## 技术栈
- 后端：Spring Boot + JPA + MySQL
- 前端：Thymeleaf + Bootstrap 5 + JavaScript
- 通信：Modbus TCP + MQTT
- 实时更新：WebSocket + 定时轮询

# 提议的解决方案
基于现有架构，采用增量开发方式：

## 方案选择
选择**增量扩展方案**，原因：
1. 复用现有的Device和DataItem实体及服务
2. 利用现有的ModbusService写入功能
3. 扩展现有的导航条和页面结构
4. 最小化对现有功能的影响

## 核心组件设计
1. **导航条扩展**：在navbar.html中添加设备管理按钮
2. **新页面控制器**：创建DeviceManagementController
3. **新页面模板**：创建device/management.html
4. **数据写入API**：扩展现有的写入接口，支持单个数据项写入
5. **前端交互**：JavaScript实现实时数据显示和写入操作

## 页面功能设计
1. **设备卡片展示**：每个设备一个卡片，显示设备名称和连接状态
2. **数据项列表**：在设备卡片内显示该设备的所有数据项
3. **实时数值显示**：定时获取并更新数据项的最新值
4. **数值修改功能**：每个数据项旁边有修改按钮，点击弹出输入框
5. **写入操作**：调用ModbusService写入新值到设备

## 技术实现要点
1. 复用现有的设备和数据项查询API
2. 利用现有的ModbusService.writeRegister方法
3. 使用定时轮询更新实时数值（与现有模式一致）
4. 采用Bootstrap卡片布局，保持UI一致性

# 当前执行步骤："4. EXECUTE - 实施设备管理功能"

# 任务进度
[2024-12-19 已完成]
- 修改：navbar.html - 添加设备管理按钮，支持activeMenu状态
- 修改：创建DeviceManagementController.java - 设备管理页面控制器
- 修改：创建device/management.html - 完整的设备管理页面模板，包含实时数据显示和修改功能
- 修改：ModbusMqttController.java - 添加POST /api/data-item/{id}/write端点
- 修改：DeviceService.java - 添加getDataItem和saveDataItem方法
- 更改：完成了完整的设备管理功能，包括前端交互、后端API、实时数据更新、数据写入操作
- 原因：按照用户需求实现设备管理页面，复用现有数据源和服务
- 阻碍：无
- 状态：成功

# 最终审查
[待完成]
