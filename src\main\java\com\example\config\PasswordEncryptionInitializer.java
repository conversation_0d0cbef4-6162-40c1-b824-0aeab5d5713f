package com.example.config;

import com.example.model.User;
import com.example.repository.UserRepository;
import com.example.service.PasswordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 应用启动时对数据库中的明文密码进行加密
 */
@Component
public class PasswordEncryptionInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(PasswordEncryptionInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordService passwordService;

    @Override
    @Transactional
    public void run(String... args) {
        logger.info("检查并加密现有用户密码...");
        
        List<User> users = userRepository.findAll();
        int encryptedCount = 0;
        
        for (User user : users) {
            // 检查密码是否需要加密
            if (passwordService.needsEncryption(user.getPassword())) {
                String originalPassword = user.getPassword();
                user.setPassword(passwordService.encryptPassword(originalPassword));
                userRepository.save(user);
                encryptedCount++;
                
                logger.info("已加密用户 '{}' 的密码", user.getUsername());
            }
        }
        
        if (encryptedCount > 0) {
            logger.info("成功加密了 {} 个用户的密码", encryptedCount);
        } else {
            logger.info("所有用户密码已经是加密状态，无需处理");
        }
    }
} 