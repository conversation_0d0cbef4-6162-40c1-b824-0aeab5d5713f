<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多折线图组件测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .config-panel {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .config-item {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>多折线图组件测试</h1>
    
    <div class="test-container">
        <h2>基础多折线图测试</h2>
        <div class="config-panel">
            <div class="config-item">
                <label>双Y轴模式:</label>
                <input type="checkbox" id="enableDualYAxis" onchange="updateChart()">
            </div>
            <div class="config-item">
                <label>平滑曲线:</label>
                <input type="checkbox" id="smooth" checked onchange="updateChart()">
            </div>
            <div class="config-item">
                <label>显示标记点:</label>
                <input type="checkbox" id="showSymbol" checked onchange="updateChart()">
            </div>
            <div class="config-item">
                <label>面积填充:</label>
                <input type="checkbox" id="showArea" onchange="updateChart()">
            </div>
            <div class="config-item">
                <button onclick="loadTestData1()">标准格式</button>
                <button onclick="loadTestData1B()">多折线格式</button>
                <button onclick="loadTestData2()">双Y轴测试</button>
                <button onclick="loadEmptyData()">空数据测试</button>
            </div>
        </div>
        <div id="multiLineChart" class="chart-container"></div>
    </div>

    <div class="test-container">
        <h2>当前测试数据</h2>
        <div id="currentData" class="test-data">等待加载数据...</div>
    </div>

    <script src="/js/bi-echarts-components.js"></script>
    <script>
        let currentChart = null;
        let currentData = null;

        // 测试数据1 - 标准格式（项目兼容）
        function loadTestData1() {
            currentData = {
                labels: ['07-25', '07-26', '07-27', '07-28', '07-29'],
                values: [12, 19, 15, 25, 22]
            };
            updateChart();
            updateDataDisplay();
        }

        // 测试数据1B - 多折线格式
        function loadTestData1B() {
            currentData = {
                xAxis: ['07-25', '07-26', '07-27', '07-28', '07-29'],
                series: [
                    {
                        name: '成功',
                        type: 'line',
                        data: [2, 5, 15, 10, 9],
                        yAxisIndex: 0
                    },
                    {
                        name: '失败',
                        type: 'line',
                        data: [10, 20, 30, 12, 16],
                        yAxisIndex: 0
                    }
                ]
            };
            updateChart();
            updateDataDisplay();
        }

        // 测试数据2 - 双Y轴多折线
        function loadTestData2() {
            currentData = {
                xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                series: [
                    {
                        name: '销售额',
                        type: 'line',
                        data: [120, 132, 101, 134, 90, 230],
                        yAxisIndex: 0
                    },
                    {
                        name: '利润率',
                        type: 'line',
                        data: [15, 18, 12, 22, 8, 35],
                        yAxisIndex: 1
                    },
                    {
                        name: '客户数',
                        type: 'line',
                        data: [850, 920, 780, 1100, 650, 1300],
                        yAxisIndex: 0
                    }
                ]
            };
            updateChart();
            updateDataDisplay();
        }

        // 空数据测试
        function loadEmptyData() {
            currentData = {
                xAxis: ['加载中...'],
                series: [
                    {
                        name: '数据加载中',
                        type: 'line',
                        data: [0],
                        yAxisIndex: 0
                    }
                ]
            };
            updateChart();
            updateDataDisplay();
        }

        // 更新图表
        function updateChart() {
            if (!currentData) return;

            const config = {
                enableDualYAxis: document.getElementById('enableDualYAxis').checked,
                smooth: document.getElementById('smooth').checked,
                showSymbol: document.getElementById('showSymbol').checked,
                showArea: document.getElementById('showArea').checked,
                isShowLegend: true,
                colorScheme: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
            };

            console.log('更新图表配置:', config);
            console.log('更新图表数据:', currentData);

            try {
                if (typeof createEChartsMultiLineChart === 'function') {
                    currentChart = createEChartsMultiLineChart('multiLineChart', currentData, config);
                    console.log('多折线图创建成功');
                } else {
                    console.error('createEChartsMultiLineChart 函数未找到');
                    document.getElementById('multiLineChart').innerHTML = 
                        '<div style="text-align: center; padding-top: 150px; color: red;">createEChartsMultiLineChart 函数未加载</div>';
                }
            } catch (error) {
                console.error('图表创建失败:', error);
                document.getElementById('multiLineChart').innerHTML = 
                    '<div style="text-align: center; padding-top: 150px; color: red;">图表创建失败: ' + error.message + '</div>';
            }
        }

        // 更新数据显示
        function updateDataDisplay() {
            document.getElementById('currentData').textContent = JSON.stringify(currentData, null, 2);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始测试');
            loadTestData1();
        });
    </script>
</body>
</html>
