package com.example.repository;

import com.example.model.DataItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface DataItemRepository extends JpaRepository<DataItem, String> {
    @Query("SELECT d FROM DataItem d WHERE d.device.id = :deviceId ORDER BY d.sortOrder ASC, d.name ASC")
    List<DataItem> findByDeviceId(String deviceId);

    @Query("SELECT DISTINCT d FROM DataItem d LEFT JOIN FETCH d.device ORDER BY d.sortOrder ASC, d.name ASC")
    List<DataItem> findAllWithDevice();

    List<DataItem> findByHistoryEnabledTrue();

    List<DataItem> findByStatsEnabledTrue();
} 