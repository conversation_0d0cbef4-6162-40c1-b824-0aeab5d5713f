package com.example.service.connector;

import java.util.List;
import java.util.Map;

/**
 * 数据源连接器接口
 * 定义各种数据源的统一访问接口
 */
public interface DataSourceConnector {
    
    /**
     * 获取连接器类型
     */
    String getType();
    
    /**
     * 测试数据源连接
     * @param connectionConfig 连接配置（JSON字符串）
     * @return 连接测试结果
     */
    ConnectionTestResult testConnection(String connectionConfig);
    
    /**
     * 执行查询
     * @param queryConfig 查询配置（JSON字符串）
     * @param connectionConfig 连接配置（JSON字符串）
     * @return 查询结果
     */
    QueryResult executeQuery(String queryConfig, String connectionConfig);
    
    /**
     * 获取数据源的元数据信息
     * @param connectionConfig 连接配置（JSON字符串）
     * @return 元数据信息（如表列表、字段信息等）
     */
    MetadataResult getMetadata(String connectionConfig);
    
    /**
     * 验证查询配置
     * @param queryConfig 查询配置（JSON字符串）
     * @param connectionConfig 连接配置（JSON字符串）
     * @return 验证结果
     */
    ValidationResult validateQuery(String queryConfig, String connectionConfig);
    
    /**
     * 连接测试结果
     */
    class ConnectionTestResult {
        private boolean success;
        private String message;
        private long responseTime; // 响应时间（毫秒）
        private Map<String, Object> details; // 详细信息
        
        public ConnectionTestResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public ConnectionTestResult(boolean success, String message, long responseTime) {
            this.success = success;
            this.message = message;
            this.responseTime = responseTime;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public long getResponseTime() { return responseTime; }
        public void setResponseTime(long responseTime) { this.responseTime = responseTime; }
        public Map<String, Object> getDetails() { return details; }
        public void setDetails(Map<String, Object> details) { this.details = details; }
    }
    
    /**
     * 查询结果
     */
    class QueryResult {
        private boolean success;
        private String message;
        private List<Map<String, Object>> data;
        private int rowCount;
        private long executeTime; // 执行时间（毫秒）
        private Map<String, Object> metadata; // 结果元数据
        
        public QueryResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public QueryResult(boolean success, List<Map<String, Object>> data) {
            this.success = success;
            this.data = data;
            this.rowCount = data != null ? data.size() : 0;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<Map<String, Object>> getData() { return data; }
        public void setData(List<Map<String, Object>> data) { 
            this.data = data; 
            this.rowCount = data != null ? data.size() : 0;
        }
        public int getRowCount() { return rowCount; }
        public void setRowCount(int rowCount) { this.rowCount = rowCount; }
        public long getExecuteTime() { return executeTime; }
        public void setExecuteTime(long executeTime) { this.executeTime = executeTime; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }
    
    /**
     * 元数据结果
     */
    class MetadataResult {
        private boolean success;
        private String message;
        private Map<String, Object> metadata;
        
        public MetadataResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public MetadataResult(boolean success, Map<String, Object> metadata) {
            this.success = success;
            this.metadata = metadata;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }
    
    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String message;
        private List<String> errors;
        
        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }
}
