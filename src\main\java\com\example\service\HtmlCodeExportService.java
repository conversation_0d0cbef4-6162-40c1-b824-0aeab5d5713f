package com.example.service;

import com.example.entity.HtmlCodeSnippet;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * HTML代码片段导出服务
 * 用于将HTML代码片段导出为文件
 */
@Service
@Slf4j
public class HtmlCodeExportService {

    @Autowired
    private ObjectMapper objectMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 导出HTML代码片段到指定目录
     */
    public Map<String, Object> exportHtmlSnippets(List<HtmlCodeSnippet> snippets, Path outputDir) {
        log.info("开始导出HTML代码片段，数量: {}", snippets.size());

        Map<String, Object> result = new HashMap<>();
        List<String> exportedFiles = new ArrayList<>();
        List<Map<String, Object>> snippetManifest = new ArrayList<>();

        try {
            // 创建HTML代码目录
            Path htmlDir = outputDir.resolve("resources/html_codes");
            Files.createDirectories(htmlDir);

            // 导出每个代码片段
            for (HtmlCodeSnippet snippet : snippets) {
                try {
                    String filename = generateHtmlFilename(snippet);
                    Path htmlFile = htmlDir.resolve(filename);

                    // 生成HTML文件内容
                    String htmlContent = generateHtmlFileContent(snippet);
                    Files.write(htmlFile, htmlContent.getBytes(StandardCharsets.UTF_8));

                    exportedFiles.add(filename);

                    // 添加到清单
                    Map<String, Object> snippetInfo = new HashMap<>();
                    snippetInfo.put("id", snippet.getId());
                    snippetInfo.put("title", snippet.getTitle());
                    snippetInfo.put("description", snippet.getDescription());
                    snippetInfo.put("filename", filename);
                    snippetInfo.put("category", snippet.getCategory() != null ? snippet.getCategory().getName() : null);
                    snippetInfo.put("tags", snippet.getTags());
                    snippetInfo.put("createdAt", snippet.getCreatedAt() != null ? 
                                   snippet.getCreatedAt().format(DATE_FORMATTER) : null);

                    snippetManifest.add(snippetInfo);

                    log.debug("成功导出HTML代码片段: {}", filename);

                } catch (Exception e) {
                    log.error("导出HTML代码片段失败，ID: {}, 标题: {}", snippet.getId(), snippet.getTitle(), e);
                }
            }

            // 生成HTML代码清单文件
            generateHtmlManifest(snippetManifest, htmlDir);

            result.put("exportedFiles", exportedFiles);
            result.put("snippetManifest", snippetManifest);
            result.put("totalExported", exportedFiles.size());

            log.info("HTML代码片段导出完成，成功: {}", exportedFiles.size());

            return result;

        } catch (Exception e) {
            log.error("导出HTML代码片段失败", e);
            throw new RuntimeException("HTML代码片段导出失败: " + e.getMessage());
        }
    }

    /**
     * 生成HTML文件名
     */
    private String generateHtmlFilename(HtmlCodeSnippet snippet) {
        // 清理标题，移除特殊字符
        String cleanTitle = snippet.getTitle()
                .replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]", "_")
                .replaceAll("_{2,}", "_")
                .trim();

        if (cleanTitle.length() > 50) {
            cleanTitle = cleanTitle.substring(0, 50);
        }

        return String.format("snippet_%d_%s.html", snippet.getId(), cleanTitle);
    }

    /**
     * 生成HTML文件内容
     */
    private String generateHtmlFileContent(HtmlCodeSnippet snippet) {
        StringBuilder content = new StringBuilder();

        // 添加文件头注释
        content.append("<!--\n");
        content.append("HTML代码片段导出文件\n");
        content.append("===================\n");
        content.append("ID: ").append(snippet.getId()).append("\n");
        content.append("标题: ").append(snippet.getTitle()).append("\n");
        if (snippet.getDescription() != null && !snippet.getDescription().trim().isEmpty()) {
            content.append("描述: ").append(snippet.getDescription()).append("\n");
        }
        if (snippet.getCategory() != null) {
            content.append("分类: ").append(snippet.getCategory().getName()).append("\n");
        }
        if (snippet.getTags() != null && !snippet.getTags().trim().isEmpty()) {
            content.append("标签: ").append(snippet.getTags()).append("\n");
        }
        if (snippet.getCreatedAt() != null) {
            content.append("创建时间: ").append(snippet.getCreatedAt().format(DATE_FORMATTER)).append("\n");
        }
        content.append("导出时间: ").append(new Date()).append("\n");
        content.append("===================\n");
        content.append("使用说明：\n");
        content.append("1. 此文件包含完整的HTML代码片段\n");
        content.append("2. 可以直接在浏览器中打开预览效果\n");
        content.append("3. 复制<body>标签内的内容到您的项目中使用\n");
        content.append("4. 如需修改样式，请编辑<style>标签内的CSS代码\n");
        content.append("-->\n\n");

        // 检查HTML内容是否已经是完整的HTML文档
        String htmlContent = snippet.getHtmlContent();
        if (htmlContent.toLowerCase().contains("<!doctype") || 
            htmlContent.toLowerCase().contains("<html")) {
            // 已经是完整的HTML文档，直接使用
            content.append(htmlContent);
        } else {
            // 不是完整的HTML文档，包装成完整的HTML
            content.append("<!DOCTYPE html>\n");
            content.append("<html lang=\"zh-CN\">\n");
            content.append("<head>\n");
            content.append("    <meta charset=\"UTF-8\">\n");
            content.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
            content.append("    <title>").append(snippet.getTitle()).append("</title>\n");
            
            // 添加基础样式
            content.append("    <style>\n");
            content.append("        body {\n");
            content.append("            margin: 0;\n");
            content.append("            padding: 20px;\n");
            content.append("            font-family: Arial, sans-serif;\n");
            content.append("            background-color: #f5f5f5;\n");
            content.append("        }\n");
            content.append("        .container {\n");
            content.append("            max-width: 1200px;\n");
            content.append("            margin: 0 auto;\n");
            content.append("            background-color: white;\n");
            content.append("            padding: 20px;\n");
            content.append("            border-radius: 8px;\n");
            content.append("            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n");
            content.append("        }\n");
            content.append("    </style>\n");
            content.append("</head>\n");
            content.append("<body>\n");
            content.append("    <div class=\"container\">\n");
            content.append("        <h1>").append(snippet.getTitle()).append("</h1>\n");
            if (snippet.getDescription() != null && !snippet.getDescription().trim().isEmpty()) {
                content.append("        <p><strong>描述：</strong>").append(snippet.getDescription()).append("</p>\n");
            }
            content.append("        <hr>\n");
            content.append("        <!-- HTML代码片段开始 -->\n");
            content.append(htmlContent);
            content.append("\n        <!-- HTML代码片段结束 -->\n");
            content.append("    </div>\n");
            content.append("</body>\n");
            content.append("</html>");
        }

        return content.toString();
    }

    /**
     * 生成HTML代码清单文件
     */
    private void generateHtmlManifest(List<Map<String, Object>> snippetManifest, Path htmlDir) {
        try {
            // 生成JSON格式的清单
            Path jsonManifest = htmlDir.resolve("html_snippets_manifest.json");
            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter()
                                           .writeValueAsString(snippetManifest);
            Files.write(jsonManifest, jsonContent.getBytes(StandardCharsets.UTF_8));

            // 生成可读的文本清单
            Path textManifest = htmlDir.resolve("html_snippets_list.txt");
            StringBuilder textContent = new StringBuilder();
            textContent.append("HTML代码片段清单\n");
            textContent.append("================\n");
            textContent.append("导出时间: ").append(new Date()).append("\n");
            textContent.append("总数量: ").append(snippetManifest.size()).append("\n\n");

            for (int i = 0; i < snippetManifest.size(); i++) {
                Map<String, Object> snippet = snippetManifest.get(i);
                textContent.append(String.format("%d. %s\n", i + 1, snippet.get("title")));
                textContent.append("   文件名: ").append(snippet.get("filename")).append("\n");
                if (snippet.get("description") != null) {
                    textContent.append("   描述: ").append(snippet.get("description")).append("\n");
                }
                if (snippet.get("category") != null) {
                    textContent.append("   分类: ").append(snippet.get("category")).append("\n");
                }
                if (snippet.get("tags") != null) {
                    textContent.append("   标签: ").append(snippet.get("tags")).append("\n");
                }
                textContent.append("\n");
            }

            Files.write(textManifest, textContent.toString().getBytes(StandardCharsets.UTF_8));

            log.info("生成HTML代码清单文件: {} 和 {}", jsonManifest.getFileName(), textManifest.getFileName());

        } catch (Exception e) {
            log.error("生成HTML代码清单失败", e);
        }
    }

    /**
     * 验证HTML代码片段
     */
    public boolean validateHtmlSnippet(HtmlCodeSnippet snippet) {
        if (snippet == null) {
            return false;
        }

        if (snippet.getTitle() == null || snippet.getTitle().trim().isEmpty()) {
            return false;
        }

        if (snippet.getHtmlContent() == null || snippet.getHtmlContent().trim().isEmpty()) {
            return false;
        }

        return true;
    }
}
