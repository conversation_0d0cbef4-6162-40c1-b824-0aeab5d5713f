// BI大屏数据源配置实现快速检查脚本
// 在浏览器控制台中直接运行此脚本

(function() {
    console.log('=== BI大屏数据源配置实现检查 ===');
    
    let score = 0;
    let total = 0;
    
    // 1. 基础检查
    console.log('\n1. 基础组件检查:');
    
    const classExists = typeof BiDataSourceManager !== 'undefined';
    console.log('- BiDataSourceManager类:', classExists ? '✅' : '❌');
    if (classExists) score++; total++;
    
    const instanceExists = typeof window.biDataSourceManager !== 'undefined';
    console.log('- 全局实例存在:', instanceExists ? '✅' : '❌');
    if (instanceExists) score++; total++;
    
    const instanceValid = window.biDataSourceManager instanceof BiDataSourceManager;
    console.log('- 实例类型正确:', instanceValid ? '✅' : '❌');
    if (instanceValid) score++; total++;
    
    // 2. 新增方法检查
    console.log('\n2. 新增方法检查:');
    const newMethods = [
        'createComponentContext',
        'validateDataSourceConfig', 
        'configureComponentDataSource',
        'quickFetchData'
    ];
    
    newMethods.forEach(method => {
        total++;
        const exists = window.biDataSourceManager && typeof window.biDataSourceManager[method] === 'function';
        console.log(`- ${method}:`, exists ? '✅' : '❌');
        if (exists) score++;
    });
    
    // 3. 标准化函数检查
    console.log('\n3. 标准化函数检查:');
    
    const initFuncExists = typeof initializeBiDataSourceManager === 'function';
    console.log('- initializeBiDataSourceManager:', initFuncExists ? '✅' : '❌');
    if (initFuncExists) score++; total++;
    
    // 4. 实际调用检查
    console.log('\n4. 实际调用检查:');
    
    // 检查配置收集
    if (typeof applyWidgetConfig === 'function') {
        total++;
        const funcStr = applyWidgetConfig.toString();
        const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('collectDataSourceConfig');
        console.log('- 配置收集使用BiDataSourceManager:', usesBiManager ? '✅' : '❌');
        if (usesBiManager) score++;
    }
    
    // 检查配置恢复
    if (typeof updatePropertyPanel === 'function') {
        total++;
        const funcStr = updatePropertyPanel.toString();
        const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('restoreDataSourceConfig');
        console.log('- 配置恢复使用BiDataSourceManager:', usesBiManager ? '✅' : '❌');
        if (usesBiManager) score++;
    }
    
    // 检查数据获取
    if (typeof updateWidgetData === 'function') {
        total++;
        const funcStr = updateWidgetData.toString();
        const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('fetchWidgetData');
        console.log('- 数据获取使用BiDataSourceManager:', usesBiManager ? '✅' : '❌');
        if (usesBiManager) score++;
    }
    
    // 5. getMultiDataSourceConfig检查
    console.log('\n5. getMultiDataSourceConfig实现检查:');
    if (typeof getMultiDataSourceConfig === 'function') {
        console.log('- 函数存在: ✅');
        
        // 测试是否使用BiDataSourceManager
        if (window.biDataSourceManager && window.biDataSourceManager.getMultiDataSourceConfig) {
            total++;
            try {
                const testResult = getMultiDataSourceConfig('pie-chart');
                const managerResult = window.biDataSourceManager.getMultiDataSourceConfig('pie-chart');
                const isUsingManager = JSON.stringify(testResult) === JSON.stringify(managerResult);
                console.log('- 使用BiDataSourceManager:', isUsingManager ? '✅' : '❌ (降级配置)');
                if (isUsingManager) score++;
            } catch (error) {
                console.log('- 测试调用失败:', error.message);
            }
        }
    } else {
        console.log('- 函数存在: ❌');
    }
    
    // 6. 上下文管理检查
    console.log('\n6. 上下文管理检查:');
    if (window.biDataSourceManager) {
        const hasContexts = window.biDataSourceManager.componentContexts instanceof Map;
        const hasCache = window.biDataSourceManager.validationCache instanceof Map;
        
        console.log('- componentContexts:', hasContexts ? '✅' : '❌');
        console.log('- validationCache:', hasCache ? '✅' : '❌');
        
        if (hasContexts) {
            console.log('- 当前上下文数量:', window.biDataSourceManager.componentContexts.size);
        }
        if (hasCache) {
            console.log('- 当前缓存数量:', window.biDataSourceManager.validationCache.size);
        }
    }
    
    // 7. 综合评估
    console.log('\n7. 综合评估:');
    const percentage = Math.round((score / total) * 100);
    console.log(`模块化实现完成度: ${score}/${total} (${percentage}%)`);
    
    if (percentage >= 90) {
        console.log('🎉 系统已成功使用新的模块化数据源配置');
        console.log('✅ 重构成功，所有功能正常工作');
    } else if (percentage >= 70) {
        console.log('⚠️ 系统部分使用新的模块化配置');
        console.log('💡 可能存在降级情况，建议检查初始化过程');
    } else if (percentage >= 50) {
        console.log('⚠️ 系统混合使用新旧配置');
        console.log('🔧 建议检查文件加载和初始化代码');
    } else {
        console.log('❌ 系统主要使用旧的数据源配置实现');
        console.log('🚨 重构可能未生效，需要检查文件更新情况');
    }
    
    // 8. 快速功能测试
    console.log('\n8. 快速功能测试:');
    if (window.biDataSourceManager && percentage >= 70) {
        try {
            // 测试组件上下文
            const testWidget = { id: 'test-' + Date.now(), type: 'bar-chart' };
            const context = window.biDataSourceManager.createComponentContext(testWidget);
            
            if (context) {
                console.log('- 上下文创建: ✅');
                window.biDataSourceManager.clearComponentContext(testWidget);
                console.log('- 上下文清理: ✅');
            } else {
                console.log('- 上下文管理: ❌');
            }
            
            // 测试配置验证
            const validation = window.biDataSourceManager.validateDataSourceConfig(testWidget, {
                dataSourceType: 'static'
            });
            console.log('- 配置验证:', validation && validation.hasOwnProperty('isValid') ? '✅' : '❌');
            
        } catch (error) {
            console.log('- 功能测试异常:', error.message);
        }
    } else {
        console.log('- 跳过功能测试（基础检查未通过）');
    }
    
    console.log('\n=== 检查完成 ===');
    
    // 返回检查结果
    return {
        score: score,
        total: total,
        percentage: percentage,
        status: percentage >= 90 ? 'success' : percentage >= 70 ? 'partial' : 'failed',
        usingNewImplementation: percentage >= 90
    };
})();
