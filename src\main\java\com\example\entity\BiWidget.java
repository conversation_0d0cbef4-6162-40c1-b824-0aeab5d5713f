package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bi_widgets")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BiWidget {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "dashboard_id", nullable = false)
    private Long dashboardId;

    @Column(name = "widget_type", nullable = false, length = 50)
    private String widgetType;

    // 参考report项目的标准字段结构
    @Column(name = "setup", columnDefinition = "TEXT")
    private String setup; // 样式配置JSON（包含所有样式相关配置）

    @Column(name = "data", columnDefinition = "TEXT")
    private String data; // 数据配置JSON（包含数据源配置）

    @Column(name = "position", columnDefinition = "TEXT")
    private String position; // 位置配置JSON（包含位置、大小、层级）

    @Column(name = "options", columnDefinition = "TEXT")
    private String options; // 选项配置JSON（组件可配置选项）

    // 兼容性字段（保持向后兼容）
    @Column(name = "position_x")
    private Integer positionX = 0;

    @Column(name = "position_y")
    private Integer positionY = 0;

    @Column(name = "width")
    private Integer width = 300;

    @Column(name = "height")
    private Integer height = 200;

    @Column(name = "config", columnDefinition = "TEXT")
    private String config;

    @Column(name = "data_source_config", columnDefinition = "TEXT")
    private String dataSourceConfig;

    // 新增字段（参考report项目）
    @Column(name = "z_index")
    private Integer zIndex = 1000; // 图层顺序

    @Column(name = "refresh_seconds")
    private Integer refreshSeconds; // 自动刷新间隔秒

    @Column(name = "sort")
    private Long sort; // 排序，图层的概念

    @Column(name = "enable_flag")
    private Integer enableFlag = 1; // 0--已禁用 1--已启用

    @Column(name = "delete_flag")
    private Integer deleteFlag = 0; // 0--未删除 1--已删除

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark; // 备注字段，用于存储组件状态信息

    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 移除双向关系，避免序列化问题
    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "dashboard_id")
    // private BiDashboard dashboard;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
