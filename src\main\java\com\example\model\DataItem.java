package com.example.model;

import lombok.Data;
import javax.persistence.*;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonIgnore;

@Data
@Entity
@Table(name = "data_items")
public class DataItem {
    @Id
    private String id;  // 使用前端生成的UUID作为ID
    
    @Column(nullable = false)
    private String name;
    
    @Column(nullable = false)
    private String address;
    
    @Column(nullable = false)
    private Integer refreshInterval;
    
    @Column
    private Integer latestValue;
    
    @Column(nullable = false)
    private Boolean historyEnabled = false;  // 历史记录开关，默认关闭

    @Column
    private Integer historyRetentionHours = 720;  // 默认30天 (30天 * 24小时 = 720小时)，-1表示永久保存

    @Column(nullable = false)
    private Boolean statsEnabled = false;  // 统计功能开关，默认关闭

    @Column(nullable = false)
    private Integer sortOrder = 0;  // 排序字段，默认为0

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "device_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private Device device;

    @OneToMany(mappedBy = "dataItem", cascade = CascadeType.REMOVE)
    @JsonIgnore
    private List<DataHistory> histories;
} 