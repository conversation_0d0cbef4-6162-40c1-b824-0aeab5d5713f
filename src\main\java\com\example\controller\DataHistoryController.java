package com.example.controller;

import com.example.model.DataHistory;
import com.example.model.DataItem;
import com.example.model.ErrorResponse;
import com.example.repository.DataHistoryRepository;
import com.example.repository.DataItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/data-history")
@RequiredArgsConstructor
public class DataHistoryController {
    private final DataItemRepository dataItemRepository;
    private final DataHistoryRepository dataHistoryRepository;
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @GetMapping("/latest/{dataItemId}")
    public ResponseEntity<?> getLatestData(@PathVariable String dataItemId) {
        try {
            DataItem dataItem = dataItemRepository.findById(dataItemId)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));
            
            List<DataHistory> histories = dataHistoryRepository.findByDataItemOrderByTimestampDesc(dataItem);
            if (histories.isEmpty()) {
                Map<String, Integer> result = new HashMap<>();
                result.put("value", 0);
                return ResponseEntity.ok(result);
            }
            
            DataHistory latest = histories.get(0);
            Map<String, Object> result = new HashMap<>();
            result.put("value", latest.getValue());
            result.put("timestamp", latest.getTimestamp().format(DATE_FORMATTER));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting latest data for item {}: {}", dataItemId, e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取数据失败", e.getMessage()));
        }
    }
    
    @GetMapping("/{dataItemId}")
    public ResponseEntity<?> getHistoryData(
            @PathVariable String dataItemId,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        try {
            DataItem dataItem = dataItemRepository.findById(dataItemId)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));
            
            LocalDateTime start = LocalDateTime.parse(startTime, DATE_FORMATTER);
            LocalDateTime end = LocalDateTime.parse(endTime, DATE_FORMATTER);
            
            List<DataHistory> histories = dataHistoryRepository
                .findByDataItemAndTimestampBetween(dataItem, start, end);
            
            // 转换时间格式
            List<Map<String, Object>> result = histories.stream()
                .map(history -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", history.getValue());
                    map.put("timestamp", history.getTimestamp().format(DATE_FORMATTER));
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting history data for item {}: {}", dataItemId, e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取历史数据失败", e.getMessage()));
        }
    }

    @DeleteMapping("/clean-invalid")
    public ResponseEntity<?> cleanInvalidHistoryData() {
        try {
            // 1. 获取所有启用了历史记录的监控项ID
            List<String> activeDataItemIds = dataItemRepository.findByHistoryEnabledTrue()
                .stream()
                .map(DataItem::getId)
                .collect(Collectors.toList());
            
            // 2. 删除不属于这些监控项的历史数据
            int deletedCount = 0;
            if (activeDataItemIds.isEmpty()) {
                // 如果没有启用了历史记录的监控项，清理所有历史数据
                deletedCount = dataHistoryRepository.cleanAllHistoryData();
            } else {
                deletedCount = dataHistoryRepository.cleanInvalidHistoryData(activeDataItemIds);
            }
            
            log.info("已清理{}条无效历史数据", deletedCount);
            
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", deletedCount);
            result.put("message", "已成功清理" + deletedCount + "条无效历史数据");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理无效历史数据失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("清理失败", e.getMessage()));
        }
    }
} 