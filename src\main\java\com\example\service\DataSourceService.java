package com.example.service;

import com.example.entity.DataSource;
import com.example.entity.DataSet;
import com.example.repository.DataSourceRepository;
import com.example.repository.DataSetRepository;
import com.example.service.connector.DataSourceConnector;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源管理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSourceService {
    
    private final DataSourceRepository dataSourceRepository;
    private final DataSetRepository dataSetRepository;
    private final List<DataSourceConnector> connectors;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取所有数据源
     */
    public List<DataSource> getAllDataSources() {
        return dataSourceRepository.findAll();
    }
    
    /**
     * 根据ID获取数据源
     */
    public Optional<DataSource> getDataSourceById(String id) {
        return dataSourceRepository.findById(id);
    }
    
    /**
     * 根据类型获取数据源
     */
    public List<DataSource> getDataSourcesByType(String type) {
        return dataSourceRepository.findByType(type);
    }
    
    /**
     * 获取启用的数据源
     */
    public List<DataSource> getEnabledDataSources() {
        return dataSourceRepository.findByEnabled(true);
    }
    
    /**
     * 创建数据源
     */
    @Transactional
    public DataSource createDataSource(DataSource dataSource) {
        // 检查名称是否已存在
        if (dataSourceRepository.existsByName(dataSource.getName())) {
            throw new IllegalArgumentException("数据源名称已存在: " + dataSource.getName());
        }
        
        // 生成ID
        if (dataSource.getId() == null || dataSource.getId().isEmpty()) {
            dataSource.setId(generateDataSourceId());
        }
        
        // 设置默认值
        if (dataSource.getEnabled() == null) {
            dataSource.setEnabled(true);
        }
        if (dataSource.getStatus() == null) {
            dataSource.setStatus("disconnected");
        }
        
        return dataSourceRepository.save(dataSource);
    }
    
    /**
     * 更新数据源
     */
    @Transactional
    public DataSource updateDataSource(String id, DataSource dataSource) {
        DataSource existing = dataSourceRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + id));
        
        // 检查名称是否已存在（排除当前数据源）
        if (!existing.getName().equals(dataSource.getName()) && 
            dataSourceRepository.existsByName(dataSource.getName())) {
            throw new IllegalArgumentException("数据源名称已存在: " + dataSource.getName());
        }
        
        // 更新字段
        existing.setName(dataSource.getName());
        existing.setDescription(dataSource.getDescription());
        existing.setType(dataSource.getType());
        existing.setConnectionConfig(dataSource.getConnectionConfig());
        existing.setEnabled(dataSource.getEnabled());
        existing.setUpdatedBy(dataSource.getUpdatedBy());
        
        return dataSourceRepository.save(existing);
    }
    
    /**
     * 删除数据源
     */
    @Transactional
    public void deleteDataSource(String id) {
        DataSource dataSource = dataSourceRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + id));
        
        // 检查是否有关联的数据集
        long dataSetCount = dataSetRepository.countByDataSourceId(id);
        if (dataSetCount > 0) {
            throw new IllegalArgumentException("无法删除数据源，存在 " + dataSetCount + " 个关联的数据集");
        }
        
        dataSourceRepository.delete(dataSource);
    }
    
    /**
     * 测试数据源连接
     */
    public DataSourceConnector.ConnectionTestResult testConnection(String id) {
        DataSource dataSource = dataSourceRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + id));
        
        DataSourceConnector connector = getConnector(dataSource.getType());
        DataSourceConnector.ConnectionTestResult result = connector.testConnection(dataSource.getConnectionConfig());
        
        // 更新数据源状态
        dataSource.setStatus(result.isSuccess() ? "connected" : "error");
        dataSource.setLastTestTime(LocalDateTime.now());
        dataSource.setTestMessage(result.getMessage());
        dataSourceRepository.save(dataSource);
        
        return result;
    }
    
    /**
     * 获取数据源元数据
     */
    public DataSourceConnector.MetadataResult getMetadata(String id) {
        DataSource dataSource = dataSourceRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + id));
        
        DataSourceConnector connector = getConnector(dataSource.getType());
        return connector.getMetadata(dataSource.getConnectionConfig());
    }
    
    /**
     * 获取数据源统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        long totalCount = dataSourceRepository.count();
        long enabledCount = dataSourceRepository.findByEnabled(true).size();
        stats.put("total", totalCount);
        stats.put("enabled", enabledCount);
        stats.put("disabled", totalCount - enabledCount);
        
        // 按类型统计
        List<Object[]> typeStats = dataSourceRepository.countByType();
        Map<String, Long> typeMap = typeStats.stream()
            .collect(Collectors.toMap(
                arr -> (String) arr[0],
                arr -> (Long) arr[1]
            ));
        stats.put("byType", typeMap);
        
        // 按状态统计
        List<Object[]> statusStats = dataSourceRepository.countByStatus();
        Map<String, Long> statusMap = statusStats.stream()
            .collect(Collectors.toMap(
                arr -> (String) arr[0],
                arr -> (Long) arr[1]
            ));
        stats.put("byStatus", statusMap);
        
        return stats;
    }
    
    /**
     * 获取支持的数据源类型
     */
    public List<Map<String, Object>> getSupportedTypes() {
        return connectors.stream()
            .map(connector -> {
                Map<String, Object> type = new HashMap<>();
                type.put("type", connector.getType());
                type.put("name", getTypeName(connector.getType()));
                type.put("description", getTypeDescription(connector.getType()));
                return type;
            })
            .collect(Collectors.toList());
    }

    /**
     * 测试临时数据源连接（不保存到数据库）
     */
    public DataSourceConnector.ConnectionTestResult testTempConnection(DataSource dataSource) {
        if (dataSource.getType() == null || dataSource.getType().isEmpty()) {
            throw new IllegalArgumentException("数据源类型不能为空");
        }

        if (dataSource.getConnectionConfig() == null || dataSource.getConnectionConfig().isEmpty()) {
            throw new IllegalArgumentException("连接配置不能为空");
        }

        DataSourceConnector connector = getConnector(dataSource.getType());
        return connector.testConnection(dataSource.getConnectionConfig());
    }
    
    /**
     * 根据类型获取连接器
     */
    private DataSourceConnector getConnector(String type) {
        return connectors.stream()
            .filter(connector -> connector.getType().equals(type))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("不支持的数据源类型: " + type));
    }
    
    /**
     * 生成数据源ID
     */
    private String generateDataSourceId() {
        return "ds_" + System.currentTimeMillis();
    }
    
    /**
     * 获取类型显示名称
     */
    private String getTypeName(String type) {
        switch (type) {
            case "database": return "数据库";
            case "api": return "API接口";
            case "file": return "文件";
            case "mqtt": return "MQTT";
            default: return type;
        }
    }
    
    /**
     * 获取类型描述
     */
    private String getTypeDescription(String type) {
        switch (type) {
            case "database": return "支持MySQL、PostgreSQL、SQL Server等数据库";
            case "api": return "支持REST API、GraphQL等接口";
            case "file": return "支持CSV、Excel、JSON等文件格式";
            case "mqtt": return "支持MQTT实时数据流";
            default: return "";
        }
    }
}
