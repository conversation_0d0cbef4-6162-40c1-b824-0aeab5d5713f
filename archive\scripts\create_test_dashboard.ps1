Write-Host "=== 创建测试大屏 ===" -ForegroundColor Green

# 创建测试大屏
Write-Host "创建测试大屏..." -ForegroundColor Yellow
$dashboardData = @{
    name = "测试大屏"
    description = "用于测试组件状态管理的大屏"
    canvasConfig = @{
        width = 1920
        height = 1080
        backgroundColor = "#000000"
    } | ConvertTo-Json
} | ConvertTo-Json

$createResponse = Invoke-RestMethod -Uri 'http://localhost:8080/api/bi/dashboards' -Method Post -Body $dashboardData -ContentType 'application/json'

if ($createResponse.success) {
    $dashboardId = $createResponse.data.id
    Write-Host "✅ 大屏创建成功，ID: $dashboardId" -ForegroundColor Green
    
    # 创建测试组件
    Write-Host "创建测试组件..." -ForegroundColor Yellow
    $layoutData = @{
        canvasConfig = @{
            width = 1920
            height = 1080
            backgroundColor = "#000000"
        }
        widgets = @(
            @{
                type = "text-label"
                value = @{
                    widgetId = 1001
                    setup = @{
                        text = "测试文本组件"
                        fontSize = 18
                        color = "#ffffff"
                        backgroundColor = "#333333"
                    }
                    data = @{
                        dataSourceType = "static"
                        dataItemId = $null
                    }
                    position = @{
                        left = 100
                        top = 100
                        width = 200
                        height = 50
                        zIndex = 1001
                    }
                }
                isModified = $true
                lastModified = (Get-Date).ToString('yyyy-MM-ddTHH:mm:ss.fffZ')
                modifiedPaths = @("setup.text", "setup.fontSize", "setup.color")
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $saveResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$dashboardId/layout" -Method Put -Body $layoutData -ContentType 'application/json'
    
    if ($saveResponse.success) {
        Write-Host "✅ 组件创建成功" -ForegroundColor Green
        Write-Host "保存的组件数量: $($saveResponse.savedWidgets)" -ForegroundColor Cyan
        
        # 验证组件状态保存
        Write-Host "验证组件状态..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
        
        $widgets = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$dashboardId/widgets" -Method Get
        
        if ($widgets.data.Count -gt 0) {
            $widget = $widgets.data[0]
            Write-Host "组件类型: $($widget.widgetType)" -ForegroundColor Cyan
            Write-Host "组件配置长度: $($widget.config.Length)" -ForegroundColor Cyan
            
            if ($widget.config.Contains('_stateInfo')) {
                Write-Host "✅ 组件状态信息已保存" -ForegroundColor Green
                
                # 解析状态信息
                $config = $widget.config | ConvertFrom-Json
                if ($config._stateInfo) {
                    Write-Host "修改状态: $($config._stateInfo.isModified)" -ForegroundColor Cyan
                    Write-Host "最后修改时间: $($config._stateInfo.lastModified)" -ForegroundColor Cyan
                    Write-Host "修改路径: $($config._stateInfo.modifiedPaths -join ', ')" -ForegroundColor Cyan
                }
            } else {
                Write-Host "❌ 组件状态信息未保存" -ForegroundColor Red
            }
        }
        
        Write-Host "`n大屏设计器URL: http://localhost:8080/bi/dashboard/$dashboardId/designer" -ForegroundColor Green
        
    } else {
        Write-Host "❌ 组件创建失败" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 大屏创建失败" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
