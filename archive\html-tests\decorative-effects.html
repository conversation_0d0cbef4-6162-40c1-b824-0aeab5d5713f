<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>装饰动态效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
        }
        
        .decorative-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: transparent;
        }
        
        /* 浮动粒子装饰 */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #00ffff, transparent);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
            opacity: 0.7;
        }
        
        .particle:nth-child(1) { left: 10%; top: 20%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; top: 80%; animation-delay: 1s; }
        .particle:nth-child(3) { left: 80%; top: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { left: 70%; top: 70%; animation-delay: 3s; }
        .particle:nth-child(5) { left: 50%; top: 10%; animation-delay: 4s; }
        .particle:nth-child(6) { left: 30%; top: 50%; animation-delay: 5s; }
        
        @keyframes float {
            0%, 100% { 
                transform: translateY(0px) scale(1);
                opacity: 0.7;
            }
            50% { 
                transform: translateY(-20px) scale(1.2);
                opacity: 1;
            }
        }
        
        /* 旋转几何装饰 */
        .geometric-decorations {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .geo-shape {
            position: absolute;
            border: 2px solid;
            animation: rotate 8s linear infinite;
        }
        
        .triangle {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 25px solid #ff00cc;
            top: 15%;
            right: 10%;
            animation: rotate 10s linear infinite;
        }
        
        .diamond {
            width: 20px;
            height: 20px;
            background: transparent;
            border: 2px solid #00ff88;
            transform: rotate(45deg);
            top: 60%;
            left: 15%;
            animation: rotate 12s linear infinite reverse;
        }
        
        .hexagon {
            width: 30px;
            height: 17px;
            background: transparent;
            border: 2px solid #ffaa00;
            position: relative;
            top: 25%;
            right: 20%;
        }
        
        .hexagon:before,
        .hexagon:after {
            content: "";
            position: absolute;
            width: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
        }
        
        .hexagon:before {
            bottom: 100%;
            border-bottom: 8px solid #ffaa00;
        }
        
        .hexagon:after {
            top: 100%;
            border-top: 8px solid #ffaa00;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 光效装饰 */
        .light-effects {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .light-beam {
            position: absolute;
            width: 2px;
            height: 100px;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(0, 212, 255, 0.8), 
                transparent);
            animation: beam-move 4s ease-in-out infinite;
        }
        
        .beam-1 {
            left: 25%;
            top: 10%;
            animation-delay: 0s;
        }
        
        .beam-2 {
            right: 30%;
            bottom: 20%;
            animation-delay: 2s;
        }
        
        @keyframes beam-move {
            0%, 100% { 
                opacity: 0.3;
                transform: scaleY(0.5);
            }
            50% { 
                opacity: 1;
                transform: scaleY(1.2);
            }
        }
        
        /* 能量波纹 */
        .energy-ripples {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .ripple {
            position: absolute;
            border: 2px solid rgba(0, 255, 255, 0.6);
            border-radius: 50%;
            animation: ripple-expand 3s ease-out infinite;
        }
        
        .ripple-1 {
            width: 50px;
            height: 50px;
            top: 40%;
            left: 60%;
            animation-delay: 0s;
        }
        
        .ripple-2 {
            width: 30px;
            height: 30px;
            bottom: 30%;
            left: 20%;
            animation-delay: 1.5s;
        }
        
        @keyframes ripple-expand {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(3);
                opacity: 0;
            }
        }
        
        /* 装饰线条 */
        .decorative-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .deco-line {
            position: absolute;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 0, 204, 0.8), 
                transparent);
            animation: line-flow 5s ease-in-out infinite;
        }
        
        .line-horizontal {
            width: 150px;
            height: 2px;
            top: 35%;
            left: 10%;
        }
        
        .line-vertical {
            width: 2px;
            height: 120px;
            right: 15%;
            top: 45%;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(0, 255, 136, 0.8), 
                transparent);
        }
        
        @keyframes line-flow {
            0%, 100% { 
                opacity: 0.4;
                transform: scale(1);
            }
            50% { 
                opacity: 1;
                transform: scale(1.1);
            }
        }
        
        /* 星光闪烁 */
        .starlight {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .star {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #ffffff;
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            animation: twinkle 2s ease-in-out infinite;
        }
        
        .star:nth-child(1) { top: 25%; left: 45%; animation-delay: 0s; }
        .star:nth-child(2) { top: 65%; right: 25%; animation-delay: 0.7s; }
        .star:nth-child(3) { bottom: 40%; left: 35%; animation-delay: 1.4s; }
        
        @keyframes twinkle {
            0%, 100% { 
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% { 
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .particle { width: 3px; height: 3px; }
            .geo-shape { transform: scale(0.8); }
            .light-beam { height: 60px; }
        }
    </style>
</head>
<body>
    <div class="decorative-container">
        <!-- 浮动粒子 -->
        <div class="floating-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <!-- 几何装饰 -->
        <div class="geometric-decorations">
            <div class="geo-shape triangle"></div>
            <div class="geo-shape diamond"></div>
            <div class="geo-shape hexagon"></div>
        </div>
        
        <!-- 光效 -->
        <div class="light-effects">
            <div class="light-beam beam-1"></div>
            <div class="light-beam beam-2"></div>
        </div>
        
        <!-- 能量波纹 -->
        <div class="energy-ripples">
            <div class="ripple ripple-1"></div>
            <div class="ripple ripple-2"></div>
        </div>
        
        <!-- 装饰线条 -->
        <div class="decorative-lines">
            <div class="deco-line line-horizontal"></div>
            <div class="deco-line line-vertical"></div>
        </div>
        
        <!-- 星光 -->
        <div class="starlight">
            <div class="star"></div>
            <div class="star"></div>
            <div class="star"></div>
        </div>
    </div>
</body>
</html>
