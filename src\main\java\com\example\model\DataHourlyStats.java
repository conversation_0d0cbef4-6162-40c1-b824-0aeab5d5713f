package com.example.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "data_hourly_stats")
public class DataHourlyStats {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "data_item_id", nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnore
    private DataItem dataItem;
    
    @Column(name = "data_item_name", nullable = false)
    private String dataItemName;
    
    @Column(name = "data_item_address", nullable = false)
    private String dataItemAddress;
    
    @Column(name = "device_id", nullable = false)
    private String deviceId;
    
    @Column(name = "device_name", nullable = false)
    private String deviceName;
    
    @Column(name = "hour_timestamp", nullable = false)
    private LocalDateTime hourTimestamp;
    
    @Column(name = "max_value", nullable = false)
    private Integer maxValue;
    
    @Column(name = "min_value", nullable = false)
    private Integer minValue;
    
    @Column(name = "avg_value", nullable = false, precision = 10, scale = 2)
    private BigDecimal avgValue;
    
    @Column(name = "diff_value")
    private Integer diffValue;
    
    @Column(name = "sample_count", nullable = false)
    private Integer sampleCount;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
}
