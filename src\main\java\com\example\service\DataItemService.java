package com.example.service;

import com.example.model.DataItem;
import com.example.repository.DataItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class DataItemService {
    
    @Autowired
    private DataItemRepository dataItemRepository;
    
    @Transactional(readOnly = true)
    public Optional<DataItem> getDataItem(String id) {
        return dataItemRepository.findById(id);
    }
    
    @Transactional(readOnly = true)
    public List<DataItem> getDeviceDataItems(String deviceId) {
        return dataItemRepository.findByDeviceId(deviceId);
    }
    
    @Transactional
    public DataItem updateDataItem(String id, String name, Integer refreshInterval) {
        DataItem dataItem = dataItemRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据项不存在: " + id));
            
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("名称不能为空");
        }
        if (refreshInterval == null || refreshInterval < 100) {
            throw new IllegalArgumentException("更新间隔不能小于100毫秒");
        }
        
        dataItem.setName(name.trim());
        dataItem.setRefreshInterval(refreshInterval);
        
        return dataItemRepository.save(dataItem);
    }
    
    @Transactional
    public void deleteDataItem(String id) {
        dataItemRepository.deleteById(id);
    }
} 