import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * SDPLC Serial Number Generator - GUI Version
 * Standalone application, no external dependencies required
 */
public class SDPLCSerialGenerator extends JFrame {
    
    // Constants (consistent with LicenseUtils)
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String DATE_FORMAT = "yyyyMMdd";
    
    // UI Components
    private JTextField userIdField;
    private JTextField startDateField;
    private JTextField endDateField;
    private JTextField licenseStartDateField;
    private JTextField licenseEndDateField;
    private JTextArea serialNumberArea;
    private JButton generateButton;
    private JButton copyButton;
    private JButton clearButton;
    private JLabel statusLabel;
    
    public SDPLCSerialGenerator() {
        initializeUI();
        setDefaultValues();
    }
    
    private void initializeUI() {
        setTitle("SDPLC序列号生成器 v2.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
        
        // Set system look and feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            // Ignore exception, use default style
        }
        
        // Create main panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        mainPanel.setBackground(Color.WHITE);
        
        // Add components
        mainPanel.add(createTitlePanel(), BorderLayout.NORTH);
        mainPanel.add(createInputPanel(), BorderLayout.CENTER);
        mainPanel.add(createOutputPanel(), BorderLayout.SOUTH);
        
        add(mainPanel);
        pack();
        setLocationRelativeTo(null); // Center on screen
    }
    
    private JPanel createTitlePanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel("🔑 SDPLC序列号生成器");
        titleLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 24));
        titleLabel.setForeground(new Color(51, 122, 183));
        
        panel.add(titleLabel);
        return panel;
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(51, 122, 183), 2),
            "序列号参数设置",
            0, 0,
            new Font("Microsoft YaHei", Font.BOLD, 14),
            new Color(51, 122, 183)
        ));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.WEST;
        
        // User ID
        gbc.gridx = 0; gbc.gridy = 0;
        JLabel userIdLabel = new JLabel("用户ID:");
        userIdLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(userIdLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 0;
        userIdField = new JTextField(15);
        userIdField.setFont(new Font("Consolas", Font.PLAIN, 14));
        userIdField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        // Add input listener to convert to uppercase
        userIdField.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyReleased(java.awt.event.KeyEvent evt) {
                String text = userIdField.getText();
                userIdField.setText(text.toUpperCase());
            }
        });
        panel.add(userIdField, gbc);

        gbc.gridx = 2; gbc.gridy = 0;
        JButton randomUserButton = new JButton("随机生成");
        randomUserButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        randomUserButton.addActionListener(e -> generateRandomUserId());
        panel.add(randomUserButton, gbc);

        // Add user ID format hint below the button
        gbc.gridx = 1; gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(35, 10, 0, 10); // Top margin to place below button
        JLabel userIdHint = new JLabel("格式：USER001（仅支持大写字母和数字）");
        userIdHint.setFont(new Font("Microsoft YaHei", Font.PLAIN, 10));
        userIdHint.setForeground(Color.GRAY);
        panel.add(userIdHint, gbc);

        // Reset constraints
        gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // Start Date
        gbc.gridx = 0; gbc.gridy = 1;
        JLabel startDateLabel = new JLabel("开始日期:");
        startDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(startDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 1;
        startDateField = new JTextField(15);
        startDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        startDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(startDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 1;
        JButton todayButton = new JButton("今天");
        todayButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        todayButton.addActionListener(e -> setToday());
        panel.add(todayButton, gbc);
        
        // End Date
        gbc.gridx = 0; gbc.gridy = 2;
        JLabel endDateLabel = new JLabel("激活结束日期:");
        endDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(endDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 2;
        endDateField = new JTextField(15);
        endDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        endDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(endDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 2;
        JButton tomorrowButton = new JButton("明天");
        tomorrowButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        tomorrowButton.addActionListener(e -> setTomorrow());
        panel.add(tomorrowButton, gbc);

        // License Start Date
        gbc.gridx = 0; gbc.gridy = 3;
        JLabel licenseStartDateLabel = new JLabel("许可开始日期:");
        licenseStartDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(licenseStartDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 3;
        licenseStartDateField = new JTextField(15);
        licenseStartDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        licenseStartDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(licenseStartDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 3;
        JButton licenseStartTodayButton = new JButton("今天");
        licenseStartTodayButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        licenseStartTodayButton.addActionListener(e -> setLicenseStartToday());
        panel.add(licenseStartTodayButton, gbc);

        // License End Date
        gbc.gridx = 0; gbc.gridy = 4;
        JLabel licenseEndDateLabel = new JLabel("许可结束日期:");
        licenseEndDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(licenseEndDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 4;
        licenseEndDateField = new JTextField(15);
        licenseEndDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        licenseEndDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(licenseEndDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 4;
        JButton licenseEndButton = new JButton("1年后");
        licenseEndButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        licenseEndButton.addActionListener(e -> setLicenseEndOneYear());
        panel.add(licenseEndButton, gbc);
        
        // Quick setup buttons for activation period
        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 3;
        JPanel quickActivationPanel = new JPanel(new FlowLayout());
        quickActivationPanel.setBackground(Color.WHITE);
        quickActivationPanel.setBorder(BorderFactory.createTitledBorder("激活期快捷设置"));

        JButton day1Button = new JButton("1天激活期");
        day1Button.addActionListener(e -> setActivationPeriod(1));
        JButton day7Button = new JButton("7天激活期");
        day7Button.addActionListener(e -> setActivationPeriod(7));
        JButton day30Button = new JButton("30天激活期");
        day30Button.addActionListener(e -> setActivationPeriod(30));

        quickActivationPanel.add(day1Button);
        quickActivationPanel.add(day7Button);
        quickActivationPanel.add(day30Button);

        panel.add(quickActivationPanel, gbc);

        // Quick setup buttons for license period
        gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 3;
        JPanel quickLicensePanel = new JPanel(new FlowLayout());
        quickLicensePanel.setBackground(Color.WHITE);
        quickLicensePanel.setBorder(BorderFactory.createTitledBorder("许可期快捷设置"));

        JButton month1Button = new JButton("1个月许可");
        month1Button.addActionListener(e -> setLicensePeriod(30));
        JButton month6Button = new JButton("6个月许可");
        month6Button.addActionListener(e -> setLicensePeriod(180));
        JButton year1Button = new JButton("1年许可");
        year1Button.addActionListener(e -> setLicensePeriod(365));
        JButton year3Button = new JButton("3年许可");
        year3Button.addActionListener(e -> setLicensePeriod(1095));

        quickLicensePanel.add(month1Button);
        quickLicensePanel.add(month6Button);
        quickLicensePanel.add(year1Button);
        quickLicensePanel.add(year3Button);

        panel.add(quickLicensePanel, gbc);
        
        return panel;
    }
    
    private JPanel createOutputPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(40, 167, 69), 2),
            "生成的序列号",
            0, 0,
            new Font("Microsoft YaHei", Font.BOLD, 14),
            new Color(40, 167, 69)
        ));
        
        // Serial number display area
        serialNumberArea = new JTextArea(4, 50);
        serialNumberArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        serialNumberArea.setLineWrap(true);
        serialNumberArea.setWrapStyleWord(true);
        serialNumberArea.setEditable(false);
        serialNumberArea.setBackground(new Color(248, 249, 250));
        serialNumberArea.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JScrollPane scrollPane = new JScrollPane(serialNumberArea);
        scrollPane.setPreferredSize(new Dimension(500, 100));
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(Color.WHITE);
        
        generateButton = new JButton("🔧 生成序列号");
        generateButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 14));
        generateButton.setBackground(new Color(51, 122, 183));
        generateButton.setForeground(Color.WHITE);
        generateButton.setPreferredSize(new Dimension(150, 40));
        generateButton.addActionListener(new GenerateAction());

        copyButton = new JButton("📋 复制到剪贴板");
        copyButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        copyButton.setBackground(new Color(40, 167, 69));
        copyButton.setForeground(Color.WHITE);
        copyButton.setPreferredSize(new Dimension(150, 40));
        copyButton.setEnabled(false);
        copyButton.addActionListener(new CopyAction());

        clearButton = new JButton("🗑️ 清空");
        clearButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        clearButton.setBackground(new Color(220, 53, 69));
        clearButton.setForeground(Color.WHITE);
        clearButton.setPreferredSize(new Dimension(100, 40));
        clearButton.addActionListener(e -> clearAll());

        JButton parseButton = new JButton("🔍 许可解析");
        parseButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        parseButton.setBackground(new Color(40, 167, 69));
        parseButton.setForeground(Color.WHITE);
        parseButton.setPreferredSize(new Dimension(120, 40));
        parseButton.addActionListener(new ParseLicenseAction());

        buttonPanel.add(generateButton);
        buttonPanel.add(copyButton);
        buttonPanel.add(clearButton);
        buttonPanel.add(parseButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Status label
        statusLabel = new JLabel("准备就绪");
        statusLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        statusLabel.setForeground(Color.GRAY);
        panel.add(statusLabel, BorderLayout.NORTH);
        
        return panel;
    }
    
    private void setDefaultValues() {
        userIdField.setText("USER001");
        LocalDate today = LocalDate.now();
        // 默认激活期：今天到30天后
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        endDateField.setText(today.plusDays(30).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        // 默认许可期：今天到1年后
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        licenseEndDateField.setText(today.plusYears(1).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void generateRandomUserId() {
        int randomNum = (int) (Math.random() * 999) + 1;
        userIdField.setText(String.format("USER%03d", randomNum));
    }
    
    private void setToday() {
        LocalDate today = LocalDate.now();
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void setTomorrow() {
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        endDateField.setText(tomorrow.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void setActivationPeriod(int days) {
        LocalDate today = LocalDate.now();
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        endDateField.setText(today.plusDays(days).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicensePeriod(int days) {
        LocalDate today = LocalDate.now();
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        licenseEndDateField.setText(today.plusDays(days).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicenseStartToday() {
        LocalDate today = LocalDate.now();
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicenseEndOneYear() {
        LocalDate oneYearLater = LocalDate.now().plusYears(1);
        licenseEndDateField.setText(oneYearLater.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void showLicenseParserDialog() {
        LicenseParserDialog dialog = new LicenseParserDialog(this);
        dialog.setVisible(true);
    }
    
    private void clearAll() {
        serialNumberArea.setText("");
        copyButton.setEnabled(false);
        statusLabel.setText("已清空");
    }

    // Core serial number generation method
    private String generateSerialNumber(String userId, String startDate, String endDate,
                                      String licenseStartDate, String licenseEndDate) {
        try {
            // Generate checksum (include all date information)
            String checksum = generateChecksum(userId + startDate + endDate + licenseStartDate + licenseEndDate);

            // Construct JSON string manually (avoid Jackson dependency)
            String json = String.format(
                "{\"userId\":\"%s\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"licenseStartDate\":\"%s\",\"licenseEndDate\":\"%s\",\"checksum\":\"%s\"}",
                userId, startDate, endDate, licenseStartDate, licenseEndDate, checksum
            );

            // AES encrypt
            byte[] encrypted = aesEncrypt(json);

            // Base64 encode and add prefix
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败: " + e.getMessage(), e);
        }
    }

    private String generateChecksum(String input) {
        return md5(input).substring(0, 6).toUpperCase();
    }

    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5计算失败", e);
        }
    }

    private byte[] aesEncrypt(String data) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    // Event handler classes
    private class ParseLicenseAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            showLicenseParserDialog();
        }
    }

    private class GenerateAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String userId = userIdField.getText().trim();
                String startDate = startDateField.getText().trim();
                String endDate = endDateField.getText().trim();
                String licenseStartDate = licenseStartDateField.getText().trim();
                String licenseEndDate = licenseEndDateField.getText().trim();

                // Validate input
                if (userId.isEmpty() || startDate.isEmpty() || endDate.isEmpty() ||
                    licenseStartDate.isEmpty() || licenseEndDate.isEmpty()) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "请填写所有字段！", "输入错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("输入不完整");
                    return;
                }

                // Validate user ID format (no lowercase letters)
                if (!isValidUserId(userId)) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "用户ID格式不正确！\n只支持大写字母和数字，不支持小写字母。\n正确格式示例：USER001",
                        "用户ID格式错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("用户ID格式错误");
                    return;
                }

                // Validate date format
                if (!isValidDate(startDate) || !isValidDate(endDate) ||
                    !isValidDate(licenseStartDate) || !isValidDate(licenseEndDate)) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "日期格式不正确！请使用YYYYMMDD格式，例如：20250728",
                        "日期格式错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("日期格式错误");
                    return;
                }

                statusLabel.setText("正在生成序列号...");

                // Generate serial number with license period
                String serialNumber = generateSerialNumber(userId, startDate, endDate, licenseStartDate, licenseEndDate);
                serialNumberArea.setText(serialNumber);

                copyButton.setEnabled(true);
                statusLabel.setText("序列号生成成功！");

                // Show success message
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "序列号生成成功！\n\n" +
                    "用户ID: " + userId + "\n" +
                    "激活有效期: " + startDate + " 至 " + endDate + "\n" +
                    "（在此期间内可以使用序列号进行激活安装）\n" +
                    "许可使用期: " + licenseStartDate + " 至 " + licenseEndDate + "\n" +
                    "（激活后软件的实际使用期限）\n\n" +
                    "请点击'复制到剪贴板'按钮复制序列号。",
                    "生成成功", JOptionPane.INFORMATION_MESSAGE);

            } catch (Exception ex) {
                statusLabel.setText("生成失败");
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "生成失败：" + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private class CopyAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String serialNumber = serialNumberArea.getText();
            if (!serialNumber.isEmpty()) {
                StringSelection selection = new StringSelection(serialNumber);
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
                statusLabel.setText("序列号已复制到剪贴板");
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "序列号已成功复制到剪贴板！\n\n" +
                    "现在可以在SDPLC应用中粘贴使用。",
                    "复制成功", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    private boolean isValidDate(String date) {
        if (date.length() != 8) return false;
        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_FORMAT));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        // Check if contains only uppercase letters, digits, and allowed special characters
        return userId.matches("^[A-Z0-9_-]+$");
    }

    public static void main(String[] args) {
        // Set system look and feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            // Ignore exception
        }

        SwingUtilities.invokeLater(() -> {
            new SDPLCSerialGenerator().setVisible(true);
        });
    }

    // 许可解析对话框类
    private static class LicenseParserDialog extends JDialog {
        private static final String DEFAULT_LICENSE_PATH = "C:\\ProgramData\\SHENGDA-PLC\\license.json";

        private JTextField pathField;
        private JTextArea resultArea;
        private JButton parseButton;
        private JButton browseButton;
        private JButton copyButton;

        public LicenseParserDialog(JFrame parent) {
            super(parent, "许可文件解析器", true);
            initializeComponents();
            setupLayout();
            setDefaultCloseOperation(DISPOSE_ON_CLOSE);
            setSize(600, 500);
            setLocationRelativeTo(parent);
        }

        private void initializeComponents() {
            pathField = new JTextField(DEFAULT_LICENSE_PATH);
            pathField.setFont(new Font("Consolas", Font.PLAIN, 12));

            resultArea = new JTextArea(15, 50);
            resultArea.setFont(new Font("Consolas", Font.PLAIN, 12));
            resultArea.setEditable(false);
            resultArea.setBackground(new Color(248, 249, 250));
            resultArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

            parseButton = new JButton("🔍 解析许可文件");
            parseButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
            parseButton.setBackground(new Color(0, 123, 255));
            parseButton.setForeground(Color.WHITE);
            parseButton.addActionListener(e -> parseLicenseFile());

            browseButton = new JButton("📁 浏览");
            browseButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
            browseButton.addActionListener(e -> browseFile());

            copyButton = new JButton("📋 复制结果");
            copyButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
            copyButton.setEnabled(false);
            copyButton.addActionListener(e -> copyResult());
        }

        private void setupLayout() {
            setLayout(new BorderLayout(10, 10));

            // 顶部面板 - 文件路径选择
            JPanel topPanel = new JPanel(new BorderLayout(5, 5));
            topPanel.setBorder(BorderFactory.createTitledBorder("许可文件路径"));
            topPanel.add(new JLabel("文件路径:"), BorderLayout.WEST);
            topPanel.add(pathField, BorderLayout.CENTER);
            topPanel.add(browseButton, BorderLayout.EAST);

            // 中间面板 - 解析结果显示
            JPanel centerPanel = new JPanel(new BorderLayout());
            centerPanel.setBorder(BorderFactory.createTitledBorder("解析结果"));
            JScrollPane scrollPane = new JScrollPane(resultArea);
            centerPanel.add(scrollPane, BorderLayout.CENTER);

            // 底部面板 - 操作按钮
            JPanel bottomPanel = new JPanel(new FlowLayout());
            bottomPanel.add(parseButton);
            bottomPanel.add(copyButton);

            add(topPanel, BorderLayout.NORTH);
            add(centerPanel, BorderLayout.CENTER);
            add(bottomPanel, BorderLayout.SOUTH);

            // 添加边距
            ((JComponent) getContentPane()).setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        }

        private void browseFile() {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new javax.swing.filechooser.FileFilter() {
                @Override
                public boolean accept(File f) {
                    return f.isDirectory() || f.getName().toLowerCase().endsWith(".json");
                }

                @Override
                public String getDescription() {
                    return "JSON文件 (*.json)";
                }
            });

            if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
                pathField.setText(fileChooser.getSelectedFile().getAbsolutePath());
            }
        }

        private void parseLicenseFile() {
            String filePath = pathField.getText().trim();
            if (filePath.isEmpty()) {
                showError("请输入许可文件路径");
                return;
            }

            try {
                File licenseFile = new File(filePath);
                if (!licenseFile.exists()) {
                    showError("许可文件不存在: " + filePath);
                    return;
                }

                // 读取加密的许可文件
                String encryptedContent = new String(java.nio.file.Files.readAllBytes(licenseFile.toPath()),
                                                   StandardCharsets.UTF_8);

                // 解密许可证内容
                String licenseJson = decryptLicenseFile(encryptedContent);

                // 解析JSON (手动解析避免Jackson依赖)
                Map<String, String> license = parseJsonManually(licenseJson);

                // 显示解析结果
                displayLicenseInfo(license);

                copyButton.setEnabled(true);

            } catch (Exception e) {
                showError("解析失败: " + e.getMessage());
            }
        }

        private void displayLicenseInfo(Map<String, String> license) {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 许可文件信息 ===\n\n");

            // 基本信息
            sb.append("用户ID: ").append(license.getOrDefault("userId", "未知")).append("\n");
            sb.append("安装时间: ").append(license.getOrDefault("installTime", "未知")).append("\n");
            sb.append("硬件ID: ").append(license.getOrDefault("hardwareId", "未知")).append("\n\n");

            // 许可期限信息
            String licenseStartDate = license.get("licenseStartDate");
            String licenseEndDate = license.get("licenseEndDate");

            if (licenseStartDate != null && licenseEndDate != null) {
                sb.append("=== 许可使用期限 ===\n");
                sb.append("开始日期: ").append(formatDate(licenseStartDate)).append("\n");
                sb.append("结束日期: ").append(formatDate(licenseEndDate)).append("\n");

                // 计算剩余天数
                try {
                    LocalDate today = LocalDate.now();
                    LocalDate endDate = LocalDate.parse(licenseEndDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    long daysRemaining = java.time.temporal.ChronoUnit.DAYS.between(today, endDate);

                    if (daysRemaining > 0) {
                        sb.append("剩余天数: ").append(daysRemaining).append(" 天\n");
                        sb.append("状态: ✅ 有效\n");
                    } else if (daysRemaining == 0) {
                        sb.append("状态: ⚠️ 今天到期\n");
                    } else {
                        sb.append("状态: ❌ 已过期 (").append(-daysRemaining).append(" 天前)\n");
                    }
                } catch (Exception e) {
                    sb.append("状态: ❓ 无法计算\n");
                }
            } else {
                sb.append("=== 许可期限信息 ===\n");
                sb.append("⚠️ 未找到许可期限信息 (可能是旧格式许可文件)\n");
            }

            sb.append("\n=== 硬件绑定验证 ===\n");
            try {
                String currentHardwareId = generateHardwareId();
                String fileHardwareId = license.getOrDefault("hardwareId", "");

                sb.append("当前硬件ID: ").append(currentHardwareId).append("\n");
                sb.append("许可硬件ID: ").append(fileHardwareId).append("\n");

                if (currentHardwareId.equals(fileHardwareId)) {
                    sb.append("硬件绑定: ✅ 匹配\n");
                } else {
                    sb.append("硬件绑定: ❌ 不匹配\n");
                }
            } catch (Exception e) {
                sb.append("硬件绑定: ❓ 验证失败\n");
            }

            // 序列号信息
            String serialNumber = license.get("serialNumber");
            if (serialNumber != null) {
                sb.append("\n=== 序列号信息 ===\n");
                sb.append("序列号: ").append(serialNumber).append("\n");

                try {
                    Map<String, String> serialData = parseSerialNumber(serialNumber);
                    sb.append("序列号用户ID: ").append(serialData.getOrDefault("userId", "未知")).append("\n");
                    sb.append("激活时间窗口: ").append(formatDate(serialData.get("startDate")))
                      .append(" 至 ").append(formatDate(serialData.get("endDate"))).append("\n");

                    if (serialData.containsKey("licenseStartDate")) {
                        sb.append("序列号许可期限: ").append(formatDate(serialData.get("licenseStartDate")))
                          .append(" 至 ").append(formatDate(serialData.get("licenseEndDate"))).append("\n");
                    }
                } catch (Exception e) {
                    sb.append("序列号解析: ❌ 失败 - ").append(e.getMessage()).append("\n");
                }
            }

            resultArea.setText(sb.toString());
        }

        private String formatDate(String dateStr) {
            if (dateStr == null || dateStr.length() != 8) {
                return dateStr;
            }
            try {
                return dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8);
            } catch (Exception e) {
                return dateStr;
            }
        }

        private void copyResult() {
            try {
                StringSelection selection = new StringSelection(resultArea.getText());
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
                JOptionPane.showMessageDialog(this, "解析结果已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                showError("复制失败: " + e.getMessage());
            }
        }

        private void showError(String message) {
            JOptionPane.showMessageDialog(this, message, "错误", JOptionPane.ERROR_MESSAGE);
            resultArea.setText("错误: " + message);
            copyButton.setEnabled(false);
        }

        // 许可文件解密方法 (复制自LicenseUtils)
        private String decryptLicenseFile(String encryptedData) throws Exception {
            byte[] encrypted = Base64.getDecoder().decode(encryptedData);
            return aesDecrypt(encrypted);
        }

        private String aesDecrypt(byte[] encryptedData) throws Exception {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(encryptedData);
            return new String(decrypted, StandardCharsets.UTF_8);
        }

        // 手动JSON解析方法
        private Map<String, String> parseJsonManually(String json) {
            Map<String, String> result = new HashMap<>();

            json = json.trim();
            if (json.startsWith("{") && json.endsWith("}")) {
                json = json.substring(1, json.length() - 1);
                String[] pairs = json.split(",");

                for (String pair : pairs) {
                    String[] keyValue = pair.split(":", 2);
                    if (keyValue.length == 2) {
                        String key = keyValue[0].trim().replaceAll("\"", "");
                        String value = keyValue[1].trim().replaceAll("\"", "");
                        result.put(key, value);
                    }
                }
            }

            return result;
        }

        // 序列号解析方法 (复制自主类)
        private Map<String, String> parseSerialNumber(String serialNumber) throws Exception {
            if (!serialNumber.startsWith(SERIAL_PREFIX)) {
                throw new IllegalArgumentException("序列号格式不正确");
            }

            String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
            byte[] encrypted = Base64.getDecoder().decode(base64Data);
            String json = aesDecrypt(encrypted);

            return parseJsonManually(json);
        }

        // 硬件ID生成方法 (复制自LicenseUtils逻辑)
        private String generateHardwareId() {
            try {
                String computerName = System.getProperty("user.name");
                String userName = System.getProperty("user.name");
                String input = computerName + userName;
                return md5(input).substring(0, 16).toUpperCase();
            } catch (Exception e) {
                return "UNKNOWN";
            }
        }

        // MD5哈希方法
        private String md5(String input) {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
                StringBuilder sb = new StringBuilder();
                for (byte b : hashBytes) {
                    sb.append(String.format("%02x", b));
                }
                return sb.toString();
            } catch (Exception e) {
                throw new RuntimeException("MD5计算失败", e);
            }
        }
    }
}
