import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * SDPLC Serial Number Generator - GUI Version
 * Standalone application, no external dependencies required
 */
public class SDPLCSerialGenerator extends JFrame {
    
    // Constants (consistent with LicenseUtils)
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String DATE_FORMAT = "yyyyMMdd";
    
    // UI Components
    private JTextField userIdField;
    private JTextField startDateField;
    private JTextField endDateField;
    private JTextField licenseStartDateField;
    private JTextField licenseEndDateField;
    private JTextArea serialNumberArea;
    private JButton generateButton;
    private JButton copyButton;
    private JButton clearButton;
    private JLabel statusLabel;
    
    public SDPLCSerialGenerator() {
        initializeUI();
        setDefaultValues();
    }
    
    private void initializeUI() {
        setTitle("SDPLC序列号生成器 v2.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setResizable(false);
        
        // Set system look and feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            // Ignore exception, use default style
        }
        
        // Create main panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        mainPanel.setBackground(Color.WHITE);
        
        // Add components
        mainPanel.add(createTitlePanel(), BorderLayout.NORTH);
        mainPanel.add(createInputPanel(), BorderLayout.CENTER);
        mainPanel.add(createOutputPanel(), BorderLayout.SOUTH);
        
        add(mainPanel);
        pack();
        setLocationRelativeTo(null); // Center on screen
    }
    
    private JPanel createTitlePanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel("🔑 SDPLC序列号生成器");
        titleLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 24));
        titleLabel.setForeground(new Color(51, 122, 183));
        
        panel.add(titleLabel);
        return panel;
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(51, 122, 183), 2),
            "序列号参数设置",
            0, 0,
            new Font("Microsoft YaHei", Font.BOLD, 14),
            new Color(51, 122, 183)
        ));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.WEST;
        
        // User ID
        gbc.gridx = 0; gbc.gridy = 0;
        JLabel userIdLabel = new JLabel("用户ID:");
        userIdLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(userIdLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 0;
        userIdField = new JTextField(15);
        userIdField.setFont(new Font("Consolas", Font.PLAIN, 14));
        userIdField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        // Add input listener to convert to uppercase
        userIdField.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyReleased(java.awt.event.KeyEvent evt) {
                String text = userIdField.getText();
                userIdField.setText(text.toUpperCase());
            }
        });
        panel.add(userIdField, gbc);

        gbc.gridx = 2; gbc.gridy = 0;
        JButton randomUserButton = new JButton("随机生成");
        randomUserButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        randomUserButton.addActionListener(e -> generateRandomUserId());
        panel.add(randomUserButton, gbc);

        // Add user ID format hint below the button
        gbc.gridx = 1; gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(35, 10, 0, 10); // Top margin to place below button
        JLabel userIdHint = new JLabel("格式：USER001（仅支持大写字母和数字）");
        userIdHint.setFont(new Font("Microsoft YaHei", Font.PLAIN, 10));
        userIdHint.setForeground(Color.GRAY);
        panel.add(userIdHint, gbc);

        // Reset constraints
        gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // Start Date
        gbc.gridx = 0; gbc.gridy = 1;
        JLabel startDateLabel = new JLabel("开始日期:");
        startDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(startDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 1;
        startDateField = new JTextField(15);
        startDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        startDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(startDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 1;
        JButton todayButton = new JButton("今天");
        todayButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        todayButton.addActionListener(e -> setToday());
        panel.add(todayButton, gbc);
        
        // End Date
        gbc.gridx = 0; gbc.gridy = 2;
        JLabel endDateLabel = new JLabel("激活结束日期:");
        endDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(endDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 2;
        endDateField = new JTextField(15);
        endDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        endDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(endDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 2;
        JButton tomorrowButton = new JButton("明天");
        tomorrowButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        tomorrowButton.addActionListener(e -> setTomorrow());
        panel.add(tomorrowButton, gbc);

        // License Start Date
        gbc.gridx = 0; gbc.gridy = 3;
        JLabel licenseStartDateLabel = new JLabel("许可开始日期:");
        licenseStartDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(licenseStartDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 3;
        licenseStartDateField = new JTextField(15);
        licenseStartDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        licenseStartDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(licenseStartDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 3;
        JButton licenseStartTodayButton = new JButton("今天");
        licenseStartTodayButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        licenseStartTodayButton.addActionListener(e -> setLicenseStartToday());
        panel.add(licenseStartTodayButton, gbc);

        // License End Date
        gbc.gridx = 0; gbc.gridy = 4;
        JLabel licenseEndDateLabel = new JLabel("许可结束日期:");
        licenseEndDateLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        panel.add(licenseEndDateLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 4;
        licenseEndDateField = new JTextField(15);
        licenseEndDateField.setFont(new Font("Consolas", Font.PLAIN, 14));
        licenseEndDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        panel.add(licenseEndDateField, gbc);

        gbc.gridx = 2; gbc.gridy = 4;
        JButton licenseEndButton = new JButton("1年后");
        licenseEndButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        licenseEndButton.addActionListener(e -> setLicenseEndOneYear());
        panel.add(licenseEndButton, gbc);
        
        // Quick setup buttons for activation period
        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 3;
        JPanel quickActivationPanel = new JPanel(new FlowLayout());
        quickActivationPanel.setBackground(Color.WHITE);
        quickActivationPanel.setBorder(BorderFactory.createTitledBorder("激活期快捷设置"));

        JButton day1Button = new JButton("1天激活期");
        day1Button.addActionListener(e -> setActivationPeriod(1));
        JButton day7Button = new JButton("7天激活期");
        day7Button.addActionListener(e -> setActivationPeriod(7));
        JButton day30Button = new JButton("30天激活期");
        day30Button.addActionListener(e -> setActivationPeriod(30));

        quickActivationPanel.add(day1Button);
        quickActivationPanel.add(day7Button);
        quickActivationPanel.add(day30Button);

        panel.add(quickActivationPanel, gbc);

        // Quick setup buttons for license period
        gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 3;
        JPanel quickLicensePanel = new JPanel(new FlowLayout());
        quickLicensePanel.setBackground(Color.WHITE);
        quickLicensePanel.setBorder(BorderFactory.createTitledBorder("许可期快捷设置"));

        JButton month1Button = new JButton("1个月许可");
        month1Button.addActionListener(e -> setLicensePeriod(30));
        JButton month6Button = new JButton("6个月许可");
        month6Button.addActionListener(e -> setLicensePeriod(180));
        JButton year1Button = new JButton("1年许可");
        year1Button.addActionListener(e -> setLicensePeriod(365));
        JButton year3Button = new JButton("3年许可");
        year3Button.addActionListener(e -> setLicensePeriod(1095));

        quickLicensePanel.add(month1Button);
        quickLicensePanel.add(month6Button);
        quickLicensePanel.add(year1Button);
        quickLicensePanel.add(year3Button);

        panel.add(quickLicensePanel, gbc);
        
        return panel;
    }
    
    private JPanel createOutputPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBackground(Color.WHITE);
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(40, 167, 69), 2),
            "生成的序列号",
            0, 0,
            new Font("Microsoft YaHei", Font.BOLD, 14),
            new Color(40, 167, 69)
        ));
        
        // Serial number display area
        serialNumberArea = new JTextArea(4, 50);
        serialNumberArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        serialNumberArea.setLineWrap(true);
        serialNumberArea.setWrapStyleWord(true);
        serialNumberArea.setEditable(false);
        serialNumberArea.setBackground(new Color(248, 249, 250));
        serialNumberArea.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JScrollPane scrollPane = new JScrollPane(serialNumberArea);
        scrollPane.setPreferredSize(new Dimension(500, 100));
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(Color.WHITE);
        
        generateButton = new JButton("🔧 生成序列号");
        generateButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 14));
        generateButton.setBackground(new Color(51, 122, 183));
        generateButton.setForeground(Color.WHITE);
        generateButton.setPreferredSize(new Dimension(150, 40));
        generateButton.addActionListener(new GenerateAction());

        copyButton = new JButton("📋 复制到剪贴板");
        copyButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        copyButton.setBackground(new Color(40, 167, 69));
        copyButton.setForeground(Color.WHITE);
        copyButton.setPreferredSize(new Dimension(150, 40));
        copyButton.setEnabled(false);
        copyButton.addActionListener(new CopyAction());

        clearButton = new JButton("🗑️ 清空");
        clearButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        clearButton.setBackground(new Color(220, 53, 69));
        clearButton.setForeground(Color.WHITE);
        clearButton.setPreferredSize(new Dimension(100, 40));
        clearButton.addActionListener(e -> clearAll());
        
        buttonPanel.add(generateButton);
        buttonPanel.add(copyButton);
        buttonPanel.add(clearButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Status label
        statusLabel = new JLabel("准备就绪");
        statusLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        statusLabel.setForeground(Color.GRAY);
        panel.add(statusLabel, BorderLayout.NORTH);
        
        return panel;
    }
    
    private void setDefaultValues() {
        userIdField.setText("USER001");
        LocalDate today = LocalDate.now();
        // 默认激活期：今天到30天后
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        endDateField.setText(today.plusDays(30).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        // 默认许可期：今天到1年后
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        licenseEndDateField.setText(today.plusYears(1).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void generateRandomUserId() {
        int randomNum = (int) (Math.random() * 999) + 1;
        userIdField.setText(String.format("USER%03d", randomNum));
    }
    
    private void setToday() {
        LocalDate today = LocalDate.now();
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void setTomorrow() {
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        endDateField.setText(tomorrow.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void setActivationPeriod(int days) {
        LocalDate today = LocalDate.now();
        startDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        endDateField.setText(today.plusDays(days).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicensePeriod(int days) {
        LocalDate today = LocalDate.now();
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        licenseEndDateField.setText(today.plusDays(days).format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicenseStartToday() {
        LocalDate today = LocalDate.now();
        licenseStartDateField.setText(today.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }

    private void setLicenseEndOneYear() {
        LocalDate oneYearLater = LocalDate.now().plusYears(1);
        licenseEndDateField.setText(oneYearLater.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
    }
    
    private void clearAll() {
        serialNumberArea.setText("");
        copyButton.setEnabled(false);
        statusLabel.setText("已清空");
    }

    // Core serial number generation method
    private String generateSerialNumber(String userId, String startDate, String endDate,
                                      String licenseStartDate, String licenseEndDate) {
        try {
            // Generate checksum (include all date information)
            String checksum = generateChecksum(userId + startDate + endDate + licenseStartDate + licenseEndDate);

            // Construct JSON string manually (avoid Jackson dependency)
            String json = String.format(
                "{\"userId\":\"%s\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"licenseStartDate\":\"%s\",\"licenseEndDate\":\"%s\",\"checksum\":\"%s\"}",
                userId, startDate, endDate, licenseStartDate, licenseEndDate, checksum
            );

            // AES encrypt
            byte[] encrypted = aesEncrypt(json);

            // Base64 encode and add prefix
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败: " + e.getMessage(), e);
        }
    }

    private String generateChecksum(String input) {
        return md5(input).substring(0, 6).toUpperCase();
    }

    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5计算失败", e);
        }
    }

    private byte[] aesEncrypt(String data) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    // Event handler classes
    private class GenerateAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String userId = userIdField.getText().trim();
                String startDate = startDateField.getText().trim();
                String endDate = endDateField.getText().trim();
                String licenseStartDate = licenseStartDateField.getText().trim();
                String licenseEndDate = licenseEndDateField.getText().trim();

                // Validate input
                if (userId.isEmpty() || startDate.isEmpty() || endDate.isEmpty() ||
                    licenseStartDate.isEmpty() || licenseEndDate.isEmpty()) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "请填写所有字段！", "输入错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("输入不完整");
                    return;
                }

                // Validate user ID format (no lowercase letters)
                if (!isValidUserId(userId)) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "用户ID格式不正确！\n只支持大写字母和数字，不支持小写字母。\n正确格式示例：USER001",
                        "用户ID格式错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("用户ID格式错误");
                    return;
                }

                // Validate date format
                if (!isValidDate(startDate) || !isValidDate(endDate) ||
                    !isValidDate(licenseStartDate) || !isValidDate(licenseEndDate)) {
                    JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                        "日期格式不正确！请使用YYYYMMDD格式，例如：20250728",
                        "日期格式错误", JOptionPane.ERROR_MESSAGE);
                    statusLabel.setText("日期格式错误");
                    return;
                }

                statusLabel.setText("正在生成序列号...");

                // Generate serial number with license period
                String serialNumber = generateSerialNumber(userId, startDate, endDate, licenseStartDate, licenseEndDate);
                serialNumberArea.setText(serialNumber);

                copyButton.setEnabled(true);
                statusLabel.setText("序列号生成成功！");

                // Show success message
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "序列号生成成功！\n\n" +
                    "用户ID: " + userId + "\n" +
                    "激活有效期: " + startDate + " 至 " + endDate + "\n" +
                    "（在此期间内可以使用序列号进行激活安装）\n" +
                    "许可使用期: " + licenseStartDate + " 至 " + licenseEndDate + "\n" +
                    "（激活后软件的实际使用期限）\n\n" +
                    "请点击'复制到剪贴板'按钮复制序列号。",
                    "生成成功", JOptionPane.INFORMATION_MESSAGE);

            } catch (Exception ex) {
                statusLabel.setText("生成失败");
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "生成失败：" + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private class CopyAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String serialNumber = serialNumberArea.getText();
            if (!serialNumber.isEmpty()) {
                StringSelection selection = new StringSelection(serialNumber);
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
                statusLabel.setText("序列号已复制到剪贴板");
                JOptionPane.showMessageDialog(SDPLCSerialGenerator.this,
                    "序列号已成功复制到剪贴板！\n\n" +
                    "现在可以在SDPLC应用中粘贴使用。",
                    "复制成功", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    private boolean isValidDate(String date) {
        if (date.length() != 8) return false;
        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_FORMAT));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        // Check if contains only uppercase letters, digits, and allowed special characters
        return userId.matches("^[A-Z0-9_-]+$");
    }

    public static void main(String[] args) {
        // Set system look and feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            // Ignore exception
        }

        SwingUtilities.invokeLater(() -> {
            new SDPLCSerialGenerator().setVisible(true);
        });
    }
}
