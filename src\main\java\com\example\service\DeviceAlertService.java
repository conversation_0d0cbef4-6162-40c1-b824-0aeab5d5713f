package com.example.service;

import com.example.model.DataItem;
import com.example.model.Device;
import com.example.model.DeviceAlert;
import com.example.repository.DataItemRepository;
import com.example.repository.DeviceAlertRepository;
import com.example.repository.DeviceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class DeviceAlertService {

    @Autowired
    private DeviceAlertRepository deviceAlertRepository;
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private DataItemRepository dataItemRepository;
    
    /**
     * 获取设备的所有预警配置
     */
    @Transactional(readOnly = true)
    public List<DeviceAlert> getDeviceAlerts(String deviceId) {
        return deviceAlertRepository.findByDeviceId(deviceId);
    }
    
    /**
     * 获取单个预警配置
     */
    @Transactional(readOnly = true)
    public Optional<DeviceAlert> getDeviceAlert(Long id) {
        return deviceAlertRepository.findById(id);
    }
    
    /**
     * 创建预警配置
     */
    @Transactional
    public DeviceAlert createDeviceAlert(String deviceId, String dataItemId, String name, 
                                        Integer normalMin, Integer normalMax,
                                        Integer warningMin, Integer warningMax,
                                        Integer dangerMin, Integer dangerMax,
                                        String remark) {
        Device device = deviceRepository.findById(deviceId)
                .orElseThrow(() -> new RuntimeException("设备不存在"));
        
        DataItem dataItem = dataItemRepository.findById(dataItemId)
                .orElseThrow(() -> new RuntimeException("数据项不存在"));
        
        DeviceAlert alert = new DeviceAlert();
        alert.setDevice(device);
        alert.setDataItem(dataItem);
        alert.setName(name);
        alert.setNormalMin(normalMin);
        alert.setNormalMax(normalMax);
        alert.setWarningMin(warningMin);
        alert.setWarningMax(warningMax);
        alert.setDangerMin(dangerMin);
        alert.setDangerMax(dangerMax);
        alert.setRemark(remark);
        
        return deviceAlertRepository.save(alert);
    }
    
    /**
     * 更新预警配置
     */
    @Transactional
    public DeviceAlert updateDeviceAlert(Long id, String name, 
                                        Integer normalMin, Integer normalMax,
                                        Integer warningMin, Integer warningMax,
                                        Integer dangerMin, Integer dangerMax,
                                        String remark) {
        DeviceAlert alert = deviceAlertRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("预警配置不存在"));
        
        alert.setName(name);
        alert.setNormalMin(normalMin);
        alert.setNormalMax(normalMax);
        alert.setWarningMin(warningMin);
        alert.setWarningMax(warningMax);
        alert.setDangerMin(dangerMin);
        alert.setDangerMax(dangerMax);
        alert.setRemark(remark);
        
        return deviceAlertRepository.save(alert);
    }
    
    /**
     * 删除预警配置
     */
    @Transactional
    public void deleteDeviceAlert(Long id) {
        deviceAlertRepository.deleteById(id);
    }
    
    /**
     * 获取数据项的当前状态
     * 返回值: 0-未知(灰色), 1-正常(绿色), 2-预警(黄色), 3-异常(红色)
     */
    public int getAlertStatus(DeviceAlert alert, Integer currentValue) {
        if (currentValue == null) {
            return 0; // 未知状态
        }
        
        // 检查是否在正常范围内
        if (alert.getNormalMin() != null && alert.getNormalMax() != null) {
            if (currentValue >= alert.getNormalMin() && currentValue <= alert.getNormalMax()) {
                return 1; // 正常
            }
        } else if (alert.getNormalMin() != null) {
            if (currentValue >= alert.getNormalMin()) {
                return 1; // 正常
            }
        } else if (alert.getNormalMax() != null) {
            if (currentValue <= alert.getNormalMax()) {
                return 1; // 正常
            }
        }
        
        // 检查是否在预警范围内
        if (alert.getWarningMin() != null && alert.getWarningMax() != null) {
            if (currentValue >= alert.getWarningMin() && currentValue <= alert.getWarningMax()) {
                return 2; // 预警
            }
        } else if (alert.getWarningMin() != null) {
            if (currentValue >= alert.getWarningMin()) {
                return 2; // 预警
            }
        } else if (alert.getWarningMax() != null) {
            if (currentValue <= alert.getWarningMax()) {
                return 2; // 预警
            }
        }
        
        // 检查是否在异常范围内
        if (alert.getDangerMin() != null && alert.getDangerMax() != null) {
            if (currentValue >= alert.getDangerMin() && currentValue <= alert.getDangerMax()) {
                return 3; // 异常
            }
        } else if (alert.getDangerMin() != null) {
            if (currentValue >= alert.getDangerMin()) {
                return 3; // 异常
            }
        } else if (alert.getDangerMax() != null) {
            if (currentValue <= alert.getDangerMax()) {
                return 3; // 异常
            }
        }
        
        return 0; // 默认未知状态
    }
} 