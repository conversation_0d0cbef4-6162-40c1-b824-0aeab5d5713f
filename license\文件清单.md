# 📁 License目录文件清单

## 📋 当前有效文件列表

### 🔧 核心程序文件
| 文件名 | 类型 | 状态 | 说明 |
|--------|------|------|------|
| `SDPLCSerialGenerator.java` | 源码 | ✅ 当前使用 | 主要的图形界面序列号生成器（推荐） |
| `SimpleSerialGenerator.java` | 源码 | ✅ 备用 | 命令行版本序列号生成器 |
| `DebugLicense.java` | 源码 | ✅ 工具 | 许可证调试工具 |

### 📊 编译文件
| 文件名 | 类型 | 状态 | 说明 |
|--------|------|------|------|
| `SDPLCSerialGenerator.class` | 编译文件 | ✅ 当前 | 主程序编译文件 |
| `SDPLCSerialGenerator$1.class` | 编译文件 | ✅ 当前 | 内部类编译文件 |
| `SDPLCSerialGenerator$CopyAction.class` | 编译文件 | ✅ 当前 | 复制动作类编译文件 |
| `SDPLCSerialGenerator$GenerateAction.class` | 编译文件 | ✅ 当前 | 生成动作类编译文件 |
| `SimpleSerialGenerator.class` | 编译文件 | ✅ 备用 | 命令行版本编译文件 |
| `DebugLicense.class` | 编译文件 | ✅ 工具 | 调试工具编译文件 |

### 🚀 启动脚本
| 文件名 | 类型 | 状态 | 说明 |
|--------|------|------|------|
| `start-generator.bat` | 脚本 | ✅ 当前 | Windows启动脚本 |
| `start-generator.sh` | 脚本 | ✅ 当前 | Linux/Mac启动脚本 |

### 📄 文档文件
| 文件名 | 类型 | 状态 | 说明 |
|--------|------|------|------|
| `README.md` | 文档 | ✅ 当前 | 许可证系统完整说明 |
| `序列号生成器使用说明.md` | 文档 | ✅ 当前 | 详细使用指南（中文） |
| `测试序列号生成器.md` | 文档 | ✅ 当前 | 测试指南和检查清单 |
| `license-spec.md` | 文档 | ✅ 当前 | 许可证技术规范 |
| `test-license.md` | 文档 | ✅ 当前 | 许可证测试文档 |
| `文件清单.md` | 文档 | ✅ 当前 | 本文件清单 |

### 📊 配置文件
| 文件名 | 类型 | 状态 | 说明 |
|--------|------|------|------|
| `license.json` | 配置 | ✅ 示例 | 许可证配置文件示例 |

## 🗑️ 已移除的无效文件

### ❌ 旧版本程序文件
- `SerialGenerator.java` - 旧版图形界面生成器（依赖Jackson库）
- `GuiSerialGenerator.java` - 早期图形界面版本
- `GenerateSerial.class` - 无用的编译文件
- `start.bat` - 重复的启动脚本

### 📝 移除原因
1. **SerialGenerator.java** - 依赖外部Jackson库，不如当前版本独立
2. **GuiSerialGenerator.java** - 早期版本，功能不如当前版本完善
3. **GenerateSerial.class** - 孤立的编译文件，无对应源码
4. **start.bat** - 功能重复，保留更完善的版本

## 🎯 当前推荐使用

### 主要使用方式
1. **图形界面版本**（推荐）：
   ```bash
   java SDPLCSerialGenerator
   ```

2. **使用启动脚本**：
   ```bash
   # Windows
   start-generator.bat
   
   # Linux/Mac
   ./start-generator.sh
   ```

3. **命令行版本**（备用）：
   ```bash
   java SimpleSerialGenerator
   ```

### 调试工具
```bash
java DebugLicense
```

## 📈 文件统计

- **总文件数**: 17个
- **源码文件**: 3个
- **编译文件**: 6个
- **脚本文件**: 2个
- **文档文件**: 6个
- **配置文件**: 1个

## 🔄 维护建议

1. **定期清理**: 删除过期的.class文件，重新编译
2. **文档更新**: 保持文档与代码同步
3. **版本控制**: 使用git管理，避免文件混乱
4. **备份重要**: 定期备份源码和文档

---
**清理日期**: 2025-07-28  
**清理版本**: v2.0  
**维护者**: SDPLC开发团队
