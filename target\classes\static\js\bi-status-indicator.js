/**
 * BI状态指示器组件
 * 支持多种形状的状态指示器，可根据数据值和设备状态显示不同颜色
 */

class BiStatusIndicator {
    constructor() {
        this.supportedShapes = ['circle', 'square', 'rectangle', 'diamond'];
        this.defaultColors = {
            condition1: {
                gradient: 'linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%)',
                solid: '#28a745',
                glow: 'rgba(40, 167, 69, 0.8)',
                shadow: 'rgba(40, 167, 69, 0.3)'
            },
            condition2: {
                gradient: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 50%, #e83e8c 100%)',
                solid: '#ffc107',
                glow: 'rgba(255, 193, 7, 0.8)',
                shadow: 'rgba(255, 193, 7, 0.3)'
            },
            condition3: {
                gradient: 'linear-gradient(135deg, #dc3545 0%, #e74c3c 50%, #c0392b 100%)',
                solid: '#dc3545',
                glow: 'rgba(220, 53, 69, 0.8)',
                shadow: 'rgba(220, 53, 69, 0.3)'
            },
            offline: {
                gradient: 'linear-gradient(135deg, #6c757d 0%, #495057 50%, #343a40 100%)',
                solid: '#6c757d',
                glow: 'rgba(108, 117, 125, 0.6)',
                shadow: 'rgba(108, 117, 125, 0.2)'
            }
        };
    }

    /**
     * 渲染状态指示器组件
     */
    renderStatusIndicator(widget, container, data = null) {
        console.log('渲染状态指示器组件:', widget.id, widget.config);
        
        const config = widget.config || {};
        const indicatorContainer = document.createElement('div');
        indicatorContainer.className = 'status-indicator-container';
        indicatorContainer.style.cssText = `
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        `;

        // 创建图表标题（如果启用）
        if (config.showChartTitle !== false && config.title) {
            const titleElement = this.createChartTitle(config);
            indicatorContainer.appendChild(titleElement);
        }

        // 创建指示器元素
        const indicator = this.createIndicator(config);
        indicatorContainer.appendChild(indicator);

        // 创建条件名称显示
        if (config.showConditionName) {
            const nameLabel = this.createNameLabel(config);
            indicatorContainer.appendChild(nameLabel);
        }

        // 清空容器并添加新内容
        container.innerHTML = '';
        container.appendChild(indicatorContainer);

        // 更新状态
        this.updateIndicatorStatus(widget, data);

        return indicatorContainer;
    }

    /**
     * 创建指示器元素
     */
    createIndicator(config) {
        const shape = config.shape || 'circle';
        const size = config.size || 60;
        const borderWidth = config.borderWidth || 2;
        const borderColor = config.borderColor || '#cccccc';

        const indicator = document.createElement('div');
        indicator.className = `status-indicator status-indicator-${shape} enhanced-visual`;
        indicator.id = `indicator-${Date.now()}`;

        // 基础样式 - 增强版
        const baseStyle = `
            width: ${size}px;
            height: ${size}px;
            border: ${borderWidth}px solid ${borderColor};
            position: relative;
            transition: all ${config.animationDuration || 300}ms cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.1),
                0 2px 4px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        `;

        // 根据形状设置特定样式
        let shapeStyle = '';
        switch (shape) {
            case 'circle':
                shapeStyle = `
                    border-radius: 50%;
                    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
                `;
                break;
            case 'square':
                shapeStyle = `
                    border-radius: 8px;
                    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
                `;
                break;
            case 'rectangle':
                shapeStyle = `
                    border-radius: 8px;
                    width: ${size * 1.5}px;
                    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
                `;
                break;

            case 'diamond':
                shapeStyle = `
                    border-radius: 12px;
                    transform: rotate(45deg);
                    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
                `;
                break;

        }

        indicator.style.cssText = baseStyle + shapeStyle;
        return indicator;
    }





    /**
     * 创建图表标题
     */
    createChartTitle(config) {
        const titleElement = document.createElement('div');
        titleElement.className = 'status-indicator-chart-title';
        titleElement.style.cssText = `
            margin-bottom: 8px;
            font-size: ${config.titleFontSize || 16}px;
            color: ${config.titleColor || '#333333'};
            font-weight: ${config.titleFontWeight || 'bold'};
            text-align: ${config.titlePosition || 'center'};
            white-space: nowrap;
            font-family: ${config.titleFontFamily || 'Microsoft YaHei'};
        `;
        titleElement.textContent = config.title || '';
        return titleElement;
    }

    /**
     * 创建条件名称标签
     */
    createNameLabel(config) {
        const label = document.createElement('div');
        label.className = 'status-indicator-label';
        label.style.cssText = `
            margin-top: 8px;
            font-size: ${config.fontSize || 12}px;
            color: ${config.fontColor || '#333333'};
            font-weight: ${config.fontWeight || 'normal'};
            text-align: center;
            white-space: nowrap;
        `;
        label.textContent = '等待数据...';
        return label;
    }

    /**
     * 更新组件 - 统一接口
     */
    updateWidget(widget, data, contentElement) {
        console.log('更新状态指示器组件:', widget.id, data);

        // 检查是否需要重新渲染（配置改变或容器为空）
        const existingContainer = contentElement.querySelector('.status-indicator-container');
        const needsRerender = !existingContainer || this.shouldRerender(widget, existingContainer);

        if (needsRerender) {
            console.log('重新渲染状态指示器组件');
            this.render(widget, contentElement, data);
        } else {
            // 否则只更新状态
            this.updateIndicatorStatus(widget, data);
        }
    }

    /**
     * 检查是否需要重新渲染
     */
    shouldRerender(widget, existingContainer) {
        const config = widget.config || {};

        // 检查标题配置是否改变
        const existingTitle = existingContainer.querySelector('.status-indicator-chart-title');
        const shouldShowTitle = config.showChartTitle !== false && config.title;

        if (shouldShowTitle && !existingTitle) {
            console.log('需要重新渲染：标题需要显示但不存在');
            return true;
        }

        if (!shouldShowTitle && existingTitle) {
            console.log('需要重新渲染：标题需要隐藏但存在');
            return true;
        }

        // 检查标题内容是否改变
        if (shouldShowTitle && existingTitle) {
            if (existingTitle.textContent !== (config.title || '')) {
                console.log('需要重新渲染：标题内容改变');
                return true;
            }
        }

        return false;
    }

    /**
     * 渲染组件 - 统一接口
     */
    render(widget, contentElement, data = null) {
        this.renderStatusIndicator(widget, contentElement, data);

        // 如果有数据，立即更新状态
        if (data) {
            this.updateIndicatorStatus(widget, data);
        }
    }

    /**
     * 更新指示器状态
     */
    updateIndicatorStatus(widget, data) {
        console.log('=== 状态指示器状态更新 ===');
        console.log('Widget ID:', widget.id);
        console.log('接收到的数据:', JSON.stringify(data, null, 2));
        console.log('Widget配置:', JSON.stringify(widget.config, null, 2));

        const container = document.querySelector(`#widget-${widget.id} .status-indicator-container`);
        console.log('查找容器:', `#widget-${widget.id} .status-indicator-container`);
        console.log('找到的容器:', container);

        if (!container) {
            console.warn('未找到状态指示器容器:', widget.id);
            return;
        }

        let indicator = container.querySelector('.status-indicator');
        let label = container.querySelector('.status-indicator-label');
        console.log('找到的指示器元素:', indicator);
        console.log('找到的标签元素:', label);

        const config = widget.config || {};

        // 如果指示器元素不存在，重新创建
        if (!indicator) {
            console.log('指示器元素不存在，重新创建...');
            // 清空容器并重新渲染
            container.innerHTML = '';
            this.renderStatusIndicator(widget, container, data);
            // 重新获取元素
            indicator = container.querySelector('.status-indicator');
            label = container.querySelector('.status-indicator-label');
            console.log('重新创建后的指示器元素:', indicator);
            console.log('重新创建后的标签元素:', label);
        }

        // 判断状态
        const status = this.determineStatus(data, config);
        console.log('判断的状态:', status);

        // 更新指示器颜色
        this.updateIndicatorColor(indicator, status, config);

        // 更新标签文本
        if (label && config.showConditionName) {
            label.textContent = status.name;
        }

        console.log('=== 状态指示器状态更新完成 ===');
    }

    /**
     * 判断当前状态
     */
    determineStatus(data, config) {
        // 优先级1: 设备离线
        if (data && data.deviceOffline) {
            return {
                name: '设备离线',
                color: config.offlineColor || this.defaultColors.offline,
                condition: 'offline'
            };
        }

        // 获取数值
        let value = null;
        if (data && data.success && data.data && data.data.length > 0) {
            value = parseFloat(data.data[0].value);
        }

        if (value === null || isNaN(value)) {
            return {
                name: '无数据',
                color: config.offlineColor || this.defaultColors.offline.solid,
                condition: 'offline'
            };
        }

        // 优先级2: 条件3 (最高优先级)
        const condition3 = config.condition3 || {};
        if (value >= condition3.min && value <= condition3.max) {
            return {
                name: condition3.name || '危险',
                color: condition3.color || this.defaultColors.condition3.solid,
                condition: 'condition3'
            };
        }

        // 优先级3: 条件2
        const condition2 = config.condition2 || {};
        if (value >= condition2.min && value <= condition2.max) {
            return {
                name: condition2.name || '警告',
                color: condition2.color || this.defaultColors.condition2.solid,
                condition: 'condition2'
            };
        }

        // 优先级4: 条件1 (默认正常状态)
        const condition1 = config.condition1 || {};
        return {
            name: condition1.name || '正常',
            color: condition1.color || this.defaultColors.condition1.solid,
            condition: 'condition1'
        };
    }

    /**
     * 更新指示器颜色 - 增强版
     */
    updateIndicatorColor(indicator, status, config) {
        console.log('=== 更新指示器颜色（增强版）===');
        console.log('指示器元素:', indicator);
        console.log('状态:', status);
        console.log('配置:', config);

        if (!indicator) {
            console.warn('指示器元素不存在，无法更新颜色');
            return;
        }

        const shape = config.shape || 'circle';
        console.log('形状:', shape);

        // 获取状态对应的颜色配置
        const colorConfig = this.getColorConfig(status.condition, status.color);
        console.log('颜色配置:', colorConfig);

        // 根据形状应用不同的视觉效果
        this.applyVisualEffects(indicator, shape, colorConfig, status.condition);

        // 添加状态类名用于CSS动画
        indicator.className = indicator.className.replace(/status-\w+/g, '');
        indicator.classList.add(`status-${status.condition}`);
        console.log('添加状态类名:', `status-${status.condition}`);
        console.log('=== 指示器颜色更新完成 ===');
    }

    /**
     * 获取颜色配置
     */
    getColorConfig(condition, customColor) {
        // 如果有自定义颜色，使用自定义颜色创建配置
        if (customColor && typeof customColor === 'string' && customColor.startsWith('#')) {
            return {
                gradient: `linear-gradient(135deg, ${customColor} 0%, ${this.adjustColor(customColor, -20)} 50%, ${this.adjustColor(customColor, -40)} 100%)`,
                solid: customColor,
                glow: this.hexToRgba(customColor, 0.8),
                shadow: this.hexToRgba(customColor, 0.3)
            };
        }

        // 否则使用默认配置
        return this.defaultColors[condition] || this.defaultColors.condition1;
    }

    /**
     * 应用视觉效果
     */
    applyVisualEffects(indicator, shape, colorConfig, condition) {
        // 所有形状使用统一的渐变背景和视觉效果
        indicator.style.background = colorConfig.gradient;
        indicator.style.boxShadow = `
            0 0 20px ${colorConfig.glow},
            0 4px 8px rgba(0, 0, 0, 0.1),
            0 2px 4px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.1)
        `;
    }

    /**
     * 调整颜色亮度
     */
    adjustColor(hex, percent) {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    /**
     * 将十六进制颜色转换为RGBA
     */
    hexToRgba(hex, alpha) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (result) {
            const r = parseInt(result[1], 16);
            const g = parseInt(result[2], 16);
            const b = parseInt(result[3], 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return `rgba(0, 0, 0, ${alpha})`;
    }

    /**
     * 获取示例数据
     */
    getExampleData(widget) {
        return {
            success: true,
            data: [{
                value: Math.floor(Math.random() * 100),
                timestamp: new Date().toISOString()
            }],
            deviceOffline: Math.random() < 0.1 // 10%概率模拟设备离线
        };
    }
}

// 导出类到全局作用域
window.BiStatusIndicator = BiStatusIndicator;

// 创建全局实例
window.biStatusIndicator = new BiStatusIndicator();
