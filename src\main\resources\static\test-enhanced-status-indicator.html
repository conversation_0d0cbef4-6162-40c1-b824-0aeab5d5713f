<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版状态指示器测试</title>
    <link href="/css/bi-status-indicator.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
            font-size: 28px;
            font-weight: bold;
        }
        
        .test-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #555;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .indicator-demo {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .indicator-label {
            margin-top: 15px;
            font-weight: bold;
            color: #666;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎨 增强版状态指示器视觉效果测试</h1>
        
        <div class="test-section">
            <h2 class="section-title">圆形指示器</h2>
            <div class="indicators-grid">
                <div class="indicator-demo">
                    <div id="circle-normal"></div>
                    <div class="indicator-label">正常状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="circle-warning"></div>
                    <div class="indicator-label">警告状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="circle-danger"></div>
                    <div class="indicator-label">危险状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="circle-offline"></div>
                    <div class="indicator-label">离线状态</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">方形指示器</h2>
            <div class="indicators-grid">
                <div class="indicator-demo">
                    <div id="square-normal"></div>
                    <div class="indicator-label">正常状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="square-warning"></div>
                    <div class="indicator-label">警告状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="square-danger"></div>
                    <div class="indicator-label">危险状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="square-offline"></div>
                    <div class="indicator-label">离线状态</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">三角形指示器</h2>
            <div class="indicators-grid">
                <div class="indicator-demo">
                    <div id="triangle-normal"></div>
                    <div class="indicator-label">正常状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="triangle-warning"></div>
                    <div class="indicator-label">警告状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="triangle-danger"></div>
                    <div class="indicator-label">危险状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="triangle-offline"></div>
                    <div class="indicator-label">离线状态</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">六边形指示器</h2>
            <div class="indicators-grid">
                <div class="indicator-demo">
                    <div id="hexagon-normal"></div>
                    <div class="indicator-label">正常状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="hexagon-warning"></div>
                    <div class="indicator-label">警告状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="hexagon-danger"></div>
                    <div class="indicator-label">危险状态</div>
                </div>
                <div class="indicator-demo">
                    <div id="hexagon-offline"></div>
                    <div class="indicator-label">离线状态</div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="toggleAnimation()">切换动画效果</button>
            <button class="btn" onclick="changeSize()">改变大小</button>
            <button class="btn" onclick="testDataUpdate()">模拟数据更新</button>
        </div>
    </div>

    <script src="/js/bi-status-indicator.js?v=20250730-enhanced"></script>
    <script>
        let statusIndicator;
        let animationEnabled = true;
        let currentSize = 80;
        
        // 初始化状态指示器
        function initIndicators() {
            statusIndicator = new BiStatusIndicator();
            
            // 创建不同形状和状态的指示器
            const configs = [
                // 圆形
                { id: 'circle-normal', shape: 'circle', condition: 'condition1' },
                { id: 'circle-warning', shape: 'circle', condition: 'condition2' },
                { id: 'circle-danger', shape: 'circle', condition: 'condition3' },
                { id: 'circle-offline', shape: 'circle', condition: 'offline' },
                
                // 方形
                { id: 'square-normal', shape: 'square', condition: 'condition1' },
                { id: 'square-warning', shape: 'square', condition: 'condition2' },
                { id: 'square-danger', shape: 'square', condition: 'condition3' },
                { id: 'square-offline', shape: 'square', condition: 'offline' },
                
                // 三角形
                { id: 'triangle-normal', shape: 'triangle', condition: 'condition1' },
                { id: 'triangle-warning', shape: 'triangle', condition: 'condition2' },
                { id: 'triangle-danger', shape: 'triangle', condition: 'condition3' },
                { id: 'triangle-offline', shape: 'triangle', condition: 'offline' },
                
                // 六边形
                { id: 'hexagon-normal', shape: 'hexagon', condition: 'condition1' },
                { id: 'hexagon-warning', shape: 'hexagon', condition: 'condition2' },
                { id: 'hexagon-danger', shape: 'hexagon', condition: 'condition3' },
                { id: 'hexagon-offline', shape: 'hexagon', condition: 'offline' }
            ];
            
            configs.forEach(config => {
                const container = document.getElementById(config.id);
                const widget = {
                    id: config.id,
                    config: {
                        shape: config.shape,
                        size: currentSize,
                        showLabel: false,
                        condition1: { min: 0, max: 30, name: '正常', color: '#28a745' },
                        condition2: { min: 31, max: 70, name: '警告', color: '#ffc107' },
                        condition3: { min: 71, max: 100, name: '危险', color: '#dc3545' }
                    }
                };
                
                // 模拟不同状态的数据
                let data;
                switch(config.condition) {
                    case 'condition1':
                        data = [{ value: 25, timestamp: new Date().toISOString() }];
                        break;
                    case 'condition2':
                        data = [{ value: 55, timestamp: new Date().toISOString() }];
                        break;
                    case 'condition3':
                        data = [{ value: 85, timestamp: new Date().toISOString() }];
                        break;
                    case 'offline':
                        data = [{ value: null, timestamp: new Date().toISOString(), deviceOffline: true }];
                        break;
                }
                
                statusIndicator.render(widget, container, data);
            });
        }
        
        // 切换动画效果
        function toggleAnimation() {
            animationEnabled = !animationEnabled;
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                if (animationEnabled) {
                    indicator.classList.remove('no-animation');
                } else {
                    indicator.classList.add('no-animation');
                }
            });
        }
        
        // 改变大小
        function changeSize() {
            currentSize = currentSize === 80 ? 120 : 80;
            initIndicators();
        }
        
        // 模拟数据更新
        function testDataUpdate() {
            // 随机更新一些指示器的状态
            const indicators = ['circle-normal', 'square-warning', 'triangle-danger'];
            indicators.forEach(id => {
                const container = document.getElementById(id);
                const randomValue = Math.floor(Math.random() * 100);
                const widget = {
                    id: id,
                    config: {
                        shape: id.split('-')[0],
                        size: currentSize,
                        condition1: { min: 0, max: 30, name: '正常', color: '#28a745' },
                        condition2: { min: 31, max: 70, name: '警告', color: '#ffc107' },
                        condition3: { min: 71, max: 100, name: '危险', color: '#dc3545' }
                    }
                };
                const data = [{ value: randomValue, timestamp: new Date().toISOString() }];
                statusIndicator.updateWidget(widget, data, container);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initIndicators);
    </script>
</body>
</html>
