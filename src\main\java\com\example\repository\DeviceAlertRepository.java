package com.example.repository;

import com.example.model.Device;
import com.example.model.DeviceAlert;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeviceAlertRepository extends JpaRepository<DeviceAlert, Long> {
    
    /**
     * 根据设备查找所有预警配置
     */
    List<DeviceAlert> findByDevice(Device device);
    
    /**
     * 根据设备ID查找所有预警配置
     */
    List<DeviceAlert> findByDeviceId(String deviceId);
    
    /**
     * 删除设备的所有预警配置
     */
    void deleteByDeviceId(String deviceId);
} 