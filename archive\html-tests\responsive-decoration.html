<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式装饰效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            width: 100%;
            height: 100%;
        }
        
        .responsive-decoration-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: transparent;
            overflow: hidden;
        }
        
        /* 角落装饰 - 使用相对单位 */
        .corner-decoration {
            position: absolute;
            width: 8%;
            height: 8%;
            max-width: 60px;
            max-height: 60px;
            min-width: 20px;
            min-height: 20px;
            border: 0.3vw solid #00ffff;
            border-width: clamp(2px, 0.3vw, 4px);
        }
        
        .corner-tl {
            top: 2%;
            left: 2%;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }
        
        .corner-tr {
            top: 2%;
            right: 2%;
            border-left: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate 0.5s;
        }
        
        .corner-bl {
            bottom: 2%;
            left: 2%;
            border-right: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }
        
        .corner-br {
            bottom: 2%;
            right: 2%;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1.5s;
        }
        
        @keyframes corner-glow {
            0% {
                box-shadow: 0 0 0.5vw #00ffff;
                border-color: #00ffff;
            }
            100% {
                box-shadow: 0 0 2vw #00ffff, 0 0 3vw #00ffff;
                border-color: #66ffff;
            }
        }
        
        /* 中心装饰圆环 - 响应式尺寸 */
        .center-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .ring {
            position: absolute;
            border: clamp(1px, 0.2vw, 3px) solid rgba(255, 0, 204, 0.6);
            border-radius: 50%;
            animation: ring-pulse 3s ease-in-out infinite;
        }
        
        .ring-1 {
            width: 15vw;
            height: 15vw;
            max-width: 80px;
            max-height: 80px;
            min-width: 40px;
            min-height: 40px;
            top: -7.5vw;
            left: -7.5vw;
            animation-delay: 0s;
        }
        
        .ring-2 {
            width: 25vw;
            height: 25vw;
            max-width: 130px;
            max-height: 130px;
            min-width: 70px;
            min-height: 70px;
            top: -12.5vw;
            left: -12.5vw;
            animation-delay: 1s;
        }
        
        .ring-3 {
            width: 35vw;
            height: 35vw;
            max-width: 180px;
            max-height: 180px;
            min-width: 100px;
            min-height: 100px;
            top: -17.5vw;
            left: -17.5vw;
            animation-delay: 2s;
        }
        
        @keyframes ring-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
        }
        
        /* 浮动装饰点 - 响应式定位 */
        .floating-dots {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .dot {
            position: absolute;
            width: clamp(6px, 1.5vw, 12px);
            height: clamp(6px, 1.5vw, 12px);
            background: radial-gradient(circle, #00ff88, transparent);
            border-radius: 50%;
            animation: dot-float 4s ease-in-out infinite;
        }
        
        .dot:nth-child(1) {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }
        
        .dot:nth-child(2) {
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }
        
        .dot:nth-child(3) {
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }
        
        .dot:nth-child(4) {
            bottom: 35%;
            right: 15%;
            animation-delay: 3s;
        }
        
        @keyframes dot-float {
            0%, 100% {
                transform: translateY(0px);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-1.5vw);
                opacity: 1;
            }
        }
        
        /* 装饰线条 - 响应式尺寸 */
        .decorative-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation: line-glow 3s ease-in-out infinite;
        }
        
        .line-top {
            top: 8%;
            left: 15%;
            width: 25%;
            height: clamp(1px, 0.2vw, 3px);
            animation-delay: 0s;
        }
        
        .line-bottom {
            bottom: 8%;
            right: 15%;
            width: 30%;
            height: clamp(1px, 0.2vw, 3px);
            animation-delay: 1.5s;
        }
        
        .line-left {
            left: 8%;
            top: 15%;
            width: clamp(1px, 0.2vw, 3px);
            height: 20%;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 0.75s;
        }
        
        .line-right {
            right: 8%;
            bottom: 15%;
            width: clamp(1px, 0.2vw, 3px);
            height: 25%;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 2.25s;
        }
        
        @keyframes line-glow {
            0%, 100% {
                opacity: 0.4;
                filter: brightness(1);
            }
            50% {
                opacity: 1;
                filter: brightness(1.5);
            }
        }
        
        /* 内容区域 - 响应式文字 */
        .content-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #00d4ff;
            z-index: 10;
            padding: 2%;
        }
        
        .content-area h2 {
            font-size: clamp(1rem, 4vw, 2.5rem);
            margin-bottom: 0.5em;
            background: linear-gradient(90deg, #00d4ff, #ff00cc);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 0.5em rgba(0, 212, 255, 0.5);
        }
        
        .content-area p {
            font-size: clamp(0.7rem, 2.5vw, 1.2rem);
            color: #66ddff;
            text-shadow: 0 0 0.3em rgba(102, 221, 255, 0.5);
            max-width: 80vw;
        }
        
        /* 响应式断点优化 */
        @media (max-width: 480px) {
            .ring-1 { top: -20px; left: -20px; }
            .ring-2 { top: -35px; left: -35px; }
            .ring-3 { top: -50px; left: -50px; }
            
            .dot {
                animation-name: dot-float-mobile;
            }
            
            @keyframes dot-float-mobile {
                0%, 100% {
                    transform: translateY(0px);
                    opacity: 0.6;
                }
                50% {
                    transform: translateY(-10px);
                    opacity: 1;
                }
            }
        }
        
        @media (min-width: 1200px) {
            .ring-1 { top: -40px; left: -40px; }
            .ring-2 { top: -65px; left: -65px; }
            .ring-3 { top: -90px; left: -90px; }
        }
    </style>
</head>
<body>
    <div class="responsive-decoration-container">
        <!-- 角落装饰 -->
        <div class="corner-decoration corner-tl"></div>
        <div class="corner-decoration corner-tr"></div>
        <div class="corner-decoration corner-bl"></div>
        <div class="corner-decoration corner-br"></div>
        
        <!-- 中心圆环 -->
        <div class="center-rings">
            <div class="ring ring-1"></div>
            <div class="ring ring-2"></div>
            <div class="ring ring-3"></div>
        </div>
        
        <!-- 浮动装饰点 -->
        <div class="floating-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
        
        <!-- 装饰线条 -->
        <div class="decorative-lines">
            <div class="line line-top"></div>
            <div class="line line-bottom"></div>
            <div class="line line-left"></div>
            <div class="line line-right"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <h2>响应式装饰</h2>
            <p>自适应容器大小的装饰效果</p>
        </div>
    </div>
</body>
</html>
