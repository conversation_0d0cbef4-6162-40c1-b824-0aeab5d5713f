/**
 * BI大屏 ECharts组件系统
 * 基于Apache ECharts的美观图表组件
 */

// ECharts实例管理
const echartsInstances = {};

/**
 * 获取柱状图圆角配置
 * @param {number} radius - 圆角半径
 * @param {string} mode - 圆角模式：'top' 仅顶部圆角，'right' 仅右侧圆角，'all' 全圆角
 * @returns {Array} ECharts圆角配置数组
 */
function getBorderRadiusConfig(radius, mode) {
    switch (mode) {
        case 'all':
            // 全圆角：[左上, 右上, 右下, 左下]
            return [radius, radius, radius, radius];
        case 'right':
            // 仅右侧圆角（水平柱状图）：[左上, 右上, 右下, 左下]
            return [0, radius, radius, 0];
        case 'top':
        default:
            // 仅顶部圆角（垂直柱状图）：[左上, 右上, 右下, 左下]
            return [radius, radius, 0, 0];
    }
}

/**
 * 生成通用ECharts配置
 * 所有组件都支持的基础配置
 */
function getCommonEChartsConfig(config = {}) {
    // 处理透明背景配置
    let backgroundColor = 'transparent';
    if (config.transparent === true) {
        backgroundColor = 'transparent';
        console.log('ECharts组件应用透明背景');
    } else if (config.backgroundColor) {
        backgroundColor = config.backgroundColor;
        console.log('ECharts组件应用背景颜色:', config.backgroundColor);
    }

    return {
        // 背景配置
        backgroundColor: backgroundColor,

        // 标题配置
        title: {
            text: (config.showChartTitle !== false) ? (config.title || '') : '',
            subtext: config.subtitle || '',
            left: config.titlePosition || 'center',
            top: config.borderlessMode ? '2%' : '5%',
            textStyle: {
                fontSize: config.titleFontSize || 18,
                color: config.titleColor || '#333',
                fontWeight: config.titleFontWeight || 'bold',
                fontFamily: config.titleFontFamily || 'Microsoft YaHei'
            },
            subtextStyle: {
                fontSize: config.subtitleFontSize || 12,
                color: config.subtitleColor || '#666'
            }
        },

        // 工具提示配置
        tooltip: {
            backgroundColor: config.tooltipBgColor || 'rgba(50,50,50,0.95)',
            borderColor: config.tooltipBorderColor || '#333',
            borderWidth: config.tooltipBorderWidth || 1,
            borderRadius: config.tooltipBorderRadius || 6,
            textStyle: {
                color: config.tooltipTextColor || '#fff',
                fontSize: config.tooltipFontSize || 12
            },
            padding: [8, 12],
            extraCssText: 'box-shadow: 0 4px 12px rgba(0,0,0,0.15);'
        },

        // 图例配置（与设计页面保持一致）
        legend: {
            show: (config.showLegend !== false),
            // 横向位置：支持left、center、right或具体数值
            left: config.lateralPosition || config.legendLeft || 'center',
            // 纵向位置：支持top、middle、bottom或具体数值
            top: config.longitudinalPosition || config.legendTop || 'bottom',
            // 布局方向：horizontal（水平）或vertical（垂直）
            orient: config.layoutFront || config.legendOrient || 'horizontal',
            textStyle: {
                fontSize: config.legendFontSize || 12,
                color: config.legendFontColor || config.legendColor || '#666',
                fontWeight: config.legendFontWeight || 'normal',
                fontStyle: config.legendFontStyle || 'normal',
                fontFamily: config.legendFontFamily || 'Microsoft YaHei'
            },
            itemWidth: config.legendItemWidth || config.legendWidth || 25,
            itemHeight: config.legendItemHeight || config.legendHeight || 14,
            itemGap: config.legendItemGap || 10
        },

        // 网格配置
        grid: {
            left: config.borderlessMode ? '2%' : (config.gridLeft || '8%'),
            right: config.borderlessMode ? '2%' : (config.gridRight || '8%'),
            bottom: config.borderlessMode ? '5%' : (config.gridBottom || '15%'),
            top: config.borderlessMode ? '8%' : (config.gridTop || '20%'),
            containLabel: !config.borderlessMode,
            backgroundColor: config.gridBgColor || 'transparent',
            borderColor: config.gridBorderColor || '#ccc',
            borderWidth: config.gridBorderWidth || 0
        },

        // 全局动画配置
        animation: config.animation !== false,
        animationThreshold: 2000,
        animationDuration: config.animationDuration || 1000,
        animationEasing: config.animationEasing || 'cubicOut'
    };
}

/**
 * 生成通用轴配置
 * 适用于有坐标轴的图表（折线图、柱状图）
 */
function getCommonAxisConfig(config = {}) {
    return {
        xAxis: {
            type: 'category',
            boundaryGap: config.xAxisBoundaryGap !== undefined ? config.xAxisBoundaryGap : false,
            show: config.showXAxis !== false,
            axisLine: {
                show: (config.showXAxisLine !== false) && (config.showXAxis !== false),
                lineStyle: {
                    color: config.xAxisLineColor || '#e0e6ed',
                    width: config.xAxisLineWidth || 1,
                    type: config.xAxisLineType || 'solid'
                }
            },
            axisTick: {
                show: (config.showXAxisTick !== false) && (config.showXAxis !== false),
                alignWithLabel: true,
                lineStyle: {
                    color: config.xAxisTickColor || '#e0e6ed'
                }
            },
            axisLabel: {
                show: (config.showXAxisLabel !== false) && (config.showXAxis !== false),
                color: config.xAxisFontColor || '#8c8c8c',
                fontSize: config.xAxisFontSize || 12,
                fontFamily: config.xAxisLabelFontFamily || 'Microsoft YaHei',
                rotate: config.xAxisLabelRotate || 0,
                margin: config.xAxisLabelMargin || 8
            },
            splitLine: {
                show: (config.showXAxisSplitLine || false) && (config.showXAxis !== false),
                lineStyle: {
                    color: config.xAxisSplitLineColor || '#f0f0f0',
                    type: config.xAxisSplitLineType || 'dashed'
                }
            }
        },
        yAxis: {
            type: 'value',
            scale: config.yAxisScale || false,
            show: config.showYAxis !== false,
            axisLine: {
                show: (config.showYAxisLine !== false) && (config.showYAxis !== false),
                lineStyle: {
                    color: config.yAxisLineColor || '#e0e6ed',
                    width: config.yAxisLineWidth || 1
                }
            },
            axisTick: {
                show: (config.showYAxisTick !== false) && (config.showYAxis !== false)
            },
            axisLabel: {
                show: (config.showYAxisLabels !== false) && (config.showYAxis !== false),
                color: config.yAxisFontColor || '#8c8c8c',
                fontSize: config.yAxisFontSize || 12,
                fontFamily: config.yAxisLabelFontFamily || 'Microsoft YaHei',
                formatter: config.yAxisLabelFormatter || '{value}'
            },
            splitLine: {
                show: (config.showYAxisSplitLine !== false) && (config.showYAxis !== false),
                lineStyle: {
                    color: config.yAxisSplitLineColor || '#f5f5f5',
                    width: config.yAxisSplitLineWidth || 1,
                    type: config.yAxisSplitLineType || 'solid'
                }
            },
            splitArea: {
                show: (config.showYAxisSplitArea || false) && (config.showYAxis !== false),
                areaStyle: {
                    color: ['rgba(250,250,250,0.05)', 'rgba(200,200,200,0.02)']
                }
            }
        }
    };
}

// 主题配置
const ECHARTS_THEMES = {
    default: {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        backgroundColor: 'transparent',
        textStyle: {
            fontFamily: 'Microsoft YaHei, Arial, sans-serif',
            fontSize: 12,
            color: '#333'
        },
        title: {
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#333'
            }
        },
        legend: {
            textStyle: {
                fontSize: 12,
                color: '#666'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        }
    },
    dark: {
        color: ['#dd6b66', '#759aa0', '#e69d87', '#8dc1a9', '#ea7e53', '#eedd78', '#73a373', '#73b9bc', '#7289ab'],
        backgroundColor: '#100c2a',
        textStyle: {
            color: '#fff'
        },
        title: {
            textStyle: {
                color: '#fff'
            }
        },
        legend: {
            textStyle: {
                color: '#ccc'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        }
    },
    blue: {
        color: ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa541c', '#eb2f96'],
        backgroundColor: 'transparent',
        textStyle: {
            color: '#333'
        }
    }
};

/**
 * 创建ECharts折线图 - 专业级配置
 */
function createEChartsLineChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;



    // 高级渐变色配置
    const getGradientColor = (color1, color2) => ({
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 0,
        colorStops: [
            { offset: 0, color: color1 },
            { offset: 1, color: color2 }
        ]
    });

    // 阴影配置
    const getShadowStyle = (config) => {
        if (!config.enableShadow) {
            return {};
        }
        return {
            shadowColor: config.shadowColor || 'rgba(0, 0, 0, 0.3)',
            shadowBlur: config.shadowBlur || 10,
            shadowOffsetX: config.shadowOffsetX || 0,
            shadowOffsetY: config.shadowOffsetY || 3
        };
    };

    // 获取通用配置
    const commonConfig = getCommonEChartsConfig(config);
    const axisConfig = getCommonAxisConfig(config);

    // 调试图例配置
    console.log('=== 折线图图例配置调试 ===');
    console.log('样式配置中的图例参数:', {
        showLegend: config.showLegend,
        lateralPosition: config.lateralPosition,
        longitudinalPosition: config.longitudinalPosition,
        layoutFront: config.layoutFront,
        legendFontSize: config.legendFontSize,
        legendFontColor: config.legendFontColor,
        legendColor: config.legendColor
    });
    console.log('最终图例配置:', commonConfig.legend);

    const option = {
        ...commonConfig,

        // 折线图特有的工具提示配置
        tooltip: {
            ...commonConfig.tooltip,
            trigger: 'axis',
            axisPointer: {
                type: config.axisPointerType || 'cross',
                lineStyle: {
                    color: config.axisPointerColor || '#999',
                    width: 1,
                    type: 'dashed'
                },
                crossStyle: {
                    color: config.axisPointerColor || '#999'
                }
            },

        },

        // 使用通用轴配置并添加数据
        xAxis: {
            ...axisConfig.xAxis,
            data: data.labels || []
        },
        yAxis: axisConfig.yAxis,

        // 系列配置
        series: [{
            name: config.seriesName || '数据',
            type: 'line',
            data: data.values || [],
            smooth: config.smooth !== false,
            smoothMonotone: config.smoothMonotone || 'x',

            // 线条样式
            lineStyle: {
                width: config.lineWidth || 3,
                color: config.useGradientLine ?
                    getGradientColor(config.lineColor || '#5470c6', config.lineGradientColor || '#91cc75') :
                    config.lineColor || '#5470c6',
                type: config.lineType || 'solid',
                cap: 'round',
                join: 'round',
                ...getShadowStyle(config)
            },

            // 面积样式
            areaStyle: config.showArea ? {
                color: config.useGradientArea ? {
                    type: 'linear',
                    x: 0, y: 0, x2: 0, y2: 1,
                    colorStops: [
                        { offset: 0, color: config.areaStartColor || 'rgba(84, 112, 198, 0.4)' },
                        { offset: 0.5, color: config.areaMidColor || 'rgba(84, 112, 198, 0.2)' },
                        { offset: 1, color: config.areaEndColor || 'rgba(84, 112, 198, 0.05)' }
                    ]
                } : config.areaColor || 'rgba(84, 112, 198, 0.2)',
                ...getShadowStyle(config)
            } : null,

            // 数据点样式
            symbol: config.showSymbol !== false ? (config.symbolType || 'circle') : 'none',
            symbolSize: config.symbolSize || 8,
            itemStyle: {
                color: config.symbolColor || config.lineColor || '#5470c6',
                borderColor: config.symbolBorderColor || '#fff',
                borderWidth: config.symbolBorderWidth || 2,
                ...getShadowStyle(config)
            },

            // 数据标签
            label: {
                show: config.showDataLabels === true,
                position: 'top',
                fontSize: config.dataLabelFontSize || 11,
                color: config.dataLabelFontColor || config.lineColor || '#5470c6',
                fontWeight: 'bold',
                formatter: function(params) {
                    // 使用数值，但会在updateEChartsData中动态更新formatter
                    return params.value;
                }
            },

            // 高亮样式
            emphasis: {
                focus: 'series',
                lineStyle: {
                    width: (config.lineWidth || 3) + 2
                },
                itemStyle: {
                    borderWidth: (config.symbolBorderWidth || 2) + 1,
                    shadowBlur: 10,
                    shadowColor: config.lineColor || '#5470c6'
                }
            },

            // 动画配置
            animation: config.animation !== false,
            animationDuration: config.animationDuration || 1000,
            animationEasing: config.animationEasing || 'cubicOut',
            animationDelay: config.animationDelay || 0
        }]
    };

    chart.setOption(option);

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 创建ECharts多折线图 - 适配项目标准
 */
function createEChartsMultiLineChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;

    // 智能数据格式检测和转换
    const formattedData = formatMultiLineDataSmart(data, config);

    // 获取通用配置（使用项目标准）
    const commonConfig = getCommonEChartsConfig(config);
    const axisConfig = getCommonAxisConfig(config);

    // 构建多折线图配置
    const option = {
        ...commonConfig,

        // 工具提示配置（使用项目标准）
        tooltip: {
            ...commonConfig.tooltip,
            trigger: 'axis',
            axisPointer: {
                type: config.axisPointerType || 'line'
            }
        },

        // 图例配置（使用项目标准命名）
        legend: {
            show: config.isShowLegend !== false,
            data: formattedData.legendData || [],
            left: config.legendPosition === 'left' ? 'left' :
                  config.legendPosition === 'right' ? 'right' : 'center',
            top: config.legendPosition === 'bottom' ? 'bottom' : 'top',
            orient: config.legendPosition === 'left' || config.legendPosition === 'right' ? 'vertical' : 'horizontal',
            textStyle: {
                color: config.legendColor || '#666666',
                fontSize: config.legendFontSize || 12
            }
        }
    };

    // 设置坐标轴（使用项目标准）
    if (config.enableDualYAxis) {
        setupDualYAxisConfig(option, formattedData, config, axisConfig);
    } else {
        setupSingleYAxisConfig(option, formattedData, config, axisConfig);
    }

    // 设置系列数据（使用项目标准）
    setupMultiLineSeriesStandard(option, formattedData, config);

    chart.setOption(option);

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 智能数据格式化 - 支持项目标准格式和多折线格式
 */
function formatMultiLineDataSmart(data, config) {
    console.log('多折线图智能数据格式化:', { data, config });

    if (!data) {
        return {
            xAxisData: [],
            seriesData: [],
            legendData: []
        };
    }

    // 检测数据格式类型
    if (data.labels && data.values) {
        // 标准格式：{labels: [], values: []} - 转换为单折线
        const seriesName = config.seriesName || '数据';
        console.log('检测到标准格式数据，系列名称:', seriesName);
        return {
            xAxisData: data.labels,
            seriesData: [{
                name: seriesName,
                type: 'line',
                data: data.values,
                yAxisIndex: 0
            }],
            legendData: [seriesName]
        };
    } else if (data.xAxis && data.series) {
        // 多折线格式：{xAxis: [], series: []} - 适配外部数据源
        console.log('检测到多折线格式数据');
        const result = {
            xAxisData: data.xAxis,
            seriesData: [],
            legendData: []
        };

        data.series.forEach((series, index) => {
            const seriesName = series.name || `系列${index + 1}`;
            result.seriesData.push({
                name: seriesName,
                type: 'line',
                data: series.data || [],
                yAxisIndex: series.yAxisIndex || 0
            });
            result.legendData.push(seriesName);
        });

        // 如果数据中包含图例信息，优先使用
        if (data.legendData && Array.isArray(data.legendData)) {
            result.legendData = data.legendData;
            console.log('使用数据中的图例信息:', result.legendData);
        }

        console.log('多折线格式数据处理结果:', {
            xAxisLength: result.xAxisData.length,
            seriesCount: result.seriesData.length,
            legendCount: result.legendData.length,
            legendData: result.legendData
        });
        return result;
    } else if (Array.isArray(data)) {
        // 数组格式：尝试智能解析
        console.log('检测到数组格式数据，尝试智能解析');
        if (data.length > 0 && typeof data[0] === 'object') {
            // 对象数组格式
            const keys = Object.keys(data[0]);
            if (keys.length >= 2) {
                const labelKey = keys[0];
                const valueKey = keys[1];
                const labels = data.map(item => item[labelKey]);
                const values = data.map(item => item[valueKey]);

                return {
                    xAxisData: labels,
                    seriesData: [{
                        name: config.seriesName || valueKey,
                        type: 'line',
                        data: values,
                        yAxisIndex: 0
                    }],
                    legendData: [config.seriesName || valueKey]
                };
            }
        }
    } else if (data.success && (data.labels || data.xAxis)) {
        // 带有success标志的响应格式
        console.log('检测到API响应格式数据');
        if (data.labels && data.values) {
            const seriesName = config.seriesName || '数据';
            return {
                xAxisData: data.labels,
                seriesData: [{
                    name: seriesName,
                    type: 'line',
                    data: data.values,
                    yAxisIndex: 0
                }],
                legendData: [seriesName]
            };
        } else if (data.xAxis && data.series) {
            const result = {
                xAxisData: data.xAxis,
                seriesData: data.series.map((series, index) => ({
                    name: series.name || `系列${index + 1}`,
                    type: 'line',
                    data: series.data || [],
                    yAxisIndex: series.yAxisIndex || 0
                })),
                legendData: data.series.map((series, index) => series.name || `系列${index + 1}`)
            };

            // 如果数据中包含图例信息，优先使用（适配外部数据源）
            if (data.legendData && Array.isArray(data.legendData)) {
                result.legendData = data.legendData;
            }

            return result;
        }
    }

    // 兜底处理
    console.warn('无法识别的数据格式，使用空数据:', data);
    return {
        xAxisData: [],
        seriesData: [],
        legendData: []
    };
}

/**
 * 设置单Y轴配置 - 使用项目标准
 */
function setupSingleYAxisConfig(option, data, config, axisConfig) {
    // X轴配置（使用项目通用配置）
    option.xAxis = {
        ...axisConfig.xAxis,
        data: data.xAxisData,
        boundaryGap: false  // 折线图不需要边界间隙
    };

    // Y轴配置（使用项目通用配置）
    option.yAxis = axisConfig.yAxis;
}

/**
 * 设置双Y轴配置 - 扩展项目标准
 */
function setupDualYAxisConfig(option, data, config, axisConfig) {
    // 双Y轴需要特殊的网格配置
    option.grid = [
        {
            left: '10%',
            right: '10%',
            top: '15%',
            bottom: '50%',
            containLabel: true
        },
        {
            left: '10%',
            right: '10%',
            top: '51%',
            bottom: '10%',
            containLabel: true
        }
    ];

    // 双X轴配置
    option.xAxis = [
        {
            ...axisConfig.xAxis,
            gridIndex: 0,
            data: data.xAxisData,
            boundaryGap: false
        },
        {
            ...axisConfig.xAxis,
            gridIndex: 1,
            data: data.xAxisData,
            boundaryGap: false,
            position: 'top',
            axisLabel: { show: false }
        }
    ];

    // 双Y轴配置
    option.yAxis = [
        {
            ...axisConfig.yAxis,
            gridIndex: 0
        },
        {
            ...axisConfig.yAxis,
            gridIndex: 1,
            inverse: true  // 下方Y轴反向
        }
    ];
}

/**
 * 设置多折线图系列数据 - 使用项目标准
 */
function setupMultiLineSeriesStandard(option, data, config) {
    // 使用项目标准的颜色方案
    const colors = config.colorScheme || ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];

    console.log('设置多折线图系列数据，配置信息:', {
        hasMultiLineStyles: config.hasMultiLineStyles,
        individualLineStyles: config.individualLineStyles?.length || 0,
        seriesCount: data.seriesData.length
    });

    option.series = data.seriesData.map((series, index) => {
        const defaultColor = colors[index % colors.length];

        // 获取该折线的独立样式配置
        const lineStyle = config.individualLineStyles && config.individualLineStyles[index]
            ? config.individualLineStyles[index]
            : {};

        console.log(`折线 ${index + 1} 样式配置:`, lineStyle);

        // 构建系列配置
        const seriesConfig = {
            name: series.name,
            type: 'line',
            data: series.data,
            xAxisIndex: config.enableDualYAxis ? series.yAxisIndex : 0,
            yAxisIndex: series.yAxisIndex || 0,

            // 线条样式（优先使用独立配置）
            lineStyle: {
                color: lineStyle.color || defaultColor,
                width: lineStyle.width !== undefined ? lineStyle.width : (config.multiLineWidth || config.lineWidth || 2),
                type: lineStyle.type || 'solid'
            },

            // 标记点样式（优先使用独立配置）
            symbol: lineStyle.symbolType || 'circle',
            showSymbol: lineStyle.showSymbol !== undefined ? lineStyle.showSymbol : (config.multiLineShowSymbol !== false),
            symbolSize: lineStyle.symbolSize !== undefined ? lineStyle.symbolSize : (config.multiLineSymbolSize || config.symbolSize || 6),
            itemStyle: {
                color: lineStyle.symbolColor || lineStyle.color || defaultColor,
                borderColor: lineStyle.symbolBorderColor,
                borderWidth: lineStyle.symbolBorderWidth
            },

            // 平滑曲线（优先使用独立配置，然后全局配置）
            smooth: lineStyle.smooth !== undefined ? lineStyle.smooth : (config.multiLineSmooth !== false),

            // 动画配置
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut'
        };

        // 渐变色处理
        if (lineStyle.useGradient && lineStyle.gradientColor) {
            seriesConfig.lineStyle.color = {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                    { offset: 0, color: lineStyle.color || defaultColor },
                    { offset: 1, color: lineStyle.gradientColor }
                ]
            };
        }

        // 面积填充（优先使用独立配置）
        if (lineStyle.showArea !== undefined ? lineStyle.showArea : config.multiLineShowArea) {
            const areaOpacity = lineStyle.areaOpacity !== undefined ? lineStyle.areaOpacity / 100 : 0.3;
            seriesConfig.areaStyle = {
                opacity: areaOpacity,
                color: {
                    type: 'linear',
                    x: 0, y: 0, x2: 0, y2: 1,
                    colorStops: [
                        { offset: 0, color: lineStyle.color || defaultColor },
                        { offset: 1, color: 'transparent' }
                    ]
                }
            };
        }

        // 数据标签（优先使用独立配置）
        if (lineStyle.showLabel !== undefined ? lineStyle.showLabel : config.showDataLabel) {
            seriesConfig.label = {
                show: true,
                position: lineStyle.labelPosition || 'top',
                fontSize: lineStyle.labelFontSize || config.dataLabelFontSize || 12,
                color: lineStyle.labelColor || lineStyle.color || defaultColor,
                fontWeight: lineStyle.labelFontWeight || 'normal'
            };
        }

        console.log(`折线 ${index + 1} 最终配置:`, seriesConfig);
        return seriesConfig;
    });
}

/**
 * 创建ECharts柱状图
 */
function createEChartsBarChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;



    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;

    // 获取通用配置
    const commonConfig = getCommonEChartsConfig(config);
    const axisConfig = getCommonAxisConfig(config);

    // 调试图例配置
    console.log('=== 柱状图图例配置调试 ===');
    console.log('样式配置中的图例参数:', {
        showLegend: config.showLegend,
        lateralPosition: config.lateralPosition,
        longitudinalPosition: config.longitudinalPosition,
        layoutFront: config.layoutFront,
        legendFontSize: config.legendFontSize,
        legendFontColor: config.legendFontColor,
        legendColor: config.legendColor
    });
    console.log('最终图例配置:', commonConfig.legend);

    const option = {
        ...commonConfig,

        // 柱状图特有的工具提示配置
        tooltip: {
            ...commonConfig.tooltip,
            trigger: 'axis',

        },

        // 使用通用轴配置并添加数据
        xAxis: {
            ...axisConfig.xAxis,
            data: data.labels || [],
            boundaryGap: true  // 柱状图需要边界间隙
        },
        yAxis: axisConfig.yAxis,
        // 柱状图特有的系列配置
        series: [{
            name: config.seriesName || '数据',
            type: 'bar',
            data: data.values || [],
            itemStyle: {
                color: config.useGradientBar ?
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: config.barStartColor || config.barColor || '#5470c6' },
                        { offset: 1, color: config.barEndColor || '#91cc75' }
                    ]) : (config.barColor || '#5470c6'),
                borderRadius: getBorderRadiusConfig(config.borderRadius || 4, config.borderRadiusMode || 'all')
            },
            barWidth: config.barWidth ? config.barWidth + '%' : '60%',
            // 数据标签
            label: {
                show: config.showDataLabels === true,
                position: 'top',
                fontSize: config.dataLabelFontSize || 11,
                color: config.dataLabelFontColor || config.barColor || '#5470c6',
                fontWeight: 'bold',
                formatter: function(params) {
                    // 使用数值，但会在updateEChartsData中动态更新formatter
                    return params.value;
                }
            },
            // 动画配置
            animation: config.animation !== false,
            animationDuration: config.animationDuration || 1000,
            animationEasing: config.animationEasing || 'cubicOut',
            animationDelay: config.animationDelay || 0
        }]
    };

    chart.setOption(option);

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 创建ECharts水平柱状图
 */
function createEChartsHorizontalBarChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;

    // 获取通用配置
    const commonConfig = getCommonEChartsConfig(config);
    const axisConfig = getCommonAxisConfig(config);

    const option = {
        ...commonConfig,

        // 水平柱状图特有的工具提示配置
        tooltip: {
            ...commonConfig.tooltip,
            trigger: 'axis',
        },

        // 水平柱状图：交换X轴和Y轴配置
        xAxis: axisConfig.yAxis, // 使用Y轴配置作为X轴（数值轴）
        yAxis: {
            ...axisConfig.xAxis,
            data: data.labels || [],
            boundaryGap: true  // 柱状图需要边界间隙
        },
        // 水平柱状图特有的系列配置
        series: [{
            name: config.seriesName || '数据',
            type: 'bar',
            data: data.values || [],
            itemStyle: {
                color: config.useGradientBar ?
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [  // 水平渐变
                        { offset: 0, color: config.barStartColor || config.barColor || '#5470c6' },
                        { offset: 1, color: config.barEndColor || '#91cc75' }
                    ]) : (config.barColor || '#5470c6'),
                borderRadius: getBorderRadiusConfig(config.borderRadius || 4, config.borderRadiusMode || 'all')
            },
            barWidth: config.barWidth ? config.barWidth + '%' : '60%',
            // 数据标签
            label: {
                show: config.showDataLabels === true,
                position: 'right',  // 水平柱状图标签在右侧
                fontSize: config.dataLabelFontSize || 11,
                color: config.dataLabelFontColor || config.barColor || '#5470c6',
                fontWeight: 'bold',
                formatter: function(params) {
                    // 使用数值，但会在updateEChartsData中动态更新formatter
                    return params.value;
                }
            },
            // 动画配置
            animation: config.animation !== false,
            animationDuration: config.animationDuration || 1000,
            animationEasing: config.animationEasing || 'cubicOut',
            animationDelay: config.animationDelay || 0
        }]
    };

    chart.setOption(option);

    // 标记图表类型，用于数据更新时识别
    chart._chartType = 'horizontal-bar';

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 创建ECharts仪表盘
 */
function createEChartsGauge(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;

    // 提取数值（兼容多种数据格式）
    let value = 0;
    if (typeof data === 'number') {
        // 直接传递数值（向后兼容）
        value = data;
    } else if (data && typeof data === 'object') {
        // 数据对象格式
        if (data.value !== undefined) {
            value = data.value;
        } else if (data.values && data.values.length > 0) {
            value = data.values[0];
        }
    }

    console.log('仪表盘数据处理:', {
        原始数据: data,
        提取数值: value,
        数据类型: typeof data
    });

    // 获取通用配置（仪表盘不需要轴配置）
    const commonConfig = getCommonEChartsConfig(config);

    const option = {
        ...commonConfig,

        // 仪表盘特有的标题位置调整
        title: {
            ...commonConfig.title,
            top: '10%'
        },
        // 仪表盘特有的系列配置
        series: [{
            name: config.name || '仪表盘',
            type: 'gauge',
            center: ['50%', '60%'],
            radius: config.gaugeRadius ? config.gaugeRadius + '%' : '80%',
            min: config.gaugeMin || 0,
            max: config.gaugeMax || 100,
            splitNumber: config.splitNumber || 10,
            axisLine: {
                lineStyle: {
                    width: config.axisLineWidth || 10,
                    color: [
                        [0.3, config.color1 || '#67e0e3'],
                        [0.7, config.color2 || '#37a2da'],
                        [1, config.color3 || '#fd666d']
                    ]
                }
            },
            pointer: {
                itemStyle: {
                    color: config.pointerColor || 'auto'
                }
            },
            axisTick: {
                distance: -30,
                length: 8,
                lineStyle: {
                    color: '#fff',
                    width: 2
                }
            },
            splitLine: {
                distance: -30,
                length: 30,
                lineStyle: {
                    color: '#fff',
                    width: 4
                }
            },
            axisLabel: {
                color: config.yAxisFontColor || 'auto',
                distance: 40,
                fontSize: config.yAxisFontSize || 12
            },
            detail: {
                valueAnimation: true,
                formatter: '{value}' + (config.gaugeUnit || '%'),
                color: config.valueColor || 'auto',
                fontSize: config.valueFontSize || 20,
                offsetCenter: [0, '70%']
            },
            data: [{
                value: value || 0,
                name: config.dataName || ''
            }],
            // 动画配置
            animation: config.animation !== false,
            animationDuration: config.animationDuration || 1000,
            animationEasing: config.animationEasing || 'cubicOut'
        }]
    };

    chart.setOption(option);

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 创建ECharts饼图
 */
function createEChartsPieChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
    }

    // 创建新实例
    const chart = echarts.init(container, config.theme || 'default');
    echartsInstances[containerId] = chart;

    // 转换数据格式
    const pieData = [];
    if (data.labels && data.values) {
        for (let i = 0; i < data.labels.length; i++) {
            pieData.push({
                name: data.labels[i],
                value: data.values[i]
            });
        }
    }

    // 获取通用配置（饼图不需要轴配置）
    const commonConfig = getCommonEChartsConfig(config);

    // 获取自定义颜色
    const customColors = getPieColors(config, pieData.length);

    // 调试饼图标签配置
    console.log('=== 饼图初始化标签配置 ===');
    console.log('配置参数:', {
        pieShowDataLabels: config.pieShowDataLabels,
        dataLabelFontSize: config.dataLabelFontSize,
        dataLabelFontColor: config.dataLabelFontColor,
        showLabels: config.pieShowDataLabels !== false
    });
    console.log('=== 饼图标签配置检查结束 ===');

    const option = {
        ...commonConfig,

        // 应用自定义颜色
        color: customColors,

        // 饼图特有的工具提示配置
        tooltip: {
            ...commonConfig.tooltip,
            trigger: 'item',
            formatter: function(params) {
                // 如果数据项有显示值（包含后缀），使用显示值，否则使用原值
                let value = params.value;
                if (params.data.displayValue !== undefined) {
                    value = params.data.displayValue;
                }
                return params.seriesName + '<br/>' + params.name + ': ' + value + ' (' + params.percent + '%)';
            }
        },


        // 饼图特有的系列配置
        series: [{
            name: config.seriesName || '数据',
            type: 'pie',
            radius: getPieRadius(config),
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: getPieBorderRadius(config),
                borderColor: config.pieBorderColor || '#fff',
                borderWidth: config.showPieBorder !== false ? (config.pieBorderWidth || 2) : 0
            },
            label: {
                show: config.pieShowDataLabels !== false,
                position: 'outside',
                fontSize: config.dataLabelFontSize || 12,
                color: config.dataLabelFontColor || '#666',
                formatter: function(params) {
                    // 如果数据项有显示值（包含后缀），使用显示值，否则使用原值
                    if (params.data.displayValue !== undefined) {
                        return params.name + ': ' + params.data.displayValue;
                    } else {
                        return params.name + ': ' + params.value;
                    }
                }
            },
            emphasis: {
                label: {
                    show: config.pieShowDataLabels !== false,
                    fontSize: (config.dataLabelFontSize || 12) + 2,
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: config.showLabelLine !== false
            },
            data: pieData,
            // 动画配置
            animation: config.animation !== false,
            animationDuration: config.animationDuration || 1000,
            animationEasing: config.animationEasing || 'cubicOut'
        }]
    };

    chart.setOption(option);

    // 响应式
    window.addEventListener('resize', () => {
        chart.resize();
    });

    return chart;
}

/**
 * 更新图表数据
 */
function updateEChartsData(containerId, data, config = {}) {
    const chart = echartsInstances[containerId];
    if (!chart) return;

    console.log('更新ECharts数据:', containerId, data);

    const option = chart.getOption();

    // 检查是否是水平柱状图
    const isHorizontalBar = containerId.includes('horizontal-bar') ||
                           (chart._chartType && chart._chartType === 'horizontal-bar');

    // 更新轴数据
    if (isHorizontalBar) {
        // 水平柱状图：Y轴是分类轴，X轴是数值轴
        if (option.yAxis && option.yAxis[0]) {
            option.yAxis[0].data = data.labels || [];
        }
    } else {
        // 垂直图表：X轴是分类轴
        if (option.xAxis && option.xAxis[0]) {
            option.xAxis[0].data = data.labels || [];
        }
    }

    if (option.series && option.series[0]) {
        if (option.series[0].type === 'gauge') {
            // 仪表盘：更新数值
            option.series[0].data[0].value = data.value || 0;

            // 直接更新formatter显示转换后的值
            if (data.displayValue !== undefined) {
                option.series[0].detail.formatter = data.displayValue;
                console.log('仪表盘显示值更新为:', data.displayValue);
            } else {
                option.series[0].detail.formatter = '{value}%';
            }
        } else if (option.series[0].type === 'pie') {
            // 饼图：使用转换后的数据结构并重新应用颜色配置
            let pieData = [];
            if (data.data && Array.isArray(data.data)) {
                pieData = data.data;
                option.series[0].data = pieData;
                console.log('饼图数据更新:', pieData);
            } else if (data.labels && data.values) {
                // 如果没有转换后的数据结构，使用原始数据
                for (let i = 0; i < data.labels.length; i++) {
                    pieData.push({
                        name: data.labels[i],
                        value: data.values[i]
                    });
                }
                option.series[0].data = pieData;
                console.log('饼图数据转换并更新:', pieData);
            }

            // 重新应用颜色配置（关键修复：确保自定义颜色在数据更新时不丢失）
            if (config && pieData.length > 0) {
                const customColors = getPieColors(config, pieData.length);
                option.color = customColors;
                console.log('饼图颜色配置重新应用:', {
                    useCustomColors: config.useCustomPieColors,
                    colorCount: customColors.length,
                    colors: customColors
                });
            }

            // 重新应用标签配置（关键修复：确保数值标签在数据更新时正确显示）
            if (config) {
                option.series[0].label = {
                    show: config.pieShowDataLabels !== false,
                    position: 'outside',
                    fontSize: config.dataLabelFontSize || 12,
                    color: config.dataLabelFontColor || '#666',
                    formatter: function(params) {
                        // 如果数据项有显示值（包含后缀），使用显示值，否则使用原值
                        if (params.data.displayValue !== undefined) {
                            return params.name + ': ' + params.data.displayValue;
                        } else {
                            return params.name + ': ' + params.value;
                        }
                    }
                };

                // 重新应用悬停时的标签配置
                option.series[0].emphasis = {
                    label: {
                        show: config.pieShowDataLabels !== false,
                        fontSize: (config.dataLabelFontSize || 12) + 2,
                        fontWeight: 'bold'
                    }
                };

                console.log('饼图标签配置重新应用:', {
                    showLabels: config.pieShowDataLabels !== false,
                    fontSize: config.dataLabelFontSize || 12,
                    fontColor: config.dataLabelFontColor || '#666'
                });

                // 重新应用边线配置（关键修复：确保边线配置在数据更新时不丢失）
                option.series[0].itemStyle = {
                    ...option.series[0].itemStyle,
                    borderColor: config.pieBorderColor || '#fff',
                    borderWidth: config.showPieBorder !== false ? (config.pieBorderWidth || 2) : 0
                };

                console.log('饼图边线配置重新应用:', {
                    showBorder: config.showPieBorder !== false,
                    borderColor: config.pieBorderColor || '#fff',
                    borderWidth: config.showPieBorder !== false ? (config.pieBorderWidth || 2) : 0
                });
            }
        } else if (option.series[0].type === 'liquidFill') {
            // 水波图：更新数值和重新计算百分比
            console.log('更新水波图数据:', data);

            // 重新创建水波图，因为liquidFill需要重新计算配置
            const containerId = chart.getDom().id;
            const currentConfig = chart._waterChartConfig || {};

            // 销毁当前实例
            chart.dispose();
            delete echartsInstances[containerId];

            // 重新创建水波图
            const newChart = createEChartsWaterChart(containerId, data, currentConfig);
            console.log('水波图重新创建完成');
            return; // 直接返回，不需要继续执行setOption
        } else if (containerId.includes('column-percentage') || (chart._columnPercentageConfig)) {
            // 柱状百分比图：更新数值和重新计算百分比
            console.log('更新柱状百分比图数据:', data);

            // 重新创建柱状百分比图，因为需要重新计算百分比和颜色
            const containerId = chart.getDom().id;
            const currentConfig = chart._columnPercentageConfig || {};

            // 销毁当前实例
            chart.dispose();
            delete echartsInstances[containerId];

            // 重新创建柱状百分比图
            const newChart = createEChartsColumnPercentageChart(containerId, data, currentConfig);
            console.log('柱状百分比图重新创建完成');
            return; // 直接返回，不需要继续执行setOption
        } else {
            // 折线图/柱状图：使用数值数据绘制图形
            option.series[0].data = data.values || [];

            // 确保水平柱状图的标签位置正确（关键修复：数据更新时保持标签位置）
            if (isHorizontalBar) {
                // 水平柱状图：标签应该在右侧
                if (option.series[0].label) {
                    option.series[0].label.position = 'right';
                    console.log('水平柱状图数据更新时重新设置标签位置为右侧');
                }
            } else {
                // 垂直图表：标签在上方
                if (option.series[0].label) {
                    option.series[0].label.position = 'top';
                }
            }

            // 如果有显示值数组，更新label和tooltip formatter
            if (data.displayValues && Array.isArray(data.displayValues)) {
                // 更新数据标签formatter显示包含后缀的值
                option.series[0].label.formatter = function(params) {
                    return data.displayValues[params.dataIndex] || params.value;
                };

                // 更新工具提示formatter显示包含后缀的值
                if (option.tooltip) {
                    option.tooltip.formatter = function(params) {
                        if (!Array.isArray(params)) params = [params];
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            let value = data.displayValues[param.dataIndex] || param.value;
                            result += param.marker + param.seriesName + ': ' + value + '<br/>';
                        });
                        return result;
                    };
                }

                console.log('图表数值数据:', data.values);
                console.log('图表显示值数组:', data.displayValues);
            } else {
                // 如果没有显示值数组，恢复默认formatter
                option.series[0].label.formatter = function(params) {
                    return params.value;
                };

                if (option.tooltip) {
                    option.tooltip.formatter = null; // 使用默认formatter
                }
            }
        }
    }

    chart.setOption(option, true);
}

/**
 * 销毁图表实例
 */
function disposeEChart(containerId) {
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
        delete echartsInstances[containerId];
    }
}

/**
 * 获取默认ECharts配置
 */
function getDefaultEChartsConfig(chartType) {
    const baseConfig = {
        theme: 'default',
        titleFontSize: 16,
        titleColor: '#333',
        labelColor: '#666',
        labelFontSize: 12,
        axisColor: '#ccc',
        gridLineColor: '#f0f0f0'
    };

    switch (chartType) {
        case 'line':
            return {
                ...baseConfig,
                smooth: true,
                showArea: false,
                showSymbol: true,
                lineWidth: 2,
                symbolSize: 6
            };
        case 'bar':
            return {
                ...baseConfig,
                barWidth: '60%',
                borderRadius: [4, 4, 0, 0]
            };
        case 'gauge':
            return {
                ...baseConfig,
                min: 0,
                max: 100,
                radius: '80%',
                unit: '%'
            };
        case 'multi-line':
            return {
                ...baseConfig,
                smooth: true,
                showArea: false,
                showSymbol: true,
                lineWidth: 2,
                symbolSize: 6,
                enableDualYAxis: false,
                showLegend: true,
                legendPosition: 'top'
            };
        case 'water-chart':
            return {
                ...baseConfig,
                borderWidth: 5,
                borderColor: '#5470c6',
                waveColor: '#5470c6',
                amplitude: 20,
                targetValue: 100,
                enableTarget: false
            };
        default:
            return baseConfig;
    }
}

/**
 * 获取饼图半径配置
 */
function getPieRadius(config) {
    if (config.pieType === 'solid') {
        // 实心饼图：使用单一半径
        const radiusValue = config.pieRadius || 70;
        return radiusValue + '%';
    } else {
        // 环形饼图：使用内环外环配置
        const innerRadius = config.pieInnerRadius || 40;
        const outerRadius = config.pieOuterRadius || 70;

        // 确保外环大于内环
        const validInner = Math.min(innerRadius, outerRadius - 10);
        const validOuter = Math.max(outerRadius, validInner + 10);

        return [validInner + '%', validOuter + '%'];
    }
}

/**
 * 获取饼图颜色配置
 */
function getPieColors(config, dataLength) {
    console.log('=== 饼图颜色配置计算 ===');
    console.log('配置参数:', {
        useCustomPieColors: config.useCustomPieColors,
        pieColorsLength: config.pieColors ? config.pieColors.length : 0,
        dataLength: dataLength
    });

    if (config.useCustomPieColors && config.pieColors && config.pieColors.length > 0) {
        const colors = [];
        console.log('使用自定义颜色配置:', config.pieColors);

        for (let i = 0; i < dataLength; i++) {
            const colorIndex = i % config.pieColors.length;
            const colorConfig = config.pieColors[colorIndex];

            if (colorConfig.gradient && colorConfig.gradientColor) {
                // 创建线性渐变色
                const gradientColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                        { offset: 0, color: colorConfig.color },
                        { offset: 1, color: colorConfig.gradientColor }
                    ]
                };
                colors.push(gradientColor);
                console.log(`数据项 ${i} 使用渐变色:`, colorConfig.color, '→', colorConfig.gradientColor);
            } else {
                // 使用纯色
                colors.push(colorConfig.color);
                console.log(`数据项 ${i} 使用纯色:`, colorConfig.color);
            }
        }

        console.log('自定义颜色配置完成，颜色数量:', colors.length);
        console.log('=== 颜色配置计算结束 ===');
        return colors;
    }

    // 使用默认颜色
    console.log('使用默认颜色配置');
    const defaultColors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272',
        '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f', '#87ceeb', '#32cd32'
    ];

    const colors = [];
    for (let i = 0; i < dataLength; i++) {
        colors.push(defaultColors[i % defaultColors.length]);
    }

    console.log('默认颜色配置完成，颜色数量:', colors.length);
    console.log('=== 颜色配置计算结束 ===');
    return colors;
}

/**
 * 获取饼图圆角配置
 */
function getPieBorderRadius(config) {
    const radius = config.pieBorderRadius || 0;
    const mode = config.pieBorderRadiusMode || 'all';

    if (radius === 0) {
        return 0;
    }

    if (mode === 'outer') {
        // 仅最外部圆角：只有外边缘有圆角
        if (config.pieType === 'solid') {
            // 实心饼图：所有边都是外边缘
            return radius;
        } else {
            // 环形饼图：只有外圆有圆角，内圆保持直角
            return [0, radius];
        }
    } else {
        // 全圆角：所有边都有圆角
        return radius;
    }
}

/**
 * 创建ECharts水波图
 */
function createEChartsWaterChart(containerId, data, config = {}) {
    const container = document.getElementById(containerId);
    if (!container) return null;

    // 销毁已存在的实例
    if (echartsInstances[containerId]) {
        echartsInstances[containerId].dispose();
        delete echartsInstances[containerId];
    }

    // 获取通用配置
    const commonConfig = getCommonEChartsConfig(config);

    // 处理数据
    let currentValue = 0;
    let targetValue = config.targetValue || 100;

    console.log('水波图原始数据:', data);
    console.log('水波图配置:', config);

    // 用于显示的值（可能包含后缀）
    let displayCurrentValue = null;
    let displayTargetValue = null;

    if (data && data.success) {
        if (data.values && data.values.length > 0) {
            currentValue = parseFloat(data.values[0]) || 0;
        } else if (data.value !== undefined) {
            currentValue = parseFloat(data.value) || 0;
        }

        // 处理目标值数据
        if (data.targetValue !== undefined) {
            targetValue = parseFloat(data.targetValue) || targetValue;
        }

        // 处理显示值（数值转换后的格式化值）
        if (data.displayValue !== undefined) {
            displayCurrentValue = data.displayValue;
            console.log('使用转换后的显示值:', displayCurrentValue);
        }
    } else if (data && data.value !== undefined) {
        // 处理默认数据情况（包括静态数据）
        currentValue = parseFloat(data.value) || 0;
        if (data.targetValue !== undefined) {
            targetValue = parseFloat(data.targetValue) || targetValue;
        }
        if (data.displayValue !== undefined) {
            displayCurrentValue = data.displayValue;
        }
    } else {
        // 如果没有数据，使用默认值
        currentValue = 65;
    }

    // 从配置中获取目标值设置
    if (config.enableTarget) {
        if (config.targetSource === 'manual' && config.targetValue) {
            targetValue = parseFloat(config.targetValue) || targetValue;
        }
        // 如果是监控项数据源，targetValue已经在上面从data中获取了
    }

    // 计算进度百分比
    let percentage;
    let finalDisplayValue = null; // 最终显示值（经过数值转换的百分比）

    if (config.enableTarget) {
        // 目标值模式：当前值 / 目标值
        percentage = targetValue > 0 ? (currentValue / targetValue) : 0;
    } else {
        // 普通模式：当前值就是百分比值，需要除以100转换为0-1范围
        percentage = currentValue / 100;
    }
    const displayPercentage = Math.min(Math.max(percentage, 0), 1);

    // 如果有数值转换配置，对百分比结果进行转换
    if (data && data.success && data.enableDataTransform) {
        const percentageValue = Math.round(displayPercentage * 100);
        console.log('对百分比结果进行数值转换:', percentageValue);

        // 应用数值转换到百分比结果
        const operation = data.transformOperation || 'add';
        const operand = parseFloat(data.transformValue) || 1;
        const decimalPlaces = parseInt(data.decimalPlaces) || 0;
        const suffix = data.dataSuffix || '';

        let transformedPercentage = percentageValue;
        switch (operation) {
            case 'add':
                transformedPercentage = percentageValue + operand;
                break;
            case 'subtract':
                transformedPercentage = percentageValue - operand;
                break;
            case 'multiply':
                transformedPercentage = percentageValue * operand;
                break;
            case 'divide':
                transformedPercentage = operand !== 0 ? percentageValue / operand : percentageValue;
                break;
        }

        const formattedValue = transformedPercentage.toFixed(decimalPlaces);
        finalDisplayValue = formattedValue + suffix;
        console.log('百分比数值转换结果:', percentageValue, '→', transformedPercentage, '显示为:', finalDisplayValue);
    }

    console.log('水波图数据处理:', {
        currentValue,
        targetValue,
        enableTarget: config.enableTarget,
        percentage,
        displayPercentage,
        config
    });

    // 获取水波颜色配置
    const waveColor = getWaterWaveColor(config);
    const borderColor = getWaterBorderColor(config);

    const option = {
        backgroundColor: 'transparent',
        animation: true,

        // 图表标题配置
        title: {
            show: config.showChartTitle !== false,
            text: config.title || '',
            left: 'center',
            top: '10px',
            textStyle: {
                fontSize: config.titleFontSize || 16,
                color: config.titleColor || '#333',
                fontWeight: 'bold'
            }
        },

        series: [{
            type: 'liquidFill',
            data: [displayPercentage],
            color: [waveColor],
            radius: '75%',
            center: ['50%', '50%'],
            amplitude: config.waterAmplitude || config.amplitude || 20,
            waveLength: '80%',
            period: 2000,
            direction: 'right',
            shape: 'circle',
            outline: {
                show: config.waterBorderWidth > 0, // 只有配置了边框宽度且大于0时才显示边框
                borderDistance: config.waterBorderWidth || 0,
                itemStyle: {
                    borderWidth: config.waterBorderWidth || 0, // 默认边框宽度为0
                    borderColor: config.waterBorderWidth > 0 ? borderColor : 'transparent', // 无边框时设置为透明
                    shadowBlur: config.waterBorderWidth > 0 ? 20 : 0, // 只有有边框时才显示阴影
                    shadowColor: 'rgba(0, 0, 0, 0.25)'
                }
            },
            label: {
                show: config.showWaterLabel !== false,
                fontSize: config.waterLabelFontSize || 20,
                fontWeight: 'bold',
                color: config.waterLabelFontColor || '#333333',
                formatter: function() {
                    console.log('水波图标签配置:', {
                        enableTarget: config.enableTarget,
                        showTargetValues: config.showTargetValues,
                        showPercentageSymbol: config.showPercentageSymbol,
                        currentValue,
                        targetValue,
                        displayCurrentValue,
                        displayTargetValue,
                        displayPercentage,
                        finalDisplayValue
                    });

                    if (config.enableTarget) {
                        if (config.showTargetValues !== false) {
                            // 使用显示值（如果有的话）或原始值
                            const showCurrentValue = displayCurrentValue || currentValue;
                            const showTargetValue = displayTargetValue || targetValue;

                            // 百分比部分
                            let percentagePart;
                            if (finalDisplayValue) {
                                // 使用转换后的百分比显示值
                                percentagePart = finalDisplayValue;
                            } else {
                                // 使用原始百分比
                                const percentageValue = Math.round(displayPercentage * 100);
                                percentagePart = config.showPercentageSymbol !== false ? `${percentageValue}%` : percentageValue;
                            }

                            return `${showCurrentValue}/${showTargetValue}\n${percentagePart}`;
                        } else {
                            // 只显示百分比
                            if (finalDisplayValue) {
                                return finalDisplayValue;
                            } else {
                                const percentageValue = Math.round(displayPercentage * 100);
                                return config.showPercentageSymbol !== false ? `${percentageValue}%` : percentageValue;
                            }
                        }
                    } else {
                        // 普通模式：优先使用百分比转换结果，然后是原始数值转换结果，最后是原始值
                        if (finalDisplayValue) {
                            // 使用百分比转换后的显示值
                            return finalDisplayValue;
                        } else if (displayCurrentValue) {
                            // 使用原始数值转换后的显示值
                            return displayCurrentValue;
                        } else {
                            // 使用原始值
                            return config.showPercentageSymbol !== false ? `${currentValue}%` : currentValue;
                        }
                    }
                }
            },
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
                opacity: 0.8,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.4)'
            }
        }]
    };

    console.log('水波图配置:', { waveColor, borderColor, option });

    // 创建图表实例
    const chart = echarts.init(container);
    chart.setOption(option);

    // 保存配置以便更新时使用
    chart._waterChartConfig = config;

    // 保存实例引用
    echartsInstances[containerId] = chart;

    // 响应式处理
    const resizeObserver = new ResizeObserver(() => {
        if (chart && !chart.isDisposed()) {
            chart.resize();
        }
    });
    resizeObserver.observe(container);

    return chart;
}

/**
 * 获取水波颜色配置
 */
function getWaterWaveColor(config) {
    // 使用更明显的默认颜色
    const defaultColor = '#1890ff';

    if (config.useWaterWaveGradient && config.waterWaveGradientColor) {
        const gradient = {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
                { offset: 0, color: config.waterWaveColor || defaultColor },
                { offset: 1, color: config.waterWaveGradientColor }
            ]
        };
        console.log('水波渐变色配置:', gradient);
        return gradient;
    }

    const solidColor = config.waterWaveColor || defaultColor;
    console.log('水波纯色配置:', solidColor);
    return solidColor;
}

/**
 * 获取水波图外框颜色配置
 */
function getWaterBorderColor(config) {
    // 使用更明显的默认边框颜色
    const defaultBorderColor = '#1890ff';

    if (config.useWaterBorderGradient && config.waterBorderGradientColor) {
        const gradient = {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
                { offset: 0, color: config.waterBorderColor || defaultBorderColor },
                { offset: 1, color: config.waterBorderGradientColor }
            ]
        };
        console.log('外框渐变色配置:', gradient);
        return gradient;
    }

    const solidBorderColor = config.waterBorderColor || defaultBorderColor;
    console.log('外框纯色配置:', solidBorderColor);
    return solidBorderColor;
}

/**
 * 创建柱状百分比图表
 */
function createEChartsColumnPercentageChart(containerId, data, config) {
    console.log('创建柱状百分比图:', { containerId, data, config });

    const container = document.getElementById(containerId);
    if (!container) {
        console.error('柱状百分比图容器不存在:', containerId);
        return null;
    }

    // 处理数据
    const value = data.value || 0;
    const maxValue = config.maxValue || 100;

    // 获取目标值：优先从数据中获取，然后从配置中获取
    let targetValue = data.targetValue;
    if (!targetValue && config.enableColumnTarget) {
        if (config.columnTargetSource === 'manual') {
            targetValue = config.columnTargetValue;
        }
        // 如果是监控项数据源，目标值应该已经在data.targetValue中
    }

    // 如果还没有目标值，尝试从旧的配置方式获取（向后兼容）
    if (!targetValue) {
        targetValue = config.targetValue;
    }

    console.log('柱状百分比图原始数据:', {
        'data.value': data.value,
        'data.targetValue': data.targetValue,
        'config.targetValue': config.targetValue,
        'config.maxValue': config.maxValue,
        'config.enableColumnTarget': config.enableColumnTarget,
        'data对象': data,
        'config对象': config
    });

    // 计算百分比：只有启用目标值且目标值有效时才使用目标值作为基准，否则使用最大值
    let percentage;
    let baseValue;

    if (config.enableColumnTarget && targetValue && targetValue > 0) {
        // 启用目标值且目标值有效时，使用目标值作为100%的基准
        baseValue = targetValue;
        percentage = (value / targetValue) * 100;
        console.log('启用目标值模式，使用目标值作为基准:', {
            enableColumnTarget: config.enableColumnTarget,
            value,
            targetValue,
            percentage
        });
    } else {
        // 未启用目标值或目标值无效时，使用最大值作为100%的基准
        baseValue = maxValue;
        percentage = (value / maxValue) * 100;
        console.log('使用最大值模式，使用最大值作为基准:', {
            enableColumnTarget: config.enableColumnTarget,
            targetValue,
            value,
            maxValue,
            percentage
        });
    }

    // 确保百分比不为负数，但允许超过100%
    percentage = Math.max(percentage, 0);

    // 如果配置了不允许超额显示，则限制在100%以内
    const allowOverflow = config.allowOverflow !== false; // 默认允许超额
    if (!allowOverflow) {
        percentage = Math.min(percentage, 100);
    }

    console.log('柱状百分比图最终计算结果:', {
        value,
        maxValue,
        targetValue,
        baseValue,
        percentage: percentage.toFixed(1) + '%',
        allowOverflow
    });

    console.log('柱状百分比图标题配置:', {
        isShowTitle: config.isShowTitle,
        title: config.title,
        titleText: config.titleText,
        titlePosition: config.titlePosition,
        titleColor: config.titleColor,
        titleFontSize: config.titleFontSize
    });

    console.log('柱状百分比图完整配置对象:', config);

    // 获取颜色配置
    const barColor = getColumnPercentageColor(config, percentage);

    // 构建ECharts配置
    const option = {
        backgroundColor: 'transparent',

        // 标题配置
        title: config.isShowTitle ? {
            text: config.title || '柱状百分比图',
            left: config.titlePosition || 'center',
            top: 10,
            textStyle: {
                color: config.titleColor || '#333333',
                fontSize: config.titleFontSize || 16,
                fontWeight: 'bold'
            }
        } : null,

        // 图例配置（柱状百分比图不需要图例）
        legend: {
            show: false
        },

        // 网格配置
        grid: {
            left: '15%',
            right: '15%',
            top: config.isShowTitle ? '25%' : '10%',
            bottom: '15%',
            containLabel: true
        },

        // X轴配置（隐藏）
        xAxis: {
            type: 'value',
            show: false,
            min: 0,
            max: 100
        },

        // Y轴配置（隐藏）
        yAxis: {
            type: 'category',
            show: false,
            data: ['']
        },

        // 系列配置
        series: []
    };

    // 设置柱子间距为负值，让柱子重叠
    option.barGap = '-100%';

    // 柱子宽度配置
    const barWidth = config.barWidth || 40;

    // 添加背景柱子（如果启用）
    if (config.showBarBackground !== false) {
        const barBackgroundColor = config.barBackgroundColor || '#f5f5f5';
        const borderRadius = config.borderRadius || 8;

        // 使用bar作为背景，通过负间距实现重叠效果
        option.series.push({
            type: 'bar',
            data: [100],
            barWidth: barWidth, // 使用原始宽度
            itemStyle: {
                color: barBackgroundColor,
                borderRadius: [borderRadius, borderRadius, borderRadius, borderRadius]
            },
            label: (config.showDataLabel && ['center', 'vertical-top', 'vertical-bottom'].includes(config.dataLabelPosition)) ? {
                show: true,
                position: (() => {
                    // 垂直位置映射到背景柱子的预定义位置
                    const verticalPositionMap = {
                        'vertical-top': 'top',
                        'center': 'inside',
                        'vertical-bottom': 'bottom'
                    };
                    return verticalPositionMap[config.dataLabelPosition] || 'inside';
                })(),
                formatter: function(params) {
                    let labelText = '';

                    // 显示百分比
                    if (config.showPercentage) {
                        labelText = percentage.toFixed(1) + '%';
                    } else {
                        labelText = value.toString();
                    }

                    // 如果启用目标值标签显示，添加当前值/目标值格式
                    if (config.showColumnTargetLabel && targetValue) {
                        if (config.showPercentage) {
                            labelText += '  ' + value + '/' + targetValue;
                        } else {
                            labelText = value + '/' + targetValue;
                        }
                    }

                    return labelText;
                },
                color: config.dataLabelColor || '#333333',
                fontSize: config.dataLabelFontSize || 14,
                fontWeight: 'bold'
            } : null,
            silent: true,
            z: 1 // 中间层
        });
    }

    // 添加数值柱子
    option.series.push({
        type: 'bar',
        data: [percentage],
        barWidth: barWidth, // 使用原始宽度
        itemStyle: {
            color: barColor,
            borderRadius: [config.borderRadius || 8, config.borderRadius || 8, config.borderRadius || 8, config.borderRadius || 8],
            shadowBlur: config.enableShadow ? (config.shadowBlur || 10) : 0,
            shadowColor: config.shadowColor || 'rgba(0, 0, 0, 0.1)',
            shadowOffsetY: config.enableShadow ? 2 : 0
        },
        label: (config.showDataLabel && !['center', 'vertical-top', 'vertical-bottom'].includes(config.dataLabelPosition)) ? {
            show: true,
            position: config.dataLabelPosition || 'top',
            formatter: function(params) {
                let labelText = '';

                // 显示百分比
                if (config.showPercentage) {
                    labelText = percentage.toFixed(1) + '%';
                } else {
                    labelText = value.toString();
                }

                // 如果启用目标值标签显示，添加当前值/目标值格式
                if (config.showColumnTargetLabel && targetValue) {
                    if (config.showPercentage) {
                        labelText += '  ' + value + '/' + targetValue;
                    } else {
                        labelText = value + '/' + targetValue;
                    }
                }

                return labelText;
            },
            color: config.dataLabelColor || '#333333',
            fontSize: config.dataLabelFontSize || 14,
            fontWeight: 'bold'
        } : null,
        z: 2
    });






    console.log('柱状百分比图ECharts配置:', option);
    console.log('柱状百分比图标题配置详情:', option.title);
    console.log('柱状百分比图图例配置详情:', option.legend);

    // 创建图表实例
    const chart = echarts.init(container);
    chart.setOption(option);

    // 保存配置以便更新时使用
    chart._columnPercentageConfig = config;

    // 保存实例引用
    echartsInstances[containerId] = chart;

    // 响应式处理
    const resizeObserver = new ResizeObserver(() => {
        if (chart && !chart.isDisposed()) {
            chart.resize();
        }
    });
    resizeObserver.observe(container);

    return chart;
}

/**
 * 获取柱状百分比图颜色配置
 */
function getColumnPercentageColor(config, percentage) {
    const defaultStartColor = '#4facfe';
    const defaultEndColor = '#00f2fe';

    if (config.useGradient) {
        const gradient = {
            type: 'linear',
            x: 0,
            y: 1,  // 从下到上的渐变
            x2: 0,
            y2: 0,
            colorStops: [
                { offset: 0, color: config.startColor || defaultStartColor },
                { offset: 1, color: config.endColor || defaultEndColor }
            ]
        };
        console.log('柱状百分比图渐变色配置:', gradient);
        return gradient;
    }

    const solidColor = config.startColor || defaultStartColor;
    console.log('柱状百分比图纯色配置:', solidColor);
    return solidColor;
}
