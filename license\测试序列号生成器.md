# 🧪 SDPLC序列号生成器测试指南

## 📋 测试目标

验证中文界面序列号生成器的所有功能是否正常工作，特别是用户ID格式验证功能。

## 🚀 启动测试

### 1. 启动程序
```bash
java SDPLCSerialGenerator
```

程序启动后应该显示：
- 标题：🔑 SDPLC序列号生成器
- 中文界面元素
- 默认用户ID：USER001
- 默认30天安装有效期
- 用户ID格式提示应该位于"随机生成"按钮下方，不重叠

## 🔧 功能测试

### 测试1：正常序列号生成
**目标**：验证基本生成功能

**步骤**：
1. 保持默认设置（用户ID: USER001，30天安装有效期）
2. 点击"🔧 生成序列号"
3. 点击"📋 复制到剪贴板"

**预期结果**：
- 显示"序列号生成成功！"消息，包含"安装有效期"说明
- 序列号以"SDPLC-"开头
- 复制功能正常工作

### 测试2：用户ID格式验证（重要）
**目标**：验证用户ID不支持小写字母的限制

**测试用例A - 小写字母（应该失败）**：
1. 清空用户ID字段
2. 输入：`user001`
3. 点击"🔧 生成序列号"

**预期结果**：
- 显示错误消息："用户ID格式不正确！只支持大写字母和数字，不支持小写字母。"
- 不生成序列号

**测试用例B - 混合大小写（应该失败）**：
1. 清空用户ID字段
2. 输入：`User001`
3. 点击"🔧 生成序列号"

**预期结果**：
- 显示相同的格式错误消息

**测试用例C - 自动转换功能**：
1. 清空用户ID字段
2. 输入：`test`
3. 观察输入框内容

**预期结果**：
- 输入框自动显示：`TEST`（自动转换为大写）

### 测试3：日期格式验证
**目标**：验证日期格式检查

**测试用例**：
1. 设置用户ID：USER001
2. 设置开始日期：`2025-07-28`（错误格式）
3. 点击"🔧 生成序列号"

**预期结果**：
- 显示错误消息："日期格式不正确！请使用YYYYMMDD格式"

### 测试4：快捷按钮功能
**目标**：验证所有快捷按钮

**测试步骤**：
1. 点击"随机生成" - 应该生成USER###格式的ID
2. 点击"今天" - 应该设置今天的日期
3. 点击"明天" - 应该设置明天的日期
4. 点击"30天有效期" - 应该设置30天的安装有效期

### 测试5：清空功能
**目标**：验证清空按钮

**测试步骤**：
1. 生成一个序列号
2. 点击"🗑️ 清空"

**预期结果**：
- 序列号显示区域被清空
- 复制按钮变为不可用状态
- 状态显示"已清空"

## 📊 测试结果记录

### ✅ 通过的测试
- [ ] 基本序列号生成功能
- [ ] 用户ID小写字母验证
- [ ] 用户ID自动转换大写
- [ ] 日期格式验证
- [ ] 随机生成用户ID
- [ ] 快捷日期设置
- [ ] 有效期快捷设置
- [ ] 复制到剪贴板功能
- [ ] 清空功能
- [ ] 中文界面显示

### ❌ 失败的测试
记录任何失败的测试和错误信息：

```
测试项目：
错误信息：
重现步骤：
```

## 🎯 实际使用测试

### 生成真实序列号进行激活测试

**步骤**：
1. 使用序列号生成器生成序列号：
   - 用户ID: USER001
   - 30天安装有效期
2. 复制生成的序列号
3. 在SDPLC应用中进行激活测试：
   - 访问：http://localhost:8080/license/activation
   - 输入用户ID: USER001
   - 粘贴序列号
   - 点击激活

**预期结果**：
- 激活成功（因为在安装有效期内）
- 许可证文件保存到：C:\ProgramData\SHENGDA-PLC\license.json
- 激活后软件可以正常使用，没有时间限制

## 🔍 界面检查清单

### 中文界面元素检查
- [ ] 窗口标题：SDPLC序列号生成器 v2.0
- [ ] 主标题：🔑 SDPLC序列号生成器
- [ ] 参数区域标题：序列号参数设置
- [ ] 用户ID标签：用户ID:
- [ ] 开始日期标签：开始日期:
- [ ] 结束日期标签：结束日期:
- [ ] 按钮文字：随机生成、今天、明天
- [ ] 有效期按钮：1天有效期、7天有效期、30天有效期、365天有效期
- [ ] 结果区域标题：生成的序列号
- [ ] 操作按钮：🔧 生成序列号、📋 复制到剪贴板、🗑️ 清空
- [ ] 状态标签：准备就绪
- [ ] 用户ID格式提示：格式：USER001（仅支持大写字母和数字）

### 错误消息检查
- [ ] 空字段错误：请填写所有字段！
- [ ] 用户ID格式错误：用户ID格式不正确！只支持大写字母和数字，不支持小写字母。
- [ ] 日期格式错误：日期格式不正确！请使用YYYYMMDD格式
- [ ] 成功消息：序列号生成成功！
- [ ] 复制成功：序列号已成功复制到剪贴板！

## 📝 测试总结

完成所有测试后，记录：
1. 总体功能是否正常
2. 中文界面是否完整
3. 用户ID验证是否有效
4. 是否发现任何问题
5. 改进建议

---
**测试日期**：2025-07-28  
**测试版本**：SDPLCSerialGenerator v2.0  
**测试环境**：Windows + Java 8+
