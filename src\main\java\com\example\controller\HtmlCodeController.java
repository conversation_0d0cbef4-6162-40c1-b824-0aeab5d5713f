package com.example.controller;

import com.example.entity.HtmlCodeCategory;
import com.example.entity.HtmlCodeSnippet;
import com.example.service.HtmlCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * HTML代码管理控制器
 */
@RestController
@RequestMapping("/api/html-codes")
@Slf4j
public class HtmlCodeController {
    
    @Autowired
    private HtmlCodeService htmlCodeService;
    
    // ==================== 分类管理接口 ====================
    
    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public ResponseEntity<List<HtmlCodeCategory>> getAllCategories() {
        try {
            List<HtmlCodeCategory> categories = htmlCodeService.getAllCategories();
            return ResponseEntity.ok(categories);
        } catch (Exception e) {
            log.error("获取HTML代码分类失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 创建新分类
     */
    @PostMapping("/categories")
    public ResponseEntity<Map<String, Object>> createCategory(
            @RequestParam String name,
            @RequestParam(required = false) String description) {
        Map<String, Object> response = new HashMap<>();
        try {
            HtmlCodeCategory category = htmlCodeService.createCategory(name, description);
            response.put("success", true);
            response.put("data", category);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建HTML代码分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 更新分类
     */
    @PutMapping("/categories/{id}")
    public ResponseEntity<Map<String, Object>> updateCategory(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam(required = false) String description) {
        Map<String, Object> response = new HashMap<>();
        try {
            HtmlCodeCategory category = htmlCodeService.updateCategory(id, name, description);
            response.put("success", true);
            response.put("data", category);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新HTML代码分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    public ResponseEntity<Map<String, Object>> deleteCategory(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            htmlCodeService.deleteCategory(id);
            response.put("success", true);
            response.put("message", "分类删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除HTML代码分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    // ==================== 代码片段管理接口 ====================
    
    /**
     * 获取所有代码片段
     */
    @GetMapping("/snippets")
    public ResponseEntity<List<HtmlCodeSnippet>> getAllSnippets() {
        try {
            List<HtmlCodeSnippet> snippets = htmlCodeService.getAllSnippets();
            return ResponseEntity.ok(snippets);
        } catch (Exception e) {
            log.error("获取HTML代码片段失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 根据分类获取代码片段
     */
    @GetMapping("/snippets/category/{categoryId}")
    public ResponseEntity<List<HtmlCodeSnippet>> getSnippetsByCategory(@PathVariable Long categoryId) {
        try {
            List<HtmlCodeSnippet> snippets = htmlCodeService.getSnippetsByCategory(categoryId);
            return ResponseEntity.ok(snippets);
        } catch (Exception e) {
            log.error("根据分类获取HTML代码片段失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    

    
    /**
     * 搜索代码片段
     */
    @GetMapping("/snippets/search")
    public ResponseEntity<List<HtmlCodeSnippet>> searchSnippets(@RequestParam String keyword) {
        try {
            List<HtmlCodeSnippet> snippets = htmlCodeService.searchSnippets(keyword);
            return ResponseEntity.ok(snippets);
        } catch (Exception e) {
            log.error("搜索HTML代码片段失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 根据ID获取代码片段
     */
    @GetMapping("/snippets/{id}")
    public ResponseEntity<HtmlCodeSnippet> getSnippetById(@PathVariable Long id) {
        try {
            Optional<HtmlCodeSnippet> snippet = htmlCodeService.getSnippetById(id);
            if (snippet.isPresent()) {
                return ResponseEntity.ok(snippet.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取HTML代码片段失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 创建代码片段
     */
    @PostMapping("/snippets")
    public ResponseEntity<Map<String, Object>> createSnippet(
            @RequestParam String title,
            @RequestParam(required = false) String description,
            @RequestParam String htmlContent,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String tags) {
        Map<String, Object> response = new HashMap<>();
        try {
            HtmlCodeSnippet snippet = htmlCodeService.createSnippet(title, description, htmlContent,
                                                                   categoryId, tags);
            response.put("success", true);
            response.put("data", snippet);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建HTML代码片段失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 更新代码片段
     */
    @PutMapping("/snippets/{id}")
    public ResponseEntity<Map<String, Object>> updateSnippet(
            @PathVariable Long id,
            @RequestParam String title,
            @RequestParam(required = false) String description,
            @RequestParam String htmlContent,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String tags) {
        Map<String, Object> response = new HashMap<>();
        try {
            HtmlCodeSnippet snippet = htmlCodeService.updateSnippet(id, title, description, htmlContent,
                                                                   categoryId, tags);
            response.put("success", true);
            response.put("data", snippet);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新HTML代码片段失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 删除代码片段
     */
    @DeleteMapping("/snippets/{id}")
    public ResponseEntity<Map<String, Object>> deleteSnippet(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            htmlCodeService.deleteSnippet(id);
            response.put("success", true);
            response.put("message", "代码片段删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除HTML代码片段失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    

}
