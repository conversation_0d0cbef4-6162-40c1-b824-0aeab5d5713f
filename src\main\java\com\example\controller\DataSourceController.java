package com.example.controller;

import com.example.entity.DataSource;
import com.example.service.DataSourceService;
import com.example.service.connector.DataSourceConnector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源管理控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@RequestMapping("/datasource")
public class DataSourceController {
    
    private final DataSourceService dataSourceService;
    
    /**
     * 数据源管理页面
     */
    @GetMapping
    public String index(Model model) {
        model.addAttribute("title", "数据源管理");
        return "datasource/index";
    }
    
    /**
     * 获取所有数据源
     */
    @GetMapping("/api/list")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAllDataSources() {
        try {
            List<DataSource> dataSources = dataSourceService.getAllDataSources();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dataSources);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据源列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取数据源列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据ID获取数据源
     */
    @GetMapping("/api/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDataSourceById(@PathVariable String id) {
        try {
            return dataSourceService.getDataSourceById(id)
                .map(dataSource -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("data", dataSource);
                    return ResponseEntity.ok(response);
                })
                .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("获取数据源失败: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取数据源失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建数据源
     */
    @PostMapping("/api/create")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createDataSource(@RequestBody DataSource dataSource) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSource created = dataSourceService.createDataSource(dataSource);
            response.put("success", true);
            response.put("message", "数据源创建成功");
            response.put("data", created);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("创建数据源失败", e);
            response.put("success", false);
            response.put("message", "创建数据源失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新数据源
     */
    @PutMapping("/api/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateDataSource(@PathVariable String id, @RequestBody DataSource dataSource) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSource updated = dataSourceService.updateDataSource(id, dataSource);
            response.put("success", true);
            response.put("message", "数据源更新成功");
            response.put("data", updated);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("更新数据源失败: {}", id, e);
            response.put("success", false);
            response.put("message", "更新数据源失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除数据源
     */
    @DeleteMapping("/api/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteDataSource(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            dataSourceService.deleteDataSource(id);
            response.put("success", true);
            response.put("message", "数据源删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("删除数据源失败: {}", id, e);
            response.put("success", false);
            response.put("message", "删除数据源失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 测试数据源连接
     */
    @PostMapping("/api/{id}/test")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> testConnection(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSourceConnector.ConnectionTestResult result = dataSourceService.testConnection(id);
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("responseTime", result.getResponseTime());
            response.put("details", result.getDetails());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("测试数据源连接失败: {}", id, e);
            response.put("success", false);
            response.put("message", "测试连接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取数据源元数据
     */
    @GetMapping("/api/{id}/metadata")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getMetadata(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSourceConnector.MetadataResult result = dataSourceService.getMetadata(id);
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("metadata", result.getMetadata());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("获取数据源元数据失败: {}", id, e);
            response.put("success", false);
            response.put("message", "获取元数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取数据源统计信息
     */
    @GetMapping("/api/statistics")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = dataSourceService.getStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取数据源统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取支持的数据源类型
     */
    @GetMapping("/api/types")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getSupportedTypes() {
        try {
            List<Map<String, Object>> types = dataSourceService.getSupportedTypes();
            return ResponseEntity.ok(types);
        } catch (Exception e) {
            log.error("获取支持的数据源类型失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 测试临时数据源连接
     */
    @PostMapping("/api/test-temp")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> testTempConnection(@RequestBody DataSource dataSource) {
        Map<String, Object> response = new HashMap<>();

        try {
            DataSourceConnector.ConnectionTestResult result = dataSourceService.testTempConnection(dataSource);
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("responseTime", result.getResponseTime());
            response.put("details", result.getDetails());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("测试临时数据源连接失败", e);
            response.put("success", false);
            response.put("message", "测试连接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
