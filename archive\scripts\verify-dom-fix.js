// 验证DOM冲突修复的脚本
// 模拟修复前后的行为差异

console.log('=== DOM冲突修复验证 ===\n');

// 模拟DOM结构
const mockDOM = {
    // 数据源模态框内的配置区域
    dataSourceModal: {
        databaseConfig: { id: 'databaseConfig', className: 'config-section', visible: true }
    },
    // 数据集向导的配置区域
    datasetWizard: {
        tableConfig: { id: 'tableConfig', className: 'config-section', visible: true },
        fieldConfig: { id: 'fieldConfig', className: 'config-section', visible: true },
        filterConfig: { id: 'filterConfig', className: 'config-section', visible: true }
    }
};

// 修复前的方法（有问题的版本）
function hideAllConfigSectionsOld(dom) {
    console.log('执行修复前的方法: document.querySelectorAll(\'.config-section\')');
    
    // 影响所有带有 .config-section 类的元素
    Object.values(dom.dataSourceModal).forEach(element => {
        if (element.className.includes('config-section')) {
            element.visible = false;
        }
    });
    
    Object.values(dom.datasetWizard).forEach(element => {
        if (element.className.includes('config-section')) {
            element.visible = false;
        }
    });
    
    return dom;
}

// 修复后的方法（正确的版本）
function hideAllConfigSectionsNew(dom) {
    console.log('执行修复后的方法: document.querySelectorAll(\'#dataSourceModal .config-section\')');
    
    // 只影响数据源模态框内的配置区域
    Object.values(dom.dataSourceModal).forEach(element => {
        if (element.className.includes('config-section')) {
            element.visible = false;
        }
    });
    
    // 数据集向导的配置区域不受影响
    // Object.values(dom.datasetWizard) 保持不变
    
    return dom;
}

// 重置DOM状态
function resetDOM() {
    return {
        dataSourceModal: {
            databaseConfig: { id: 'databaseConfig', className: 'config-section', visible: true }
        },
        datasetWizard: {
            tableConfig: { id: 'tableConfig', className: 'config-section', visible: true },
            fieldConfig: { id: 'fieldConfig', className: 'config-section', visible: true },
            filterConfig: { id: 'filterConfig', className: 'config-section', visible: true }
        }
    };
}

// 检查数据集向导配置区域的可见性
function checkDatasetWizardVisibility(dom) {
    const datasetSections = Object.values(dom.datasetWizard);
    const visibleCount = datasetSections.filter(section => section.visible).length;
    const totalCount = datasetSections.length;
    
    return {
        visibleCount,
        totalCount,
        allVisible: visibleCount === totalCount,
        hiddenSections: datasetSections.filter(section => !section.visible).map(s => s.id)
    };
}

// 测试修复前的行为
console.log('1. 测试修复前的行为:');
let domBefore = resetDOM();
console.log('初始状态 - 数据集向导配置区域:', checkDatasetWizardVisibility(domBefore));

domBefore = hideAllConfigSectionsOld(domBefore);
const resultBefore = checkDatasetWizardVisibility(domBefore);
console.log('执行hideAllConfigSections后 - 数据集向导配置区域:', resultBefore);
console.log(`结果: ${resultBefore.allVisible ? '✅ 正常' : '❌ 有问题'} - ${resultBefore.visibleCount}/${resultBefore.totalCount} 个区域可见`);
if (!resultBefore.allVisible) {
    console.log(`被隐藏的区域: ${resultBefore.hiddenSections.join(', ')}`);
}

console.log('\n2. 测试修复后的行为:');
let domAfter = resetDOM();
console.log('初始状态 - 数据集向导配置区域:', checkDatasetWizardVisibility(domAfter));

domAfter = hideAllConfigSectionsNew(domAfter);
const resultAfter = checkDatasetWizardVisibility(domAfter);
console.log('执行hideAllConfigSections后 - 数据集向导配置区域:', resultAfter);
console.log(`结果: ${resultAfter.allVisible ? '✅ 正常' : '❌ 有问题'} - ${resultAfter.visibleCount}/${resultAfter.totalCount} 个区域可见`);
if (!resultAfter.allVisible) {
    console.log(`被隐藏的区域: ${resultAfter.hiddenSections.join(', ')}`);
}

console.log('\n3. 修复效果对比:');
console.log(`修复前: ${resultBefore.allVisible ? '正常' : '有问题'} (${resultBefore.visibleCount}/${resultBefore.totalCount} 可见)`);
console.log(`修复后: ${resultAfter.allVisible ? '正常' : '有问题'} (${resultAfter.visibleCount}/${resultAfter.totalCount} 可见)`);

if (resultBefore.allVisible === false && resultAfter.allVisible === true) {
    console.log('✅ 修复成功！问题已解决');
} else if (resultBefore.allVisible === false && resultAfter.allVisible === false) {
    console.log('❌ 修复失败！问题仍然存在');
} else if (resultBefore.allVisible === true && resultAfter.allVisible === true) {
    console.log('ℹ️ 在此测试环境中未重现问题，但修复逻辑正确');
} else {
    console.log('⚠️ 意外的测试结果');
}

console.log('\n4. 代码修改总结:');
console.log('修改文件: src/main/resources/static/js/datasource-manager.js');
console.log('修改方法: hideAllConfigSections()');
console.log('修改内容:');
console.log('  修复前: document.querySelectorAll(\'.config-section\')');
console.log('  修复后: document.querySelectorAll(\'#dataSourceModal .config-section\')');
console.log('修复原理: 限制选择器作用范围，只影响数据源模态框内的元素');

console.log('\n=== 验证完成 ===');
