<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D立方体科技感动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <script type="module">
        import * as THREE from 'https://cdn.skypack.dev/three@0.136.0/build/three.module.js';
        import { EffectComposer } from 'https://cdn.skypack.dev/three@0.136.0/examples/jsm/postprocessing/EffectComposer.js';
        import { RenderPass } from 'https://cdn.skypack.dev/three@0.136.0/examples/jsm/postprocessing/RenderPass.js';
        import { UnrealBloomPass } from 'https://cdn.skypack.dev/three@0.136.0/examples/jsm/postprocessing/UnrealBloomPass.js';
        import { ShaderPass } from 'https://cdn.skypack.dev/three@0.136.0/examples/jsm/postprocessing/ShaderPass.js';

        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(10, 10, 10);

        const renderer = new THREE.WebGLRenderer({ alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);
        document.body.appendChild(renderer.domElement);

        let composer;
        composer = new EffectComposer(renderer);
        const renderPass = new RenderPass(scene, camera);
        composer.addPass(renderPass);

        const bloomPass = new UnrealBloomPass(new THREE.Vector2(window.innerWidth, window.innerHeight));
        bloomPass.strength = 0.8;
        bloomPass.threshold = 0.1;
        bloomPass.radius = 1.0;
        composer.addPass(bloomPass);

        const scanLineShader = {
            blending: THREE.AdditiveBlending,
            uniforms: {
                "tDiffuse": { value: null },
                "time": { value: 0.0 },
                "lineHeight": { value: 4.0 },
                "lineSpacing": { value: 2.0 },
                "opacity": { value: 0.1 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D tDiffuse;
                uniform float time;
                uniform float lineHeight;
                uniform float lineSpacing;
                uniform float opacity;
                varying vec2 vUv;

                void main() {
                    vec4 color = texture2D(tDiffuse, vUv);
                    float scanline = step(lineSpacing, mod(gl_FragCoord.y, lineHeight)) * opacity;
                    color.rgb += scanline;
                    gl_FragColor = color;
                }
            `
        };

        const scanLinePass = new ShaderPass(scanLineShader);
        composer.addPass(scanLinePass);

        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0x00bfff, 1);
        directionalLight.position.set(5, 5, 5).normalize();
        scene.add(directionalLight);

        const cubes = [];
        const velocities = [];

        // 科技感颜色系统
        const techColors = [
            0x00bfff, // 深天蓝
            0x00ffff, // 青色
            0x0080ff, // 蓝色
            0x8a2be2, // 蓝紫色
            0x9370db, // 中紫色
            0x00ff7f, // 春绿色
            0x32cd32, // 酸橙绿
            0x1e90ff, // 道奇蓝
            0x4169e1, // 皇家蓝
            0x7b68ee  // 中石板蓝
        ];

        function getTechColor() {
            const baseColor = techColors[Math.floor(Math.random() * techColors.length)];
            const variation = Math.floor(Math.random() * 30) - 15;
            return baseColor + (variation << 16) + (variation << 8) + variation;
        }

        function generateTechCubes(numCubes) {
            // 清理场景
            while (scene.children.length > 0) {
                scene.remove(scene.children[0]);
            }
            scene.add(ambientLight);
            scene.add(directionalLight);
            cubes.length = 0;
            velocities.length = 0;

            for (let i = 0; i < numCubes; i++) {
                const geometry = new THREE.BoxGeometry(
                    Math.random() * 0.5 + 0.3,
                    Math.random() * 0.5 + 0.3,
                    Math.random() * 0.5 + 0.3
                );

                const materials = [
                    new THREE.MeshStandardMaterial({ 
                        color: getTechColor(), 
                        roughness: Math.random() * 0.3 + 0.1, 
                        metalness: Math.random() * 0.8 + 0.2,
                        emissive: new THREE.Color(getTechColor()).multiplyScalar(0.1)
                    }),
                    new THREE.MeshPhongMaterial({ 
                        color: getTechColor(), 
                        shininess: Math.random() * 100 + 50,
                        emissive: new THREE.Color(getTechColor()).multiplyScalar(0.05)
                    })
                ];
                const material = materials[Math.floor(Math.random() * materials.length)];

                const cube = new THREE.Mesh(geometry, material);

                cube.position.x = Math.random() * 10 - 5;
                cube.position.y = Math.random() * 10 - 5;
                cube.position.z = Math.random() * 10 - 5;

                // 降低动态速度
                const velocity = new THREE.Vector3(
                    (Math.random() - 0.5) * 0.3,  // 从0.9降低到0.3
                    (Math.random() - 0.5) * 0.03, // 从0.1降低到0.03
                    (Math.random() - 0.5) * 0.03  // 从0.1降低到0.03
                );

                cube.rotation.x = Math.random() * Math.PI;
                cube.rotation.y = Math.random() * Math.PI;
                cube.rotation.z = Math.random() * Math.PI;

                scene.add(cube);
                cubes.push(cube);
                velocities.push(velocity);
            }
        }

        generateTechCubes(80);

        function animate() {
            requestAnimationFrame(animate);

            for (let i = 0; i < cubes.length; i++) {
                cubes[i].position.add(velocities[i]);

                // 边界反弹
                if (cubes[i].position.x > 5 || cubes[i].position.x < -5) velocities[i].x = -velocities[i].x;
                if (cubes[i].position.y > 5 || cubes[i].position.y < -5) velocities[i].y = -velocities[i].y;
                if (cubes[i].position.z > 5 || cubes[i].position.z < -5) velocities[i].z = -velocities[i].z;

                // 缓慢旋转
                cubes[i].rotation.x += 0.005;
                cubes[i].rotation.y += 0.003;
                cubes[i].rotation.z += 0.002;
            }

            // 相机缓慢旋转
            camera.position.x = Math.cos(Date.now() * 0.0001) * 12;
            camera.position.z = Math.sin(Date.now() * 0.0001) * 12;
            camera.lookAt(scene.position);

            composer.render();
        }

        animate();

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            composer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>
