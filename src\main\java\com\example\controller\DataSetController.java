package com.example.controller;

import com.example.entity.DataSet;
import com.example.service.DataSetService;
import com.example.service.connector.DataSourceConnector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据集管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/dataset")
public class DataSetController {
    
    private final DataSetService dataSetService;
    
    /**
     * 获取所有数据集
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getAllDataSets() {
        try {
            List<DataSet> dataSets = dataSetService.getAllDataSets();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dataSets);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据集列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取数据集列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据ID获取数据集
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getDataSetById(@PathVariable String id) {
        try {
            return dataSetService.getDataSetById(id)
                .map(dataSet -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("data", dataSet);
                    return ResponseEntity.ok(response);
                })
                .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("获取数据集失败: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取数据集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据数据源ID获取数据集
     */
    @GetMapping("/by-datasource/{dataSourceId}")
    public ResponseEntity<Map<String, Object>> getDataSetsByDataSourceId(@PathVariable String dataSourceId) {
        try {
            List<DataSet> dataSets = dataSetService.getDataSetsByDataSourceId(dataSourceId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dataSets);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取数据源的数据集失败: {}", dataSourceId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取数据集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建数据集
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createDataSet(@RequestBody DataSet dataSet) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSet created = dataSetService.createDataSet(dataSet);
            response.put("success", true);
            response.put("message", "数据集创建成功");
            response.put("data", created);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("创建数据集失败", e);
            response.put("success", false);
            response.put("message", "创建数据集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新数据集
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateDataSet(@PathVariable String id, @RequestBody DataSet dataSet) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSet updated = dataSetService.updateDataSet(id, dataSet);
            response.put("success", true);
            response.put("message", "数据集更新成功");
            response.put("data", updated);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("更新数据集失败: {}", id, e);
            response.put("success", false);
            response.put("message", "更新数据集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除数据集
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteDataSet(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            dataSetService.deleteDataSet(id);
            response.put("success", true);
            response.put("message", "数据集删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("删除数据集失败: {}", id, e);
            response.put("success", false);
            response.put("message", "删除数据集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 执行数据集查询
     */
    @PostMapping("/{id}/execute")
    public ResponseEntity<Map<String, Object>> executeDataSet(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSourceConnector.QueryResult result = dataSetService.executeDataSet(id);
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("data", result.getData());
            response.put("rowCount", result.getRowCount());
            response.put("executeTime", result.getExecuteTime());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("执行数据集查询失败: {}", id, e);
            response.put("success", false);
            response.put("message", "执行查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取数据源的表元数据
     */
    @GetMapping("/metadata/{dataSourceId}")
    public ResponseEntity<Map<String, Object>> getTableMetadata(@PathVariable String dataSourceId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            DataSourceConnector.MetadataResult result = dataSetService.getTableMetadata(dataSourceId);
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("metadata", result.getMetadata());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("获取表元数据失败: {}", dataSourceId, e);
            response.put("success", false);
            response.put("message", "获取元数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 预览查询结果
     */
    @PostMapping("/preview/{dataSourceId}")
    public ResponseEntity<Map<String, Object>> previewQuery(
            @PathVariable String dataSourceId,
            @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String queryConfig = (String) request.get("queryConfig");
            Integer limit = (Integer) request.getOrDefault("limit", 10);
            
            List<Map<String, Object>> data = dataSetService.previewQuery(dataSourceId, queryConfig, limit);
            response.put("success", true);
            response.put("data", data);
            response.put("rowCount", data.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("预览查询失败: {}", dataSourceId, e);
            response.put("success", false);
            response.put("message", "预览查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 格式化数据集为图表数据
     */
    @GetMapping("/{id}/chart-data")
    public ResponseEntity<Map<String, Object>> getChartData(
            @PathVariable String id,
            @RequestParam(defaultValue = "bar-chart") String chartType) {
        try {
            Map<String, Object> chartData = dataSetService.formatDataSetForChart(id, chartType);
            return ResponseEntity.ok(chartData);
        } catch (Exception e) {
            log.error("获取图表数据失败: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取图表数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
