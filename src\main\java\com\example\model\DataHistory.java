package com.example.model;

import lombok.Data;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "data_history")
public class DataHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "data_item_id", nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    private DataItem dataItem;

    @Column(name = "data_item_name", nullable = false)
    private String dataItemName;

    @Column(name = "data_item_address", nullable = false)
    private String dataItemAddress;

    @Column(nullable = false)
    private int value;

    @Column(name = "device_id", nullable = false)
    private String deviceId;

    @Column(name = "device_name", nullable = false)
    private String deviceName;

    @Column(nullable = false)
    private LocalDateTime timestamp;
} 