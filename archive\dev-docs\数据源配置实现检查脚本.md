# 数据源配置实现检查脚本

## 概述

此脚本用于检查当前BI大屏系统实际使用的是新的模块化数据源配置还是旧的代码实现。

## 检查方法

### 1. 在浏览器控制台执行检查脚本

打开BI大屏设计页面，按F12打开开发者工具，在控制台中执行以下脚本：

```javascript
console.log('=== BI大屏数据源配置实现检查 ===');

// 1. 检查BiDataSourceManager类和实例
console.log('\n1. BiDataSourceManager基础检查:');
console.log('- BiDataSourceManager类已定义:', typeof BiDataSourceManager !== 'undefined');
console.log('- 全局实例存在:', typeof window.biDataSourceManager !== 'undefined');
console.log('- 实例类型正确:', window.biDataSourceManager instanceof BiDataSourceManager);

// 2. 检查新增的方法是否存在
console.log('\n2. 新增方法可用性检查:');
if (window.biDataSourceManager) {
    const newMethods = [
        'createComponentContext',
        'clearComponentContext', 
        'getComponentSpecificElement',
        'validateDataSourceConfig',
        'repairDataSourceConfig',
        'getDataSourceConfigDefaults',
        'configureComponentDataSource',
        'quickFetchData',
        'batchConfigureComponents'
    ];
    
    newMethods.forEach(method => {
        console.log(`- ${method}:`, typeof window.biDataSourceManager[method] === 'function' ? '✅ 可用' : '❌ 不可用');
    });
} else {
    console.log('❌ BiDataSourceManager实例不存在，无法检查新增方法');
}

// 3. 检查标准化初始化函数
console.log('\n3. 标准化初始化函数检查:');
console.log('- initializeBiDataSourceManager:', typeof initializeBiDataSourceManager === 'function' ? '✅ 可用' : '❌ 不可用');
console.log('- checkBiDataSourceManagerAvailability:', typeof checkBiDataSourceManagerAvailability === 'function' ? '✅ 可用' : '❌ 不可用');

// 4. 检查getMultiDataSourceConfig函数的实现
console.log('\n4. getMultiDataSourceConfig函数实现检查:');
if (typeof getMultiDataSourceConfig === 'function') {
    console.log('- 函数存在: ✅');
    
    // 测试函数调用路径
    const testResult = getMultiDataSourceConfig('pie-chart');
    console.log('- 测试调用结果:', testResult);
    
    // 检查是否使用了BiDataSourceManager
    if (window.biDataSourceManager && window.biDataSourceManager.getMultiDataSourceConfig) {
        const managerResult = window.biDataSourceManager.getMultiDataSourceConfig('pie-chart');
        const isUsingManager = JSON.stringify(testResult) === JSON.stringify(managerResult);
        console.log('- 使用BiDataSourceManager:', isUsingManager ? '✅ 是' : '❌ 否（使用降级配置）');
    }
} else {
    console.log('- 函数存在: ❌');
}

// 5. 检查实际调用情况
console.log('\n5. 实际调用情况检查:');

// 检查配置收集调用
console.log('- 配置收集调用检查:');
if (typeof applyWidgetConfig === 'function') {
    console.log('  applyWidgetConfig函数存在: ✅');
    // 检查函数源码中是否包含BiDataSourceManager调用
    const funcStr = applyWidgetConfig.toString();
    const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('collectDataSourceConfig');
    console.log('  使用BiDataSourceManager.collectDataSourceConfig:', usesBiManager ? '✅ 是' : '❌ 否');
} else {
    console.log('  applyWidgetConfig函数存在: ❌');
}

// 检查配置恢复调用
console.log('- 配置恢复调用检查:');
if (typeof updatePropertyPanel === 'function') {
    console.log('  updatePropertyPanel函数存在: ✅');
    const funcStr = updatePropertyPanel.toString();
    const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('restoreDataSourceConfig');
    console.log('  使用BiDataSourceManager.restoreDataSourceConfig:', usesBiManager ? '✅ 是' : '❌ 否');
} else {
    console.log('  updatePropertyPanel函数存在: ❌');
}

// 检查数据获取调用
console.log('- 数据获取调用检查:');
if (typeof updateWidgetData === 'function') {
    console.log('  updateWidgetData函数存在: ✅');
    const funcStr = updateWidgetData.toString();
    const usesBiManager = funcStr.includes('window.biDataSourceManager') && funcStr.includes('fetchWidgetData');
    console.log('  使用BiDataSourceManager.fetchWidgetData:', usesBiManager ? '✅ 是' : '❌ 否');
} else {
    console.log('  updateWidgetData函数存在: ❌');
}

// 6. 检查组件上下文管理
console.log('\n6. 组件上下文管理检查:');
if (window.biDataSourceManager) {
    console.log('- componentContexts属性:', window.biDataSourceManager.componentContexts ? '✅ 存在' : '❌ 不存在');
    console.log('- validationCache属性:', window.biDataSourceManager.validationCache ? '✅ 存在' : '❌ 不存在');
    
    if (window.biDataSourceManager.componentContexts) {
        console.log('- 当前上下文数量:', window.biDataSourceManager.componentContexts.size);
    }
    
    if (window.biDataSourceManager.validationCache) {
        console.log('- 当前缓存数量:', window.biDataSourceManager.validationCache.size);
    }
}

// 7. 综合评估
console.log('\n7. 综合评估:');
let score = 0;
let total = 0;

// 基础功能评分
if (typeof BiDataSourceManager !== 'undefined') score++; total++;
if (window.biDataSourceManager instanceof BiDataSourceManager) score++; total++;

// 新增方法评分
const newMethods = ['createComponentContext', 'validateDataSourceConfig', 'configureComponentDataSource'];
newMethods.forEach(method => {
    total++;
    if (window.biDataSourceManager && typeof window.biDataSourceManager[method] === 'function') {
        score++;
    }
});

// 标准化函数评分
if (typeof initializeBiDataSourceManager === 'function') score++; total++;

// 实际调用评分
if (typeof applyWidgetConfig === 'function') {
    total++;
    const funcStr = applyWidgetConfig.toString();
    if (funcStr.includes('window.biDataSourceManager') && funcStr.includes('collectDataSourceConfig')) {
        score++;
    }
}

const percentage = Math.round((score / total) * 100);
console.log(`模块化实现完成度: ${score}/${total} (${percentage}%)`);

if (percentage >= 90) {
    console.log('✅ 系统已成功使用新的模块化数据源配置');
} else if (percentage >= 70) {
    console.log('⚠️ 系统部分使用新的模块化配置，可能存在降级情况');
} else {
    console.log('❌ 系统主要使用旧的数据源配置实现');
}

console.log('\n=== 检查完成 ===');
```

### 2. 功能测试脚本

在确认基础检查通过后，可以执行以下功能测试：

```javascript
console.log('=== 功能测试开始 ===');

// 创建测试组件
const testWidget = {
    id: 'test-widget-' + Date.now(),
    type: 'bar-chart',
    widgetType: 'bar-chart'
};

console.log('测试组件:', testWidget);

// 测试1: 组件上下文管理
console.log('\n测试1: 组件上下文管理');
try {
    const context = window.biDataSourceManager.createComponentContext(testWidget);
    console.log('创建上下文:', context ? '✅ 成功' : '❌ 失败');
    
    if (context) {
        const clearResult = window.biDataSourceManager.clearComponentContext(testWidget);
        console.log('清理上下文:', clearResult ? '✅ 成功' : '❌ 失败');
    }
} catch (error) {
    console.log('上下文管理测试失败:', error.message);
}

// 测试2: 配置验证
console.log('\n测试2: 配置验证');
try {
    const testConfig = {
        dataSourceType: 'static',
        staticLabels: '测试标签',
        staticValues: '100'
    };
    
    const validation = window.biDataSourceManager.validateDataSourceConfig(testWidget, testConfig);
    console.log('配置验证:', validation.isValid ? '✅ 通过' : '❌ 失败');
    console.log('验证结果:', validation);
} catch (error) {
    console.log('配置验证测试失败:', error.message);
}

// 测试3: 便捷API
console.log('\n测试3: 便捷API');
try {
    const configResult = window.biDataSourceManager.configureComponentDataSource(testWidget, {
        dataSourceType: 'static',
        staticLabels: '测试标签',
        staticValues: '200',
        autoApply: false
    });
    
    console.log('便捷配置API:', configResult ? '✅ 成功' : '❌ 失败');
} catch (error) {
    console.log('便捷API测试失败:', error.message);
}

console.log('\n=== 功能测试完成 ===');
```

## 预期结果

### 新模块化配置正常工作的标志：
1. **BiDataSourceManager实例存在且功能完整**
2. **所有新增方法都可用**
3. **getMultiDataSourceConfig使用BiDataSourceManager而非降级配置**
4. **主要调用函数都使用BiDataSourceManager的方法**
5. **组件上下文管理功能正常**
6. **模块化实现完成度 ≥ 90%**

### 可能的问题情况：
1. **BiDataSourceManager未正确初始化** - 检查JavaScript文件加载顺序
2. **使用降级配置** - 检查初始化代码是否正确执行
3. **新增方法不可用** - 检查文件是否正确更新
4. **功能测试失败** - 检查具体错误信息进行调试

## 故障排除

如果检查发现问题：

1. **刷新页面重新检查**
2. **检查浏览器控制台是否有JavaScript错误**
3. **确认bi-data-source-manager.js文件已正确加载**
4. **检查页面初始化代码是否正确执行**
5. **验证文件修改是否已生效（检查文件时间戳）**
