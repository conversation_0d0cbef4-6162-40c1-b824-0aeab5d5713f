-- HTML代码管理功能数据库迁移脚本

-- 创建HTML代码分类表
CREATE TABLE IF NOT EXISTS html_code_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description VARCHAR(500) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_html_category_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='HTML代码分类表';

-- 创建HTML代码片段表
CREATE TABLE IF NOT EXISTS html_code_snippets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '代码片段标题',
    description VARCHAR(1000) COMMENT '代码片段描述',
    html_content TEXT NOT NULL COMMENT 'HTML代码内容',
    category_id BIGINT COMMENT '所属分类ID',
    tags VARCHAR(500) COMMENT '标签，用逗号分隔',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    sort_order INT DEFAULT 0 COMMENT '在分类中的排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (category_id) REFERENCES html_code_categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='HTML代码片段表';

-- 插入默认分类数据
INSERT INTO html_code_categories (name, description, sort_order) VALUES
('装饰', '装饰性HTML代码片段，包括各种美化元素', 1),
('边框', '各种边框样式的HTML代码片段', 2),
('按钮', '按钮样式的HTML代码片段', 3);

-- 插入示例HTML代码片段
INSERT INTO html_code_snippets (title, description, html_content, category_id, tags, sort_order) VALUES
('渐变按钮', '带有渐变背景的现代按钮样式', 
'<button style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; transition: transform 0.2s;">点击我</button>', 
(SELECT id FROM html_code_categories WHERE name = '按钮'), 
'按钮,渐变,现代', 1),

('阴影卡片', '带有阴影效果的卡片容器', 
'<div style="background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 20px; margin: 10px; transition: transform 0.2s;"><h3>卡片标题</h3><p>这是卡片内容区域，可以放置任何内容。</p></div>', 
(SELECT id FROM html_code_categories WHERE name = '装饰'), 
'卡片,阴影,容器', 1),

('虚线边框', '简洁的虚线边框样式', 
'<div style="border: 2px dashed #ccc; padding: 15px; border-radius: 4px; background-color: #f9f9f9;"><p>虚线边框内容区域</p></div>', 
(SELECT id FROM html_code_categories WHERE name = '边框'), 
'边框,虚线,简洁', 1);
