package com.example.service;

import com.example.model.DeviceCondition;
import com.example.model.OperationLog;
import com.example.repository.OperationLogRepository;
import com.example.repository.DeviceConditionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class OperationLogService {
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private DeviceConditionRepository deviceConditionRepository;
    
    @Transactional
    public OperationLog logOperation(DeviceCondition condition, Integer sourceValue, 
            Integer targetValue, Boolean success, String message) {
        OperationLog log = new OperationLog();
        log.setCondition(condition);
        log.setSourceValue(sourceValue);
        log.setTargetValue(targetValue);
        log.setSuccess(success);
        log.setMessage(message);
        
        return operationLogRepository.save(log);
    }
    
    @Transactional(readOnly = true)
    public List<OperationLog> getConditionLogs(DeviceCondition condition) {
        return operationLogRepository.findByCondition(condition);
    }
    
    @Transactional(readOnly = true)
    public Page<OperationLog> getConditionLogsPage(DeviceCondition condition, Pageable pageable) {
        return operationLogRepository.findByConditionOrderByCreatedAtDesc(condition, pageable);
    }
    
    @Transactional(readOnly = true)
    public Page<OperationLog> getDeviceLogsPage(String deviceId, LocalDateTime start, LocalDateTime end, Pageable pageable) {
        if (start != null && end != null) {
            return operationLogRepository.findByDeviceIdAndCreatedAtBetweenOrderByCreatedAtDesc(deviceId, start, end, pageable);
        }
        return operationLogRepository.findByDeviceIdOrderByCreatedAtDesc(deviceId, pageable);
    }
    
    @Transactional(readOnly = true)
    public List<OperationLog> getLogsByTimeRange(LocalDateTime start, LocalDateTime end) {
        return operationLogRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(start, end);
    }
} 