<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件状态管理测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">大屏设计器组件状态管理测试</h1>
        
        <div>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testBasicFunctions()">测试基础功能</button>
            <button onclick="testStateManagement()">测试状态管理</button>
            <button onclick="testConfigMerging()">测试配置合并</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="results"></div>
        
        <h3>测试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addResult(testName, success, message) {
            testResults.push({ testName, success, message });
            updateResults();
        }
        
        function updateResults() {
            const resultsElement = document.getElementById('results');
            resultsElement.innerHTML = '';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.success ? 'success' : 'error'}`;
                div.innerHTML = `
                    <strong>${result.testName}</strong>: 
                    ${result.success ? '✅ 通过' : '❌ 失败'} - ${result.message}
                `;
                resultsElement.appendChild(div);
            });
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            testResults = [];
            updateResults();
        }
        
        // 模拟组件状态管理函数（从实际代码中复制）
        function markWidgetAsModified(widget, configPath = null) {
            if (!widget) return;
            
            widget.isModified = true;
            widget.lastModified = new Date().toISOString();
            widget.modifiedPaths = widget.modifiedPaths || new Set();
            
            if (configPath) {
                widget.modifiedPaths.add(configPath);
            }
            
            log(`组件 ${widget.id} (${widget.type}) 已标记为修改: ${configPath || '整体配置'}`);
        }
        
        function ensureWidgetModifiedState(widget) {
            if (!widget) return;
            
            const shouldBeModified = checkIfWidgetShouldBeModified(widget);
            
            if (shouldBeModified && !widget.isModified) {
                log(`修复组件 ${widget.id} 的修改状态：应为已修改但当前为未修改`);
                markWidgetAsModified(widget, 'state_correction');
            }
            
            if (widget.isModified && !widget.modifiedPaths) {
                widget.modifiedPaths = new Set(['config_restored']);
            }
        }
        
        function checkIfWidgetShouldBeModified(widget) {
            if (!widget) return false;
            
            // 检查位置
            if (widget.x !== 0 || widget.y !== 0) {
                return true;
            }
            
            // 检查配置
            if (widget.config && Object.keys(widget.config).length > 0) {
                return true;
            }
            
            // 检查数据源
            if (widget.dataSourceConfig && widget.dataSourceConfig !== '{}') {
                return true;
            }
            
            return false;
        }
        
        function calculateConfigDifference(config1, config2) {
            if (!config1 || !config2) return 1;
            
            const keys1 = Object.keys(config1);
            const keys2 = Object.keys(config2);
            const allKeys = new Set([...keys1, ...keys2]);
            
            let differences = 0;
            let total = allKeys.size;
            
            for (const key of allKeys) {
                if (config1[key] !== config2[key]) {
                    differences++;
                }
            }
            
            return total > 0 ? differences / total : 0;
        }
        
        // 测试函数
        function testBasicFunctions() {
            log('开始测试基础功能...');
            
            try {
                // 测试组件修改状态跟踪
                const testWidget = {
                    id: 999,
                    type: 'text-label',
                    x: 100,
                    y: 100,
                    config: { text: '测试文本', fontSize: 16 },
                    isModified: false,
                    modifiedPaths: new Set()
                };
                
                markWidgetAsModified(testWidget, 'config.text');
                
                if (testWidget.isModified && testWidget.modifiedPaths.has('config.text')) {
                    addResult('组件修改状态跟踪', true, '组件状态正确标记为已修改');
                } else {
                    addResult('组件修改状态跟踪', false, '组件状态标记失败');
                }
                
            } catch (error) {
                addResult('组件修改状态跟踪', false, `错误: ${error.message}`);
            }
        }
        
        function testStateManagement() {
            log('开始测试状态管理...');
            
            try {
                // 测试状态确保功能
                const widget1 = {
                    id: 998,
                    type: 'chart-widget',
                    x: 50,
                    y: 50,
                    config: { title: '测试图表' },
                    isModified: false
                };
                
                ensureWidgetModifiedState(widget1);
                
                if (widget1.isModified) {
                    addResult('状态自动修正', true, '正确识别并修正了组件状态');
                } else {
                    addResult('状态自动修正', false, '未能正确修正组件状态');
                }
                
                // 测试状态检查
                const shouldBeModified = checkIfWidgetShouldBeModified(widget1);
                if (shouldBeModified) {
                    addResult('状态检查逻辑', true, '正确识别应该被修改的组件');
                } else {
                    addResult('状态检查逻辑', false, '状态检查逻辑有误');
                }
                
            } catch (error) {
                addResult('状态管理测试', false, `错误: ${error.message}`);
            }
        }
        
        function testConfigMerging() {
            log('开始测试配置合并...');
            
            try {
                // 测试配置差异检测
                const config1 = {
                    text: '文本1',
                    fontSize: 14,
                    color: '#000000'
                };
                
                const config2 = {
                    text: '文本2',
                    fontSize: 16,
                    color: '#000000'
                };
                
                const difference = calculateConfigDifference(config1, config2);
                
                if (difference > 0 && difference < 1) {
                    addResult('配置差异检测', true, `正确计算配置差异: ${(difference * 100).toFixed(1)}%`);
                } else {
                    addResult('配置差异检测', false, `配置差异计算错误: ${difference}`);
                }
                
                // 测试相同配置
                const sameDifference = calculateConfigDifference(config1, config1);
                if (sameDifference === 0) {
                    addResult('相同配置检测', true, '正确识别相同配置');
                } else {
                    addResult('相同配置检测', false, '相同配置检测失败');
                }
                
            } catch (error) {
                addResult('配置合并测试', false, `错误: ${error.message}`);
            }
        }
        
        function runAllTests() {
            log('=== 开始运行所有测试 ===');
            clearLog();
            
            testBasicFunctions();
            testStateManagement();
            testConfigMerging();
            
            log('=== 所有测试完成 ===');
            
            // 统计结果
            const totalTests = testResults.length;
            const passedTests = testResults.filter(r => r.success).length;
            
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-result ${passedTests === totalTests ? 'success' : 'warning'}`;
            summaryDiv.innerHTML = `
                <strong>测试总结</strong>: ${passedTests}/${totalTests} 个测试通过
                ${passedTests === totalTests ? '🎉 所有测试通过！' : '⚠️ 部分测试失败'}
            `;
            document.getElementById('results').appendChild(summaryDiv);
        }
        
        // 页面加载完成后显示说明
        window.addEventListener('load', function() {
            log('组件状态管理测试页面已加载');
            log('点击"运行所有测试"按钮开始测试');
        });
    </script>
</body>
</html>
