package com.example.service;

import com.example.entity.PublishedTopology;
import com.example.entity.Topology;
import com.example.repository.PublishedTopologyRepository;
import com.example.repository.TopologyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
public class PublishedTopologyService {
    
    @Autowired
    private PublishedTopologyRepository publishedTopologyRepository;
    
    @Autowired
    private TopologyRepository topologyRepository;
    
    @Value("${server.external-url:localhost:8080}")
    private String serverExternalUrl;
    
    /**
     * 发布组态布局
     * 
     * @param topologyId 组态布局ID
     * @param expiryDays 有效期天数，null表示永久有效
     * @return 已发布的组态布局实体
     */
    @Transactional
    public PublishedTopology publishTopology(Long topologyId, Integer expiryDays) {
        log.info("发布组态布局，ID: {}, 有效期: {} 天", topologyId, expiryDays);
        
        Topology topology = topologyRepository.findById(topologyId)
                .orElseThrow(() -> new RuntimeException("未找到指定的组态布局"));
        
        // 生成唯一的访问令牌
        String accessToken = generateAccessToken();
        
        PublishedTopology publishedTopology = new PublishedTopology();
        publishedTopology.setTopology(topology);
        publishedTopology.setName(topology.getName());
        // 不再存储布局数据的副本，只存储布局名称
        publishedTopology.setData(null);
        publishedTopology.setAccessToken(accessToken);
        publishedTopology.setStatus("ACTIVE");
        
        // 设置过期时间
        if (expiryDays != null && expiryDays > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, expiryDays);
            publishedTopology.setExpiryDate(calendar.getTime());
        }
        
        return publishedTopologyRepository.save(publishedTopology);
    }
    
    /**
     * 根据访问令牌获取已发布的组态布局
     * 
     * @param accessToken 访问令牌
     * @return 已发布的组态布局实体，如果不存在或已过期则返回空
     */
    @Transactional(readOnly = true)
    public Optional<PublishedTopology> getPublishedTopologyByToken(String accessToken) {
        log.info("通过令牌获取已发布的组态布局: {}", accessToken);
        
        Optional<PublishedTopology> publishedTopology = publishedTopologyRepository.findByAccessToken(accessToken);
        
        if (publishedTopology.isPresent() && publishedTopology.get().isValid()) {
            return publishedTopology;
        }
        
        return Optional.empty();
    }
    
    /**
     * 获取组态布局的所有发布记录
     * 
     * @param topologyId 组态布局ID
     * @return 发布记录列表
     */
    @Transactional(readOnly = true)
    public List<PublishedTopology> getPublishedTopologiesByTopologyId(Long topologyId) {
        log.info("获取组态布局的所有发布记录，拓扑ID: {}", topologyId);
        return publishedTopologyRepository.findByTopologyId(topologyId);
    }
    
    /**
     * 获取所有有效的发布记录
     *
     * @return 有效的发布记录列表
     */
    @Transactional(readOnly = true)
    public List<PublishedTopology> getAllActivePublishedTopologies() {
        log.info("获取所有有效的发布记录");
        return publishedTopologyRepository.findByStatus("ACTIVE");
    }

    /**
     * 获取所有发布记录（包括所有状态）
     *
     * @return 所有发布记录列表
     */
    @Transactional(readOnly = true)
    public List<PublishedTopology> getAllPublishedTopologies() {
        log.info("获取所有发布记录");
        return publishedTopologyRepository.findAll();
    }
    
    /**
     * 删除发布记录
     *
     * @param publishedId 已发布组态布局ID
     */
    @Transactional
    public void deletePublishedTopology(Long publishedId) {
        log.info("删除发布记录，ID: {}", publishedId);

        PublishedTopology publishedTopology = publishedTopologyRepository.findById(publishedId)
                .orElseThrow(() -> new RuntimeException("未找到指定的发布记录"));

        publishedTopologyRepository.deleteById(publishedId);
        log.info("已删除发布记录，ID: {}", publishedId);
    }
    
    /**
     * 生成访问URL
     * 
     * @param publishedTopology 已发布的组态布局
     * @return 访问URL
     */
    public String generateAccessUrl(PublishedTopology publishedTopology) {
        return "http://" + serverExternalUrl + "/published/" + publishedTopology.getAccessToken();
    }
    
    /**
     * 每天检查并更新过期的发布记录
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨执行
    @Transactional
    public void updateExpiredPublishedTopologies() {
        log.info("检查并更新过期的发布记录");
        
        List<PublishedTopology> expiredTopologies = publishedTopologyRepository.findByExpiryDateBefore(new Date());
        
        for (PublishedTopology topology : expiredTopologies) {
            if ("ACTIVE".equals(topology.getStatus())) {
                topology.setStatus("EXPIRED");
                publishedTopologyRepository.save(topology);
                log.info("已将过期的发布记录标记为过期状态，ID: {}", topology.getId());
            }
        }
    }
    
    /**
     * 生成唯一的访问令牌
     * 
     * @return 访问令牌
     */
    private String generateAccessToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }
} 