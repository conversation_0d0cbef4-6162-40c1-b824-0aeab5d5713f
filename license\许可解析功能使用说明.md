# 许可解析功能使用说明

## 功能概述

序列号生成器现已集成许可文件解析功能，可以读取、解密和分析SDPLC系统的许可文件，为管理员提供详细的许可状态信息。

## 功能特性

### 1. 许可文件读取
- **默认路径**：自动定位到 `C:\ProgramData\SHENGDA-PLC\license.json`
- **自定义路径**：支持浏览选择任意位置的许可文件
- **文件验证**：自动检查文件存在性和可读性

### 2. 信息解析显示
- **基本信息**：用户ID、安装时间、硬件ID
- **许可期限**：开始日期、结束日期、剩余天数
- **状态指示**：有效/过期/即将到期状态
- **硬件绑定**：当前硬件ID与许可硬件ID的匹配验证
- **序列号分析**：解析许可文件中的序列号信息

### 3. 用户体验
- **直观界面**：清晰的对话框布局
- **文件浏览**：便捷的文件选择器
- **结果复制**：一键复制解析结果到剪贴板
- **错误提示**：友好的错误信息和处理建议

## 使用步骤

### 1. 启动许可解析
1. 运行序列号生成器：`java SDPLCSerialGenerator`
2. 点击主界面的 **"🔍 许可解析"** 按钮
3. 许可解析对话框将自动打开

### 2. 选择许可文件
- **使用默认路径**：路径框已预填默认位置，直接点击"解析许可文件"
- **选择自定义文件**：
  1. 点击 **"📁 浏览"** 按钮
  2. 在文件选择器中选择许可文件
  3. 点击"确定"返回

### 3. 解析许可文件
1. 确认文件路径正确
2. 点击 **"🔍 解析许可文件"** 按钮
3. 系统将自动：
   - 读取加密的许可文件
   - 解密文件内容
   - 解析JSON数据
   - 显示详细信息

### 4. 查看解析结果
解析成功后，将显示以下信息：

#### 许可文件信息
- 用户ID
- 安装时间
- 硬件ID

#### 许可使用期限
- 开始日期（格式：YYYY-MM-DD）
- 结束日期（格式：YYYY-MM-DD）
- 剩余天数
- 状态指示：
  - ✅ 有效：许可在有效期内
  - ⚠️ 今天到期：许可今日到期
  - ❌ 已过期：许可已过期（显示过期天数）

#### 硬件绑定验证
- 当前硬件ID
- 许可硬件ID
- 匹配状态：
  - ✅ 匹配：硬件绑定正确
  - ❌ 不匹配：硬件绑定失败

#### 序列号信息
- 完整序列号
- 序列号用户ID
- 激活时间窗口
- 序列号许可期限（新格式）

### 5. 复制结果
1. 解析完成后，**"📋 复制结果"** 按钮将变为可用
2. 点击按钮将完整的解析结果复制到剪贴板
3. 可粘贴到文本编辑器或邮件中

## 错误处理

### 常见错误及解决方案

1. **"许可文件不存在"**
   - 检查文件路径是否正确
   - 确认许可文件确实存在于指定位置

2. **"解析失败: 解密错误"**
   - 文件可能已损坏
   - 文件格式不正确
   - 加密密钥不匹配

3. **"硬件绑定不匹配"**
   - 许可文件绑定到其他计算机
   - 需要重新激活或更换许可

4. **"许可已过期"**
   - 许可使用期限已到
   - 需要续期或重新激活

## 技术说明

### 支持的文件格式
- **文件类型**：JSON格式的加密文件
- **加密算法**：AES/ECB/PKCS5Padding
- **编码方式**：Base64
- **兼容性**：支持新旧两种许可文件格式

### 安全特性
- **加密存储**：许可文件采用AES加密存储
- **硬件绑定**：基于系统属性生成硬件ID
- **完整性验证**：校验码确保数据完整性

## 故障排除

### 程序无法启动
1. 确认Java环境已正确安装
2. 检查文件权限
3. 使用命令行查看详细错误信息

### 解析结果异常
1. 检查许可文件是否为最新版本
2. 确认文件未被第三方软件修改
3. 尝试重新激活生成新的许可文件

### 界面显示问题
1. 确认系统支持中文字符显示
2. 检查字体设置
3. 尝试调整系统DPI设置

## 联系支持

如遇到其他问题，请联系技术支持并提供：
- 错误信息截图
- 许可文件路径
- 系统环境信息
- 操作步骤描述

---

**版本**：v2.0  
**更新日期**：2025-08-01  
**适用系统**：Windows 7/8/10/11
