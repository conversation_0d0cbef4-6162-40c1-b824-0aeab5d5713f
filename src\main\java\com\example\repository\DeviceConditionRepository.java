package com.example.repository;

import com.example.model.Device;
import com.example.model.DeviceCondition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeviceConditionRepository extends JpaRepository<DeviceCondition, Long> {
    List<DeviceCondition> findByDevice(Device device);
    
    List<DeviceCondition> findByDeviceAndEnabled(Device device, Boolean enabled);
    
    @Query("SELECT DISTINCT dc.device FROM DeviceCondition dc")
    List<Device> findDistinctDevices();
} 