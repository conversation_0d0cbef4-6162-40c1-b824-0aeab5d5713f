@echo off
echo 正在启动SHENGDA-PLC应用程序...
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 8并配置了JAVA_HOME
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven并配置了PATH
    pause
    exit /b 1
)

echo Java和Maven环境检查通过
echo.

REM 清理并运行应用
echo 正在清理项目...
mvn clean >nul 2>&1

echo 正在启动应用程序...
echo 应用启动后可以通过以下地址访问:
echo   - 主页: http://localhost:8080
echo   - 许可证激活: http://localhost:8080/license/activation
echo   - 许可证测试: http://localhost:8080/license/test
echo.
echo 按 Ctrl+C 停止应用程序
echo.

mvn spring-boot:run
