<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备状态</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --card-shadow: 0 2px 8px rgba(0,0,0,0.1);
            --hover-shadow: 0 4px 12px rgba(0,0,0,0.15);
            --border-radius: 0.375rem;
            --transition: all 0.3s ease;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .container-fluid {
            padding: 2rem;
        }

        h2 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 2rem;
            text-align: center;
        }

        .device-card {
            margin-bottom: 2rem;
            cursor: move;
            border: none;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            overflow: hidden;
            background: white;
        }

        .device-card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-4px);
        }

        .device-card.dragging {
            opacity: 0.7;
            border: 2px dashed #667eea;
            transform: rotate(2deg);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            padding: 1.5rem;
            border-bottom: none;
        }

        .device-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0;
        }

        .device-info {
            display: flex;
            align-items: center;
        }

        .device-status {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 12px;
            border: 2px solid white;
            position: relative;
        }

        .device-status.connected {
            background-color: #28a745;
            box-shadow: 0 0 12px rgba(40, 167, 69, 0.6);
            animation: pulse-green 2s infinite;
        }

        .device-status.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 12px rgba(220, 53, 69, 0.6);
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 12px rgba(40, 167, 69, 0.6); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.9); }
        }

        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 12px rgba(220, 53, 69, 0.6); }
            50% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.9); }
        }

        .card-title {
            margin: 0;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .connection-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            backdrop-filter: blur(4px);
        }

        .card-body {
            padding: 0;
        }

        .table {
            margin: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            font-weight: 600;
            padding: 1rem;
            border: none;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 1rem;
            border: none;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: var(--transition);
        }

        .badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .badge:hover::before {
            left: 100%;
        }

        .badge-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .badge-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        .badge-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .badge-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 1rem;
            }

            .device-card {
                margin-bottom: 1rem;
            }

            .card-header {
                padding: 1rem;
            }

            .table th,
            .table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .device-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }

        /* 加载动画 */
        .device-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h2 class="mb-4">设备状态概览</h2>
        <div id="deviceStatusContainer">
            <!-- 设备状态卡片将动态添加到这里 -->
        </div>
    </div>

    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script>
        // 添加拖拽相关函数
        function initDragAndDrop() {
            const container = document.getElementById('deviceStatusContainer');
            
            // 获取所有设备卡片
            const cards = container.getElementsByClassName('device-card');
            
            // 为每个卡片添加拖拽事件监听器
            Array.from(cards).forEach(card => {
                card.setAttribute('draggable', 'true');
                
                card.addEventListener('dragstart', (e) => {
                    e.target.classList.add('dragging');
                    e.dataTransfer.setData('text/plain', e.target.id);
                });
                
                card.addEventListener('dragend', (e) => {
                    e.target.classList.remove('dragging');
                });
                
                card.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    const draggingCard = container.querySelector('.dragging');
                    const cards = [...container.getElementsByClassName('device-card')];
                    const afterCard = getDragAfterElement(container, e.clientY);
                    
                    if (afterCard == null) {
                        container.appendChild(draggingCard);
                    } else {
                        container.insertBefore(draggingCard, afterCard);
                    }
                });
            });
        }
        
        // 获取拖动后的位置
        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.device-card:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }
        
        // 获取所有设备的预警状态
        async function loadDeviceStatuses() {
            try {
                const response = await fetch('/api/devices/connected');
                const devices = await response.json();
                
                const container = document.getElementById('deviceStatusContainer');
                
                for (const device of devices) {
                    // 获取设备的预警配置
                    const alertsResponse = await fetch(`/api/alerts/device/${device.id}`);
                    const alerts = await alertsResponse.json();
                    
                    // 检查设备卡片是否已存在
                    let deviceCard = document.querySelector(`#device-${device.id}`);
                    if (!deviceCard) {
                        // 如果设备卡片不存在，创建新的卡片
                        deviceCard = document.createElement('div');
                        deviceCard.id = `device-${device.id}`;
                        deviceCard.className = 'card device-card';
                        container.appendChild(deviceCard);
                    }
                    
                    // 更新设备连接状态
                    const existingHeader = deviceCard.querySelector('.device-header');
                    if (!existingHeader || existingHeader.dataset.connected !== String(device.connected)) {
                        deviceCard.innerHTML = `
                            <div class="card-header">
                                <div class="device-header" data-connected="${device.connected}">
                                    <div class="device-info">
                                        <span class="device-status ${device.connected ? 'connected' : 'disconnected'}"></span>
                                        <h5 class="card-title">${device.name}</h5>
                                    </div>
                                    <span class="connection-badge">${device.connected ? '已连接' : '未连接'}</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>预警名称</th>
                                            <th>数据项</th>
                                            <th>实时数值</th>
                                            <th>正常范围</th>
                                            <th>预警范围</th>
                                            <th>异常范围</th>
                                            <th>当前状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="alert-tbody-${device.id}">
                                    </tbody>
                                </table>
                            </div>
                        `;
                    }
                    
                    // 更新预警表格内容
                    const tbody = deviceCard.querySelector(`#alert-tbody-${device.id}`);
                    const existingAlerts = new Set(Array.from(tbody.children).map(row => row.dataset.alertId));
                    const newAlerts = new Set(alerts.map(alert => alert.id));
                    
                    // 移除不再存在的预警行
                    for (const alertId of existingAlerts) {
                        if (!newAlerts.has(alertId)) {
                            tbody.querySelector(`tr[data-alert-id="${alertId}"]`)?.remove();
                        }
                    }
                    
                    // 添加或更新预警行
                    for (const alert of alerts) {
                        let alertRow = tbody.querySelector(`tr[data-alert-id="${alert.id}"]`);
                        if (!alertRow) {
                            // 如果预警行不存在，创建新行
                            alertRow = document.createElement('tr');
                            alertRow.dataset.alertId = alert.id;
                            tbody.appendChild(alertRow);
                        }
                        
                        // 只更新静态内容（不包括实时值和状态）
                        alertRow.innerHTML = `
                            <td>${alert.name}</td>
                            <td>${alert.dataItem.name} (${alert.dataItem.address})</td>
                            <td class="value-${alert.id}">-</td>
                            <td>${formatRange(alert.normalMin, alert.normalMax)}</td>
                            <td>${formatRange(alert.warningMin, alert.warningMax)}</td>
                            <td>${formatRange(alert.dangerMin, alert.dangerMax)}</td>
                            <td class="status-${alert.id}">
                                <span class="badge badge-secondary">未知</span>
                            </td>
                        `;
                    }
                    
                    // 更新实时值和状态
                    for (const alert of alerts) {
                        updateAlertStatus(alert.id);
                    }
                }
                
                // 移除不再存在的设备卡片
                const existingDeviceIds = new Set(Array.from(container.children).map(card => card.id.replace('device-', '')));
                const currentDeviceIds = new Set(devices.map(device => device.id));
                for (const deviceId of existingDeviceIds) {
                    if (!currentDeviceIds.has(deviceId)) {
                        document.querySelector(`#device-${deviceId}`)?.remove();
                    }
                }
                
                // 在所有设备卡片更新完成后初始化拖拽功能
                initDragAndDrop();
            } catch (error) {
                console.error('加载设备状态失败:', error);
            }
        }
        
        // 格式化范围显示
        function formatRange(min, max) {
            if (min !== null && max !== null) {
                return `${min} - ${max}`;
            } else if (min !== null) {
                return `≥ ${min}`;
            } else if (max !== null) {
                return `≤ ${max}`;
            } else {
                return '-';
            }
        }
        
        // 获取状态徽章
        function getStatusBadge(status) {
            switch (status) {
                case 1:
                    return '<span class="badge badge-success">正常</span>';
                case 2:
                    return '<span class="badge badge-warning">预警</span>';
                case 3:
                    return '<span class="badge badge-danger">异常</span>';
                default:
                    return '<span class="badge badge-secondary">未知</span>';
            }
        }
        
        // 更新预警状态
        async function updateAlertStatus(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/status`);
                const data = await response.json();
                
                const valueCell = document.querySelector(`.value-${alertId}`);
                const statusCell = document.querySelector(`.status-${alertId}`);
                
                if (valueCell && statusCell) {
                    // 只在值发生变化时更新DOM
                    if (valueCell.textContent !== String(data.currentValue)) {
                        valueCell.textContent = data.currentValue !== null ? data.currentValue : '-';
                    }
                    
                    const newStatusHtml = getStatusBadge(data.status);
                    if (statusCell.innerHTML !== newStatusHtml) {
                        statusCell.innerHTML = newStatusHtml;
                    }
                }
                
                // 3秒后再次更新
                setTimeout(() => updateAlertStatus(alertId), 3000);
            } catch (error) {
                console.error('更新预警状态失败:', error);
                // 发生错误时，10秒后重试
                setTimeout(() => updateAlertStatus(alertId), 10000);
            }
        }
        
        // 页面加载完成后开始加载设备状态
        document.addEventListener('DOMContentLoaded', () => {
            loadDeviceStatuses();
            // 每10秒检查一次设备和预警配置的变化
            setInterval(loadDeviceStatuses, 10000);
        });
    </script>
</body>
</html> 