Write-Host "=== 基础API测试 ===" -ForegroundColor Green

Write-Host "测试应用连接..." -ForegroundColor Yellow
$response = Invoke-WebRequest -Uri 'http://localhost:8080' -Method Get
Write-Host "应用状态: $($response.StatusCode)" -ForegroundColor Green

Write-Host "测试大屏API..." -ForegroundColor Yellow
$dashboards = Invoke-RestMethod -Uri 'http://localhost:8080/api/bi/dashboards' -Method Get
Write-Host "大屏数量: $($dashboards.data.Count)" -ForegroundColor Cyan

if ($dashboards.data.Count -gt 0) {
    $dashboard = $dashboards.data[0]
    Write-Host "测试组件API..." -ForegroundColor Yellow
    $widgets = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$($dashboard.id)/widgets" -Method Get
    Write-Host "组件数量: $($widgets.data.Count)" -ForegroundColor Cyan
    
    if ($widgets.data.Count -gt 0) {
        $widget = $widgets.data[0]
        Write-Host "组件类型: $($widget.widgetType)" -ForegroundColor Cyan
        if ($widget.config -and $widget.config.Contains('_stateInfo')) {
            Write-Host "✅ 发现状态信息" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 未发现状态信息" -ForegroundColor Yellow
        }
    }
}

Write-Host "测试完成" -ForegroundColor Green
