# 外部数据源API完整分析报告

## API概览

外部数据源系统提供了完整的数据集管理和数据获取API，支持BI组件的各种数据需求。

## 1. 数据集列表API

### 🔗 API接口
```
GET /api/bi/datasets
```

### 📋 功能说明
获取所有可用的数据集列表，用于BI组件的数据源选择。

### 📤 响应格式
```javascript
{
    "success": true,
    "data": [
        {
            "id": "dataset_1752559477408",        // 数据集ID
            "name": "测试1",                      // 数据集名称 ⭐
            "description": "测试数据集描述",       // 数据集描述
            "dataSourceName": "MySQL数据源",      // 数据源名称
            "createdAt": "2024-01-01T10:00:00"   // 创建时间
        },
        {
            "id": "dataset_1752559477409",
            "name": "销售数据",
            "description": "销售业绩数据",
            "dataSourceName": "PostgreSQL数据源",
            "createdAt": "2024-01-02T11:00:00"
        }
    ]
}
```

### 🎯 关键字段说明
- **`id`**: 数据集唯一标识符，用于后续数据获取
- **`name`**: 数据集名称，**这是多折线图系列名称的来源** ⭐
- **`description`**: 数据集描述信息
- **`dataSourceName`**: 所属数据源名称

## 2. 数据集字段信息API

### 🔗 API接口
```
GET /api/bi/dataset/{id}/fields
```

### 📋 功能说明
获取指定数据集的字段信息，用于字段选择和自动配置。

### 📤 响应格式
```javascript
{
    "success": true,
    "fields": [
        {
            "name": "timestamp",              // 字段名称 ⭐
            "displayName": "时间戳",          // 显示名称
            "type": "datetime",               // 字段类型
            "sample": "2024-01-01 10:00:00"  // 示例值
        },
        {
            "name": "value",                  // 字段名称 ⭐
            "displayName": "数值",            // 显示名称
            "type": "number",                 // 字段类型
            "sample": "100.5"                 // 示例值
        },
        {
            "name": "category",
            "displayName": "分类",
            "type": "text",
            "sample": "类别A"
        }
    ]
}
```

### 🎯 关键字段说明
- **`name`**: 字段名称，用于数据查询参数
- **`type`**: 字段类型，用于自动字段选择
- **`sample`**: 示例值，帮助用户理解字段内容

## 3. 数据集数据获取API

### 🔗 API接口
```
GET /api/bi/dataset/{id}/data?labelField={labelField}&valueField={valueField}
```

### 📋 功能说明
获取指定数据集的实际数据，支持字段选择，用于图表渲染。

### 📥 请求参数
- **`id`**: 数据集ID (路径参数)
- **`labelField`**: X轴字段名 (查询参数) ⭐
- **`valueField`**: Y轴字段名 (查询参数) ⭐

### 📤 响应格式
```javascript
{
    "success": true,
    "data": {
        "labels": [                           // X轴标签数据 ⭐
            "2024-01-01 10:00:00",
            "2024-01-01 11:00:00",
            "2024-01-01 12:00:00",
            "2024-01-01 13:00:00",
            "2024-01-01 14:00:00"
        ],
        "values": [                           // Y轴数值数据 ⭐
            100.5,
            120.3,
            95.8,
            110.2,
            105.7
        ]
    }
}
```

### 🎯 关键字段说明
- **`labels`**: X轴标签数组，**这是多折线图X轴数据的来源** ⭐
- **`values`**: Y轴数值数组，**这是多折线图折线数据的来源** ⭐

### 🔄 数据转换逻辑
**Java后端实现** (`BiDashboardController.java:690-740`):
```java
private Map<String, Object> convertWithSpecifiedFields(
    List<Map<String, Object>> rawData, 
    String labelField, 
    String valueField) {
    
    Map<String, Object> result = new HashMap<>();
    List<String> labels = new ArrayList<>();
    List<Number> values = new ArrayList<>();

    for (Map<String, Object> row : rawData) {
        Object labelObj = row.get(labelField);    // 获取标签字段值
        Object valueObj = row.get(valueField);    // 获取数值字段值

        String label = labelObj != null ? labelObj.toString() : "";
        Number value = convertToNumber(valueObj); // 转换为数值

        labels.add(label);
        values.add(value);
    }

    result.put("labels", labels);  // X轴数据
    result.put("values", values);  // Y轴数据
    return result;
}
```

## 4. 批量数据集API (多数据集模式)

### 🔗 API接口
```
POST /api/bi/datasets/batch
```

### 📋 功能说明
批量获取多个数据集的数据，用于多数据集模式的图表渲染。

### 📥 请求格式
```javascript
{
    "dataSets": [
        {
            "dataSetId": "dataset_1752559477408",
            "alias": "数据集1",                    // 数据集别名 ⭐
            "labelField": "timestamp",
            "valueField": "value"
        },
        {
            "dataSetId": "dataset_1752559477409",
            "alias": "数据集2",                    // 数据集别名 ⭐
            "labelField": "time",
            "valueField": "amount"
        }
    ],
    "mergeStrategy": "union",                      // 合并策略
    "componentType": "multi-line-chart"            // 组件类型
}
```

### 📤 响应格式
```javascript
{
    "success": true,
    "data": {
        "labels": ["2024-01-01", "2024-01-02", "2024-01-03"],  // 合并后的X轴
        "series": [                                             // 多系列数据 ⭐
            {
                "name": "数据集1",                              // 系列名称 = 别名
                "data": [100, 120, 95],
                "alias": "数据集1"
            },
            {
                "name": "数据集2",                              // 系列名称 = 别名
                "data": [80, 90, 110],
                "alias": "数据集2"
            }
        ]
    },
    "message": "成功处理 2 个数据集",
    "warnings": []                                              // 警告信息
}
```

## 5. 数据对应关系总结

### 多折线图数据映射

| 图表元素 | 数据来源 | API字段 | 说明 |
|---------|---------|---------|------|
| **折线名称** | 数据集名称/别名 | `name` / `alias` | 显示在图例中的系列名称 ⭐ |
| **X轴标签** | 标签字段数据 | `data.labels` | 时间轴或分类轴数据 ⭐ |
| **Y轴数值** | 数值字段数据 | `data.values` | 折线的实际数据点 ⭐ |
| **图例显示** | 系列名称集合 | `series[].name` | 图例中显示的名称列表 |

### 单数据集模式流程
```
1. 用户选择数据集 → 获取数据集名称 (name)
2. 自动选择字段 → 确定 labelField 和 valueField
3. 调用数据API → GET /api/bi/dataset/{id}/data?labelField=xxx&valueField=yyy
4. 获取数据 → {labels: [...], values: [...]}
5. 格式化为多折线 → {xAxis: labels, series: [{name: 数据集名称, data: values}]}
```

### 多数据集模式流程
```
1. 用户添加多个数据集 → 每个数据集有独立的别名 (alias)
2. 批量调用API → POST /api/bi/datasets/batch
3. 后端合并数据 → 生成统一的X轴和多个系列
4. 返回多系列格式 → {labels: [...], series: [{name: 别名1, data: [...]}, {name: 别名2, data: [...]}]}
```

## 6. 关键配置参数

### 前端数据源配置
```javascript
{
    dataSetId: "dataset_1752559477408",    // 数据集ID
    dataSetName: "测试1",                  // 数据集名称 (来自选择器)
    labelField: "timestamp",               // X轴字段
    valueField: "value",                   // Y轴字段
    alias: "数据集1"                       // 别名 (多数据集模式)
}
```

### 字段自动选择逻辑
- **时间字段优先**: `timestamp`, `time`, `date`, `created_at`
- **数值字段优先**: `value`, `amount`, `count`, `total`
- **类型匹配**: `datetime` → 标签字段, `number` → 数值字段

## 7. 调试检查要点

### API调用检查
```javascript
// 1. 检查数据集列表
fetch('/api/bi/datasets').then(r => r.json()).then(console.log);

// 2. 检查字段信息
fetch('/api/bi/dataset/{id}/fields').then(r => r.json()).then(console.log);

// 3. 检查数据获取
fetch('/api/bi/dataset/{id}/data?labelField=timestamp&valueField=value')
  .then(r => r.json()).then(console.log);
```

### 数据格式验证
```javascript
// 验证响应格式
console.log('数据集名称:', response.data[0].name);        // 折线名称来源
console.log('X轴数据:', response.data.labels);           // X轴标签
console.log('Y轴数据:', response.data.values);           // Y轴数值
console.log('数据长度:', response.data.labels.length);   // 数据点数量
```

这个完整的API分析应该能帮助您准确理解外部数据源的数据流和格式要求。
