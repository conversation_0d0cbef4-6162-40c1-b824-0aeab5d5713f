<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - 设备管理</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .device-card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease-in-out;
            overflow: hidden;
        }
        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .device-card.has-image {
            border-radius: 16px;
        }
        .device-image-container {
            position: relative;
            height: 180px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            margin: 0;
        }
        .device-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }
        .image-upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }
        .device-image-container:hover .image-upload-overlay {
            opacity: 1;
        }
        .upload-icon {
            color: white;
            font-size: 2rem;
        }
        .no-image-placeholder {
            color: #6c757d;
            font-size: 1.2rem;
            text-align: center;
        }
        .image-upload-btn {
            /* 移除绝对定位，使用正常布局 */
        }
        .device-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 0.375rem 0.375rem 0 0;
        }
        .device-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            border: 2px solid white;
        }
        .device-status.connected {
            background-color: #28a745;
            box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
        }
        .device-status.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 8px rgba(220, 53, 69, 0.6);
        }
        .data-item-table {
            margin-bottom: 0;
        }
        .data-item-table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        .data-item-table td {
            vertical-align: middle;
        }
        .value-display {
            font-weight: bold;
            color: #007bff;
            font-family: 'Courier New', monospace;
        }
        .btn-edit {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .alert-custom {
            border: none;
            border-radius: 0.5rem;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }
        .modal-header .btn-close {
            filter: invert(1);
        }
        .image-selection-item {
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
            padding: 8px;
            margin-bottom: 15px;
        }
        .image-selection-item:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }
        .image-selection-item.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .image-selection-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }
        .image-selection-item .image-info {
            padding: 8px 4px 4px;
            text-align: center;
        }
        .image-selection-item .image-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .image-selection-item .image-size {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* 卡片大小切换样式 */
        .card-size-controls .btn {
            transition: all 0.3s ease;
        }

        .card-size-controls .btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }

        /* 不同卡片尺寸的优化 */
        .device-card.card-size-small .device-header {
            padding: 12px 15px;
        }

        .device-card.card-size-small .device-header h5 {
            font-size: 1rem;
        }

        .device-card.card-size-small .device-header small {
            font-size: 0.75rem;
        }

        .device-card.card-size-small .device-image-container {
            height: 140px;
        }

        .device-card.card-size-large .device-header {
            padding: 20px 25px;
        }

        .device-card.card-size-large .device-header h5 {
            font-size: 1.3rem;
        }

        .device-card.card-size-large .device-image-container {
            height: 220px;
        }

        /* 地址列显示/隐藏控制 */
        .data-item-table {
            table-layout: fixed;
            width: 100%;
        }

        .data-item-table th:nth-child(1) { /* 监控项名称 */
            width: 40%;
        }

        .data-item-table th:nth-child(2) { /* 地址 */
            width: 25%;
        }

        .data-item-table th:nth-child(3) { /* 当前值 */
            width: 15%;
        }

        .data-item-table th:nth-child(4) { /* 操作 */
            width: 20%;
        }

        /* 隐藏地址列时的布局调整 */
        .address-hidden .data-item-table th:nth-child(1) { /* 监控项名称 */
            width: 50%;
        }

        .address-hidden .data-item-table th:nth-child(3) { /* 当前值 */
            width: 25%;
        }

        .address-hidden .data-item-table th:nth-child(4) { /* 操作 */
            width: 25%;
        }

        .address-column {
            transition: all 0.3s ease;
        }

        .address-hidden .address-column {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航条 -->
    <div th:replace="~{fragments/navbar :: navbar('胜大科技智联管理系统 - 设备管理', 'deviceManagement', true, true, true, null)}"></div>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">
                        <i class="bi bi-cpu me-2"></i>设备管理
                    </h2>
                    <div class="d-flex align-items-center">
                        <!-- 卡片大小切换按钮组 -->
                        <div class="btn-group card-size-controls me-3" role="group" aria-label="卡片大小切换">
                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                    id="cardSizeSmall" onclick="changeCardSize('small')"
                                    title="小卡片 (4列)">
                                <i class="bi bi-grid-3x3-gap"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm active"
                                    id="cardSizeMedium" onclick="changeCardSize('medium')"
                                    title="中等卡片 (3列)">
                                <i class="bi bi-grid"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                    id="cardSizeLarge" onclick="changeCardSize('large')"
                                    title="大卡片 (2列)">
                                <i class="bi bi-grid-1x2"></i>
                            </button>
                        </div>
                        <span class="text-muted me-3">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            自动刷新: <span id="refreshInterval">3</span>秒
                        </span>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshAllData()">
                            <i class="bi bi-arrow-clockwise"></i> 立即刷新
                        </button>
                    </div>
                </div>
                
                <!-- 加载提示 -->
                <div id="loadingAlert" class="alert alert-info alert-custom d-flex align-items-center">
                    <div class="loading-spinner me-2"></div>
                    正在加载设备数据...
                </div>
                
                <!-- 错误提示 -->
                <div id="errorAlert" class="alert alert-danger alert-custom d-none">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="errorMessage"></span>
                </div>
                
                <!-- 设备列表容器 -->
                <div id="deviceContainer" class="row">
                    <!-- 设备卡片将动态添加到这里 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片查看模态框 -->
    <div class="modal fade" id="imageViewModal" tabindex="-1" aria-labelledby="imageViewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageViewModalLabel">
                        <i class="bi bi-eye me-2"></i>查看设备图片
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-0">
                    <img id="viewImageElement" src="" alt="" class="img-fluid" style="max-height: 70vh; object-fit: contain;">
                </div>
                <div class="modal-footer">
                    <div class="me-auto">
                        <small class="text-muted" id="imageViewInfo"></small>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片选择模态框 -->
    <div class="modal fade" id="imageSelectionModal" tabindex="-1" aria-labelledby="imageSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageSelectionModalLabel">
                        <i class="bi bi-image me-2"></i>选择设备图片
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">从文件管理中选择图片</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="triggerNewImageUpload()">
                                    <i class="bi bi-upload me-1"></i>上传新图片
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="imageListContainer" class="row">
                        <div class="col-12 text-center">
                            <div class="loading-spinner me-2"></div>
                            正在加载图片列表...
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="removeImageBtn" onclick="removeDeviceImage()" style="display: none;">
                        <i class="bi bi-trash me-1"></i>移除图片
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据修改模态框 -->
    <div class="modal fade" id="editValueModal" tabindex="-1" aria-labelledby="editValueModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editValueModalLabel">
                        <i class="bi bi-pencil-square me-2"></i>修改数据项值
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">设备名称:</label>
                        <span id="modalDeviceName" class="fw-bold"></span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数据项名称:</label>
                        <span id="modalDataItemName" class="fw-bold"></span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数据项地址:</label>
                        <span id="modalDataItemAddress" class="fw-bold text-muted"></span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">当前值:</label>
                        <span id="modalCurrentValue" class="fw-bold text-primary"></span>
                    </div>
                    <div class="mb-3">
                        <label for="newValue" class="form-label">新值:</label>
                        <input type="number" class="form-control" id="newValue" placeholder="请输入新的数值">
                        <div class="form-text">请输入-32768到32767之间的整数</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmWrite()">
                        <i class="bi bi-check-lg me-1"></i>确认写入
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script>
        // 全局变量
        let devices = [];
        let refreshTimer = null;
        let currentEditItem = null;
        let currentDeviceId = null;
        let selectedImageUrl = null;
        let currentCardSize = 'medium'; // 默认中等尺寸
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCardSize();
            initializeAddressDisplay();
            loadDevicesData();
            startAutoRefresh();
        });

        // 初始化地址显示设置
        function initializeAddressDisplay() {
            // 全局初始化已完成，具体设备的状态在渲染时处理
        }

        // 获取设备的地址显示状态
        function getDeviceAddressDisplayState(deviceId) {
            const key = `hideDataItemAddress_${deviceId}`;
            const stored = localStorage.getItem(key);
            // 默认隐藏地址
            return stored !== null ? stored === 'true' : true;
        }

        // 设置设备的地址显示状态
        function setDeviceAddressDisplayState(deviceId, isHidden) {
            const key = `hideDataItemAddress_${deviceId}`;
            localStorage.setItem(key, isHidden.toString());
        }

        // 更新所有设备卡片的地址切换按钮状态
        function updateAllAddressToggleButtons() {
            // 遍历所有设备，为每个设备设置正确的按钮状态
            devices.forEach(device => {
                updateDeviceAddressToggleButton(device.id);
            });
        }

        // 更新指定设备的地址切换按钮状态
        function updateDeviceAddressToggleButton(deviceId) {
            const isAddressHidden = getDeviceAddressDisplayState(deviceId);

            const toggleButton = document.getElementById(`addressToggleBtn-${deviceId}`);
            const toggleIcon = document.getElementById(`addressToggleIcon-${deviceId}`);

            if (toggleButton) {
                if (isAddressHidden) {
                    toggleButton.title = '显示监控项地址';
                } else {
                    toggleButton.title = '隐藏监控项地址';
                }
            }

            if (toggleIcon) {
                if (isAddressHidden) {
                    // 隐藏状态：显示眼睛图标
                    toggleIcon.className = 'bi bi-eye';
                } else {
                    // 显示状态：显示眼睛斜杠图标
                    toggleIcon.className = 'bi bi-eye-slash';
                }
            }
        }

        // 初始化卡片大小
        function initializeCardSize() {
            // 从localStorage读取用户偏好
            const savedSize = localStorage.getItem('deviceCardSize');
            if (savedSize && ['small', 'medium', 'large'].includes(savedSize)) {
                currentCardSize = savedSize;
            }

            // 更新按钮状态
            updateCardSizeButtons();
        }

        // 获取卡片大小对应的CSS类
        function getCardSizeClass(size) {
            switch (size) {
                case 'small':
                    return 'col-lg-4 col-xl-3 col-md-6';
                case 'large':
                    return 'col-lg-12 col-xl-6 col-md-12';
                case 'medium':
                default:
                    return 'col-lg-6 col-xl-4 col-md-6';
            }
        }

        // 切换卡片大小
        function changeCardSize(size) {
            if (currentCardSize === size) return;

            currentCardSize = size;

            // 保存用户偏好
            localStorage.setItem('deviceCardSize', size);

            // 更新按钮状态
            updateCardSizeButtons();

            // 重新渲染设备卡片
            updateAllCardSizes();
        }

        // 更新卡片大小按钮状态
        function updateCardSizeButtons() {
            // 移除所有active类
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加当前选中的active类
            const activeButton = document.getElementById(`cardSize${currentCardSize.charAt(0).toUpperCase() + currentCardSize.slice(1)}`);
            if (activeButton) {
                activeButton.classList.add('active');
            }
        }

        // 更新所有卡片的大小
        function updateAllCardSizes() {
            const deviceCards = document.querySelectorAll('[id^="device-"]');
            deviceCards.forEach(card => {
                // 更新容器的CSS类
                card.className = getCardSizeClass(currentCardSize);

                // 更新卡片内容的CSS类
                const deviceCardElement = card.querySelector('.device-card');
                if (deviceCardElement) {
                    // 移除旧的尺寸类
                    deviceCardElement.classList.remove('card-size-small', 'card-size-medium', 'card-size-large');
                    // 添加新的尺寸类
                    deviceCardElement.classList.add(`card-size-${currentCardSize}`);
                }
            });
        }

        // 加载设备数据
        async function loadDevicesData() {
            try {
                showLoading(true);
                hideError();
                
                const response = await fetch('/api/devices');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                devices = await response.json();
                await renderDevices();
                
                showLoading(false);
            } catch (error) {
                console.error('加载设备数据失败:', error);
                showError('加载设备数据失败: ' + error.message);
                showLoading(false);
            }
        }
        
        // 渲染设备列表
        async function renderDevices() {
            const container = document.getElementById('deviceContainer');
            container.innerHTML = '';
            
            if (devices.length === 0) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info alert-custom text-center">
                            <i class="bi bi-info-circle me-2"></i>
                            暂无设备数据，请先添加设备。
                        </div>
                    </div>
                `;
                return;
            }
            
            for (const device of devices) {
                await renderDeviceCard(device);
            }

            // 更新所有地址切换按钮的状态
            updateAllAddressToggleButtons();
        }
        
        // 渲染单个设备卡片
        async function renderDeviceCard(device) {
            try {
                // 获取设备的数据项
                const dataItemsResponse = await fetch(`/api/device/${device.id}/data-items`);
                const dataItems = dataItemsResponse.ok ? await dataItemsResponse.json() : [];

                const deviceCard = createDeviceCardHTML(device, dataItems);
                document.getElementById('deviceContainer').appendChild(deviceCard);

                // 加载数据项的实时值
                for (const item of dataItems) {
                    await updateDataItemValue(item.id);
                }
            } catch (error) {
                console.error(`渲染设备卡片失败 (${device.name}):`, error);
            }
        }
        
        // 创建设备卡片HTML
        function createDeviceCardHTML(device, dataItems) {
            const col = document.createElement('div');
            col.className = getCardSizeClass(currentCardSize);
            col.id = `device-${device.id}`;

            const statusClass = device.connected ? 'connected' : 'disconnected';
            const statusText = device.connected ? '已连接' : '未连接';
            const hasImage = device.imageUrl && device.imageUrl.trim() !== '';

            col.innerHTML = `
                <div class="card device-card card-size-${currentCardSize} ${hasImage ? 'has-image' : ''}">
                    <div class="device-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <span class="device-status ${statusClass}"></span>
                                <div>
                                    <h5 class="mb-1">${device.name}</h5>
                                    <small class="opacity-75">${device.address}:${device.port}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-outline-light btn-sm me-2"
                                        onclick="toggleAddressDisplay('${device.id}')"
                                        title="显示/隐藏监控项地址"
                                        id="addressToggleBtn-${device.id}">
                                    <i class="bi bi-eye" id="addressToggleIcon-${device.id}"></i>
                                </button>
                                <button class="btn btn-outline-light btn-sm image-upload-btn me-2"
                                        onclick="showImageSelectionModal('${device.id}')"
                                        title="${hasImage ? '更换图片' : '添加图片'}">
                                    <i class="bi bi-${hasImage ? 'pencil' : 'plus'}"></i>
                                </button>
                                <span class="badge bg-light text-dark">${statusText}</span>
                            </div>
                        </div>
                    </div>
                    ${hasImage ? createImageContainerHTML(device) : ''}
                    <div class="card-body p-0">
                        ${dataItems.length > 0 ? createDataItemsTableHTML(dataItems, device.id) : '<div class="p-3 text-center text-muted">暂无监控项</div>'}
                    </div>
                </div>
            `;

            return col;
        }

        // 创建图片容器HTML
        function createImageContainerHTML(device) {
            return `
                <div class="device-image-container">
                    <img src="${device.imageUrl}" alt="${device.name}" class="device-image">
                    <div class="image-upload-overlay" onclick="viewDeviceImage('${device.imageUrl}', '${device.name}')">
                        <i class="bi bi-eye upload-icon"></i>
                    </div>
                </div>
            `;
        }
        
        // 创建数据项表格HTML
        function createDataItemsTableHTML(dataItems, deviceId) {
            const isAddressHidden = getDeviceAddressDisplayState(deviceId);
            const tableClass = isAddressHidden ? 'table data-item-table address-hidden' : 'table data-item-table';

            let tableHTML = `
                <table class="${tableClass}">
                    <thead>
                        <tr>
                            <th class="text-center">监控项名称</th>
                            <th class="address-column text-center">地址</th>
                            <th class="text-center">当前值</th>
                            <th class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            dataItems.forEach(item => {
                tableHTML += `
                    <tr>
                        <td class="text-center">${item.name}</td>
                        <td class="address-column text-center"><code>${item.address}</code></td>
                        <td class="text-center">
                            <span id="value-${item.id}" class="value-display">
                                <div class="loading-spinner"></div>
                            </span>
                        </td>
                        <td class="text-center">
                            <button class="btn btn-outline-primary btn-edit"
                                    onclick="openEditModal('${item.id}', '${item.name}', '${item.address}')">
                                <i class="bi bi-pencil"></i> 修改
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableHTML += `
                    </tbody>
                </table>
            `;
            
            return tableHTML;
        }
        
        // 更新数据项值
        async function updateDataItemValue(itemId) {
            try {
                const response = await fetch(`/api/data-items/${itemId}/latest-value`);
                if (response.ok) {
                    const data = await response.json();
                    const valueElement = document.getElementById(`value-${itemId}`);
                    if (valueElement) {
                        valueElement.innerHTML = data.value !== null ? data.value : '-';
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error(`更新数据项值失败 (${itemId}):`, error);
                const valueElement = document.getElementById(`value-${itemId}`);
                if (valueElement) {
                    valueElement.innerHTML = '<span class="text-danger" title="数据获取失败">错误</span>';
                }
            }
        }
        
        // 打开编辑模态框
        function openEditModal(itemId, itemName, itemAddress) {
            currentEditItem = { id: itemId, name: itemName, address: itemAddress };

            // 查找设备信息
            let deviceName = '未知设备';
            for (const device of devices) {
                // 检查这个数据项是否属于当前设备
                const deviceCard = document.querySelector(`#device-${device.id}`);
                if (deviceCard && deviceCard.querySelector(`#value-${itemId}`)) {
                    deviceName = device.name;
                    break;
                }
            }

            document.getElementById('modalDeviceName').textContent = deviceName;
            document.getElementById('modalDataItemName').textContent = itemName;
            document.getElementById('modalDataItemAddress').textContent = itemAddress;

            const currentValueElement = document.getElementById(`value-${itemId}`);
            const currentValue = currentValueElement ? currentValueElement.textContent : '-';
            document.getElementById('modalCurrentValue').textContent = currentValue;

            document.getElementById('newValue').value = '';

            const modal = new bootstrap.Modal(document.getElementById('editValueModal'));
            modal.show();
        }
        
        // 确认写入
        async function confirmWrite() {
            if (!currentEditItem) return;
            
            const newValue = document.getElementById('newValue').value;
            if (!newValue || newValue.trim() === '') {
                alert('请输入有效的数值');
                return;
            }
            
            const numValue = parseInt(newValue);
            if (isNaN(numValue) || numValue < -32768 || numValue > 32767) {
                alert('请输入-32768到32767之间的整数');
                return;
            }
            
            try {
                const response = await fetch(`/api/data-item/${currentEditItem.id}/write`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ value: numValue })
                });
                
                if (response.ok) {
                    // 写入成功，立即更新显示值
                    const valueElement = document.getElementById(`value-${currentEditItem.id}`);
                    if (valueElement) {
                        valueElement.textContent = numValue;
                    }
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editValueModal'));
                    modal.hide();
                    
                    // 显示成功提示
                    showSuccessToast('数据写入成功');
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '写入失败');
                }
            } catch (error) {
                console.error('写入数据失败:', error);
                alert('写入失败: ' + error.message);
            }
        }
        
        // 刷新所有数据
        async function refreshAllData() {
            await loadDevicesData();
        }
        
        // 开始自动刷新
        function startAutoRefresh() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
            
            refreshTimer = setInterval(async () => {
                // 只更新数据项的值，不重新渲染整个页面
                const valueElements = document.querySelectorAll('[id^="value-"]');
                for (const element of valueElements) {
                    const itemId = element.id.replace('value-', '');
                    await updateDataItemValue(itemId);
                }
            }, 3000);
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            const loadingAlert = document.getElementById('loadingAlert');
            loadingAlert.style.display = show ? 'flex' : 'none';
        }
        
        // 显示/隐藏错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorAlert.classList.remove('d-none');
        }
        
        function hideError() {
            const errorAlert = document.getElementById('errorAlert');
            errorAlert.classList.add('d-none');
        }
        
        // 显示成功提示
        function showSuccessToast(message) {
            // 简单的成功提示，可以后续优化为Toast组件
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-custom position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }
        
        // 显示图片选择模态框
        async function showImageSelectionModal(deviceId) {
            currentDeviceId = deviceId;
            selectedImageUrl = null;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('imageSelectionModal'));
            modal.show();

            // 加载图片列表
            await loadImageList();

            // 检查当前设备是否已有图片，显示移除按钮
            const device = devices.find(d => d.id === deviceId);
            const removeBtn = document.getElementById('removeImageBtn');
            if (device && device.imageUrl) {
                removeBtn.style.display = 'inline-block';
            } else {
                removeBtn.style.display = 'none';
            }
        }

        // 加载图片列表
        async function loadImageList() {
            try {
                const container = document.getElementById('imageListContainer');
                container.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="loading-spinner me-2"></div>
                        正在加载图片列表...
                    </div>
                `;

                const response = await fetch('/api/files/images');
                if (!response.ok) {
                    throw new Error('获取图片列表失败');
                }

                const files = await response.json();
                // 后端已经过滤了图片文件，直接使用
                const imageFiles = files;

                if (imageFiles.length === 0) {
                    container.innerHTML = `
                        <div class="col-12 text-center text-muted">
                            <i class="bi bi-image me-2"></i>
                            暂无可用图片，请先上传图片文件
                        </div>
                    `;
                    return;
                }

                // 渲染图片列表
                container.innerHTML = '';
                imageFiles.forEach(file => {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 col-sm-4 col-6';
                    col.innerHTML = `
                        <div class="image-selection-item" onclick="selectImage('${file.url}', this)">
                            <img src="${file.url}" alt="${file.name}" loading="lazy">
                            <div class="image-info">
                                <div class="image-name" title="${file.name}">${file.name}</div>
                                <div class="image-size">${file.size}</div>
                            </div>
                        </div>
                    `;
                    container.appendChild(col);
                });

            } catch (error) {
                console.error('加载图片列表失败:', error);
                document.getElementById('imageListContainer').innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        加载图片列表失败: ${error.message}
                    </div>
                `;
            }
        }

        // 选择图片
        function selectImage(imageUrl, element) {
            // 移除之前的选中状态
            document.querySelectorAll('.image-selection-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedImageUrl = imageUrl;

            // 立即应用图片
            applySelectedImage();
        }

        // 应用选中的图片
        async function applySelectedImage() {
            if (!currentDeviceId || !selectedImageUrl) return;

            try {
                const response = await fetch(`/api/device/${currentDeviceId}/image`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ imageUrl: selectedImageUrl })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '更新设备图片失败');
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('imageSelectionModal'));
                modal.hide();

                // 重新加载设备数据以显示新图片
                await loadDevicesData();
                showSuccessToast('设备图片更新成功');

            } catch (error) {
                console.error('应用图片失败:', error);
                alert('应用图片失败: ' + error.message);
            }
        }

        // 移除设备图片
        async function removeDeviceImage() {
            if (!currentDeviceId) return;

            if (!confirm('确定要移除设备图片吗？')) return;

            try {
                const response = await fetch(`/api/device/${currentDeviceId}/image`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ imageUrl: '' })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '移除设备图片失败');
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('imageSelectionModal'));
                modal.hide();

                // 重新加载设备数据
                await loadDevicesData();
                showSuccessToast('设备图片已移除');

            } catch (error) {
                console.error('移除图片失败:', error);
                alert('移除图片失败: ' + error.message);
            }
        }

        // 查看设备图片
        function viewDeviceImage(imageUrl, deviceName) {
            if (!imageUrl || imageUrl.trim() === '') return;

            // 设置图片信息
            const imageElement = document.getElementById('viewImageElement');
            const imageInfo = document.getElementById('imageViewInfo');
            const modalTitle = document.getElementById('imageViewModalLabel');

            imageElement.src = imageUrl;
            imageElement.alt = `${deviceName}的设备图片`;
            imageInfo.textContent = `设备: ${deviceName}`;
            modalTitle.innerHTML = `<i class="bi bi-eye me-2"></i>查看设备图片 - ${deviceName}`;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('imageViewModal'));
            modal.show();
        }

        // 触发新图片上传
        function triggerNewImageUpload() {
            if (!currentDeviceId) return;

            // 关闭选择模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('imageSelectionModal'));
            modal.hide();

            // 直接创建文件输入框并触发选择
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(event) {
                handleNewImageUpload(event, currentDeviceId);
            };
            input.click();
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 处理新图片上传
        async function handleNewImageUpload(event, deviceId) {
            const file = event.target.files[0];
            if (!file) return;

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }

            // 检查文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
                alert('图片文件大小不能超过5MB');
                return;
            }

            try {
                // 显示上传中状态
                showSuccessToast('正在上传图片...');

                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);

                // 上传图片到服务器
                const uploadResponse = await fetch('/api/upload/image', {
                    method: 'POST',
                    body: formData
                });

                if (!uploadResponse.ok) {
                    const errorData = await uploadResponse.json();
                    throw new Error(errorData.error || '图片上传失败');
                }

                const uploadResult = await uploadResponse.json();
                const imageUrl = uploadResult.url;

                // 更新设备图片
                const updateResponse = await fetch(`/api/device/${deviceId}/image`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ imageUrl: imageUrl })
                });

                if (!updateResponse.ok) {
                    const errorData = await updateResponse.json();
                    throw new Error(errorData.message || '更新设备图片失败');
                }

                // 重新加载设备数据以显示新图片
                await loadDevicesData();
                showSuccessToast('设备图片更新成功');

            } catch (error) {
                console.error('图片上传失败:', error);
                alert('图片上传失败: ' + error.message);
            }
        }

        // 切换指定设备的地址显示/隐藏
        function toggleAddressDisplay(deviceId) {
            const currentlyHidden = getDeviceAddressDisplayState(deviceId);
            const newHiddenState = !currentlyHidden;

            // 保存新状态到localStorage
            setDeviceAddressDisplayState(deviceId, newHiddenState);

            // 更新指定设备的表格显示状态
            const deviceCard = document.getElementById(`device-${deviceId}`);
            if (deviceCard) {
                const table = deviceCard.querySelector('.data-item-table');
                if (table) {
                    if (newHiddenState) {
                        table.classList.add('address-hidden');
                    } else {
                        table.classList.remove('address-hidden');
                    }
                }

                // 更新指定设备的切换按钮状态
                const toggleButton = deviceCard.querySelector(`#addressToggleBtn-${deviceId}`);
                const toggleIcon = deviceCard.querySelector(`#addressToggleIcon-${deviceId}`);

                if (toggleButton) {
                    if (newHiddenState) {
                        toggleButton.title = '显示监控项地址';
                    } else {
                        toggleButton.title = '隐藏监控项地址';
                    }
                }

                if (toggleIcon) {
                    if (newHiddenState) {
                        // 隐藏状态：显示眼睛图标
                        toggleIcon.className = 'bi bi-eye';
                    } else {
                        // 显示状态：显示眼睛斜杠图标
                        toggleIcon.className = 'bi bi-eye-slash';
                    }
                }
            }

            console.log(`设备 ${deviceId} 的地址显示状态已切换为: ${newHiddenState ? '隐藏' : '显示'}`);
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
        });
    </script>
</body>
</html>
