package com.example.controller;

import com.example.model.DataItem;
import com.example.model.ErrorResponse;
import com.example.repository.DataItemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Collections;

import com.example.service.DeviceService;
import com.example.service.DataCollectionService;
import com.example.model.Device;

@Slf4j
@RestController
@RequestMapping("/api/data-items")
public class DataItemController {

    private final DataItemRepository dataItemRepository;
    private final DeviceService deviceService;
    private final DataCollectionService dataCollectionService;

    public DataItemController(DataItemRepository dataItemRepository, DeviceService deviceService, DataCollectionService dataCollectionService) {
        this.dataItemRepository = dataItemRepository;
        this.deviceService = deviceService;
        this.dataCollectionService = dataCollectionService;
    }

    @GetMapping("/{id}/latest-value")
    public ResponseEntity<?> getLatestValue(@PathVariable String id) {
        try {
            DataItem item = dataItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("value", item.getLatestValue() != null ? item.getLatestValue() : 0);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting latest value for item {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取数据失败", e.getMessage()));
        }
    }

    @PutMapping("/{id}/history-enabled")
    public ResponseEntity<?> updateHistoryEnabled(@PathVariable String id, @RequestBody Map<String, Boolean> request) {
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return ResponseEntity.badRequest().body("Missing 'enabled' parameter");
            }

            DataItem item = dataItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));

            item.setHistoryEnabled(enabled);
            dataItemRepository.save(item);

            log.info("Updated history enabled status for item {}: {}", id, enabled);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error updating history enabled status for item {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/{id}/stats-enabled")
    public ResponseEntity<?> updateStatsEnabled(@PathVariable String id, @RequestBody Map<String, Boolean> request) {
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return ResponseEntity.badRequest().body("Missing 'enabled' parameter");
            }

            DataItem item = dataItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));

            boolean previousEnabled = item.getStatsEnabled();
            item.setStatsEnabled(enabled);
            dataItemRepository.save(item);

            // 如果从启用变为禁用，清理统计数据
            if (previousEnabled && !enabled) {
                dataCollectionService.cleanupStatsData(id);
            }

            log.info("Updated stats enabled status for item {}: {}", id, enabled);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error updating stats enabled status for item {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/{id}/history-retention")
    public ResponseEntity<?> updateHistoryRetention(@PathVariable String id, @RequestBody Map<String, Integer> request) {
        try {
            Integer retentionHours = request.get("retentionHours");
            if (retentionHours == null) {
                return ResponseEntity.badRequest().body("Missing 'retentionHours' parameter");
            }
            
            // 验证值的有效性 (-1表示永久, 其他必须是正整数)
            if (retentionHours != -1 && retentionHours <= 0) {
                return ResponseEntity.badRequest().body("Retention hours must be either -1 (permanent) or a positive integer");
            }

            DataItem item = dataItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("监控项不存在"));

            item.setHistoryRetentionHours(retentionHours);
            dataItemRepository.save(item);

            log.info("Updated history retention for item {}: {} hours", id, retentionHours);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error updating history retention for item {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/device/{deviceId}")
    public ResponseEntity<?> getDeviceDataItems(@PathVariable String deviceId) {
        try {
            Device device = deviceService.getDevice(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在: " + deviceId));
            
            List<DataItem> dataItems = dataItemRepository.findByDeviceId(deviceId);
            return ResponseEntity.ok(dataItems);
        } catch (Exception e) {
            log.error("Error getting data items for device {}: {}", deviceId, e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取数据项失败", e.getMessage()));
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllDataItems() {
        try {
            List<DataItem> dataItems = dataItemRepository.findAllWithDevice();
            return ResponseEntity.ok(dataItems);
        } catch (Exception e) {
            log.error("Error getting all data items: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取数据项失败", e.getMessage()));
        }
    }

    @PutMapping("/device/{deviceId}/sort-order")
    public ResponseEntity<?> updateDataItemsSortOrder(@PathVariable String deviceId, @RequestBody Map<String, Object> request) {
        try {
            log.info("开始更新设备 {} 的监控项排序", deviceId);
            log.debug("请求数据: {}", request);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sortItems = (List<Map<String, Object>>) request.get("items");

            if (sortItems == null || sortItems.isEmpty()) {
                log.warn("排序项目为空，设备ID: {}", deviceId);
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "排序项目不能为空"));
            }

            // 验证设备存在
            deviceService.getDevice(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在: " + deviceId));

            // 批量更新排序
            int updatedCount = 0;
            for (Map<String, Object> item : sortItems) {
                String itemId = (String) item.get("id");
                Object sortOrderObj = item.get("sortOrder");

                log.debug("处理监控项: id={}, sortOrder={}", itemId, sortOrderObj);

                if (itemId == null || sortOrderObj == null) {
                    log.warn("跳过无效项目: id={}, sortOrder={}", itemId, sortOrderObj);
                    continue;
                }

                // 处理sortOrder的类型转换
                Integer sortOrder;
                if (sortOrderObj instanceof Integer) {
                    sortOrder = (Integer) sortOrderObj;
                } else if (sortOrderObj instanceof Number) {
                    sortOrder = ((Number) sortOrderObj).intValue();
                } else {
                    try {
                        sortOrder = Integer.parseInt(sortOrderObj.toString());
                    } catch (NumberFormatException e) {
                        log.warn("无法解析sortOrder: {}", sortOrderObj);
                        continue;
                    }
                }

                DataItem dataItem = dataItemRepository.findById(itemId).orElse(null);
                if (dataItem != null && dataItem.getDevice().getId().equals(deviceId)) {
                    log.debug("更新监控项 {} 的排序为 {}", itemId, sortOrder);
                    dataItem.setSortOrder(sortOrder);
                    dataItemRepository.save(dataItem);
                    updatedCount++;
                } else {
                    log.warn("监控项不存在或不属于指定设备: itemId={}, deviceId={}", itemId, deviceId);
                }
            }

            log.info("设备 {} 的监控项排序更新成功，共更新 {} 个项目", deviceId, updatedCount);
            return ResponseEntity.ok().body(Collections.singletonMap("message", "排序更新成功"));
        } catch (Exception e) {
            log.error("Error updating data items sort order for device {}: {}", deviceId, e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("更新排序失败", e.getMessage()));
        }
    }
} 