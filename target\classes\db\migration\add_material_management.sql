-- 素材管理功能数据库迁移脚本
-- 执行时间：2024-12-20

-- 创建素材分类表
CREATE TABLE IF NOT EXISTS material_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    type ENUM('decoration', 'border') NOT NULL COMMENT '分类类型：decoration-装饰，border-边框',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='素材分类表';

-- 创建素材文件表
CREATE TABLE IF NOT EXISTS material_files (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL COMMENT '文件名（UUID生成）',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_url VARCHAR(500) NOT NULL COMMENT '文件访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型（MIME类型）',
    category_id BIGINT COMMENT '所属分类ID',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认素材',
    sort_order INT DEFAULT 0 COMMENT '在分类中的排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (category_id) REFERENCES material_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='素材文件表';

-- 创建索引
CREATE INDEX idx_material_files_category ON material_files(category_id);
CREATE INDEX idx_material_files_type ON material_files(file_type);
CREATE INDEX idx_material_categories_type ON material_categories(type);

-- 插入默认分类数据
INSERT INTO material_categories (name, type, description, sort_order) VALUES
('动态背景', 'decoration', '各种动态背景装饰素材，包括粒子效果、光效等', 1),
('图标装饰', 'decoration', '各种装饰性图标和符号', 2),
('科技元素', 'decoration', '科技感装饰元素，适用于工业大屏', 3),
('简约边框', 'border', '简洁风格的边框装饰', 1),
('科技边框', 'border', '科技感边框，适用于数据大屏', 2),
('古典边框', 'border', '古典风格边框装饰', 3);
