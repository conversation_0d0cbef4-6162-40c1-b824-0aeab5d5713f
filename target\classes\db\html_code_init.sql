-- HTML代码管理初始化数据
-- 创建HTML代码分类表（如果不存在）
CREATE TABLE IF NOT EXISTS html_code_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(500),
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建HTML代码片段表（如果不存在）
CREATE TABLE IF NOT EXISTS html_code_snippets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    html_content LONGTEXT NOT NULL,
    category_id BIGINT,
    tags VARCHAR(500),
    is_favorite BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES html_code_categories(id) ON DELETE SET NULL
);

-- 插入默认分类数据
INSERT IGNORE INTO html_code_categories (name, description, sort_order) VALUES
('装饰', '装饰效果组件', 1),
('边框', '边框效果组件', 2),
('按钮', '按钮效果组件', 3),
('动画', '动画效果组件', 4),
('图表', '图表装饰组件', 5);

-- 插入测试HTML代码片段
INSERT IGNORE INTO html_code_snippets (title, description, html_content, category_id, tags, is_favorite, sort_order) VALUES
-- 装饰类别的代码片段
('响应式装饰效果', '自适应容器的装饰效果，包含角落装饰、圆环、浮动点等', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: transparent; width: 100%; height: 100%; overflow: hidden; }
        .decoration-container { position: relative; width: 100%; height: 100%; }
        .corner { position: absolute; width: 8%; height: 8%; max-width: 40px; max-height: 40px; min-width: 20px; min-height: 20px; border: clamp(2px, 0.3vw, 4px) solid #00ffff; }
        .corner-tl { top: 2%; left: 2%; border-right: none; border-bottom: none; animation: glow 2s ease-in-out infinite alternate; }
        .corner-tr { top: 2%; right: 2%; border-left: none; border-bottom: none; animation: glow 2s ease-in-out infinite alternate 0.5s; }
        .corner-bl { bottom: 2%; left: 2%; border-right: none; border-top: none; animation: glow 2s ease-in-out infinite alternate 1s; }
        .corner-br { bottom: 2%; right: 2%; border-left: none; border-top: none; animation: glow 2s ease-in-out infinite alternate 1.5s; }
        @keyframes glow { 0% { box-shadow: 0 0 0.5vw #00ffff; } 100% { box-shadow: 0 0 2vw #00ffff; } }
        .center-ring { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 20vw; height: 20vw; max-width: 100px; max-height: 100px; min-width: 50px; min-height: 50px; border: clamp(1px, 0.2vw, 3px) solid rgba(255, 0, 204, 0.6); border-radius: 50%; animation: pulse 3s ease-in-out infinite; }
        @keyframes pulse { 0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); } 50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); } }
    </style>
</head>
<body>
    <div class="decoration-container">
        <div class="corner corner-tl"></div>
        <div class="corner corner-tr"></div>
        <div class="corner corner-bl"></div>
        <div class="corner corner-br"></div>
        <div class="center-ring"></div>
    </div>
</body>
</html>', 
(SELECT id FROM html_code_categories WHERE name = '装饰'), 'responsive,decoration,corner,ring', true, 1),

-- 边框类别的代码片段
('响应式动态边框', '彩色渐变边框动画效果，自适应容器大小', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: transparent; width: 100%; height: 100%; }
        .border-container { width: 100%; height: 100%; padding: 2%; display: flex; justify-content: center; align-items: center; }
        .dynamic-border { position: relative; width: 100%; height: 100%; border-radius: 2%; background: linear-gradient(45deg, #ff00cc, #00ccff, #00ff00, #ff0000, #ff00cc, #00ccff, #00ff00, #ff0000); background-size: 400%; animation: border-flow 8s linear infinite; }
        @keyframes border-flow { 0% { background-position: 0 0; filter: hue-rotate(0deg); } 50% { background-position: 300% 0; filter: hue-rotate(360deg); } 100% { background-position: 0 0; filter: hue-rotate(0deg); } }
        .content { background: transparent; border-radius: 1.5%; padding: 5%; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; }
        .title { font-size: clamp(1.2rem, 5vw, 3rem); margin-bottom: 0.5em; background: linear-gradient(90deg, #ff00cc, #00ccff); -webkit-background-clip: text; background-clip: text; color: transparent; font-weight: bold; }
    </style>
</head>
<body>
    <div class="border-container">
        <div class="dynamic-border">
            <div class="content">
                <h1 class="title">动态边框</h1>
            </div>
        </div>
    </div>
</body>
</html>', 
(SELECT id FROM html_code_categories WHERE name = '边框'), 'responsive,border,gradient,animation', true, 1),

-- 按钮类别的代码片段
('响应式动态按钮', '多层发光效果和边框流动动画的响应式按钮', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: transparent; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; padding: 2%; }
        .dynamic-button { position: relative; padding: 4% 8%; font-size: clamp(1rem, 4vw, 2rem); font-weight: bold; color: #00d4ff; background: transparent; border: 0.2em solid #00d4ff; border-radius: 0.6em; cursor: pointer; overflow: hidden; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 0.1em; width: 100%; max-width: 300px; min-width: 120px; height: auto; min-height: 3em; display: flex; align-items: center; justify-content: center; }
        .dynamic-button::before { content: ""; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent); transition: left 0.5s; }
        .dynamic-button::after { content: ""; position: absolute; top: -0.2em; left: -0.2em; right: -0.2em; bottom: -0.2em; background: linear-gradient(45deg, #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff); background-size: 400%; border-radius: 0.8em; z-index: -1; opacity: 0; animation: border-flow 3s linear infinite; transition: opacity 0.3s; }
        .dynamic-button:hover::before { left: 100%; }
        .dynamic-button:hover::after { opacity: 1; }
        .dynamic-button:hover { color: #ffffff; background: rgba(0, 212, 255, 0.1); box-shadow: 0 0 1em rgba(0, 212, 255, 0.4), 0 0 2em rgba(0, 212, 255, 0.2); transform: translateY(-0.1em); text-shadow: 0 0 0.5em rgba(0, 212, 255, 0.8); }
        @keyframes border-flow { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }
    </style>
</head>
<body>
    <button class="dynamic-button">点击我</button>
</body>
</html>', 
(SELECT id FROM html_code_categories WHERE name = '按钮'), 'responsive,button,glow,animation', true, 1),

-- 动画类别的代码片段
('浮动粒子动画', '科技感浮动粒子背景动画效果', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: transparent; width: 100%; height: 100%; overflow: hidden; }
        .particles-container { position: relative; width: 100%; height: 100%; }
        .particle { position: absolute; width: clamp(4px, 1vw, 8px); height: clamp(4px, 1vw, 8px); background: radial-gradient(circle, #00ff88, transparent); border-radius: 50%; animation: float 6s ease-in-out infinite; }
        .particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { top: 30%; right: 15%; animation-delay: 2s; }
        .particle:nth-child(4) { bottom: 40%; left: 30%; animation-delay: 3s; }
        .particle:nth-child(5) { bottom: 20%; right: 25%; animation-delay: 4s; }
        .particle:nth-child(6) { top: 70%; right: 40%; animation-delay: 5s; }
        @keyframes float { 0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; } 50% { transform: translateY(-20px) scale(1.2); opacity: 1; } }
    </style>
</head>
<body>
    <div class="particles-container">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
</body>
</html>', 
(SELECT id FROM html_code_categories WHERE name = '动画'), 'responsive,particles,float,animation', false, 1);
