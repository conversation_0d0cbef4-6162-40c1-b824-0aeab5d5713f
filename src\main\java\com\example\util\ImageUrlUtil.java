package com.example.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ImageUrlUtil {

    @Value("${server.external-url}")
    private String serverExternalUrl;
    
    @Value("${upload.image.url-prefix}")
    private String urlPrefix;
    
    /**
     * 将相对路径或旧格式的图片URL转换为完整的外部访问URL
     * 
     * @param imageUrl 原始图片URL或路径
     * @return 完整的外部访问URL
     */
    public String getFullImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return null;
        }
        
        // 如果已经是完整URL，直接返回
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
            return imageUrl;
        }
        
        // 如果是相对路径，转换为完整URL
        String filename = imageUrl;
        
        // 如果包含路径分隔符，提取文件名
        if (imageUrl.contains("/")) {
            filename = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
        }
        
        return "http://" + serverExternalUrl + urlPrefix + "/" + filename;
    }
} 