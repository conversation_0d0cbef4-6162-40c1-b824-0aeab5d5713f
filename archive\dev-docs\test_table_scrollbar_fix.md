# 表格组件滚动条修复测试指南

## 测试环境准备
1. 启动应用程序
2. 进入大屏设计页面
3. 准备测试数据

## 测试用例

### 测试用例1：基本滚动条显示修复
**目标**：验证保存后刷新不会错误显示滚动条

**步骤**：
1. 创建一个数据表格组件
2. 设置组件大小为 400x300 像素
3. 配置数据源，确保数据行数较少（如3-5行）
4. 保存大屏配置
5. 刷新浏览器页面
6. 观察表格组件是否显示滚动条

**预期结果**：
- 刷新后表格不应显示滚动条
- 表格内容应完整显示
- 控制台日志显示"禁用滚动条 - 内容高度在组件范围内"

### 测试用例2：必要滚动条正确显示
**目标**：验证内容超出时正确显示滚动条

**步骤**：
1. 使用测试用例1的表格组件
2. 增加数据源数据，使行数超过组件高度容量（如15-20行）
3. 保存配置
4. 刷新页面
5. 观察滚动条显示情况

**预期结果**：
- 应该显示垂直滚动条
- 可以滚动查看所有数据
- 控制台日志显示"启用滚动条 - 内容高度超出组件范围"

### 测试用例3：动态调整验证
**目标**：验证组件大小调整时滚动条正确更新

**步骤**：
1. 使用有滚动条的表格组件
2. 拖拽调整组件高度，使其足够容纳所有数据
3. 观察滚动条是否消失
4. 再次缩小组件高度
5. 观察滚动条是否重新出现

**预期结果**：
- 组件高度增加时滚动条应消失
- 组件高度减少时滚动条应重新出现
- 过渡应该平滑，无闪烁

### 测试用例4：页面一致性验证
**目标**：验证设计、预览、发布页面行为一致

**步骤**：
1. 在设计页面创建表格组件（包含适量数据）
2. 保存配置
3. 进入预览页面，观察表格显示
4. 发布大屏，进入发布页面观察
5. 对比三个页面的表格显示效果

**预期结果**：
- 三个页面的滚动条显示状态应完全一致
- 表格内容和样式应保持一致
- 滚动行为应相同

## 调试信息检查

在浏览器开发者工具控制台中，应该能看到以下日志信息：

### 正常情况（无滚动条）
```
设置智能滚动高度: 260px 组件实际高度: 300
表格高度检查 (增强版): {
    tableHeight: 120,
    maxHeight: 260,
    needsScroll: false,
    componentHeight: 300,
    heightDifference: -140
}
禁用滚动条 - 内容高度在组件范围内
```

### 需要滚动条情况
```
设置智能滚动高度: 260px 组件实际高度: 300
表格高度检查 (增强版): {
    tableHeight: 400,
    maxHeight: 260,
    needsScroll: true,
    componentHeight: 300,
    heightDifference: 140
}
启用滚动条 - 内容高度超出组件范围，设置容器高度为: 260px
```

## 问题排查

如果测试失败，检查以下方面：

1. **控制台错误**：查看是否有JavaScript错误
2. **高度计算**：检查组件实际高度是否正确获取
3. **DOM结构**：确认表格容器的DOM结构正确
4. **样式冲突**：检查是否有其他CSS样式影响
5. **数据加载**：确认表格数据正确加载

## 回归测试

修复完成后，还应测试以下功能确保没有引入新问题：

1. 表格数据刷新功能
2. 表格样式配置功能
3. 表格轮播功能（如果启用）
4. 组件拖拽和调整大小功能
5. 撤销/重做功能

## 性能验证

观察修复后的性能表现：

1. 页面加载速度是否受影响
2. 滚动条切换是否流畅
3. 大量数据时的渲染性能
4. 内存使用是否正常
