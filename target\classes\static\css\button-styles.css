/* 通用按钮样式 - 统一所有页面的按钮外观 */

/* CSS变量定义 */
:root {
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fd7e14 0%, #ff6b35 100%);
}

/* 按钮样式美化 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.btn-outline-warning {
    color: #fd7e14;
    border: 2px solid #fd7e14;
    background: transparent;
}

.btn-outline-warning:hover {
    background: var(--warning-gradient);
    color: white;
    border-color: transparent;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    margin: 0 2px;
}

.btn-group-sm .btn:hover {
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: #667eea;
    border: 2px solid #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.btn-outline-danger {
    color: #dc3545;
    border: 2px solid #dc3545;
    background: transparent;
}

.btn-outline-danger:hover {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
    border-color: transparent;
}

/* 按钮图标样式 */
.btn i {
    font-size: 1rem;
    vertical-align: middle;
    margin-right: 0.5rem;
}

/* 导航条按钮样式 */
.navbar .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    transition: var(--transition);
}

.navbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-1px);
}

.navbar .btn-outline-light.active {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: white;
    color: white;
}

/* 按钮组样式 */
.btn-group .btn {
    margin: 0;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 操作列样式 */
td .btn-group {
    display: flex;
    gap: 0.5rem;
}

/* 响应式按钮样式 */
@media (max-width: 768px) {
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* 修复导航条和设计器头部激活按钮图标可见性问题 */
.navbar .btn-outline-light.active i,
.designer-header .btn-outline-light.active i {
    color: #495057 !important; /* 深灰色，确保与白色背景对比 */
}

.navbar .btn-outline-light.active i.bi,
.designer-header .btn-outline-light.active i.bi {
    color: #495057 !important; /* 针对Bootstrap Icons的特定规则 */
}
