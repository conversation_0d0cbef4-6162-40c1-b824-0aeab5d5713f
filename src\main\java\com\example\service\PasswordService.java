package com.example.service;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 密码加密服务，负责密码的加密和验证
 */
@Service
public class PasswordService {
    
    private final BCryptPasswordEncoder passwordEncoder;
    
    public PasswordService() {
        // 创建BCrypt密码编码器，使用默认的强度（10）
        this.passwordEncoder = new BCryptPasswordEncoder();
    }
    
    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public String encryptPassword(String rawPassword) {
        if (rawPassword == null) {
            throw new IllegalArgumentException("密码不能为空");
        }
        return passwordEncoder.encode(rawPassword);
    }
    
    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 如果密码匹配返回true，否则返回false
     */
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        
        // 如果存储的密码没有被加密（没有BCrypt前缀），则进行明文比较
        // 这是为了支持系统中现有的明文密码
        if (!encodedPassword.startsWith("$2a$") && !encodedPassword.startsWith("$2b$") && !encodedPassword.startsWith("$2y$")) {
            return rawPassword.equals(encodedPassword);
        }
        
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    /**
     * 检查密码是否需要加密
     * 
     * @param password 要检查的密码
     * @return 如果密码未加密返回true，否则返回false
     */
    public boolean needsEncryption(String password) {
        // 检查密码是否已经是BCrypt格式
        return password != null && !password.startsWith("$2a$") && !password.startsWith("$2b$") && !password.startsWith("$2y$");
    }
} 