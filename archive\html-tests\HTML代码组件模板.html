<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波纹装饰效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .ripple-container {
            background: transparent;
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .ripple-inner {
            --loader-background: linear-gradient(0deg, rgba(50, 50, 50, 0.2) 0%, rgba(100, 100, 100, 0.2) 100%);
            position: relative;
            width: 80%;
            height: 80%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .box {
            position: absolute;
            background: var(--loader-background);
            border-radius: 50%;
            border-top: 1px solid rgb(100, 100, 100);
            box-shadow: rgba(0, 0, 0, 0.3) 0 10px 10px 0;
            backdrop-filter: blur(5px);
            animation: ripple 2s infinite ease-in-out;
        }

        .box:nth-child(1) {
            width: 25%;
            aspect-ratio: 1/1;
            z-index: 99;
        }

        .box:nth-child(2) {
            inset: 30%;
            z-index: 98;
            border-color: rgba(100, 100, 100, 0.8);
            animation-delay: 0.2s;
        }

        .box:nth-child(3) {
            inset: 20%;
            z-index: 97;
            border-color: rgba(100, 100, 100, 0.6);
            animation-delay: 0.4s;
        }

        .box:nth-child(4) {
            inset: 10%;
            z-index: 96;
            border-color: rgba(100, 100, 100, 0.4);
            animation-delay: 0.6s;
        }

        .box:nth-child(5) {
            inset: 0;
            z-index: 95;
            border-color: rgba(100, 100, 100, 0.2);
            animation-delay: 0.8s;
        }

        @keyframes ripple {
            0% {
                transform: scale(0.8);
                box-shadow: rgba(0, 0, 0, 0.3) 0 10px 10px 0;
            }
            50% {
                transform: scale(1.0);
                box-shadow: rgba(0, 0, 0, 0.3) 0 30px 20px 0;
            }
            100% {
                transform: scale(0.8);
                box-shadow: rgba(0, 0, 0, 0.3) 0 10px 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="ripple-container">
        <div class="ripple-inner">
            <div class="box"></div>
            <div class="box"></div>
            <div class="box"></div>
            <div class="box"></div>
            <div class="box"></div>
        </div>
    </div>
</body>
</html>


</body>
</html>