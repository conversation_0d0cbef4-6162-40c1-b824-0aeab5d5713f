<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组态布局预览</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script type="text/javascript" th:src="@{/js/lib/jsplumb.min.js}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: #0d6efd;
            padding: 0.25rem 1rem;
            color: white;
            height: 40px;
            display: flex;
            align-items: center;
        }
        
        /* 导航栏内的container-fluid样式 */
        .navbar .container-fluid {
            padding: 0 1rem;
            height: auto;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* 页面主体的container-fluid样式 */
        body > .container-fluid {
            padding: 0;
            height: calc(100vh - 40px);
            display: flex;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            overflow: auto;
            background-color: #fff;
            border: 1px solid #dee2e6;
        }
        
        .canvas {
            position: relative;
            min-width: 100%;
            min-height: 100%;
            background-color: #ffffff;
        }
        
        .device-node {
            position: absolute;
            min-width: 150px;
            min-height: 100px;
            width: auto;
            height: auto;
            background-color: #ffffff;
            border: 2px solid #0d6efd;
            border-radius: 6px;
            padding: 10px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .device-node.has-image {
            height: auto;
            min-height: 150px;
            padding: 5px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .device-node .device-image {
            text-align: center;
            margin-bottom: 5px;
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: visible;
            position: relative;
            min-height: 100px;
        }
        
        .device-node .device-image img {
            max-width: 100%;
            max-height: 100px;
            object-fit: contain;
            transform-origin: center center;
            position: relative;
        }
        
        .device-node .title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            position: relative;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.7);
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .device-node .status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
            position: relative;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.7);
            flex: 0 0 auto;
        }
        
        .device-node .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .device-node .status-indicator.connected {
            background-color: #28a745;
        }
        
        .device-node .status-indicator.disconnected {
            background-color: #dc3545;
        }
        
        /* 调整大小手柄样式 */
        .resize-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            right: 0;
            bottom: 0;
            cursor: nwse-resize;
            background-color: #0d6efd;
            border-radius: 0 0 4px 0;
            z-index: 20;
            display: none; /* 默认隐藏 */
        }
        
        .device-node.has-image .resize-handle {
            background-color: rgba(13, 110, 253, 0.7);
        }
        
        /* 预警状态项样式 */
        .alert-status-container {
            margin-top: 5px;
            font-size: 12px;
            max-height: 80px;
            overflow-y: auto;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            padding: 4px;
            flex: 0 1 auto;
        }
        
        .alert-status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
            padding: 2px 4px;
            border-radius: 3px;
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .alert-name {
            font-weight: 500;
            margin-right: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }
        
        /* 徽章样式 */
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #198754;
        }
        
        .badge-warning {
            color: #000;
            background-color: #ffc107;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
        
        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }
        
        .text-node {
            position: absolute;
            min-width: 100px;
            min-height: 40px;
            background-color: transparent;
            border: 1px dashed #6c757d;
            padding: 5px;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .text-node.no-border {
            border: none;
        }
        
        .text-node.selected {
            border: 1px dashed #0d6efd;
        }
        
        .editable {
            width: 100%;
            height: 100%;
            outline: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .image-node {
            position: absolute;
            width: 200px;
            height: 150px;
            background-color: #fff;
            border: 1px solid #d9d9d9;
            padding: 5px;
            z-index: 10;
            display: flex;
            flex-direction: column;
        }
        
        .image-title {
            text-align: center;
            font-weight: bold;
            padding: 2px 0;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .image-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .no-image {
            color: #6c757d;
            font-style: italic;
            text-align: center;
        }
        
        /* 连接线动画效果 */
        @keyframes flowAnimation {
            0% {
                stroke-dashoffset: 24;
            }
            100% {
                stroke-dashoffset: 0;
            }
        }
        
        /* 反向动画效果 */
        @keyframes reverseFlowAnimation {
            0% {
                stroke-dashoffset: 0;
            }
            100% {
                stroke-dashoffset: 24;
            }
        }
        
        /* 动态流动效果 */
        .jtk-connector.animated-connection path {
            stroke-dasharray: 10 5 !important;
            animation: flowAnimation linear infinite;
        }
        
        /* 反向动态流动效果 */
        .jtk-connector.animated-connection.reverse-flow path {
            animation-name: reverseFlowAnimation;
        }
        
        .jtk-connector.animation-slow path {
            animation-duration: 3s;
        }
        
        .jtk-connector.animation-medium path {
            animation-duration: 2s;
        }
        
        .jtk-connector.animation-fast path {
            animation-duration: 1s;
        }
        
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn {
            margin-bottom: 10px;
        }
        
        /* 端点样式 */
        .jtk-endpoint {
            z-index: 20;
            transition: opacity 0.3s ease;
        }
        
        .hide-endpoints .jtk-endpoint {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- 引入导航条片段，传递自定义按钮 -->
    <div th:with="customButtons='
        <button id=&quot;toggleEndpoints&quot; class=&quot;btn btn-outline-light btn-sm me-2&quot;>显示端点</button>
    '">
        <nav th:replace="fragments/navbar :: navbar(|组态布局预览: ${topologyName}|, 'topology', false, true, false, ${customButtons})"></nav>
    </div>
    
    <div class="container-fluid">
        <div class="canvas-container">
            <div class="canvas" id="canvas">
                <!-- 设备节点将动态添加到这里 -->
            </div>
        </div>
    </div>
    
    <script th:inline="javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化端点显示状态（默认隐藏）
            let endpointsVisible = false;
            
            // 获取切换按钮
            const toggleEndpointsBtn = document.getElementById('toggleEndpoints');
            
            // 更新按钮初始文本
            toggleEndpointsBtn.textContent = '显示端点';
            
            // 默认隐藏端点
            const canvas = document.getElementById('canvas');
            canvas.classList.add('hide-endpoints');
            
            // 添加按钮点击事件
            toggleEndpointsBtn.addEventListener('click', function() {
                // 切换端点显示状态
                endpointsVisible = !endpointsVisible;
                
                // 更新按钮文本
                toggleEndpointsBtn.textContent = endpointsVisible ? '隐藏端点' : '显示端点';
                
                // 切换画布的类名
                if (endpointsVisible) {
                    canvas.classList.remove('hide-endpoints');
                } else {
                    canvas.classList.add('hide-endpoints');
                }
            });
            
            // RGB颜色转HEX格式的辅助函数 (保留此函数，因为它可能被其他功能使用)
            function rgbToHex(rgb) {
                // 检查是否是RGB或RGBA格式
                const rgbMatch = rgb.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)$/);
                if (!rgbMatch) return null;
                
                // 提取RGB值
                const r = parseInt(rgbMatch[1], 10);
                const g = parseInt(rgbMatch[2], 10);
                const b = parseInt(rgbMatch[3], 10);
                
                // 转换为HEX
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }
            
            // 计算画布中所有元素的边界 (保留此函数，因为它可能被其他功能使用)
            function calculateCanvasBoundingBox() {
                const canvas = document.getElementById('canvas');
                const canvasRect = canvas.getBoundingClientRect();
                
                // 获取所有节点和连接线
                const nodes = canvas.querySelectorAll('.device-node, .text-node, .image-node');
                const connectors = document.querySelectorAll('.jtk-connector');
                const endpoints = document.querySelectorAll('.jtk-endpoint');
                const overlays = document.querySelectorAll('.jtk-overlay');
                
                // 初始化边界值
                let minX = Infinity;
                let minY = Infinity;
                let maxX = 0;
                let maxY = 0;
                
                // 计算所有节点的边界
                nodes.forEach(node => {
                    const left = parseInt(node.style.left) || 0;
                    const top = parseInt(node.style.top) || 0;
                    const width = node.offsetWidth;
                    const height = node.offsetHeight;
                    
                    minX = Math.min(minX, left);
                    minY = Math.min(minY, top);
                    maxX = Math.max(maxX, left + width);
                    maxY = Math.max(maxY, top + height);
                    
                    console.log(`节点 ${node.id}: left=${left}, top=${top}, width=${width}, height=${height}`);
                });
                
                // 计算所有连接线的边界
                connectors.forEach((connector, index) => {
                    const rect = connector.getBoundingClientRect();
                    
                    // 计算连接线相对于画布的位置
                    const connectorLeft = rect.left - canvasRect.left + canvas.scrollLeft;
                    const connectorTop = rect.top - canvasRect.top + canvas.scrollTop;
                    const connectorRight = connectorLeft + rect.width;
                    const connectorBottom = connectorTop + rect.height;
                    
                    minX = Math.min(minX, connectorLeft);
                    minY = Math.min(minY, connectorTop);
                    maxX = Math.max(maxX, connectorRight);
                    maxY = Math.max(maxY, connectorBottom);
                    
                    console.log(`连接线 ${index}: left=${connectorLeft}, top=${connectorTop}, right=${connectorRight}, bottom=${connectorBottom}`);
                });
                
                // 计算所有端点的边界
                endpoints.forEach((endpoint, index) => {
                    const rect = endpoint.getBoundingClientRect();
                    
                    // 计算端点相对于画布的位置
                    const endpointLeft = rect.left - canvasRect.left + canvas.scrollLeft;
                    const endpointTop = rect.top - canvasRect.top + canvas.scrollTop;
                    const endpointRight = endpointLeft + rect.width;
                    const endpointBottom = endpointTop + rect.height;
                    
                    minX = Math.min(minX, endpointLeft);
                    minY = Math.min(minY, endpointTop);
                    maxX = Math.max(maxX, endpointRight);
                    maxY = Math.max(maxY, endpointBottom);
                });
                
                // 计算所有覆盖物的边界
                overlays.forEach((overlay, index) => {
                    const rect = overlay.getBoundingClientRect();
                    
                    // 计算覆盖物相对于画布的位置
                    const overlayLeft = rect.left - canvasRect.left + canvas.scrollLeft;
                    const overlayTop = rect.top - canvasRect.top + canvas.scrollTop;
                    const overlayRight = overlayLeft + rect.width;
                    const overlayBottom = overlayTop + rect.height;
                    
                    minX = Math.min(minX, overlayLeft);
                    minY = Math.min(minY, overlayTop);
                    maxX = Math.max(maxX, overlayRight);
                    maxY = Math.max(maxY, overlayBottom);
                });
                
                // 如果没有找到任何元素，使用画布的尺寸
                if (minX === Infinity || minY === Infinity) {
                    minX = 0;
                    minY = 0;
                    maxX = canvas.offsetWidth;
                    maxY = canvas.offsetHeight;
                }
                
                // 添加更大的边距
                const padding = 150;
                minX = Math.max(0, minX - padding);
                minY = Math.max(0, minY - padding);
                maxX = maxX + padding;
                maxY = maxY + padding;
                
                console.log('最终计算的边界值:', { minX, minY, maxX, maxY });
                
                return {
                    left: minX,
                    top: minY,
                    right: maxX,
                    bottom: maxY,
                    width: maxX - minX,
                    height: maxY - minY
                };
            }
            
            // 初始化jsPlumb实例
            const jsPlumbInstance = jsPlumb.getInstance({
                Endpoint: ["Dot", { radius: 4 }],
                Connector: ["Flowchart", { cornerRadius: 5 }],
                PaintStyle: { 
                    stroke: "#0d6efd", 
                    strokeWidth: 2 
                },
                HoverPaintStyle: { 
                    stroke: "#1e8151", 
                    strokeWidth: 3 
                },
                Container: "canvas"
            });
            
            // 获取拓扑数据
            const topologyData = /*[[${topologyData}]]*/ '{}';
            const topology = JSON.parse(topologyData);
            
            // 加载布局
            loadTopology(topology);
            
            // 加载布局函数
            async function loadTopology(topology) {
                try {
                    console.log("加载拓扑数据:", topology);
                    
                    // 应用画布配置（如果有）
                    if (topology.canvasConfig) {
                        const canvas = document.getElementById('canvas');
                        const config = topology.canvasConfig;
                        
                        // 设置画布尺寸
                        if (config.width) {
                            canvas.style.width = `${config.width}px`;
                        }
                        
                        if (config.height) {
                            canvas.style.height = `${config.height}px`;
                        }
                        
                        // 设置背景颜色
                        if (config.backgroundColor) {
                            canvas.style.backgroundColor = config.backgroundColor;
                        }
                        
                        // 设置网格
                        if (config.showGrid !== false) {
                            canvas.style.backgroundImage = `
                                linear-gradient(${config.gridColor || '#e9ecef'} 1px, transparent 1px),
                                linear-gradient(90deg, ${config.gridColor || '#e9ecef'} 1px, transparent 1px)
                            `;
                            canvas.style.backgroundSize = `${config.gridSize || 20}px ${config.gridSize || 20}px`;
                        } else {
                            canvas.style.backgroundImage = 'none';
                        }
                    }
                    
                    // 创建设备节点
                    if (topology.nodes && topology.nodes.length > 0) {
                        // 先获取设备信息
                        const devicesResponse = await fetch('/api/devices');
                        if (!devicesResponse.ok) {
                            throw new Error('获取设备列表失败');
                        }
                        const devices = await devicesResponse.json();
                        const deviceMap = new Map();
                        devices.forEach(device => {
                            deviceMap.set(device.id, device);
                        });
                        
                        // 创建设备节点
                        topology.nodes.forEach(node => {
                            // 检查设备ID是否有效
                            if (!node.deviceId || node.deviceId === 'undefined') {
                                console.warn(`跳过无效的设备节点: ${JSON.stringify(node)}`);
                                return;
                            }
                            
                            console.log(`处理设备节点: ${node.name}, 预警项:`, node.alertItems);
                            
                            // 获取设备信息
                            const device = {
                                id: node.deviceId,
                                name: node.name || '未命名设备',
                                address: node.address || 'localhost',
                                port: node.port || '0',
                                imageUrl: node.imageUrl || null,
                                imageScale: node.imageScale || 1.0,
                                imageOffsetX: node.imageOffsetX || 0,
                                imageOffsetY: node.imageOffsetY || 0,
                                width: node.width,
                                height: node.height,
                                backgroundColor: node.backgroundColor,
                                borderColor: node.borderColor,
                                showTitle: node.showTitle,
                                alertItems: node.alertItems || []
                            };
                            
                            // 创建节点
                            const deviceNode = createDeviceNode(device, node.x, node.y);
                            
                            // 设置标题显示状态（如果需要）
                            if (deviceNode && node.showTitle === false) {
                                const titleElement = deviceNode.querySelector('.title');
                                if (titleElement) {
                                    titleElement.style.display = 'none';
                                }
                            }
                        });
                        
                        // 等待所有节点渲染完成后更新端点位置
                        setTimeout(() => {
                            document.querySelectorAll('.device-node').forEach(node => {
                                // 添加端点
                                addEndpoints(jsPlumbInstance, node.id);
                                // 重新验证节点以更新端点位置
                                jsPlumbInstance.revalidate(node.id);
                            });
                            jsPlumbInstance.repaintEverything();
                        }, 100);
                    }
                    
                    // 创建文本节点
                    if (topology.textNodes && topology.textNodes.length > 0) {
                        topology.textNodes.forEach(node => {
                            const textNode = document.createElement('div');
                            textNode.id = node.id;
                            textNode.className = 'text-node';
                            if (node.showBorder === false) {
                                textNode.classList.add('no-border');
                            }
                            textNode.setAttribute('data-component-type', 'text');
                            textNode.style.left = `${node.x}px`;
                            textNode.style.top = `${node.y}px`;
                            
                            // 应用保存的宽高
                            textNode.style.width = node.width ? `${node.width}px` : '100px';
                            textNode.style.height = node.height ? `${node.height}px` : '40px';
                            
                            const textElement = document.createElement('div');
                            textElement.className = 'editable';
                            textElement.contentEditable = 'false'; // 预览模式不可编辑
                            textElement.textContent = node.text;
                            
                            // 应用文本样式
                            if (node.fontSize) {
                                textElement.style.fontSize = `${node.fontSize}px`;
                            }
                            if (node.fontColor) {
                                textElement.style.color = node.fontColor;
                            }
                            if (node.fontWeight) {
                                textElement.style.fontWeight = node.fontWeight;
                            }
                            if (node.textAlign) {
                                textElement.style.textAlign = node.textAlign;
                            }
                            
                            textNode.appendChild(textElement);
                            document.getElementById('canvas').appendChild(textNode);
                        });
                    }
                    
                    // 创建图片节点
                    if (topology.imageNodes && topology.imageNodes.length > 0) {
                        topology.imageNodes.forEach(node => {
                            const imageNode = document.createElement('div');
                            imageNode.id = node.id;
                            imageNode.className = node.className || 'image-node';
                            imageNode.setAttribute('data-component-type', 'image');
                            imageNode.style.left = `${node.x}px`;
                            imageNode.style.top = `${node.y}px`;
                            
                            // 应用保存的宽高
                            if (node.width) {
                                imageNode.style.width = `${node.width}px`;
                            }
                            if (node.height) {
                                imageNode.style.height = `${node.height}px`;
                            }
                            
                            // 应用背景和边框颜色
                            if (node.backgroundColor) {
                                imageNode.style.backgroundColor = node.backgroundColor;
                            }
                            if (node.borderColor) {
                                imageNode.style.borderColor = node.borderColor;
                            }
                            
                            // 创建图片标题
                            const titleElement = document.createElement('div');
                            titleElement.className = 'image-title';
                            titleElement.textContent = node.title || '图片';
                            if (node.showTitle === false) {
                                titleElement.style.display = 'none';
                            }
                            
                            // 创建图片容器
                            const imageContainer = document.createElement('div');
                            imageContainer.className = 'image-container';
                            
                            // 如果有图片URL，创建图片元素
                            if (node.imageUrl) {
                                const imageElement = document.createElement('img');
                                imageElement.src = node.imageUrl;
                                imageElement.alt = node.title || '图片';
                                
                                // 设置图片缩放
                                if (node.imageScale) {
                                    imageElement.style.transform = `scale(${node.imageScale})`;
                                }
                                
                                imageContainer.appendChild(imageElement);
                            } else {
                                // 没有图片URL，显示提示
                                imageContainer.innerHTML = '<div class="no-image">无图片</div>';
                            }
                            
                            // 添加元素到节点
                            imageNode.appendChild(titleElement);
                            imageNode.appendChild(imageContainer);
                            document.getElementById('canvas').appendChild(imageNode);
                        });
                    }
                    
                    // 创建连接线
                    if (topology.connections && topology.connections.length > 0) {
                        // 等待所有节点和端点创建完成后再创建连接
                        setTimeout(() => {
                            topology.connections.forEach(conn => {
                                try {
                                    // 创建连接 - 使用UUID连接端点
                                    const connection = jsPlumbInstance.connect({
                                        uuids: [conn.sourceEndpointUuid, conn.targetEndpointUuid],
                                        paintStyle: { 
                                            stroke: conn.strokeColor || "#0d6efd", 
                                            strokeWidth: conn.strokeWidth || 2,
                                            strokeDasharray: conn.strokeDasharray || null
                                        }
                                    });
                                    
                                    // 应用动画效果
                                    if (conn.hasAnimation) {
                                        // 保存自定义属性
                                        connection.hasAnimation = conn.hasAnimation;
                                        connection.isReverseFlow = conn.isReverseFlow;
                                        connection.animationSpeed = conn.animationSpeed || 'medium';
                                        
                                        // 获取连接线的SVG容器元素
                                        const connector = connection.connector.canvas;
                                        if (connector) {
                                            // 添加动画类
                                            connector.classList.add('animated-connection');
                                            connector.classList.add('animation-' + (conn.animationSpeed || 'medium'));
                                            
                                            // 根据保存的设置应用反向流动
                                            if (conn.isReverseFlow) {
                                                connector.classList.add('reverse-flow');
                                            }
                                        }
                                    } else {
                                        // 保存自定义属性
                                        connection.hasAnimation = false;
                                        connection.isReverseFlow = false;
                                        connection.animationSpeed = 'medium';
                                    }
                                } catch (error) {
                                    console.error(`创建连接失败: ${error.message}`, conn);
                                }
                            });
                            
                            // 调整画布尺寸
                            adjustCanvasSize();
                        }, 200);
                    } else {
                        // 如果没有连接线，直接调整画布尺寸
                        setTimeout(adjustCanvasSize, 200);
                    }
                } catch (error) {
                    console.error('加载布局失败:', error);
                    alert('加载布局失败: ' + error.message);
                }
            }
            
            // 调整画布尺寸函数
            function adjustCanvasSize() {
                const canvas = document.getElementById('canvas');
                const nodes = canvas.querySelectorAll('.device-node, .text-node, .image-node');
                const connectors = canvas.querySelectorAll('.jtk-connector');
                
                if (nodes.length === 0 && connectors.length === 0) {
                    // 如果没有节点和连接线，设置一个默认尺寸
                    canvas.style.width = '100%';
                    canvas.style.height = '100%';
                    return;
                }
                
                // 找出所有节点中最右边和最底部的位置
                let maxRight = 0;
                let maxBottom = 0;
                
                // 计算节点的边界
                nodes.forEach(node => {
                    const rect = node.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();
                    
                    // 计算节点相对于画布的位置
                    const nodeRight = parseInt(node.style.left) + rect.width;
                    const nodeBottom = parseInt(node.style.top) + rect.height;
                    
                    maxRight = Math.max(maxRight, nodeRight);
                    maxBottom = Math.max(maxBottom, nodeBottom);
                });
                
                // 计算连接线的边界
                connectors.forEach(connector => {
                    const rect = connector.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();
                    
                    // 计算连接线相对于画布的位置
                    const connectorRight = rect.right - canvasRect.left;
                    const connectorBottom = rect.bottom - canvasRect.top;
                    
                    maxRight = Math.max(maxRight, connectorRight);
                    maxBottom = Math.max(maxBottom, connectorBottom);
                });
                
                // 预留额外空间（右侧和底部各预留500px）
                const extraSpace = 500;
                const newWidth = maxRight + extraSpace;
                const newHeight = maxBottom + extraSpace;
                
                console.log(`调整画布尺寸: ${newWidth}px × ${newHeight}px`);
                
                // 设置画布尺寸
                canvas.style.width = `${newWidth}px`;
                canvas.style.height = `${newHeight}px`;
                
                // 重新绘制所有连接
                jsPlumbInstance.repaintEverything();
            }
            
            // 创建设备节点
            function createDeviceNode(device, x, y) {
                // 检查设备ID是否有效
                if (!device || !device.id || device.id === 'undefined') {
                    console.error('无法创建设备节点: 无效的设备ID', device);
                    return null;
                }
                
                const nodeId = `node-${device.id}`;
                
                // 检查节点是否已存在
                if (document.getElementById(nodeId)) {
                    console.log(`设备节点 ${nodeId} 已存在，不重复创建`);
                    return document.getElementById(nodeId);
                }
                
                console.log(`创建设备节点: ${device.name} (${device.id}), 位置: ${x}, ${y}`);
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.id = nodeId;
                nodeElement.className = 'device-node';
                nodeElement.setAttribute('data-device-id', device.id);
                nodeElement.setAttribute('data-device-name', device.name);
                nodeElement.setAttribute('data-device-address', device.address);
                nodeElement.setAttribute('data-device-port', device.port);
                nodeElement.style.left = `${x}px`;
                nodeElement.style.top = `${y}px`;
                
                // 设置自定义宽高（如果有）
                if (device.width) {
                    nodeElement.style.width = `${device.width}px`;
                }
                if (device.height) {
                    nodeElement.style.height = `${device.height}px`;
                }
                
                // 设置背景和边框颜色（如果有）
                if (device.backgroundColor) {
                    nodeElement.style.backgroundColor = device.backgroundColor;
                }
                if (device.borderColor) {
                    nodeElement.style.borderColor = device.borderColor;
                }
                
                // 创建HTML内容 - 调整顺序为：图片、名称、连接状态、预警项
                let nodeContent = '';
                
                // 添加图片（如果有）
                if (device.imageUrl) {
                    nodeContent += `
                        <div class="device-image">
                            <img src="${device.imageUrl}" alt="${device.name}" style="transform: scale(${device.imageScale || 1.0}); transform-origin: center center; position: relative; left: ${device.imageOffsetX || 0}px; top: ${device.imageOffsetY || 0}px;" />
                        </div>
                    `;
                    nodeElement.classList.add('has-image');
                }
                
                // 添加标题
                nodeContent += `
                    <div class="title" ${device.showTitle === false ? 'style="display: none;"' : ''}>${device.name}</div>
                `;
                
                // 添加状态指示器
                nodeContent += `
                    <div class="status">
                        <span class="status-indicator disconnected"></span>
                        <span class="status-text">未连接</span>
                    </div>
                    <div class="alert-status-container">
                        <!-- 预警状态将动态添加到这里 -->
                    </div>
                    <div class="resize-handle"></div>
                `;
                
                // 设置节点内容
                nodeElement.innerHTML = nodeContent;
                
                // 添加预警项（如果有）
                if (device.alertItems && device.alertItems.length > 0) {
                    const alertStatusContainer = nodeElement.querySelector('.alert-status-container');
                    console.log(`添加预警项到节点 ${device.name}:`, device.alertItems);
                    
                    device.alertItems.forEach(alert => {
                        console.log(`处理预警项:`, alert);
                        
                        const alertItem = document.createElement('div');
                        alertItem.className = 'alert-status-item';
                        
                        // 确保预警项有ID和名称
                        const alertId = alert.id || alert.alertId || '';
                        const alertName = alert.name || alert.alertName || '未命名预警';
                        
                        alertItem.dataset.alertId = alertId;
                        alertItem.dataset.alertName = alertName;
                        
                        // 创建名称元素
                        const nameSpan = document.createElement('span');
                        nameSpan.className = 'alert-name';
                        nameSpan.textContent = alertName + ':';
                        alertItem.appendChild(nameSpan);
                        
                        // 创建状态徽章元素
                        const badgeSpan = document.createElement('span');
                        badgeSpan.className = 'badge badge-secondary';
                        badgeSpan.textContent = '未知';
                        alertItem.appendChild(badgeSpan);
                        
                        alertStatusContainer.appendChild(alertItem);
                        console.log(`添加预警项完成: ${alertItem.outerHTML}`);
                        
                        // 开始更新预警状态
                        if (alertId) {
                            updateAlertStatus(alertId, device.id);
                        }
                    });
                } else {
                    console.log(`节点 ${device.name} 没有预警项`);
                }
                
                // 开始更新设备连接状态
                updateDeviceConnectionStatus(device.id, device.address, device.port);
                
                // 添加到画布
                document.getElementById('canvas').appendChild(nodeElement);
                
                // 返回创建的节点元素
                return nodeElement;
            }
            
            // 添加端点
            function addEndpoints(instance, nodeId) {
                // 添加左侧端点
                instance.addEndpoint(nodeId, {
                    anchor: "Left",
                    isSource: true,
                    isTarget: true,
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    maxConnections: -1,
                    uuid: nodeId + "-left"
                });
                
                // 添加右侧端点
                instance.addEndpoint(nodeId, {
                    anchor: "Right",
                    isSource: true,
                    isTarget: true,
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    maxConnections: -1,
                    uuid: nodeId + "-right"
                });
                
                // 添加顶部端点
                instance.addEndpoint(nodeId, {
                    anchor: "Top",
                    isSource: true,
                    isTarget: true,
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    maxConnections: -1,
                    uuid: nodeId + "-top"
                });
                
                // 添加底部端点
                instance.addEndpoint(nodeId, {
                    anchor: "Bottom",
                    isSource: true,
                    isTarget: true,
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    maxConnections: -1,
                    uuid: nodeId + "-bottom"
                });
            }
            
            // 获取状态徽章HTML
            function getStatusBadge(status) {
                // 确保状态是数字
                const statusNum = parseInt(status);
                switch (statusNum) {
                    case 1:
                        return '<span class="badge badge-success">正常</span>';
                    case 2:
                        return '<span class="badge badge-warning">预警</span>';
                    case 3:
                        return '<span class="badge badge-danger">异常</span>';
                    default:
                        return '<span class="badge badge-secondary">未知</span>';
                }
            }
            
            // 更新预警状态
            async function updateAlertStatus(alertId, deviceId) {
                try {
                    console.log(`开始更新预警状态: alertId=${alertId}, deviceId=${deviceId}`);
                    
                    // 首先检查设备是否连接
                    const deviceResponse = await fetch(`/api/device/${deviceId}`);
                    if (!deviceResponse.ok) {
                        throw new Error(`获取设备状态失败: ${deviceResponse.status} ${deviceResponse.statusText}`);
                    }
                    
                    const deviceData = await deviceResponse.json();
                    console.log(`设备 ${deviceId} 连接状态:`, deviceData.connected);
                    
                    // 如果设备未连接，将预警状态设置为未知
                    if (!deviceData.connected) {
                        console.log(`设备 ${deviceId} 未连接，预警 ${alertId} 状态设置为未知`);
                        // 更新节点上的预警状态为未知
                        const nodeElement = document.getElementById(`node-${deviceId}`);
                        if (nodeElement) {
                            const alertItem = nodeElement.querySelector(`.alert-status-item[data-alert-id="${alertId}"]`);
                            if (alertItem) {
                                // 查找徽章元素
                                const badgeElement = alertItem.querySelector('.badge');
                                if (badgeElement) {
                                    // 更新徽章为未知状态
                                    badgeElement.outerHTML = '<span class="badge badge-secondary">未知</span>';
                                    console.log(`设备未连接，更新预警状态为未知: ${alertItem.outerHTML}`);
                                }
                            }
                        }
                        
                        // 3秒后再次更新
                        setTimeout(() => updateAlertStatus(alertId, deviceId), 3000);
                        return;
                    }
                    
                    // 设备已连接，获取预警状态
                    const response = await fetch(`/api/alerts/${alertId}/status`);
                    if (!response.ok) {
                        throw new Error(`获取预警状态失败: ${response.status} ${response.statusText}`);
                    }
                    
                    const statusData = await response.json();
                    console.log(`获取到预警状态:`, statusData);
                    
                    // 更新节点上的预警状态
                    const nodeElement = document.getElementById(`node-${deviceId}`);
                    if (nodeElement) {
                        const alertItem = nodeElement.querySelector(`.alert-status-item[data-alert-id="${alertId}"]`);
                        if (alertItem) {
                            // 查找徽章元素
                            const badgeElement = alertItem.querySelector('.badge');
                            if (badgeElement) {
                                // 更新徽章
                                badgeElement.outerHTML = getStatusBadge(statusData.status);
                                console.log(`更新预警状态成功: ${alertItem.outerHTML}`);
                            }
                        }
                    }
                    
                    // 3秒后再次更新
                    setTimeout(() => updateAlertStatus(alertId, deviceId), 3000);
                } catch (error) {
                    console.error(`更新预警状态失败:`, error);
                    // 发生错误时，10秒后重试
                    setTimeout(() => updateAlertStatus(alertId, deviceId), 10000);
                }
            }
            
            // 更新设备连接状态
            async function updateDeviceConnectionStatus(deviceId, address, port) {
                // 检查设备ID是否有效
                if (!deviceId || deviceId === 'undefined') {
                    console.error('无法更新设备状态: 无效的设备ID');
                    return;
                }
                
                console.log(`正在获取设备 ${deviceId} 的状态...`);
                
                // 添加超时处理
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('获取设备状态超时')), 5000);
                });
                
                // 实际的fetch请求
                const fetchPromise = fetch(`/api/device/${deviceId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`获取设备状态失败，状态码: ${response.status}`);
                        }
                        return response.json();
                    });
                
                // 使用Promise.race来处理超时
                Promise.race([fetchPromise, timeoutPromise])
                    .then(device => {
                        const nodeId = `node-${device.id}`;
                        const node = document.getElementById(nodeId);
                        if (node) {
                            const statusIndicator = node.querySelector('.status-indicator');
                            const statusText = node.querySelector('.status-text');
                            
                            statusIndicator.className = `status-indicator ${device.connected ? 'connected' : 'disconnected'}`;
                            statusText.textContent = device.connected ? '已连接' : '未连接';
                            
                            console.log(`设备 ${deviceId} 状态更新为: ${device.connected ? '已连接' : '未连接'}`);
                        }
                        
                        // 2秒后再次更新
                        setTimeout(() => updateDeviceConnectionStatus(deviceId, address, port), 2000);
                    })
                    .catch(error => {
                        console.error(`获取设备 ${deviceId} 状态失败:`, error);
                        
                        // 在失败后尝试重新获取（最多重试3次）
                        const retryCount = parseInt(localStorage.getItem(`retry_${deviceId}`) || '0');
                        if (retryCount < 3) {
                            localStorage.setItem(`retry_${deviceId}`, (retryCount + 1).toString());
                            console.log(`正在重试获取设备 ${deviceId} 状态，第 ${retryCount + 1} 次尝试...`);
                            setTimeout(() => updateDeviceConnectionStatus(deviceId, address, port), 2000);
                        } else {
                            localStorage.removeItem(`retry_${deviceId}`);
                            console.error(`获取设备 ${deviceId} 状态失败，已达到最大重试次数`);
                            
                            // 更新为未连接状态
                            const nodeElement = document.getElementById(`node-${deviceId}`);
                            if (nodeElement) {
                                const statusIndicator = nodeElement.querySelector('.status-indicator');
                                const statusText = nodeElement.querySelector('.status-text');
                                
                                if (statusIndicator && statusText) {
                                    statusIndicator.className = 'status-indicator disconnected';
                                    statusText.textContent = '未连接';
                                }
                            }
                            
                            // 10秒后重新开始尝试
                            setTimeout(() => {
                                localStorage.removeItem(`retry_${deviceId}`);
                                updateDeviceConnectionStatus(deviceId, address, port);
                            }, 10000);
                        }
                    });
            }
        });
    </script>
</body>
</html> 