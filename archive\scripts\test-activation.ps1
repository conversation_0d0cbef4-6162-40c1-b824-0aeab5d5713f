# 测试激活功能的PowerShell脚本

Write-Host "=== 测试序列号许可系统激活功能 ===" -ForegroundColor Green

# 测试数据
$userId = "USER001"
$serialNumber = "SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP"
$url = "http://localhost:8080/license/activate"

Write-Host "用户ID: $userId" -ForegroundColor Yellow
Write-Host "序列号: $serialNumber" -ForegroundColor Yellow
Write-Host "激活URL: $url" -ForegroundColor Yellow
Write-Host ""

# 创建POST请求的数据
$body = @{
    userId = $userId
    serialNumber = $serialNumber
}

try {
    Write-Host "发送激活请求..." -ForegroundColor Cyan
    
    # 发送POST请求
    $response = Invoke-WebRequest -Uri $url -Method POST -Body $body -ContentType "application/x-www-form-urlencoded" -UseBasicParsing
    
    Write-Host "响应状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response.Content)" -ForegroundColor Green
    
    # 检查是否生成了许可文件
    if (Test-Path "license.json") {
        Write-Host "✅ 许可文件已生成!" -ForegroundColor Green
        $licenseContent = Get-Content "license.json" -Raw
        Write-Host "许可文件内容:" -ForegroundColor Yellow
        Write-Host $licenseContent -ForegroundColor White
    } else {
        Write-Host "❌ 许可文件未生成" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 激活请求失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.Exception)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
