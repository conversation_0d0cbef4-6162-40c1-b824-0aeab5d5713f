-- 使用存储过程来添加列（如果不存在）
DELIMITER //

CREATE PROCEDURE add_columns_if_not_exist()
BEGIN
    -- 检查source_item_id列是否存在
    IF NOT EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'device_conditions' 
        AND COLUMN_NAME = 'source_item_id'
    ) THEN
        ALTER TABLE device_conditions 
        ADD COLUMN source_item_id VARCHAR(50);
    END IF;
    
    -- 检查target_item_id列是否存在
    IF NOT EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'device_conditions' 
        AND COLUMN_NAME = 'target_item_id'
    ) THEN
        ALTER TABLE device_conditions 
        ADD COLUMN target_item_id VARCHAR(50);
    END IF;
END //

DELIMITER ;

-- 执行存储过程
CALL add_columns_if_not_exist();

-- 删除存储过程
DROP PROCEDURE IF EXISTS add_columns_if_not_exist;

-- 更新数据（将别名关联的数据项ID复制到新列）
UPDATE device_conditions dc
SET source_item_id = (
    SELECT data_item_id 
    FROM address_aliases 
    WHERE id = dc.source_alias_id
),
target_item_id = (
    SELECT data_item_id 
    FROM address_aliases 
    WHERE id = dc.target_alias_id
)
WHERE source_item_id IS NULL OR target_item_id IS NULL;

-- 添加外键约束（如果不存在）
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_NAME = 'fk_device_conditions_source_item'
    AND TABLE_NAME = 'device_conditions'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE device_conditions
    ADD CONSTRAINT fk_device_conditions_source_item
    FOREIGN KEY (source_item_id) 
    REFERENCES data_items(id)',
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_NAME = 'fk_device_conditions_target_item'
    AND TABLE_NAME = 'device_conditions'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE device_conditions
    ADD CONSTRAINT fk_device_conditions_target_item
    FOREIGN KEY (target_item_id) 
    REFERENCES data_items(id)',
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除所有引用source_alias_id的外键约束
DELIMITER //
CREATE PROCEDURE drop_source_alias_foreign_keys()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE constraint_name VARCHAR(64);
    DECLARE table_name VARCHAR(64);
    DECLARE cur CURSOR FOR
        SELECT DISTINCT k.CONSTRAINT_NAME, k.TABLE_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE k
        WHERE k.REFERENCED_TABLE_NAME = 'device_conditions'
        AND k.REFERENCED_COLUMN_NAME = 'source_alias_id'
        AND k.CONSTRAINT_SCHEMA = DATABASE();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO constraint_name, table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' DROP FOREIGN KEY ', constraint_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE cur;
END //
DELIMITER ;

CALL drop_source_alias_foreign_keys();
DROP PROCEDURE IF EXISTS drop_source_alias_foreign_keys;

-- 删除所有引用target_alias_id的外键约束
DELIMITER //
CREATE PROCEDURE drop_target_alias_foreign_keys()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE constraint_name VARCHAR(64);
    DECLARE table_name VARCHAR(64);
    DECLARE cur CURSOR FOR
        SELECT DISTINCT k.CONSTRAINT_NAME, k.TABLE_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE k
        WHERE k.REFERENCED_TABLE_NAME = 'device_conditions'
        AND k.REFERENCED_COLUMN_NAME = 'target_alias_id'
        AND k.CONSTRAINT_SCHEMA = DATABASE();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO constraint_name, table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' DROP FOREIGN KEY ', constraint_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END LOOP;
    CLOSE cur;
END //
DELIMITER ;

CALL drop_target_alias_foreign_keys();
DROP PROCEDURE IF EXISTS drop_target_alias_foreign_keys;

-- 删除device_conditions表中的外键约束
SET @constraint_exists = (
    SELECT GROUP_CONCAT(CONSTRAINT_NAME)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_NAME = 'device_conditions'
    AND COLUMN_NAME IN ('source_alias_id', 'target_alias_id')
    AND CONSTRAINT_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL
);

SET @sql = CONCAT('ALTER TABLE device_conditions ',
    (SELECT GROUP_CONCAT('DROP FOREIGN KEY ', CONSTRAINT_NAME)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_NAME = 'device_conditions'
    AND COLUMN_NAME IN ('source_alias_id', 'target_alias_id')
    AND CONSTRAINT_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除旧列
ALTER TABLE device_conditions
DROP COLUMN source_alias_id,
DROP COLUMN target_alias_id; 