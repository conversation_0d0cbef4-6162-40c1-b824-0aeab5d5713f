import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试新序列号格式的生成和验证
 */
public class TestNewSerialFormat {
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String DATE_FORMAT = "yyyyMMdd";


    public static void main(String[] args) {
        try {
            System.out.println("=== 测试新序列号格式 ===");
            
            // 测试数据
            String userId = "USER001";
            LocalDate today = LocalDate.now();
            String startDate = today.format(DateTimeFormatter.ofPattern(DATE_FORMAT));
            String endDate = today.plusDays(30).format(DateTimeFormatter.ofPattern(DATE_FORMAT));
            String licenseStartDate = today.format(DateTimeFormatter.ofPattern(DATE_FORMAT));
            String licenseEndDate = today.plusYears(1).format(DateTimeFormatter.ofPattern(DATE_FORMAT));
            
            System.out.println("用户ID: " + userId);
            System.out.println("激活时间窗口: " + startDate + " 到 " + endDate);
            System.out.println("许可使用期限: " + licenseStartDate + " 到 " + licenseEndDate);
            System.out.println();
            
            // 生成新格式序列号
            String serialNumber = generateSerialNumber(userId, startDate, endDate, licenseStartDate, licenseEndDate);
            System.out.println("生成的序列号:");
            System.out.println(serialNumber);
            System.out.println();
            
            // 解析序列号
            Map<String, String> data = parseSerialNumber(serialNumber);
            System.out.println("解析结果:");
            for (Map.Entry<String, String> entry : data.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
            System.out.println();
            
            // 测试激活验证
            boolean activationValid = validateSerialNumberForActivation(userId, serialNumber);
            System.out.println("激活验证结果: " + (activationValid ? "通过" : "失败"));
            
            // 测试使用期限验证
            boolean usageValid = validateSerialNumberForUsage(userId, serialNumber);
            System.out.println("使用期限验证结果: " + (usageValid ? "通过" : "失败"));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    // 生成新格式序列号
    public static String generateSerialNumber(String userId, String startDate, String endDate, 
                                            String licenseStartDate, String licenseEndDate) {
        try {
            String checksum = generateChecksum(userId + startDate + endDate + licenseStartDate + licenseEndDate);
            
            // 手动构造JSON字符串
            String json = String.format(
                "{\"userId\":\"%s\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"licenseStartDate\":\"%s\",\"licenseEndDate\":\"%s\",\"checksum\":\"%s\"}",
                userId, startDate, endDate, licenseStartDate, licenseEndDate, checksum
            );
            byte[] encrypted = aesEncrypt(json);
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);
            
        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败", e);
        }
    }
    
    // 解析序列号
    public static Map<String, String> parseSerialNumber(String serialNumber) {
        try {
            if (!serialNumber.startsWith(SERIAL_PREFIX)) {
                throw new IllegalArgumentException("无效的序列号格式");
            }
            
            String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
            byte[] encrypted = Base64.getDecoder().decode(base64Data);
            String json = aesDecrypt(encrypted);

            return parseJsonManually(json);
            
        } catch (Exception e) {
            throw new RuntimeException("解析序列号失败", e);
        }
    }
    
    // 验证激活时间窗口
    public static boolean validateSerialNumberForActivation(String inputUserId, String serialNumber) {
        try {
            Map<String, String> data = parseSerialNumber(serialNumber);
            
            String serialUserId = data.get("userId");
            String startDate = data.get("startDate");
            String endDate = data.get("endDate");
            String checksum = data.get("checksum");
            
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }
            
            LocalDate today = LocalDate.now();
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            
            if (today.isBefore(start) || today.isAfter(end)) {
                return false;
            }
            
            String expectedChecksum;
            if (data.containsKey("licenseStartDate") && data.containsKey("licenseEndDate")) {
                String licenseStartDate = data.get("licenseStartDate");
                String licenseEndDate = data.get("licenseEndDate");
                expectedChecksum = generateChecksum(serialUserId + startDate + endDate + licenseStartDate + licenseEndDate);
            } else {
                expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
            }
            return checksum.equals(expectedChecksum);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    // 验证许可使用期限
    public static boolean validateSerialNumberForUsage(String inputUserId, String serialNumber) {
        try {
            Map<String, String> data = parseSerialNumber(serialNumber);
            
            String serialUserId = data.get("userId");
            String checksum = data.get("checksum");
            
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }
            
            if (data.containsKey("licenseStartDate") && data.containsKey("licenseEndDate")) {
                String licenseStartDate = data.get("licenseStartDate");
                String licenseEndDate = data.get("licenseEndDate");
                
                LocalDate today = LocalDate.now();
                LocalDate licenseStart = LocalDate.parse(licenseStartDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
                LocalDate licenseEnd = LocalDate.parse(licenseEndDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
                
                if (today.isBefore(licenseStart) || today.isAfter(licenseEnd)) {
                    return false;
                }
                
                String startDate = data.get("startDate");
                String endDate = data.get("endDate");
                String expectedChecksum = generateChecksum(serialUserId + startDate + endDate + licenseStartDate + licenseEndDate);
                return checksum.equals(expectedChecksum);
            } else {
                // 旧格式，回退到激活时间验证
                return validateSerialNumberForActivation(inputUserId, serialNumber);
            }
            
        } catch (Exception e) {
            return false;
        }
    }
    
    // AES加密
    private static byte[] aesEncrypt(String data) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(data.getBytes("UTF-8"));
    }
    
    // AES解密
    private static String aesDecrypt(byte[] encryptedData) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(encryptedData);
        return new String(decrypted, "UTF-8");
    }
    
    // 手动解析JSON
    private static Map<String, String> parseJsonManually(String json) {
        Map<String, String> result = new HashMap<>();

        // 简单的JSON解析（仅适用于我们的格式）
        json = json.trim();
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1);
            String[] pairs = json.split(",");

            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim().replaceAll("\"", "");
                    result.put(key, value);
                }
            }
        }

        return result;
    }

    // 生成校验码
    private static String generateChecksum(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < Math.min(3, hash.length); i++) {
                sb.append(String.format("%02X", hash[i] & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成校验码失败", e);
        }
    }
}
