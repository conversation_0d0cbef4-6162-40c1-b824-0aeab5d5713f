<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>透明科技边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: transparent;
            font-family: 'Courier New', monospace;
        }
        
        .transparent-border-box {
            position: relative;
            width: 420px;
            height: 300px;
            background: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        /* 主边框 */
        .border-frame {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid;
            border-image: linear-gradient(45deg, #00d4ff, #0099ff, #00ffff, #00d4ff) 1;
            animation: border-flow 4s linear infinite;
        }
        
        /* 四个角的装饰 */
        .corner {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #00ffff;
            z-index: 2;
        }
        
        .corner-tl {
            top: -2px;
            left: -2px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner-tr {
            top: -2px;
            right: -2px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner-bl {
            bottom: -2px;
            left: -2px;
            border-right: none;
            border-top: none;
        }
        
        .corner-br {
            bottom: -2px;
            right: -2px;
            border-left: none;
            border-top: none;
        }
        
        /* 角落发光点 */
        .corner::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background: #00ffff;
            border-radius: 50%;
            animation: dot-pulse 2s ease-in-out infinite;
            box-shadow: 0 0 10px #00ffff;
        }
        
        .corner-tl::after { top: 2px; left: 2px; }
        .corner-tr::after { top: 2px; right: 2px; }
        .corner-bl::after { bottom: 2px; left: 2px; }
        .corner-br::after { bottom: 2px; right: 2px; }
        
        /* 扫描线 */
        .scan-effect {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(0, 212, 255, 0.1) 25%,
                rgba(0, 255, 255, 0.3) 50%,
                rgba(0, 212, 255, 0.1) 75%,
                transparent 100%);
            animation: scan-move 3s linear infinite;
            z-index: 1;
        }
        
        /* 侧边指示器 */
        .side-indicator {
            position: absolute;
            width: 4px;
            height: 60px;
            background: linear-gradient(to bottom,
                transparent 0%,
                #00d4ff 20%,
                #00ffff 50%,
                #00d4ff 80%,
                transparent 100%);
            animation: indicator-pulse 3s ease-in-out infinite;
            z-index: 2;
        }
        
        .indicator-left {
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .indicator-right {
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            animation-delay: 1.5s;
        }
        
        /* 内容区域 - 完全透明 */
        .content {
            background: transparent;
            padding: 40px 30px;
            text-align: center;
            color: #00d4ff;
            width: calc(100% - 60px);
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 10;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #00d4ff, #00ffff, #66ddff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            letter-spacing: 2px;
            animation: title-glow 4s ease-in-out infinite;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
        }
        
        .subtitle {
            color: #66ddff;
            font-size: 0.9rem;
            margin-bottom: 15px;
            opacity: 0.9;
            letter-spacing: 1px;
            text-shadow: 0 0 10px rgba(102, 221, 255, 0.6);
        }
        
        p {
            color: #99ccff;
            line-height: 1.8;
            font-size: 0.95rem;
            opacity: 1;
            text-shadow: 0 0 8px rgba(153, 204, 255, 0.5);
        }
        
        /* 动画定义 */
        @keyframes border-flow {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(360deg); }
        }
        
        @keyframes dot-pulse {
            0%, 100% { 
                opacity: 0.6;
                transform: scale(1);
                box-shadow: 0 0 5px #00ffff;
            }
            50% { 
                opacity: 1;
                transform: scale(1.2);
                box-shadow: 0 0 15px #00ffff, 0 0 25px #00ffff;
            }
        }
        
        @keyframes scan-move {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        @keyframes indicator-pulse {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 1; }
        }
        
        @keyframes title-glow {
            0%, 100% { 
                filter: brightness(1) drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
            }
            50% { 
                filter: brightness(1.3) drop-shadow(0 0 15px rgba(0, 255, 255, 0.8));
            }
        }
        
        /* 悬停效果 */
        .transparent-border-box:hover .corner {
            animation: corner-hover 0.5s ease-in-out;
        }
        
        .transparent-border-box:hover .scan-effect {
            animation-duration: 1.5s;
        }
        
        @keyframes corner-hover {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .transparent-border-box {
                width: 360px;
                height: 260px;
            }
            
            h1 {
                font-size: 1.6rem;
            }
            
            .corner {
                width: 30px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="transparent-border-box">
        <div class="border-frame"></div>
        
        <div class="corner corner-tl"></div>
        <div class="corner corner-tr"></div>
        <div class="corner corner-bl"></div>
        <div class="corner corner-br"></div>
        
        <div class="scan-effect"></div>
        
        <div class="side-indicator indicator-left"></div>
        <div class="side-indicator indicator-right"></div>
        
        <div class="content">
            <h1>透明边框</h1>
            <div class="subtitle">科技感动态边框</div>
            <p>完全透明的背景，只保留彩色的动态边框效果和发光装饰。</p>
        </div>
    </div>
</body>
</html>
