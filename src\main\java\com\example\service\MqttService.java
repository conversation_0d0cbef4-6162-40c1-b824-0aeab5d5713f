package com.example.service;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class MqttService {
    private final MqttConnectOptions mqttConnectOptions;
    private MqttClient mqttClient;
    private String brokerUrl;
    private final AtomicBoolean isConnecting = new AtomicBoolean(false);
    private static final int RECONNECT_DELAY = 5000; // 5秒重连延迟

    public MqttService(MqttConnectOptions mqttConnectOptions) {
        this.mqttConnectOptions = mqttConnectOptions;
    }

    public synchronized void connect(String brokerUrl) throws MqttException {
        this.brokerUrl = brokerUrl;
        String clientId = UUID.randomUUID().toString();
        mqttClient = new MqttClient(brokerUrl, clientId);
        
        // 设置连接丢失回调
        mqttClient.setCallback(new MqttCallbackExtended() {
            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                log.info("MQTT {}连接成功: {}", reconnect ? "重新" : "", serverURI);
                isConnecting.set(false);
            }

            @Override
            public void connectionLost(Throwable cause) {
                log.warn("MQTT连接丢失: {}", cause.getMessage());
                scheduleReconnect();
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) {
                // 不处理接收到的消息
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                // 不处理消息发送完成事件
            }
        });

        doConnect();
    }

    private void doConnect() throws MqttException {
        try {
            mqttClient.connect(mqttConnectOptions);
            log.info("MQTT连接成功: {}", brokerUrl);
        } catch (MqttException e) {
            log.error("MQTT连接失败: {}", e.getMessage());
            throw e;
        }
    }

    private void scheduleReconnect() {
        if (isConnecting.compareAndSet(false, true)) {
            new Thread(() -> {
                while (isConnecting.get() && !mqttClient.isConnected()) {
                    try {
                        Thread.sleep(RECONNECT_DELAY);
                        log.info("尝试重新连接MQTT...");
                        doConnect();
                    } catch (Exception e) {
                        log.warn("MQTT重连失败: {}", e.getMessage());
                    }
                }
            }).start();
        }
    }

    public synchronized void disconnect() {
        if (mqttClient != null && mqttClient.isConnected()) {
            try {
                isConnecting.set(false); // 停止重连
                mqttClient.disconnect();
                log.info("MQTT断开连接成功");
            } catch (MqttException e) {
                log.error("MQTT断开连接失败: {}", e.getMessage());
            }
        }
    }

    public void publish(String topic, String message) throws MqttException {
        if (mqttClient == null || !mqttClient.isConnected()) {
            log.debug("MQTT未连接，跳过消息发布");
            return;
        }
        MqttMessage mqttMessage = new MqttMessage(message.getBytes());
        mqttClient.publish(topic, mqttMessage);
    }

    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    public void subscribe(String topic, IMqttMessageListener messageListener) throws MqttException {
        if (mqttClient == null || !mqttClient.isConnected()) {
            throw new MqttException(MqttException.REASON_CODE_CLIENT_NOT_CONNECTED);
        }
        mqttClient.subscribe(topic, messageListener);
    }
} 