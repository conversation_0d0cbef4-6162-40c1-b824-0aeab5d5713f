<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>霓虹玻璃边框效果</title>
    <style>
        :root {
            --hue1: 255;
            --hue2: 222;
            --border: 1px;
            --border-color: hsl(var(--hue2), 12%, 20%);
            --radius: 22px;
            --ease: cubic-bezier(0.5, 1, 0.89, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .neon-border {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: var(--radius);
            border: var(--border) solid var(--border-color);
            background: linear-gradient(235deg, hsl(var(--hue1) 50% 10% / 0.8), hsl(var(--hue1) 50% 10% / 0) 33%), 
                        linear-gradient(45deg , hsl(var(--hue2) 50% 10% / 0.8), hsl(var(--hue2) 50% 10% / 0) 33%), 
                        linear-gradient(hsl(220deg 25% 4.8% / 0.66));
            backdrop-filter: blur(12px);
            box-shadow: hsl(var(--hue2) 50% 2%) 0px 10px 16px -8px, hsl(var(--hue2) 50% 4%) 0px 20px 36px -14px;
        }

        .neon-border .shine,
        .neon-border .glow {
            animation: glow 1s var(--ease) both;
        }

        .neon-border .shine {
            animation-delay: 0s;
            animation-duration: 2s;
        }

        .neon-border .glow {
            animation-delay: 0.2s;
        }

        .neon-border .glow-bright {
            animation-delay: 0.1s;
            animation-duration: 1.5s;
        }

        .neon-border .shine-bottom {
            animation-delay: 0.1s;
            animation-duration: 1.8s;
        }

        .neon-border .glow-bottom {
            animation-delay: 0.3s;
        }

        .neon-border .glow-bright.glow-bottom {
            animation-delay: 0.3s;
            animation-duration: 1.1s;
        }

        .neon-border .shine,
        .neon-border .glow {
            --hue: var(--hue1);
        }

        .neon-border .shine-bottom,
        .neon-border .glow-bottom {
            --hue: var(--hue2);
            --conic: 135deg;
        }

        .neon-border .shine,
        .neon-border .shine::before,
        .neon-border .shine::after {
            pointer-events: none;
            border-radius: var(--radius);
            border: 1px solid transparent;
            width: 60%;
            height: auto;
            min-height: 0px;
            aspect-ratio: 1;
            display: block;
            position: absolute;
            right: 0;
            top: 0;
            left: auto;
            z-index: 1;
            --start: 12%;
            background: conic-gradient(
                from var(--conic, -45deg) at center in oklch,
                transparent var(--start,0%), hsl( var(--hue), var(--sat,80%), var(--lit,60%)), transparent  var(--end,50%)
            ) border-box;
            mask:
                linear-gradient(transparent),
                linear-gradient(black);
            mask-repeat: no-repeat;
            mask-clip: padding-box, border-box;
            mask-composite: subtract;
        }

        .neon-border .shine::before,
        .neon-border .shine::after {
            content: "";
            width: auto;
            inset: -2px;
            mask: none;
        }

        .neon-border .shine::after { 
            z-index: 2;
            --start: 17%;
            --end: 33%;
            background: conic-gradient(
                from var(--conic, -45deg) at center in oklch,
                transparent var(--start,0%), hsl( var(--hue), var(--sat,80%), var(--lit,85%)), transparent var(--end,50%) 
            );
        }

        .neon-border .shine-bottom {
            top: auto;
            bottom: 0;
            left: 0;
            right: auto;
        }

        .neon-border .glow {
            pointer-events: none;
            border-radius: calc(var(--radius) * 1.5);
            border: calc(var(--radius) * 0.8) solid transparent;
            inset: calc(var(--radius) * -1);
            width: 60%;
            height: auto;
            min-height: 0px;
            aspect-ratio: 1;
            display: block;
            position: absolute;
            right: calc(var(--radius) * -0.5);
            top: calc(var(--radius) * -0.5);
            left: auto;
            bottom: auto;
            opacity: 1;
            filter: blur(8px) saturate(1.25) brightness(0.5);
            mix-blend-mode: plus-lighter;
            z-index: 3;
        }

        .neon-border .glow.glow-bottom {
            top: auto;
            right: auto;
            bottom: calc(var(--radius) * -0.5);
            left: calc(var(--radius) * -0.5);
        }

        .neon-border .glow::before, 
        .neon-border .glow::after {
            content: "";
            position: absolute;
            inset: 0;
            border: inherit;
            border-radius: inherit;
            background: conic-gradient(
                from var(--conic, -45deg) at center in oklch,
                transparent var(--start,0%), hsl( var(--hue), var(--sat,95%), var(--lit,60%)), transparent  var(--end,50%) 
            ) border-box;
            mask: 
                linear-gradient(transparent), 
                linear-gradient(black);
            mask-repeat: no-repeat;
            mask-clip: padding-box, border-box;
            mask-composite: subtract;
            filter: saturate(2) brightness(1);
        }

        .neon-border .glow::after {
            --lit: 70%;
            --sat: 100%;
            --start: 15%;
            --end: 35%;
            border-width: calc(var(--radius) * 1.75);
            border-radius: calc(var(--radius) * 2.75);
            inset: calc(var(--radius) * -0.25);
            z-index: 4;
            opacity: 0.75;
        }

        .neon-border .glow-bright {
            --lit: 80%;
            --sat: 100%;
            --start: 13%;
            --end: 37%;
            border-width: 3px;
            border-radius: var(--radius);
            inset: -5px;
            right: -3px;
            top: -3px;
            left: auto;
            filter: blur(2px) brightness(0.66);
        }

        .neon-border .glow-bright::after {
            content: none;
        }

        .neon-border .glow-bright.glow-bottom {
            top: auto;
            right: auto;
            bottom: -3px;
            left: -3px;
        }

        @keyframes glow {
            0% {
                opacity: 0;
            }
            3% {
                opacity: 1;
            }
            10% {
                opacity: 0;
            }
            12% {
                opacity: 0.7;
            }
            16% {
                opacity: 0.3;
                animation-timing-function: var(--ease);
            }
            100% {
                opacity: 1;
                animation-timing-function: var(--ease);
            }
        }
    </style>
</head>
<body>
    <div class="neon-border">
        <span class="shine shine-top"></span>
        <span class="shine shine-bottom"></span>
        <span class="glow glow-top"></span>
        <span class="glow glow-bottom"></span>
        <span class="glow glow-bright glow-top"></span>
        <span class="glow glow-bright glow-bottom"></span>
    </div>
</body>
</html>
