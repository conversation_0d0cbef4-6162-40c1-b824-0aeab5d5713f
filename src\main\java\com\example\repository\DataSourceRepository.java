package com.example.repository;

import com.example.entity.DataSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据源Repository
 */
@Repository
public interface DataSourceRepository extends JpaRepository<DataSource, String> {
    
    /**
     * 根据类型查找数据源
     */
    List<DataSource> findByType(String type);
    
    /**
     * 根据启用状态查找数据源
     */
    List<DataSource> findByEnabled(Boolean enabled);
    
    /**
     * 根据类型和启用状态查找数据源
     */
    List<DataSource> findByTypeAndEnabled(String type, Boolean enabled);
    
    /**
     * 根据名称模糊查询
     */
    List<DataSource> findByNameContainingIgnoreCase(String name);
    
    /**
     * 根据状态查找数据源
     */
    List<DataSource> findByStatus(String status);
    
    /**
     * 统计各类型数据源数量
     */
    @Query("SELECT d.type, COUNT(d) FROM DataSource d GROUP BY d.type")
    List<Object[]> countByType();
    
    /**
     * 统计各状态数据源数量
     */
    @Query("SELECT d.status, COUNT(d) FROM DataSource d GROUP BY d.status")
    List<Object[]> countByStatus();
    
    /**
     * 检查名称是否已存在（排除指定ID）
     */
    @Query("SELECT COUNT(d) > 0 FROM DataSource d WHERE d.name = :name AND d.id != :excludeId")
    boolean existsByNameAndIdNot(@Param("name") String name, @Param("excludeId") String excludeId);
    
    /**
     * 检查名称是否已存在
     */
    boolean existsByName(String name);
}
