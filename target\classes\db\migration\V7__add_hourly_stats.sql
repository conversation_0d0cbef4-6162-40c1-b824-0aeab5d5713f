-- 添加小时级数据统计表
-- 执行时间：2024-12-29

-- 创建小时级统计数据表
CREATE TABLE IF NOT EXISTS data_hourly_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_item_id VARCHAR(36) NOT NULL COMMENT '监控项ID',
    data_item_name VARCHAR(100) NOT NULL COMMENT '监控项名称',
    data_item_address VARCHAR(100) NOT NULL COMMENT '监控项地址',
    device_id VARCHAR(36) NOT NULL COMMENT '设备ID',
    device_name VARCHAR(100) NOT NULL COMMENT '设备名称',
    hour_timestamp DATETIME NOT NULL COMMENT '统计小时的开始时间，如2024-01-01 09:00:00',
    max_value INT NOT NULL COMMENT '该小时内的最大值',
    min_value INT NOT NULL COMMENT '该小时内的最小值',
    avg_value DECIMAL(10,2) NOT NULL COMMENT '该小时内的平均值',
    diff_value INT COMMENT '与前一小时最大值的差值',
    sample_count INT NOT NULL COMMENT '该小时内的采样次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (data_item_id) REFERENCES data_items(id) ON DELETE CASCADE,
    INDEX idx_data_item_hour (data_item_id, hour_timestamp),
    INDEX idx_hour_timestamp (hour_timestamp),
    UNIQUE KEY uk_item_hour (data_item_id, hour_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级数据统计表';
