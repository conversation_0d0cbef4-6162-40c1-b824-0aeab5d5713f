<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版简洁装饰效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;           /* ✅ 透明背景 */
            width: 100%;                      /* ✅ 宽度100% */
            height: 100vh;                    /* ✅ 高度100vh */
            overflow: hidden;                 /* ✅ 防止滚动条 */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            width: 100%;                      /* ✅ 容器100%宽度 */
            height: 100%;                     /* ✅ 容器100%高度 */
            padding: 3%;                      /* ✅ 适度内边距 */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .decoration-wrapper {
            position: relative;
            width: 100%;                      /* ✅ 修复：100%宽度而不是固定400px */
            height: 100%;                     /* ✅ 修复：100%高度而不是固定300px */
            background: transparent;
            min-width: 80px;                  /* ✅ 最小宽度保证可见性 */
            min-height: 40px;                 /* ✅ 最小高度保证可见性 */
        }
        
        /* 角落装饰 */
        .corner-decoration {
            position: absolute;
            width: 8%;                        /* ✅ 修复：使用百分比而不是固定40px */
            height: 8%;                       /* ✅ 修复：使用百分比而不是固定40px */
            max-width: 60px;                  /* ✅ 最大尺寸限制 */
            max-height: 60px;
            min-width: 20px;                  /* ✅ 最小尺寸保证可见性 */
            min-height: 20px;
            border: 0.3vh solid #00ffff;      /* ✅ 修复：使用vh单位 */
        }
        
        .corner-tl {
            top: 2%;                          /* ✅ 修复：使用百分比 */
            left: 2%;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }
        
        .corner-tr {
            top: 2%;
            right: 2%;
            border-left: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate 0.5s;
        }
        
        .corner-bl {
            bottom: 2%;
            left: 2%;
            border-right: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }
        
        .corner-br {
            bottom: 2%;
            right: 2%;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1.5s;
        }
        
        @keyframes corner-glow {
            0% {
                box-shadow: 0 0 0.5vh #00ffff;   /* ✅ 修复：使用vh单位 */
                border-color: #00ffff;
            }
            100% {
                box-shadow: 0 0 2vh #00ffff, 0 0 3vh #00ffff;  /* ✅ 修复：使用vh单位 */
                border-color: #66ffff;
            }
        }
        
        /* 中心装饰圆环 */
        .center-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .ring {
            position: absolute;
            border: 0.2vh solid rgba(255, 0, 204, 0.6);  /* ✅ 修复：使用vh单位 */
            border-radius: 50%;
            animation: ring-pulse 3s ease-in-out infinite;
        }
        
        .ring-1 {
            width: 15%;                       /* ✅ 修复：使用百分比 */
            height: 15%;
            top: -7.5%;                       /* ✅ 修复：使用百分比 */
            left: -7.5%;
            animation-delay: 0s;
        }
        
        .ring-2 {
            width: 25%;                       /* ✅ 修复：使用百分比 */
            height: 25%;
            top: -12.5%;
            left: -12.5%;
            animation-delay: 1s;
        }
        
        .ring-3 {
            width: 35%;                       /* ✅ 修复：使用百分比 */
            height: 35%;
            top: -17.5%;
            left: -17.5%;
            animation-delay: 2s;
        }
        
        @keyframes ring-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
        }
        
        /* 浮动装饰点 */
        .floating-dots {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .dot {
            position: absolute;
            width: 1vh;                       /* ✅ 修复：使用vh单位 */
            height: 1vh;
            min-width: 6px;                   /* ✅ 最小尺寸 */
            min-height: 6px;
            max-width: 12px;                  /* ✅ 最大尺寸 */
            max-height: 12px;
            background: radial-gradient(circle, #00ff88, transparent);
            border-radius: 50%;
            animation: dot-float 4s ease-in-out infinite;
        }
        
        .dot:nth-child(1) {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }
        
        .dot:nth-child(2) {
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }
        
        .dot:nth-child(3) {
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }
        
        .dot:nth-child(4) {
            bottom: 35%;
            right: 15%;
            animation-delay: 3s;
        }
        
        @keyframes dot-float {
            0%, 100% {
                transform: translateY(0px);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-1vh);    /* ✅ 修复：使用vh单位 */
                opacity: 1;
            }
        }
        
        /* 装饰线条 */
        .decorative-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation: line-glow 3s ease-in-out infinite;
        }
        
        .line-top {
            top: 8%;                          /* ✅ 修复：使用百分比 */
            left: 15%;
            width: 25%;                       /* ✅ 修复：使用百分比 */
            height: 0.2vh;                    /* ✅ 修复：使用vh单位 */
            animation-delay: 0s;
        }
        
        .line-bottom {
            bottom: 8%;
            right: 15%;
            width: 30%;                       /* ✅ 修复：使用百分比 */
            height: 0.2vh;                    /* ✅ 修复：使用vh单位 */
            animation-delay: 1.5s;
        }
        
        .line-left {
            left: 8%;                         /* ✅ 修复：使用百分比 */
            top: 20%;
            width: 0.2vh;                     /* ✅ 修复：使用vh单位 */
            height: 20%;                      /* ✅ 修复：使用百分比 */
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 0.75s;
        }
        
        .line-right {
            right: 8%;
            bottom: 20%;
            width: 0.2vh;                     /* ✅ 修复：使用vh单位 */
            height: 25%;                      /* ✅ 修复：使用百分比 */
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 2.25s;
        }
        
        @keyframes line-glow {
            0%, 100% {
                opacity: 0.4;
                filter: brightness(1);
            }
            50% {
                opacity: 1;
                filter: brightness(1.5);
            }
        }
        
        /* 内容区域 */
        .content-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #00d4ff;
            z-index: 10;
            padding: 1vh;                     /* ✅ 修复：使用vh单位 */
        }
        
        .content-area h2 {
            font-size: 3vh;                   /* ✅ 修复：使用vh单位而不是rem */
            margin-bottom: 1vh;               /* ✅ 修复：使用vh单位 */
            background: linear-gradient(90deg, #00d4ff, #ff00cc);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 1vh rgba(0, 212, 255, 0.5);  /* ✅ 修复：使用vh单位 */
        }
        
        .content-area p {
            font-size: 1.5vh;                 /* ✅ 修复：使用vh单位 */
            color: #66ddff;
            text-shadow: 0 0 0.5vh rgba(102, 221, 255, 0.5);  /* ✅ 修复：使用vh单位 */
        }
        
        /* ✅ 修复：基于容器高度的响应式设计 */
        @media (max-height: 50px) {
            .corner-decoration {
                width: 15px;
                height: 15px;
                border-width: 2px;
            }
            
            .ring { border-width: 1px; }
            .dot { width: 4px; height: 4px; }
            .line-top, .line-bottom { height: 1px; }
            .line-left, .line-right { width: 1px; }
            
            .content-area h2 { font-size: 10px; }
            .content-area p { font-size: 7px; }
        }
        
        @media (min-height: 50px) and (max-height: 100px) {
            .content-area h2 { font-size: 2vh; }
            .content-area p { font-size: 1.2vh; }
        }
        
        @media (min-height: 100px) and (max-height: 200px) {
            .content-area h2 { font-size: 3vh; }
            .content-area p { font-size: 1.5vh; }
        }
        
        @media (min-height: 200px) {
            .content-area h2 { font-size: 4vh; }
            .content-area p { font-size: 2vh; }
        }
        
        /* 极小容器的特殊处理 */
        @media (max-width: 80px) or (max-height: 40px) {
            .corner-decoration {
                width: 12px;
                height: 12px;
                border-width: 1px;
            }
            
            .ring { display: none; }          /* 隐藏圆环节省空间 */
            .dot { width: 3px; height: 3px; }
            .line-top, .line-bottom { height: 1px; }
            .line-left, .line-right { width: 1px; }
            
            .content-area h2 { font-size: 8px; margin-bottom: 2px; }
            .content-area p { font-size: 6px; }
        }
        
        /* 宽度响应式补充 */
        @media (max-width: 150px) {
            .content-area h2 { font-size: 2vh; }
            .content-area p { font-size: 1vh; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration-wrapper">
            <!-- 角落装饰 -->
            <div class="corner-decoration corner-tl"></div>
            <div class="corner-decoration corner-tr"></div>
            <div class="corner-decoration corner-bl"></div>
            <div class="corner-decoration corner-br"></div>
            
            <!-- 中心圆环 -->
            <div class="center-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
            </div>
            
            <!-- 浮动装饰点 -->
            <div class="floating-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
            
            <!-- 装饰线条 -->
            <div class="decorative-lines">
                <div class="line line-top"></div>
                <div class="line line-bottom"></div>
                <div class="line line-left"></div>
                <div class="line line-right"></div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <h2>装饰效果</h2>
                <p>动态装饰组件</p>
            </div>
        </div>
    </div>
</body>
</html>
