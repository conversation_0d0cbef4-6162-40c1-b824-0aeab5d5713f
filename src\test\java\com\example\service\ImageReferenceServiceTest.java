package com.example.service;

import com.example.entity.Topology;
import com.example.model.Device;
import com.example.repository.DeviceRepository;
import com.example.repository.TopologyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ImageReferenceServiceTest {

    @Mock
    private DeviceRepository deviceRepository;

    @Mock
    private TopologyRepository topologyRepository;

    @InjectMocks
    private ImageReferenceService imageReferenceService;

    @BeforeEach
    public void setup() {
        ReflectionTestUtils.setField(imageReferenceService, "serverExternalUrl", "localhost:8080");
        ReflectionTestUtils.setField(imageReferenceService, "urlPrefix", "/images");
    }

    @Test
    public void testCheckImageUsage_NoReferences() {
        // 准备测试数据
        when(deviceRepository.findAll()).thenReturn(Collections.emptyList());
        when(topologyRepository.findAll()).thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, Object> result = imageReferenceService.checkImageUsage("test.jpg");

        // 验证结果
        assertFalse((Boolean) result.get("isUsed"));
        assertTrue(((List<?>) result.get("references")).isEmpty());
    }

    @Test
    public void testCheckImageUsage_WithDeviceReference() {
        // 准备测试数据
        Device device = new Device();
        device.setId("device1");
        device.setName("测试设备");
        device.setImageUrl("http://localhost:8080/images/test.jpg");
        
        when(deviceRepository.findAll()).thenReturn(Collections.singletonList(device));
        when(topologyRepository.findAll()).thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, Object> result = imageReferenceService.checkImageUsage("test.jpg");

        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        List<?> references = (List<?>) result.get("references");
        assertEquals(1, references.size());
        
        @SuppressWarnings("unchecked")
        Map<String, String> reference = (Map<String, String>) references.get(0);
        assertEquals("device", reference.get("type"));
        assertEquals("device1", reference.get("id"));
        assertEquals("测试设备", reference.get("name"));
    }

    @Test
    public void testCheckImageUsage_WithTopologyReference() {
        // 准备测试数据
        Topology topology = new Topology();
        topology.setName("测试拓扑");
        topology.setData("{\"nodes\":[{\"id\":\"node1\",\"name\":\"设备节点\",\"imageUrl\":\"http://localhost:8080/images/test.jpg\"}]}");
        
        when(deviceRepository.findAll()).thenReturn(Collections.emptyList());
        when(topologyRepository.findAll()).thenReturn(Collections.singletonList(topology));

        // 执行测试
        Map<String, Object> result = imageReferenceService.checkImageUsage("test.jpg");

        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        List<?> references = (List<?>) result.get("references");
        assertEquals(1, references.size());
        
        @SuppressWarnings("unchecked")
        Map<String, String> reference = (Map<String, String>) references.get(0);
        assertEquals("topology_device", reference.get("type"));
        assertEquals("测试拓扑", reference.get("topology"));
        assertEquals("设备节点", reference.get("nodeName"));
    }

    @Test
    public void testCheckImageUsage_WithTopologyImageNodeReference() {
        // 准备测试数据
        Topology topology = new Topology();
        topology.setName("测试拓扑");
        topology.setData("{\"imageNodes\":[{\"id\":\"img1\",\"title\":\"标题图片\",\"imageUrl\":\"http://localhost:8080/images/test.jpg\"}]}");
        
        when(deviceRepository.findAll()).thenReturn(Collections.emptyList());
        when(topologyRepository.findAll()).thenReturn(Collections.singletonList(topology));

        // 执行测试
        Map<String, Object> result = imageReferenceService.checkImageUsage("test.jpg");

        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        List<?> references = (List<?>) result.get("references");
        assertEquals(1, references.size());
        
        @SuppressWarnings("unchecked")
        Map<String, String> reference = (Map<String, String>) references.get(0);
        assertEquals("topology_image", reference.get("type"));
        assertEquals("测试拓扑", reference.get("topology"));
        assertEquals("标题图片", reference.get("title"));
    }

    @Test
    public void testCheckImageUsage_WithMultipleReferences() {
        // 准备设备数据
        Device device1 = new Device();
        device1.setId("device1");
        device1.setName("设备1");
        device1.setImageUrl("http://localhost:8080/images/test.jpg");
        
        Device device2 = new Device();
        device2.setId("device2");
        device2.setName("设备2");
        device2.setImageUrl("http://localhost:8080/images/other.jpg");
        
        // 准备拓扑数据
        Topology topology = new Topology();
        topology.setName("测试拓扑");
        topology.setData("{\"nodes\":[{\"id\":\"node1\",\"name\":\"设备节点\",\"imageUrl\":\"http://localhost:8080/images/test.jpg\"}]," +
                         "\"imageNodes\":[{\"id\":\"img1\",\"title\":\"标题图片\",\"imageUrl\":\"http://localhost:8080/images/test.jpg\"}]}");
        
        when(deviceRepository.findAll()).thenReturn(Arrays.asList(device1, device2));
        when(topologyRepository.findAll()).thenReturn(Collections.singletonList(topology));

        // 执行测试
        Map<String, Object> result = imageReferenceService.checkImageUsage("test.jpg");

        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        List<?> references = (List<?>) result.get("references");
        assertEquals(3, references.size()); // 一个设备引用 + 两个拓扑引用（设备节点和图片节点）
    }
    
    @Test
    public void testCheckImageUsage_WithSystemImage() {
        // 准备测试数据
        when(deviceRepository.findAll()).thenReturn(Collections.emptyList());
        when(topologyRepository.findAll()).thenReturn(Collections.emptyList());
        
        // 测试 logo.png (系统LOGO)
        Map<String, Object> result = imageReferenceService.checkImageUsage("logo.png");
        
        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        List<?> references = (List<?>) result.get("references");
        assertEquals(1, references.size());
        
        @SuppressWarnings("unchecked")
        Map<String, String> reference = (Map<String, String>) references.get(0);
        assertEquals("system", reference.get("type"));
        assertEquals("主页", reference.get("page"));
        assertEquals("系统LOGO", reference.get("description"));
        
        // 测试 tech-bg.jpg (登录页背景图)
        result = imageReferenceService.checkImageUsage("tech-bg.jpg");
        
        // 验证结果
        assertTrue((Boolean) result.get("isUsed"));
        references = (List<?>) result.get("references");
        assertEquals(1, references.size());
        
        @SuppressWarnings("unchecked")
        Map<String, String> bgReference = (Map<String, String>) references.get(0);
        assertEquals("system", bgReference.get("type"));
        assertEquals("登录页", bgReference.get("page"));
        assertEquals("登录页背景图", bgReference.get("description"));
    }
} 