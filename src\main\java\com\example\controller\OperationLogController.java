package com.example.controller;

import com.example.model.DeviceCondition;
import com.example.model.OperationLog;
import com.example.service.DeviceConditionService;
import com.example.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/logs")
public class OperationLogController {
    
    @Autowired
    private OperationLogService operationLogService;
    
    @Autowired
    private DeviceConditionService deviceConditionService;
    
    @GetMapping("/condition/{conditionId}")
    public ResponseEntity<List<OperationLog>> getConditionLogs(@PathVariable Long conditionId) {
        DeviceCondition condition = deviceConditionService.getCondition(conditionId)
            .orElseThrow(() -> new IllegalArgumentException("条件不存在: " + conditionId));
            
        return ResponseEntity.ok(operationLogService.getConditionLogs(condition));
    }
    
    @GetMapping("/condition/{conditionId}/page")
    public ResponseEntity<Page<OperationLog>> getConditionLogsPage(
            @PathVariable Long conditionId,
            Pageable pageable) {
        DeviceCondition condition = deviceConditionService.getCondition(conditionId)
            .orElseThrow(() -> new IllegalArgumentException("条件不存在: " + conditionId));
            
        return ResponseEntity.ok(operationLogService.getConditionLogsPage(condition, pageable));
    }
    
    @GetMapping("/device/{deviceId}/page")
    public ResponseEntity<Page<OperationLog>> getDeviceLogsPage(
            @PathVariable String deviceId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
            Pageable pageable) {
        return ResponseEntity.ok(operationLogService.getDeviceLogsPage(deviceId, start, end, pageable));
    }
    
    @GetMapping("/time-range")
    public ResponseEntity<List<OperationLog>> getLogsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end) {
        return ResponseEntity.ok(operationLogService.getLogsByTimeRange(start, end));
    }
} 