<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能排序限制功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .sql-demo {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .sql-before, .sql-after {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        
        .sql-before {
            border-left: 4px solid #ffc107;
        }
        
        .sql-after {
            border-left: 4px solid #28a745;
        }
        
        .improvement-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .improvement-latest {
            background-color: #d4edda;
            color: #155724;
        }
        
        .improvement-sorted {
            background-color: #cce5ff;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-sort-down"></i>
                智能排序限制功能测试
            </h2>
            
            <!-- 功能说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 功能说明</h6>
                <p class="mb-0">修复了输出限制功能，现在会自动添加智能排序，确保获取最新数据记录而不是较早的记录。系统会根据SQL类型选择最优的排序策略。</p>
            </div>
            
            <!-- 改进对比 -->
            <div class="test-section">
                <h5><i class="bi bi-arrow-left-right"></i> 改进对比</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="sql-demo">
                            <h6><i class="bi bi-x-circle text-warning"></i> 修复前（获取较早数据）</h6>
                            <div class="sql-before">SELECT * FROM data_history
LIMIT 100</div>
                            <p class="small text-muted mb-0">❌ 返回表中前100条记录（通常是较早的数据）</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="sql-demo">
                            <h6><i class="bi bi-check-circle text-success"></i> 修复后（获取最新数据）</h6>
                            <div class="sql-after">SELECT * FROM data_history
ORDER BY timestamp DESC
LIMIT 100</div>
                            <p class="small text-success mb-0">✅ 返回最新的100条记录</p>
                            <span class="improvement-badge improvement-latest">获取最新数据</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SQL类型处理策略 -->
            <div class="test-section">
                <h5><i class="bi bi-diagram-3"></i> SQL类型处理策略</h5>
                
                <!-- 简单查询 -->
                <div class="sql-demo">
                    <h6>1. 简单查询</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>原始SQL：</strong>
                            <div class="sql-before">SELECT name, value FROM data_items</div>
                        </div>
                        <div class="col-md-6">
                            <strong>智能优化：</strong>
                            <div class="sql-after">SELECT name, value FROM data_items
ORDER BY timestamp DESC
LIMIT 50</div>
                            <span class="improvement-badge improvement-latest">添加时间排序</span>
                        </div>
                    </div>
                </div>
                
                <!-- 聚合查询 -->
                <div class="sql-demo">
                    <h6>2. 聚合查询</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>原始SQL：</strong>
                            <div class="sql-before">SELECT name, MAX(value) as max_value
FROM data_history
GROUP BY name</div>
                        </div>
                        <div class="col-md-6">
                            <strong>智能优化：</strong>
                            <div class="sql-after">SELECT name, MAX(value) as max_value
FROM data_history
GROUP BY name
ORDER BY MAX(timestamp) DESC
LIMIT 50</div>
                            <span class="improvement-badge improvement-sorted">聚合时间排序</span>
                        </div>
                    </div>
                </div>
                
                <!-- 窗口函数查询 -->
                <div class="sql-demo">
                    <h6>3. 窗口函数查询</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>原始SQL：</strong>
                            <div class="sql-before">SELECT name, value
FROM (
    SELECT *, ROW_NUMBER() OVER (
        PARTITION BY name 
        ORDER BY value DESC, timestamp DESC
    ) as rn
    FROM data_history
) t
WHERE rn = 1</div>
                        </div>
                        <div class="col-md-6">
                            <strong>智能优化：</strong>
                            <div class="sql-after">SELECT name, value
FROM (
    SELECT *, ROW_NUMBER() OVER (
        PARTITION BY name 
        ORDER BY value DESC, timestamp DESC
    ) as rn
    FROM data_history
) t
WHERE rn = 1
ORDER BY timestamp DESC
LIMIT 50</div>
                            <span class="improvement-badge improvement-latest">外层时间排序</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 时间字段识别 -->
            <div class="test-section">
                <h5><i class="bi bi-clock"></i> 时间字段识别</h5>
                <p>系统会按优先级自动识别时间字段用于排序：</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="sql-demo">
                            <h6>优先级顺序</h6>
                            <ol class="small">
                                <li><code>timestamp</code> - 时间戳字段</li>
                                <li><code>created_at</code> - 创建时间</li>
                                <li><code>updated_at</code> - 更新时间</li>
                                <li><code>create_time</code> - 创建时间</li>
                                <li><code>update_time</code> - 更新时间</li>
                                <li><code>date_created</code> - 创建日期</li>
                                <li><code>date_updated</code> - 更新日期</li>
                                <li><code>time</code> - 时间字段</li>
                                <li><code>datetime</code> - 日期时间</li>
                                <li><code>id</code> - ID字段（默认）</li>
                            </ol>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="sql-demo">
                            <h6>识别来源</h6>
                            <ul class="small">
                                <li><strong>聚合配置</strong>：优先使用用户指定的时间字段</li>
                                <li><strong>字段配置器</strong>：从可用字段中匹配</li>
                                <li><strong>SQL解析</strong>：从SQL语句中查找</li>
                                <li><strong>默认字段</strong>：使用id作为备选</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实时测试 -->
            <div class="test-section">
                <h5><i class="bi bi-play"></i> 实时测试</h5>
                <div class="mb-3">
                    <label class="form-label">输出限制：</label>
                    <input type="number" class="form-control" id="outputLimit" value="50" min="1" max="1000">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">测试SQL：</label>
                    <textarea class="form-control" id="testSQL" rows="4">SELECT name, latest_value FROM data_items</textarea>
                </div>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-primary" onclick="testSmartLimit()">
                        <i class="bi bi-play"></i> 测试智能排序
                    </button>
                </div>
                
                <div id="testResult" style="display: none;">
                    <h6>优化结果：</h6>
                    <div id="optimizedSQL" class="sql-after"></div>
                    <div id="analysisResult" class="mt-2"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟字段配置器
        class MockFieldConfigurator {
            constructor() {
                this.fields = [
                    { name: 'id', type: 'INT' },
                    { name: 'name', type: 'VARCHAR' },
                    { name: 'latest_value', type: 'INT' },
                    { name: 'timestamp', type: 'DATETIME' },
                    { name: 'created_at', type: 'DATETIME' },
                    { name: 'device_id', type: 'VARCHAR' }
                ];
            }
            
            // 复制智能排序逻辑
            addLimitClause(sql) {
                const outputLimitElement = document.getElementById('outputLimit');
                if (!outputLimitElement) {
                    return sql;
                }

                const outputLimit = parseInt(outputLimitElement.value);
                if (!outputLimit || outputLimit <= 0) {
                    return sql;
                }

                if (sql.toLowerCase().includes('limit')) {
                    return sql;
                }

                return this.addSmartOrderByAndLimit(sql, outputLimit);
            }
            
            addSmartOrderByAndLimit(sql, limit) {
                try {
                    const timeField = this.identifyTimeField(sql);
                    
                    if (this.isWindowFunctionQuery(sql)) {
                        return this.addOrderByLimitToWindowFunction(sql, timeField, limit);
                    } else if (this.isAggregationQuery(sql)) {
                        return this.addOrderByLimitToAggregation(sql, timeField, limit);
                    } else {
                        return this.addOrderByLimitToSimpleQuery(sql, timeField, limit);
                    }
                } catch (error) {
                    console.error('添加智能排序失败:', error);
                    return sql + `\nLIMIT ${limit}`;
                }
            }
            
            identifyTimeField(sql) {
                const timeFieldCandidates = [
                    'timestamp', 'created_at', 'updated_at', 'create_time', 'update_time',
                    'date_created', 'date_updated', 'time', 'datetime', 'id'
                ];

                const sqlLower = sql.toLowerCase();
                
                for (const candidate of timeFieldCandidates) {
                    const field = this.fields.find(f => f.name.toLowerCase() === candidate);
                    if (field) {
                        return field.name;
                    }
                }

                for (const candidate of timeFieldCandidates) {
                    if (sqlLower.includes(candidate)) {
                        return candidate;
                    }
                }

                return 'id';
            }
            
            isWindowFunctionQuery(sql) {
                const sqlLower = sql.toLowerCase();
                return sqlLower.includes('row_number()') && sqlLower.includes('over');
            }
            
            isAggregationQuery(sql) {
                const sqlLower = sql.toLowerCase();
                return sqlLower.includes('group by') || 
                       /\b(max|min|avg|sum|count)\s*\(/i.test(sql);
            }
            
            addOrderByLimitToSimpleQuery(sql, timeField, limit) {
                const sqlLower = sql.toLowerCase();
                if (sqlLower.includes('order by')) {
                    return sql + `\nLIMIT ${limit}`;
                }
                return sql + `\nORDER BY ${timeField} DESC\nLIMIT ${limit}`;
            }
            
            addOrderByLimitToAggregation(sql, timeField, limit) {
                const sqlLower = sql.toLowerCase();
                if (sqlLower.includes('order by')) {
                    return sql + `\nLIMIT ${limit}`;
                }
                
                const groupByMatch = sql.match(/GROUP\s+BY\s+([^;]+?)(?:\s|$)/i);
                if (groupByMatch) {
                    const orderByClause = `\nORDER BY MAX(${timeField}) DESC`;
                    return sql + orderByClause + `\nLIMIT ${limit}`;
                }
                
                return sql + `\nORDER BY ${timeField} DESC\nLIMIT ${limit}`;
            }
            
            addOrderByLimitToWindowFunction(sql, timeField, limit) {
                const sqlLower = sql.toLowerCase();
                if (sqlLower.includes('order by') && !sqlLower.includes('over')) {
                    return sql + `\nLIMIT ${limit}`;
                }
                return sql + `\nORDER BY ${timeField} DESC\nLIMIT ${limit}`;
            }
        }
        
        const mockConfigurator = new MockFieldConfigurator();
        
        function testSmartLimit() {
            const sql = document.getElementById('testSQL').value;
            const optimizedSQL = mockConfigurator.addLimitClause(sql);
            
            document.getElementById('optimizedSQL').textContent = optimizedSQL;
            
            // 分析结果
            const isWindow = mockConfigurator.isWindowFunctionQuery(sql);
            const isAggregation = mockConfigurator.isAggregationQuery(sql);
            const timeField = mockConfigurator.identifyTimeField(sql);
            
            let queryType = '简单查询';
            if (isWindow) queryType = '窗口函数查询';
            else if (isAggregation) queryType = '聚合查询';
            
            document.getElementById('analysisResult').innerHTML = `
                <div class="small">
                    <strong>查询类型：</strong>${queryType}<br>
                    <strong>识别的时间字段：</strong>${timeField}<br>
                    <strong>优化策略：</strong>添加 ORDER BY ${timeField} DESC 确保获取最新数据
                </div>
            `;
            
            document.getElementById('testResult').style.display = 'block';
        }
    </script>
</body>
</html>
