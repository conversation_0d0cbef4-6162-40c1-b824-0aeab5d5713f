package com.example.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统图片工具类测试
 */
public class SystemImageUtilTest {

    @Test
    public void testIsSystemImage() {
        // 测试系统图片识别
        assertTrue(SystemImageUtil.isSystemImage("logo.png"));
        assertTrue(SystemImageUtil.isSystemImage("tech-bg.jpg"));
        assertFalse(SystemImageUtil.isSystemImage("unauthorized.png"));
        assertFalse(SystemImageUtil.isSystemImage("random.jpg"));
    }
    
    @Test
    public void testGetSystemImagePath() {
        // 测试系统图片路径生成
        assertEquals("/system-images/logo.png", SystemImageUtil.getSystemImagePath("logo.png"));
        assertEquals("/system-images/tech-bg.jpg", SystemImageUtil.getSystemImagePath("tech-bg.jpg"));
        assertNull(SystemImageUtil.getSystemImagePath("unauthorized.png"));
        assertNull(SystemImageUtil.getSystemImagePath("random.jpg"));
    }
    
    @Test
    public void testExists() {
        // 测试系统图片存在性检查
        assertTrue(SystemImageUtil.exists("logo.png"));
        assertTrue(SystemImageUtil.exists("tech-bg.jpg"));
        assertFalse(SystemImageUtil.exists("unauthorized.png"));
        assertFalse(SystemImageUtil.exists("random.jpg"));
    }
    
    @Test
    public void testGetResource() {
        // 测试系统图片资源获取
        assertNotNull(SystemImageUtil.getResource("logo.png"));
        assertNotNull(SystemImageUtil.getResource("tech-bg.jpg"));
        assertNull(SystemImageUtil.getResource("unauthorized.png"));
        assertNull(SystemImageUtil.getResource("random.jpg"));
    }
}
