-- 创建数据库
CREATE DATABASE IF NOT EXISTS sdplc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE sdplc;

-- 创建设备表
CREATE TABLE IF NOT EXISTS devices (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(100) NOT NULL,
    port INT NOT NULL,
    connected BOOLEAN NOT NULL DEFAULT FALSE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建数据项表
CREATE TABLE IF NOT EXISTS data_items (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(100) NOT NULL,
    refresh_interval INT NOT NULL,
    latest_value INT,
    device_id VARCHAR(36) NOT NULL,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 添加索引
CREATE INDEX idx_device_id ON data_items(device_id);
CREATE INDEX idx_address ON data_items(address);

-- 插入测试数据
-- 插入测试设备
INSERT INTO devices (id, name, address, port, connected) VALUES
('d001', 'PLC-1', '*************', 502, false),
('d002', 'PLC-2', '*************', 502, false);

-- 插入测试数据项
INSERT INTO data_items (id, name, address, refresh_interval, latest_value, device_id) VALUES
('i001', '温度传感器1', '40001', 1000, NULL, 'd001'),
('i002', '压力传感器1', '40002', 1000, NULL, 'd001'),
('i003', '温度传感器2', '40001', 1000, NULL, 'd002'),
('i004', '压力传感器2', '40002', 1000, NULL, 'd002'); 