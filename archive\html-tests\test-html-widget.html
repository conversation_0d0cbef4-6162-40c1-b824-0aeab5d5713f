<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .test-container {
            width: 400px;
            height: 300px;
            border: 2px solid #ccc;
            margin: 20px;
            background: white;
            position: relative;
            overflow: hidden;
        }
        
        .test-title {
            background: #333;
            color: white;
            padding: 10px;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>HTML组件测试页面</h1>
    <p>以下是不同尺寸容器中的HTML组件测试：</p>
    
    <!-- 测试1：响应式按钮 -->
    <div class="test-container">
        <div class="test-title">测试1：响应式动态按钮</div>
        <iframe 
            src="responsive-dynamic-button.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试2：响应式边框 -->
    <div class="test-container">
        <div class="test-title">测试2：响应式动态边框</div>
        <iframe 
            src="responsive-border.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试3：响应式装饰 -->
    <div class="test-container">
        <div class="test-title">测试3：响应式装饰效果</div>
        <iframe 
            src="responsive-decoration.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试4：小尺寸容器 -->
    <div class="test-container" style="width: 200px; height: 150px;">
        <div class="test-title">测试4：小尺寸容器</div>
        <iframe 
            src="responsive-dynamic-button.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试5：大尺寸容器 -->
    <div class="test-container" style="width: 600px; height: 400px;">
        <div class="test-title">测试5：大尺寸容器</div>
        <iframe 
            src="responsive-decoration.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试6：极窄容器 -->
    <div class="test-container" style="width: 150px; height: 300px;">
        <div class="test-title">测试6：极窄容器</div>
        <iframe 
            src="responsive-border.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <!-- 测试7：极宽容器 -->
    <div class="test-container" style="width: 800px; height: 200px;">
        <div class="test-title">测试7：极宽容器</div>
        <iframe 
            src="responsive-dynamic-button.html" 
            style="width: 100%; height: calc(100% - 44px); border: none;">
        </iframe>
    </div>
    
    <div style="margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 5px;">
        <h3>测试说明</h3>
        <ul>
            <li><strong>响应式特性</strong>：所有HTML组件都应该能够自动适应容器大小</li>
            <li><strong>填满容器</strong>：组件应该完全填满分配给它的容器空间</li>
            <li><strong>保持比例</strong>：在不同尺寸下保持视觉效果的协调性</li>
            <li><strong>动画效果</strong>：确保动画在不同尺寸下都能正常工作</li>
            <li><strong>隔离性</strong>：使用iframe确保HTML代码不会影响父页面</li>
        </ul>
        
        <h3>预期结果</h3>
        <ul>
            <li>所有测试容器中的HTML组件都应该完全填满容器</li>
            <li>组件的视觉效果应该根据容器大小自动调整</li>
            <li>动画和交互效果应该在所有尺寸下正常工作</li>
            <li>没有滚动条或溢出问题</li>
        </ul>
    </div>
</body>
</html>
