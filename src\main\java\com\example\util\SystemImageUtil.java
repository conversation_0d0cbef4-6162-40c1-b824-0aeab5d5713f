package com.example.util;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.Arrays;
import java.util.List;

/**
 * 系统图片工具类
 * 提供系统图片相关的工具方法，统一管理系统图片列表
 */
public class SystemImageUtil {
    
    /**
     * 系统图片文件名常量
     */
    public static final String LOGO_PNG = "logo.png";
    public static final String TECH_BG_JPG = "tech-bg.jpg";
    
    /**
     * 允许访问的系统图片列表
     */
    public static final List<String> SYSTEM_IMAGES = Arrays.asList(
        LOGO_PNG,
        TECH_BG_JPG
    );
    
    /**
     * 检查是否为系统图片
     * @param filename 文件名
     * @return 是否为系统图片
     */
    public static boolean isSystemImage(String filename) {
        return SYSTEM_IMAGES.contains(filename);
    }
    
    /**
     * 检查系统图片是否存在
     * @param filename 文件名
     * @return 图片是否存在
     */
    public static boolean exists(String filename) {
        if (!isSystemImage(filename)) {
            return false;
        }
        
        try {
            Resource resource = new ClassPathResource("static/images/" + filename);
            return resource.exists();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取系统图片资源
     * @param filename 文件名
     * @return 图片资源，如果不存在返回null
     */
    public static Resource getResource(String filename) {
        if (!isSystemImage(filename)) {
            return null;
        }
        
        try {
            Resource resource = new ClassPathResource("static/images/" + filename);
            return resource.exists() ? resource : null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取系统图片的访问路径
     * @param filename 文件名
     * @return 访问路径
     */
    public static String getSystemImagePath(String filename) {
        if (!isSystemImage(filename)) {
            return null;
        }
        return "/system-images/" + filename;
    }
}
