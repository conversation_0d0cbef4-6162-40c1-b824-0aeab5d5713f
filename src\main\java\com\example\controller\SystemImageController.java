package com.example.controller;

import com.example.util.SystemImageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 系统图片控制器
 * 专门处理系统关键图片的请求，确保这些图片从classpath加载，防止被外部文件替换
 */
@Controller
@RequestMapping("/system-images")
public class SystemImageController {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemImageController.class);
    
    /**
     * 获取系统图片
     * @param filename 图片文件名
     * @return 图片资源响应
     */
    @GetMapping("/{filename:.+}")
    public ResponseEntity<Resource> getSystemImage(@PathVariable String filename) {
        logger.debug("请求系统图片: {}", filename);
        
        // 检查是否为允许的系统图片
        if (!SystemImageUtil.isSystemImage(filename)) {
            logger.warn("尝试访问未授权的系统图片: {}", filename);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        try {
            // 从classpath加载图片资源
            Resource resource = SystemImageUtil.getResource(filename);

            if (resource == null) {
                logger.error("系统图片不存在: {}", filename);
                return ResponseEntity.notFound().build();
            }
            
            // 确定媒体类型
            MediaType mediaType = getMediaType(filename);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(mediaType);
            headers.setCacheControl("public, max-age=3600"); // 缓存1小时
            
            logger.debug("成功加载系统图片: {}", filename);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("加载系统图片失败: {}, 错误: {}", filename, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 根据文件扩展名确定媒体类型
     * @param filename 文件名
     * @return 媒体类型
     */
    private MediaType getMediaType(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "png":
                return MediaType.IMAGE_PNG;
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "gif":
                return MediaType.IMAGE_GIF;
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
}
