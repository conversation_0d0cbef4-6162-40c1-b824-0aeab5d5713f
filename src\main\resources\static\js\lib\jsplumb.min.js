(function(){function a(a,b){return[-a[0][b]+3*a[1][b]+-3*a[2][b]+a[3][b],3*a[0][b]-6*a[1][b]+3*a[2][b],-3*a[0][b]+3*a[1][b],a[0][b]]}function b(b){return[a(b,"x"),a(b,"y")]}function c(a){return 0>a?-1:a>0?1:0}function d(a,b,d,e){var f,g,h=b/a,i=d/a,j=e/a,k=(3*i-Math.pow(h,2))/9,l=(9*h*i-27*j-2*Math.pow(h,3))/54,m=Math.pow(k,3)+Math.pow(l,2),n=[];if(m>=0)f=c(l+Math.sqrt(m))*Math.pow(Math.abs(l+Math.sqrt(m)),1/3),g=c(l-Math.sqrt(m))*Math.pow(Math.abs(l-Math.sqrt(m)),1/3),n[0]=-h/3+(f+g),n[1]=-h/3-(f+g)/2,n[2]=-h/3-(f+g)/2,0!==Math.abs(Math.sqrt(3)*(f-g)/2)&&(n[1]=-1,n[2]=-1);else{var o=Math.acos(l/Math.sqrt(-Math.pow(k,3)));n[0]=2*Math.sqrt(-k)*Math.cos(o/3)-h/3,n[1]=2*Math.sqrt(-k)*Math.cos((o+2*Math.PI)/3)-h/3,n[2]=2*Math.sqrt(-k)*Math.cos((o+4*Math.PI)/3)-h/3}for(var p=0;3>p;p++)(n[p]<0||n[p]>1)&&(n[p]=-1);return n}"undefined"==typeof Math.sgn&&(Math.sgn=function(a){return 0==a?0:a>0?1:-1});var e={subtract:function(a,b){return{x:a.x-b.x,y:a.y-b.y}},dotProduct:function(a,b){return a.x*b.x+a.y*b.y},square:function(a){return Math.sqrt(a.x*a.x+a.y*a.y)},scale:function(a,b){return{x:a.x*b,y:a.y*b}}},f=64,g=Math.pow(2,-f-1),h=function(a,b){for(var c=[],d=j(a,b),f=b.length-1,g=2*f-1,h=k(d,g,c,0),i=e.subtract(a,b[0]),l=e.square(i),m=0,n=0;h>n;n++){i=e.subtract(a,o(b,f,c[n],null,null));var p=e.square(i);l>p&&(l=p,m=c[n])}return i=e.subtract(a,b[f]),p=e.square(i),l>p&&(l=p,m=1),{location:m,distance:l}},i=function(a,b){var c=h(a,b);return{point:o(b,b.length-1,c.location,null,null),location:c.location}},j=function(a,b){for(var c=b.length-1,d=2*c-1,f=[],g=[],h=[],i=[],j=[[1,.6,.3,.1],[.4,.6,.6,.4],[.1,.3,.6,1]],k=0;c>=k;k++)f[k]=e.subtract(b[k],a);for(var k=0;c-1>=k;k++)g[k]=e.subtract(b[k+1],b[k]),g[k]=e.scale(g[k],3);for(var l=0;c-1>=l;l++)for(var m=0;c>=m;m++)h[l]||(h[l]=[]),h[l][m]=e.dotProduct(g[l],f[m]);for(k=0;d>=k;k++)i[k]||(i[k]=[]),i[k].y=0,i[k].x=parseFloat(k)/d;for(var n=c,o=c-1,p=0;n+o>=p;p++){var q=Math.max(0,p-o),r=Math.min(p,n);for(k=q;r>=k;k++){var s=p-k;i[k+s].y+=h[s][k]*j[s][k]}}return i},k=function(a,b,c,d){var e,g,h=[],i=[],j=[],p=[];switch(l(a,b)){case 0:return 0;case 1:if(d>=f)return c[0]=(a[0].x+a[b].x)/2,1;if(m(a,b))return c[0]=n(a,b),1}o(a,b,.5,h,i),e=k(h,b,j,d+1),g=k(i,b,p,d+1);for(var q=0;e>q;q++)c[q]=j[q];for(var q=0;g>q;q++)c[q+e]=p[q];return e+g},l=function(a,b){var c,d,e=0;c=d=Math.sgn(a[0].y);for(var f=1;b>=f;f++)c=Math.sgn(a[f].y),c!=d&&e++,d=c;return e},m=function(a,b){var c,d,e,f,h,i,j,k,l,m,n,o,p,q,r,s;i=a[0].y-a[b].y,j=a[b].x-a[0].x,k=a[0].x*a[b].y-a[b].x*a[0].y;var t,u;t=u=0;for(var v=1;b>v;v++){var w=i*a[v].x+j*a[v].y+k;w>t?t=w:u>w&&(u=w)}return n=0,o=1,p=0,q=i,r=j,s=k-t,l=n*r-q*o,m=1/l,d=(o*s-r*p)*m,q=i,r=j,s=k-u,l=n*r-q*o,m=1/l,e=(o*s-r*p)*m,f=Math.min(d,e),h=Math.max(d,e),c=h-f,g>c?1:0},n=function(a,b){var c=1,d=0,e=a[b].x-a[0].x,f=a[b].y-a[0].y,g=a[0].x-0,h=a[0].y-0,i=e*d-f*c,j=1/i,k=(e*h-f*g)*j;return 0+c*k},o=function(a,b,c,d,e){for(var f=[[]],g=0;b>=g;g++)f[0][g]=a[g];for(var h=1;b>=h;h++)for(var g=0;b-h>=g;g++)f[h]||(f[h]=[]),f[h][g]||(f[h][g]={}),f[h][g].x=(1-c)*f[h-1][g].x+c*f[h-1][g+1].x,f[h][g].y=(1-c)*f[h-1][g].y+c*f[h-1][g+1].y;if(null!=d)for(g=0;b>=g;g++)d[g]=f[g][0];if(null!=e)for(g=0;b>=g;g++)e[g]=f[b-g][g];return f[b][0]},p={},q=function(a){var b=p[a];if(!b){b=[];var c=function(){return function(b){return Math.pow(b,a)}},d=function(){return function(b){return Math.pow(1-b,a)}},e=function(a){return function(b){return a}},f=function(){return function(a){return a}},g=function(){return function(a){return 1-a}},h=function(a){return function(b){for(var c=1,d=0;d<a.length;d++)c*=a[d](b);return c}};b.push(new c);for(var i=1;a>i;i++){for(var j=[new e(a)],k=0;a-i>k;k++)j.push(new f);for(var k=0;i>k;k++)j.push(new g);b.push(new h(j))}b.push(new d),p[a]=b}return b},r=function(a,b){for(var c=q(a.length-1),d=0,e=0,f=0;f<a.length;f++)d+=a[f].x*c[f](b),e+=a[f].y*c[f](b);return{x:d,y:e}},s=function(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))},t=function(a){return a[0].x===a[1].x&&a[0].y===a[1].y},u=function(a,b,c){if(t(a))return{point:a[0],location:b};for(var d=r(a,b),e=0,f=b,g=c>0?1:-1,h=null;e<Math.abs(c);)f+=.005*g,h=r(a,f),e+=s(h,d),d=h;return{point:h,location:f}},v=function(a){var b=(new Date).getTime();if(t(a))return 0;for(var c=r(a,0),d=0,e=0,f=1,g=null;1>e;)e+=.005*f,g=r(a,e),d+=s(g,c),c=g;return console.log("length",(new Date).getTime()-b),d},w=function(a,b,c){return u(a,b,c).point},x=function(a,b,c){return u(a,b,c).location},y=function(a,b){var c=r(a,b),d=r(a.slice(0,a.length-1),b),e=d.y-c.y,f=d.x-c.x;return 0===e?1/0:Math.atan(e/f)},z=function(a,b,c){var d=u(a,b,c);return d.location>1&&(d.location=1),d.location<0&&(d.location=0),y(a,d.location)},A=function(a,b,c,d){d=null==d?0:d;var e=u(a,b,d),f=y(a,e.location),g=Math.atan(-1/f),h=c/2*Math.sin(g),i=c/2*Math.cos(g);return[{x:e.point.x+i,y:e.point.y+h},{x:e.point.x-i,y:e.point.y-h}]},B=function(a,c,e,f,g){var h=f-c,i=a-e,j=a*(c-f)+c*(e-a),k=b(g),l=[h*k[0][0]+i*k[1][0],h*k[0][1]+i*k[1][1],h*k[0][2]+i*k[1][2],h*k[0][3]+i*k[1][3]+j],m=d.apply(null,l),n=[];if(null!=m)for(var o=0;3>o;o++){var p,q=m[o],r=Math.pow(q,2),s=Math.pow(q,3),t=[k[0][0]*s+k[0][1]*r+k[0][2]*q+k[0][3],k[1][0]*s+k[1][1]*r+k[1][2]*q+k[1][3]];p=e-a!==0?(t[0]-a)/(e-a):(t[1]-c)/(f-c),q>=0&&1>=q&&p>=0&&1>=p&&n.push(t)}return n},C=function(a,b,c,d,e){var f=[];return f.push.apply(f,B(a,b,a+c,b,e)),f.push.apply(f,B(a+c,b,a+c,b+d,e)),f.push.apply(f,B(a+c,b+d,a,b+d,e)),f.push.apply(f,B(a,b+d,a,b,e)),f},D=function(a,b){var c=[];return c.push.apply(c,B(a.x,a.y,a.x+a.w,a.y,b)),c.push.apply(c,B(a.x+a.w,a.y,a.x+a.w,a.y+a.h,b)),c.push.apply(c,B(a.x+a.w,a.y+a.h,a.x,a.y+a.h,b)),c.push.apply(c,B(a.x,a.y+a.h,a.x,a.y,b)),c},E=this.jsBezier={distanceFromCurve:h,gradientAtPoint:y,gradientAtPointAlongCurveFrom:z,nearestPointOnCurve:i,pointOnCurve:r,pointAlongCurveFrom:w,perpendicularToCurveAt:A,locationAlongCurveFrom:x,getLength:v,lineIntersection:B,boxIntersection:C,boundingBoxIntersection:D,version:"0.9.0"};"undefined"!=typeof exports&&(exports.jsBezier=E)}).call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.Biltong={version:"0.4.0"};"undefined"!=typeof exports&&(exports.Biltong=b);var c=function(a){return"[object Array]"===Object.prototype.toString.call(a)},d=function(a,b,d){return a=c(a)?a:[a.x,a.y],b=c(b)?b:[b.x,b.y],d(a,b)},e=b.gradient=function(a,b){return d(a,b,function(a,b){return b[0]==a[0]?b[1]>a[1]?1/0:-(1/0):b[1]==a[1]?b[0]>a[0]?0:-0:(b[1]-a[1])/(b[0]-a[0])})},f=(b.normal=function(a,b){return-1/e(a,b)},b.lineLength=function(a,b){return d(a,b,function(a,b){return Math.sqrt(Math.pow(b[1]-a[1],2)+Math.pow(b[0]-a[0],2))})},b.quadrant=function(a,b){return d(a,b,function(a,b){return b[0]>a[0]?b[1]>a[1]?2:1:b[0]==a[0]?b[1]>a[1]?2:1:b[1]>a[1]?3:4})}),g=(b.theta=function(a,b){return d(a,b,function(a,b){var c=e(a,b),d=Math.atan(c),g=f(a,b);return(4==g||3==g)&&(d+=Math.PI),0>d&&(d+=2*Math.PI),d})},b.intersects=function(a,b){var c=a.x,d=a.x+a.w,e=a.y,f=a.y+a.h,g=b.x,h=b.x+b.w,i=b.y,j=b.y+b.h;return g>=c&&d>=g&&i>=e&&f>=i||h>=c&&d>=h&&i>=e&&f>=i||g>=c&&d>=g&&j>=e&&f>=j||h>=c&&d>=g&&j>=e&&f>=j||c>=g&&h>=c&&e>=i&&j>=e||d>=g&&h>=d&&e>=i&&j>=e||c>=g&&h>=c&&f>=i&&j>=f||d>=g&&h>=c&&f>=i&&j>=f},b.encloses=function(a,b,c){var d=a.x,e=a.x+a.w,f=a.y,g=a.y+a.h,h=b.x,i=b.x+b.w,j=b.y,k=b.y+b.h,l=function(a,b,d,e){return c?b>=a&&d>=e:b>a&&d>e};return l(d,h,e,i)&&l(f,j,g,k)},[null,[1,-1],[1,1],[-1,1],[-1,-1]]),h=[null,[-1,-1],[-1,1],[1,1],[1,-1]];b.pointOnLine=function(a,b,c){var d=e(a,b),i=f(a,b),j=c>0?g[i]:h[i],k=Math.atan(d),l=Math.abs(c*Math.sin(k))*j[1],m=Math.abs(c*Math.cos(k))*j[0];return{x:a.x+m,y:a.y+l}},b.perpendicularLineTo=function(a,b,c){var d=e(a,b),f=Math.atan(-1/d),g=c/2*Math.sin(f),h=c/2*Math.cos(f);return[{x:b.x+h,y:b.y+g},{x:b.x-h,y:b.y-g}]}}.call("undefined"!=typeof window?window:this),function(){"use strict";function a(a,b,c,d,e,f,g,h){return new Touch({target:b,identifier:J(),pageX:c,pageY:d,screenX:e,screenY:f,clientX:g||e,clientY:h||f})}function b(){var a=[];return Array.prototype.push.apply(a,arguments),a.item=function(a){return this[a]},a}function c(c,d,e,f,g,h,i,j){return b(a.apply(null,arguments))}var d=this,e=function(a,b,c){c=c||a.parentNode;for(var d=c.querySelectorAll(b),e=0;e<d.length;e++)if(d[e]===a)return!0;return!1},f=function(a){return"string"==typeof a||a.constructor===String?document.getElementById(a):a},g=function(a){return a.srcElement||a.target},h=function(a,b,c,d){if(d){if("undefined"!=typeof a.path&&a.path.indexOf)return{path:a.path,end:a.path.indexOf(c)};var e={path:[],end:-1},f=function(a){e.path.push(a),a===c?e.end=e.path.length-1:null!=a.parentNode&&f(a.parentNode)};return f(b),e}return{path:[b],end:1}},i=function(a,b){for(var c=0,d=a.length;d>c&&a[c]!=b;c++);c<a.length&&a.splice(c,1)},j=1,k=function(a,b,c){var d=j++;return a.__ta=a.__ta||{},a.__ta[b]=a.__ta[b]||{},a.__ta[b][d]=c,c.__tauid=d,d},l=function(a,b,c){if(a.__ta&&a.__ta[b]&&delete a.__ta[b][c.__tauid],c.__taExtra){for(var d=0;d<c.__taExtra.length;d++)H(a,c.__taExtra[d][0],c.__taExtra[d][1]);c.__taExtra.length=0}c.__taUnstore&&c.__taUnstore()},m=function(a,b,c,d){if(null==a)return c;var f=a.split(","),i=function(d){i.__tauid=c.__tauid;var j=g(d),k=j,l=h(d,j,b,null!=a);if(-1!=l.end)for(var m=0;m<l.end;m++){k=l.path[m];for(var n=0;n<f.length;n++)e(k,f[n],b)&&c.apply(k,arguments)}};return n(c,d,i),i},n=function(a,b,c){a.__taExtra=a.__taExtra||[],a.__taExtra.push([b,c])},o=function(a,b,c,d){if(u&&w[b]){var e=m(d,a,c,w[b]);G(a,w[b],e,c)}"focus"===b&&null==a.getAttribute("tabindex")&&a.setAttribute("tabindex","1"),G(a,b,m(d,a,c,b),c)},p=function(a,b,c,d){if(null==a.__taSmartClicks){var e=function(b){a.__tad=A(b)},f=function(b){a.__tau=A(b)},h=function(b){if(a.__tad&&a.__tau&&a.__tad[0]===a.__tau[0]&&a.__tad[1]===a.__tau[1])for(var c=0;c<a.__taSmartClicks.length;c++)a.__taSmartClicks[c].apply(g(b),[b])};o(a,"mousedown",e,d),o(a,"mouseup",f,d),o(a,"click",h,d),a.__taSmartClicks=[]}a.__taSmartClicks.push(c),c.__taUnstore=function(){i(a.__taSmartClicks,c)}},q={tap:{touches:1,taps:1},dbltap:{touches:1,taps:2},contextmenu:{touches:2,taps:1}},r=function(a,b){return function(c,d,f,j){if("contextmenu"==d&&v)o(c,d,f,j);else{if(null==c.__taTapHandler){var k=c.__taTapHandler={tap:[],dbltap:[],contextmenu:[],down:!1,taps:0,downSelectors:[]},l=function(d){for(var f=g(d),i=h(d,f,c,null!=j),l=!1,m=0;m<i.end;m++){if(l)return;f=i.path[m];for(var o=0;o<k.downSelectors.length;o++)if(null==k.downSelectors[o]||e(f,k.downSelectors[o],c)){k.down=!0,setTimeout(n,a),setTimeout(p,b),l=!0;break}}},m=function(a){if(k.down){var b,d,f=g(a);k.taps++;var i=F(a);for(var j in q)if(q.hasOwnProperty(j)){var l=q[j];if(l.touches===i&&(1===l.taps||l.taps===k.taps))for(var m=0;m<k[j].length;m++){d=h(a,f,c,null!=k[j][m][1]);for(var n=0;n<d.end;n++)if(b=d.path[n],null==k[j][m][1]||e(b,k[j][m][1],c)){k[j][m][0].apply(b,[a]);break}}}}},n=function(){k.down=!1},p=function(){k.taps=0};o(c,"mousedown",l),o(c,"mouseup",m)}c.__taTapHandler.downSelectors.push(j),c.__taTapHandler[d].push([f,j]),f.__taUnstore=function(){i(c.__taTapHandler[d],f)}}}},s=function(a,b,c,d){for(var e in c.__tamee[a])c.__tamee[a].hasOwnProperty(e)&&c.__tamee[a][e].apply(d,[b])},t=function(){var a=[];return function(b,c,d,f){if(!b.__tamee){b.__tamee={over:!1,mouseenter:[],mouseexit:[]};var h=function(c){var d=g(c);(null==f&&d==b&&!b.__tamee.over||e(d,f,b)&&(null==d.__tamee||!d.__tamee.over))&&(s("mouseenter",c,b,d),d.__tamee=d.__tamee||{},d.__tamee.over=!0,a.push(d))},i=function(c){for(var d=g(c),f=0;f<a.length;f++)d!=a[f]||e(c.relatedTarget||c.toElement,"*",d)||(d.__tamee.over=!1,a.splice(f,1),s("mouseexit",c,b,d))};G(b,"mouseover",m(f,b,h,"mouseover"),h),G(b,"mouseout",m(f,b,i,"mouseout"),i)}d.__taUnstore=function(){delete b.__tamee[c][d.__tauid]},k(b,c,d),b.__tamee[c][d.__tauid]=d}},u="ontouchstart"in document.documentElement||navigator.maxTouchPoints,v="onmousedown"in document.documentElement,w={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"},x=function(){var a=-1;if("Microsoft Internet Explorer"==navigator.appName){var b=navigator.userAgent,c=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");null!=c.exec(b)&&(a=parseFloat(RegExp.$1))}return a}(),y=x>-1&&9>x,z=function(a,b){if(null==a)return[0,0];var c=E(a),d=D(c,0);return[d[b+"X"],d[b+"Y"]]},A=function(a){return null==a?[0,0]:y?[a.clientX+document.documentElement.scrollLeft,a.clientY+document.documentElement.scrollTop]:z(a,"page")},B=function(a){return z(a,"screen")},C=function(a){return z(a,"client")},D=function(a,b){return a.item?a.item(b):a[b]},E=function(a){return a.touches&&a.touches.length>0?a.touches:a.changedTouches&&a.changedTouches.length>0?a.changedTouches:a.targetTouches&&a.targetTouches.length>0?a.targetTouches:[a]},F=function(a){return E(a).length},G=function(a,b,c,d){if(k(a,b,c),d.__tauid=c.__tauid,a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent){var e=b+c.__tauid;a["e"+e]=c,a[e]=function(){a["e"+e]&&a["e"+e](window.event)},a.attachEvent("on"+b,a[e])}},H=function(a,b,c){null!=c&&I(a,function(){var d=f(this);if(l(d,b,c),null!=c.__tauid)if(d.removeEventListener)d.removeEventListener(b,c,!1),u&&w[b]&&d.removeEventListener(w[b],c,!1);else if(this.detachEvent){var e=b+c.__tauid;d[e]&&d.detachEvent("on"+b,d[e]),d[e]=null,d["e"+e]=null}c.__taTouchProxy&&H(a,c.__taTouchProxy[1],c.__taTouchProxy[0])})},I=function(a,b){if(null!=a){a="undefined"!=typeof Window&&"unknown"!=typeof a.top&&a==a.top?[a]:"string"!=typeof a&&null==a.tagName&&null!=a.length?a:"string"==typeof a?document.querySelectorAll(a):[a];for(var c=0;c<a.length;c++)b.apply(a[c])}},J=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0,c="x"==a?b:3&b|8;return c.toString(16)})};d.Mottle=function(a){a=a||{};var b=a.clickThreshold||250,d=a.dblClickThreshold||450,e=new t,g=new r(b,d),h=a.smartClicks,i=function(a,b,c,d){null!=c&&I(a,function(){var a=f(this);h&&"click"===b?p(a,b,c,d):"tap"===b||"dbltap"===b||"contextmenu"===b?g(a,b,c,d):"mouseenter"===b||"mouseexit"==b?e(a,b,c,d):o(a,b,c,d)})};this.remove=function(a){return I(a,function(){var a=f(this);if(a.__ta)for(var b in a.__ta)if(a.__ta.hasOwnProperty(b))for(var c in a.__ta[b])a.__ta[b].hasOwnProperty(c)&&H(a,b,a.__ta[b][c]);a.parentNode&&a.parentNode.removeChild(a)}),this},this.on=function(a,b,c,d){var e=arguments[0],f=4==arguments.length?arguments[2]:null,g=arguments[1],h=arguments[arguments.length-1];return i(e,g,h,f),this},this.off=function(a,b,c){return H(a,b,c),this},this.trigger=function(a,b,d,e){var g=v&&("undefined"==typeof MouseEvent||null==d||d.constructor===MouseEvent),h=u&&!v&&w[b]?w[b]:b,i=!(u&&!v&&w[b]),j=A(d),k=B(d),l=C(d);return I(a,function(){var a,m=f(this);d=d||{screenX:k[0],screenY:k[1],clientX:l[0],clientY:l[1]};var n=function(a){e&&(a.payload=e)},o={TouchEvent:function(a){var b=c(window,m,0,j[0],j[1],k[0],k[1],l[0],l[1]),d=a.initTouchEvent||a.initEvent;d(h,!0,!0,window,null,k[0],k[1],l[0],l[1],!1,!1,!1,!1,b,b,b,1,0)},MouseEvents:function(a){a.initMouseEvent(h,!0,!0,window,0,k[0],k[1],l[0],l[1],!1,!1,!1,!1,1,m)}};if(document.createEvent){var p=!i&&!g&&u&&w[b],q=p?"TouchEvent":"MouseEvents";a=document.createEvent(q),o[q](a),n(a),m.dispatchEvent(a)}else document.createEventObject&&(a=document.createEventObject(),a.eventType=a.eventName=h,a.screenX=k[0],a.screenY=k[1],a.clientX=l[0],a.clientY=l[1],n(a),m.fireEvent("on"+h,a))}),this}},d.Mottle.consume=function(a,b){a.stopPropagation?a.stopPropagation():a.returnValue=!1,!b&&a.preventDefault&&a.preventDefault()},d.Mottle.pageLocation=A,d.Mottle.setForceTouchEvents=function(a){u=a},d.Mottle.setForceMouseEvents=function(a){v=a},d.Mottle.version="0.8.0","undefined"!=typeof exports&&(exports.Mottle=d.Mottle)}.call("undefined"==typeof window?this:window),function(){"use strict";var a=this,b=function(a,b,c){return-1===a.indexOf(b)?(c?a.unshift(b):a.push(b),!0):!1},c=function(a,b){var c=a.indexOf(b);-1!==c&&a.splice(c,1)},d=function(a,b){for(var c=[],d=0;d<a.length;d++)-1===b.indexOf(a[d])&&c.push(a[d]);return c},e=function(a){return null==a?!1:"string"==typeof a||a.constructor===String},f=function(a){var b=a.getBoundingClientRect(),c=document.body,d=document.documentElement,e=window.pageYOffset||d.scrollTop||c.scrollTop,f=window.pageXOffset||d.scrollLeft||c.scrollLeft,g=d.clientTop||c.clientTop||0,h=d.clientLeft||c.clientLeft||0,i=b.top+e-g,j=b.left+f-h;return{top:Math.round(i),left:Math.round(j)}},g=function(a,b,c){c=c||a.parentNode;for(var d=c.querySelectorAll(b),e=0;e<d.length;e++)if(d[e]===a)return!0;return!1},h=function(a,b,c){if(g(b,c,a))return b;for(var d=b.parentNode;null!=d&&d!==a;){if(g(d,c,a))return d;d=d.parentNode}},i=function(a,b,c){for(var d=null,e=b.getAttribute("katavorio-draggable"),f=null!=e?"[katavorio-draggable='"+e+"'] ":"",i=0;i<a.length;i++)if(d=h(b,c,f+a[i].selector),null!=d){if(a[i].filter){var j=g(c,a[i].filter,d),k=a[i].filterExclude===!0;if(k&&!j||j)return null}return[a[i],d]}return null},j=function(){var a=-1;if("Microsoft Internet Explorer"===navigator.appName){var b=navigator.userAgent,c=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");null!=c.exec(b)&&(a=parseFloat(RegExp.$1))}return a}(),k=10,l=10,m=j>-1&&9>j,n=9===j,o=function(a){if(m)return[a.clientX+document.documentElement.scrollLeft,a.clientY+document.documentElement.scrollTop];var b=q(a),c=p(b,0);return n?[c.pageX||c.clientX,c.pageY||c.clientY]:[c.pageX,c.pageY]},p=function(a,b){return a.item?a.item(b):a[b]},q=function(a){return a.touches&&a.touches.length>0?a.touches:a.changedTouches&&a.changedTouches.length>0?a.changedTouches:a.targetTouches&&a.targetTouches.length>0?a.targetTouches:[a]},r={delegatedDraggable:"katavorio-delegated-draggable",draggable:"katavorio-draggable",droppable:"katavorio-droppable",drag:"katavorio-drag",selected:"katavorio-drag-selected",active:"katavorio-drag-active",hover:"katavorio-drag-hover",noSelect:"katavorio-drag-no-select",ghostProxy:"katavorio-ghost-proxy",clonedDrag:"katavorio-clone-drag"},s="katavorio-drag-scope",t=["stop","start","drag","drop","over","out","beforeStart"],u=function(){},v=function(){return!0},w=function(a,b,c){for(var d=0;d<a.length;d++)a[d]!=c&&b(a[d])},x=function(a,b,c,d){w(a,function(a){a.setActive(b),b&&a.updatePosition(),c&&a.setHover(d,b)})},y=function(a,b){if(null!=a){a=e(a)||null!=a.tagName||null==a.length?[a]:a;for(var c=0;c<a.length;c++)b.apply(a[c],[a[c]])}},z=function(a){a.stopPropagation?(a.stopPropagation(),a.preventDefault()):a.returnValue=!1},A="input,textarea,select,button,option",B=function(a,b,c){var d=a.srcElement||a.target;return!g(d,c.getInputFilterSelector(),b)},C=function(a,b,c,d){this.params=b||{},this.el=a,this.params.addClass(this.el,this._class),this.uuid=H();var e=!0;return this.setEnabled=function(a){e=a},this.isEnabled=function(){return e},this.toggleEnabled=function(){e=!e},this.setScope=function(a){this.scopes=a?a.split(/\s+/):[d]},this.addScope=function(a){var b={};y(this.scopes,function(a){b[a]=!0}),y(a?a.split(/\s+/):[],function(a){b[a]=!0}),this.scopes=[];for(var c in b)this.scopes.push(c)},this.removeScope=function(a){var b={};y(this.scopes,function(a){b[a]=!0}),y(a?a.split(/\s+/):[],function(a){delete b[a]}),this.scopes=[];for(var c in b)this.scopes.push(c)},this.toggleScope=function(a){var b={};y(this.scopes,function(a){b[a]=!0}),y(a?a.split(/\s+/):[],function(a){b[a]?delete b[a]:b[a]=!0}),this.scopes=[];for(var c in b)this.scopes.push(c)},this.setScope(b.scope),this.k=b.katavorio,b.katavorio},D=function(){return!0},E=function(){return!1},F=function(a,b,c,d){this._class=c.draggable;var h=C.apply(this,arguments);this.rightButtonCanDrag=this.params.rightButtonCanDrag;var j,m,n,p,q,s,t=[0,0],u=null,w=null,y=[0,0],A=!1,F=[0,0],G=this.params.consumeStartEvent!==!1,J=this.el,K=this.params.clone,L=(this.params.scroll,b.multipleDrop!==!1),M=!1,N=null,O=[],P=null,Q=b.ghostProxyParent;if(j=b.ghostProxy===!0?D:b.ghostProxy&&"function"==typeof b.ghostProxy?b.ghostProxy:function(a,b){return P&&P.useGhostProxy?P.useGhostProxy(a,b):!1},m=b.makeGhostProxy?b.makeGhostProxy:function(a){return P&&P.makeGhostProxy?P.makeGhostProxy(a):a.cloneNode(!0)},b.selector){var R=a.getAttribute("katavorio-draggable");null==R&&(R=""+(new Date).getTime(),a.setAttribute("katavorio-draggable",R)),O.push(b)}var S=b.snapThreshold,T=function(a,b,c,d,e){var f=Math.floor(a[0]/b),g=b*f,h=g+b,i=Math.abs(a[0]-g)<=d?g:Math.abs(h-a[0])<=d?h:a[0],j=Math.floor(a[1]/c),k=c*j,l=k+c,m=Math.abs(a[1]-k)<=e?k:Math.abs(l-a[1])<=e?l:a[1];return[i,m]};this.posses=[],this.posseRoles={},this.toGrid=function(a){if(null==this.params.grid)return a;var b=this.params.grid?this.params.grid[0]/2:S?S:k/2,c=this.params.grid?this.params.grid[1]/2:S?S:l/2;return T(a,this.params.grid[0],this.params.grid[1],b,c)},this.snap=function(a,b){if(null!=J){a=a||(this.params.grid?this.params.grid[0]:k),b=b||(this.params.grid?this.params.grid[1]:l);var c=this.params.getPosition(J),d=this.params.grid?this.params.grid[0]/2:S,e=this.params.grid?this.params.grid[1]/2:S,f=T(c,a,b,d,e);return this.params.setPosition(J,f),f}},this.setUseGhostProxy=function(a){j=a?D:E};var U,V=function(a){return b.allowNegative===!1?[Math.max(0,a[0]),Math.max(0,a[1])]:a},W=function(a){U="function"==typeof a?a:a?function(a,b,c,d){return V([Math.max(0,Math.min(c.w-d[0],a[0])),Math.max(0,Math.min(c.h-d[1],a[1]))])}.bind(this):function(a){return V(a)}}.bind(this);W("function"==typeof this.params.constrain?this.params.constrain:this.params.constrain||this.params.containment),this.setConstrain=function(a){W(a)};var X,Y=function(a,b,c,d){return null!=P&&P.constrain&&"function"==typeof P.constrain?P.constrain(a,b,c,d):U(a,b,c,d)};this.setRevert=function(a){X=a},this.params.revert&&(X=this.params.revert);var Z=function(a){return"function"==typeof a?(a._katavorioId=H(),a._katavorioId):a},$={},_=function(a){for(var b in $){var c=$[b],d=c[0](a);if(c[1]&&(d=!d),!d)return!1}return!0},aa=this.setFilter=function(b,c){if(b){var d=Z(b);$[d]=[function(c){var d,f=c.srcElement||c.target;return e(b)?d=g(f,b,a):"function"==typeof b&&(d=b(c,a)),d},c!==!1]}};this.addFilter=aa,this.removeFilter=function(a){var b="function"==typeof a?a._katavorioId:a;delete $[b]};this.clearAllFilters=function(){$={}},this.canDrag=this.params.canDrag||v;var ba,ca=[],da=[];this.addSelector=function(a){a.selector&&O.push(a)},this.downListener=function(a){if(!a.defaultPrevented){var b=this.rightButtonCanDrag||3!==a.which&&2!==a.button;if(b&&this.isEnabled()&&this.canDrag()){var d=_(a)&&B(a,this.el,this.k);if(d){if(P=null,N=null,O.length>0){var e=i(O,this.el,a.target||a.srcElement);if(null!=e&&(P=e[0],N=e[1]),null==N)return}else N=this.el;if(K)if(J=N.cloneNode(!0),this.params.addClass(J,r.clonedDrag),J.setAttribute("id",null),J.style.position="absolute",null!=this.params.parent){var g=this.params.getPosition(this.el);J.style.left=g[0]+"px",J.style.top=g[1]+"px",this.params.parent.appendChild(J)}else{var j=f(N);J.style.left=j.left+"px",J.style.top=j.top+"px",document.body.appendChild(J)}else J=N;G&&z(a),t=o(a),J&&J.parentNode&&(F=[J.parentNode.scrollLeft,J.parentNode.scrollTop]),this.params.bind(document,"mousemove",this.moveListener),this.params.bind(document,"mouseup",this.upListener),h.markSelection(this),h.markPosses(this),this.params.addClass(document.body,c.noSelect),fa("beforeStart",{el:this.el,pos:u,e:a,drag:this})}else this.params.consumeFilteredEvents&&z(a)}}}.bind(this),this.moveListener=function(a){if(t){if(!A){var b=fa("start",{el:this.el,pos:u,e:a,drag:this});if(b!==!1){if(!t)return;this.mark(!0),A=!0}else this.abort()}if(t){da.length=0;var c=o(a),d=c[0]-t[0],e=c[1]-t[1],f=this.params.ignoreZoom?1:h.getZoom();J&&J.parentNode&&(d+=J.parentNode.scrollLeft-F[0],e+=J.parentNode.scrollTop-F[1]),d/=f,e/=f,this.moveBy(d,e,a),h.updateSelection(d,e,this),h.updatePosses(d,e,this)}}}.bind(this),this.upListener=function(a){t&&(t=null,this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.params.removeClass(document.body,c.noSelect),this.unmark(a),h.unmarkSelection(this,a),h.unmarkPosses(this,a),this.stop(a),h.notifyPosseDragStop(this,a),A=!1,da.length=0,K?(J&&J.parentNode&&J.parentNode.removeChild(J),J=null):X&&X(J,this.params.getPosition(J))===!0&&(this.params.setPosition(J,u),fa("revert",J)))}.bind(this),this.getFilters=function(){return $},this.abort=function(){null!=t&&this.upListener()},this.getDragElement=function(a){return a?N||this.el:J||this.el};var ea={start:[],drag:[],stop:[],over:[],out:[],beforeStart:[],revert:[]};b.events.start&&ea.start.push(b.events.start),b.events.beforeStart&&ea.beforeStart.push(b.events.beforeStart),b.events.stop&&ea.stop.push(b.events.stop),b.events.drag&&ea.drag.push(b.events.drag),b.events.revert&&ea.revert.push(b.events.revert),this.on=function(a,b){ea[a]&&ea[a].push(b)},this.off=function(a,b){if(ea[a]){for(var c=[],d=0;d<ea[a].length;d++)ea[a][d]!==b&&c.push(ea[a][d]);ea[a]=c}};var fa=function(a,b){var c=null;if(P&&P[a])c=P[a](b);else if(ea[a])for(var d=0;d<ea[a].length;d++)try{var e=ea[a][d](b);null!=e&&(c=e)}catch(f){}return c};this.notifyStart=function(a){fa("start",{el:this.el,pos:this.params.getPosition(J),e:a,drag:this})},this.stop=function(a,b){if(b||A){var c=[],d=h.getSelection(),e=this.params.getPosition(J);if(d.length>0)for(var f=0;f<d.length;f++){var g=this.params.getPosition(d[f].el);c.push([d[f].el,{left:g[0],top:g[1]},d[f]])}else c.push([J,{left:e[0],top:e[1]},this]);fa("stop",{el:J,pos:ga||e,finalPos:e,e:a,drag:this,selection:c})}},this.mark=function(a){u=this.params.getPosition(J),w=this.params.getPosition(J,!0),y=[w[0]-u[0],w[1]-u[1]],this.size=this.params.getSize(J),ca=h.getMatchingDroppables(this),x(ca,!0,!1,this),this.params.addClass(J,this.params.dragClass||c.drag);var b;b=this.params.getConstrainingRectangle?this.params.getConstrainingRectangle(J):this.params.getSize(J.parentNode),ba={w:b[0],h:b[1]},q=0,s=0,a&&h.notifySelectionDragStart(this)};var ga;this.unmark=function(a,d){if(x(ca,!1,!0,this),M&&j(N,J)?(ga=[J.offsetLeft-q,J.offsetTop-s],J.parentNode.removeChild(J),J=N):ga=null,this.params.removeClass(J,this.params.dragClass||c.drag),ca.length=0,M=!1,!d){da.length>0&&ga&&b.setPosition(N,ga),da.sort(I);for(var e=0;e<da.length;e++){var f=da[e].drop(this,a);if(f===!0)break}}},this.moveBy=function(a,c,d){da.length=0;var e=this.toGrid([u[0]+a,u[1]+c]),f=Y(e,J,ba,this.size);if(j(this.el,J))if(e[0]!==f[0]||e[1]!==f[1]){if(!M){var g=m(N);b.addClass(g,r.ghostProxy),Q?(Q.appendChild(g),n=b.getPosition(N.parentNode,!0),p=b.getPosition(b.ghostProxyParent,!0),q=n[0]-p[0],s=n[1]-p[1]):N.parentNode.appendChild(g),J=g,M=!0}f=e}else M&&(J.parentNode.removeChild(J),J=N,M=!1,n=null,p=null,q=0,s=0);var h={x:f[0],y:f[1],w:this.size[0],h:this.size[1]},i={x:h.x+y[0],y:h.y+y[1],w:h.w,h:h.h},k=null;this.params.setPosition(J,[f[0]+q,f[1]+s]);for(var l=0;l<ca.length;l++){var o={x:ca[l].pagePosition[0],y:ca[l].pagePosition[1],w:ca[l].size[0],h:ca[l].size[1]};this.params.intersects(i,o)&&(L||null==k||k===ca[l].el)&&ca[l].canDrop(this)?(k||(k=ca[l].el),da.push(ca[l]),ca[l].setHover(this,!0,d)):ca[l].isHover()&&ca[l].setHover(this,!1,d)}fa("drag",{el:this.el,pos:f,e:d,drag:this})},this.destroy=function(){this.params.unbind(this.el,"mousedown",this.downListener),this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.downListener=null,this.upListener=null,this.moveListener=null},this.params.bind(this.el,"mousedown",this.downListener),this.params.handle?aa(this.params.handle,!1):aa(this.params.filter,this.params.filterExclude)},G=function(a,b,c,d){this._class=c.droppable,this.params=b||{},this.rank=b.rank||0,this._activeClass=this.params.activeClass||c.active,this._hoverClass=this.params.hoverClass||c.hover,C.apply(this,arguments);var e=!1;this.allowLoopback=this.params.allowLoopback!==!1,this.setActive=function(a){this.params[a?"addClass":"removeClass"](this.el,this._activeClass)},this.updatePosition=function(){this.position=this.params.getPosition(this.el),this.pagePosition=this.params.getPosition(this.el,!0),this.size=this.params.getSize(this.el)},this.canDrop=this.params.canDrop||function(a){return!0},this.isHover=function(){return e},this.setHover=function(a,b,c){(b||null==this.el._katavorioDragHover||this.el._katavorioDragHover===a.el._katavorio)&&(this.params[b?"addClass":"removeClass"](this.el,this._hoverClass),this.el._katavorioDragHover=b?a.el._katavorio:null,e!==b&&this.params.events[b?"over":"out"]({el:this.el,e:c,drag:a,drop:this}),e=b)},this.drop=function(a,b){return this.params.events.drop({drag:a,e:b,drop:this})},this.destroy=function(){this._class=null,this._activeClass=null,this._hoverClass=null,e=null}},H=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0,c="x"===a?b:3&b|8;return c.toString(16)})},I=function(a,b){return a.rank<b.rank?1:a.rank>b.rank?-1:0},J=function(a){return null==a?null:(a="string"==typeof a||a.constructor===String?document.getElementById(a):a,null==a?null:(a._katavorio=a._katavorio||H(),a))};a.Katavorio=function(a){var f=[],g={};this._dragsByScope={},this._dropsByScope={};var h=1,i=function(a,b){y(a,function(a){for(var c=0;c<a.scopes.length;c++)b[a.scopes[c]]=b[a.scopes[c]]||[],b[a.scopes[c]].push(a)})},j=function(b,c){var d=0;return y(b,function(b){for(var e=0;e<b.scopes.length;e++)if(c[b.scopes[e]]){var f=a.indexOf(c[b.scopes[e]],b);-1!==f&&(c[b.scopes[e]].splice(f,1),d++)}}),d>0},k=(this.getMatchingDroppables=function(a){for(var b=[],c={},d=0;d<a.scopes.length;d++){var e=this._dropsByScope[a.scopes[d]];if(e)for(var f=0;f<e.length;f++)!e[f].canDrop(a)||c[e[f].uuid]||!e[f].allowLoopback&&e[f].el===a.el||(c[e[f].uuid]=!0,b.push(e[f]))}return b.sort(I),b},function(b){b=b||{};var c,d={events:{}};for(c in a)d[c]=a[c];for(c in b)d[c]=b[c];for(c=0;c<t.length;c++)d.events[t[c]]=b[t[c]]||u;return d.katavorio=this,d}.bind(this)),l=function(a,b){for(var c=0;c<t.length;c++)b[t[c]]&&a.on(t[c],b[t[c]])}.bind(this),m={},n=a.css||{},o=a.scope||s;for(var p in r)m[p]=r[p];for(var p in n)m[p]=n[p];var q=a.inputFilterSelector||A;this.getInputFilterSelector=function(){return q},this.setInputFilterSelector=function(a){return q=a,this},this.draggable=function(b,c){var d=[];return y(b,function(b){if(b=J(b),null!=b)if(null==b._katavorioDrag){var e=k(c);b._katavorioDrag=new F(b,e,m,o),i(b._katavorioDrag,this._dragsByScope),d.push(b._katavorioDrag),a.addClass(b,e.selector?m.delegatedDraggable:m.draggable)}else l(b._katavorioDrag,c)}.bind(this)),d},this.droppable=function(b,c){var d=[];return y(b,function(b){if(b=J(b),null!=b){var e=new G(b,k(c),m,o);b._katavorioDrop=b._katavorioDrop||[],b._katavorioDrop.push(e),i(e,this._dropsByScope),d.push(e),a.addClass(b,m.droppable)}}.bind(this)),d},this.select=function(b){return y(b,function(){var b=J(this);b&&b._katavorioDrag&&(g[b._katavorio]||(f.push(b._katavorioDrag),g[b._katavorio]=[b,f.length-1],a.addClass(b,m.selected)))}),this},this.deselect=function(b){return y(b,function(){var b=J(this);if(b&&b._katavorio){var c=g[b._katavorio];if(c){for(var d=[],e=0;e<f.length;e++)f[e].el!==b&&d.push(f[e]);f=d,delete g[b._katavorio],a.removeClass(b,m.selected)}}}),this},this.deselectAll=function(){for(var b in g){var c=g[b];a.removeClass(c[0],m.selected)}f.length=0,g={}},this.markSelection=function(a){w(f,function(a){a.mark()},a)},this.markPosses=function(a){a.posses&&y(a.posses,function(b){a.posseRoles[b]&&C[b]&&w(C[b].members,function(a){a.mark()},a)})},this.unmarkSelection=function(a,b){w(f,function(a){a.unmark(b)},a)},this.unmarkPosses=function(a,b){a.posses&&y(a.posses,function(c){a.posseRoles[c]&&C[c]&&w(C[c].members,function(a){a.unmark(b,!0)},a)})},this.getSelection=function(){return f.slice(0)},this.updateSelection=function(a,b,c){w(f,function(c){c.moveBy(a,b)},c)};var v=function(a,b){b.posses&&y(b.posses,function(c){b.posseRoles[c]&&C[c]&&w(C[c].members,function(b){a(b)},b)})};this.updatePosses=function(a,b,c){v(function(c){c.moveBy(a,b)},c)},this.notifyPosseDragStop=function(a,b){v(function(a){
a.stop(b,!0)},a)},this.notifySelectionDragStop=function(a,b){w(f,function(a){a.stop(b,!0)},a)},this.notifySelectionDragStart=function(a,b){w(f,function(a){a.notifyStart(b)},a)},this.setZoom=function(a){h=a},this.getZoom=function(){return h};var x=function(a,b,c,d){y(a,function(a){j(a,c),a[d](b),i(a,c)})};y(["set","add","remove","toggle"],function(a){this[a+"Scope"]=function(b,c){x(b._katavorioDrag,c,this._dragsByScope,a+"Scope"),x(b._katavorioDrop,c,this._dropsByScope,a+"Scope")}.bind(this),this[a+"DragScope"]=function(b,c){x(b.constructor===F?b:b._katavorioDrag,c,this._dragsByScope,a+"Scope")}.bind(this),this[a+"DropScope"]=function(b,c){x(b.constructor===G?b:b._katavorioDrop,c,this._dropsByScope,a+"Scope")}.bind(this)}.bind(this)),this.snapToGrid=function(a,b){for(var c in this._dragsByScope)w(this._dragsByScope[c],function(c){c.snap(a,b)})},this.getDragsForScope=function(a){return this._dragsByScope[a]},this.getDropsForScope=function(a){return this._dropsByScope[a]};var z=function(a,b,c){if(a=J(a),a[b]){var d=f.indexOf(a[b]);d>=0&&f.splice(d,1),j(a[b],c)&&y(a[b],function(a){a.destroy()}),delete a[b]}},B=function(a,b,c,d){a=J(a),a[b]&&a[b].off(c,d)};this.elementRemoved=function(a){a._katavorioDrag&&this.destroyDraggable(a),a._katavorioDrop&&this.destroyDroppable(a)},this.destroyDraggable=function(a,b,c){1===arguments.length?z(a,"_katavorioDrag",this._dragsByScope):B(a,"_katavorioDrag",b,c)},this.destroyDroppable=function(a,b,c){1===arguments.length?z(a,"_katavorioDrop",this._dropsByScope):B(a,"_katavorioDrop",b,c)},this.reset=function(){this._dragsByScope={},this._dropsByScope={},f=[],g={},C={}};var C={},D=function(a,c,d){var f=e(c)?c:c.id,g=e(c)?!0:c.active!==!1,h=C[f]||function(){var a={name:f,members:[]};return C[f]=a,a}();return y(a,function(a){if(a._katavorioDrag){if(d&&null!=a._katavorioDrag.posseRoles[h.name])return;b(h.members,a._katavorioDrag),b(a._katavorioDrag.posses,h.name),a._katavorioDrag.posseRoles[h.name]=g}}),h};this.addToPosse=function(a,b){for(var c=[],d=1;d<arguments.length;d++)c.push(D(a,arguments[d]));return 1===c.length?c[0]:c},this.setPosse=function(a,b){for(var c=[],e=1;e<arguments.length;e++)c.push(D(a,arguments[e],!0).name);return y(a,function(a){if(a._katavorioDrag){var b=d(a._katavorioDrag.posses,c),e=[];Array.prototype.push.apply(e,a._katavorioDrag.posses);for(var f=0;f<b.length;f++)this.removeFromPosse(a,b[f])}}.bind(this)),1===c.length?c[0]:c},this.removeFromPosse=function(a,b){if(arguments.length<2)throw new TypeError("No posse id provided for remove operation");for(var d=1;d<arguments.length;d++)b=arguments[d],y(a,function(a){if(a._katavorioDrag&&a._katavorioDrag.posses){var d=a._katavorioDrag;y(b,function(a){c(C[a].members,d),c(d.posses,a),delete d.posseRoles[a]})}})},this.removeFromAllPosses=function(a){y(a,function(a){if(a._katavorioDrag&&a._katavorioDrag.posses){var b=a._katavorioDrag;y(b.posses,function(a){c(C[a].members,b)}),b.posses.length=0,b.posseRoles={}}})},this.setPosseState=function(a,b,c){var d=C[b];d&&y(a,function(a){a._katavorioDrag&&a._katavorioDrag.posses&&(a._katavorioDrag.posseRoles[d.name]=c)})}},a.Katavorio.version="1.0.0","undefined"!=typeof exports&&(exports.Katavorio=a.Katavorio)}.call("undefined"!=typeof window?window:this),function(){function a(a){return"[object Array]"===Object.prototype.toString.call(a)}function b(a){return"[object Number]"===Object.prototype.toString.call(a)}function c(a){return"string"==typeof a}function d(a){return"boolean"==typeof a}function e(a){return null==a}function f(a){return null==a?!1:"[object Object]"===Object.prototype.toString.call(a)}function g(a){return"[object Date]"===Object.prototype.toString.call(a)}function h(a){return"[object Function]"===Object.prototype.toString.call(a)}function i(a){return h(a)&&null!=a.name&&a.name.length>0}function j(a){for(var b in a)if(a.hasOwnProperty(b))return!1;return!0}function k(b){if(c(b))return""+b;if(d(b))return!!b;if(g(b))return new Date(b.getTime());if(h(b))return b;if(a(b)){for(var e=[],i=0;i<b.length;i++)e.push(k(b[i]));return e}if(f(b)){var j={};for(var l in b)j[l]=k(b[l]);return j}return b}function l(b,e,g,h){var i,j,l={},m={};for(g=g||[],h=h||[],j=0;j<g.length;j++)l[g[j]]=!0;for(j=0;j<h.length;j++)m[h[j]]=!0;var n=k(b);for(j in e)if(null==n[j]||m[j])n[j]=e[j];else if(c(e[j])||d(e[j]))l[j]?(i=[],i.push.apply(i,a(n[j])?n[j]:[n[j]]),i.push.apply(i,d(e[j])?e[j]:[e[j]]),n[j]=i):n[j]=e[j];else if(a(e[j]))i=[],a(n[j])&&i.push.apply(i,n[j]),i.push.apply(i,e[j]),n[j]=i;else if(f(e[j])){f(n[j])||(n[j]={});for(var o in e[j])n[j][o]=e[j][o]}return n}function m(a,b,c){if(null!=a){var d=a,e=d;return b.replace(/([^\.])+/g,function(a,b,d,f){var g=a.match(/([^\[0-9]+){1}(\[)([0-9+])/),h=d+a.length>=f.length,i=function(){return e[g[1]]||function(){return e[g[1]]=[],e[g[1]]}()};if(h)g?i()[g[3]]=c:e[a]=c;else if(g){var j=i();e=j[g[3]]||function(){return j[g[3]]={},j[g[3]]}()}else e=e[a]||function(){return e[a]={},e[a]}();return""}),a}}function n(a,b,c){for(var d=0;d<c.length;d++){var e=c[d][0][c[d][1]].apply(c[d][0],c[d][2]);if(e===b)return e}return a}function o(b,d,e,g){var i=function(a){var b=a.match(/(\${.*?})/g);if(null!=b)for(var c=0;c<b.length;c++){var e=d[b[c].substring(2,b[c].length-1)]||"";null!=e&&(a=a.replace(b[c],e))}return a},j=function(b){if(null!=b){if(c(b))return i(b);if(!h(b)||g||null!=e&&0!==(b.name||"").indexOf(e)){if(a(b)){for(var k=[],l=0;l<b.length;l++)k.push(j(b[l]));return k}if(f(b)){var m={};for(var n in b)m[n]=j(b[n]);return m}return b}return b(d)}};return j(b)}function p(a,b){if(a)for(var c=0;c<a.length;c++)if(b(a[c]))return c;return-1}function q(a,b){var c=p(a,b);return c>-1&&a.splice(c,1),-1!==c}function r(a,b){var c=a.indexOf(b);return c>-1&&a.splice(c,1),-1!==c}function s(a,b,c){-1===p(a,c)&&a.push(b)}function t(a,b,c,d){var e=a[b];return null==e&&(e=[],a[b]=e),e[d?"unshift":"push"](c),e}function u(a,b,c){return-1===a.indexOf(b)?(c?a.unshift(b):a.push(b),!0):!1}function v(b,c,d){var e;c=a(c)?c:[c];var f=function(a){for(var c=a.__proto__;null!=c;)if(null!=c.prototype){for(var d in c.prototype)c.prototype.hasOwnProperty(d)&&!b.prototype.hasOwnProperty(d)&&(b.prototype[d]=c.prototype[d]);c=c.prototype.__proto__}else c=null};for(e=0;e<c.length;e++){for(var g in c[e].prototype)c[e].prototype.hasOwnProperty(g)&&!b.prototype.hasOwnProperty(g)&&(b.prototype[g]=c[e].prototype[g]);f(c[e])}var h=function(a,b){return function(){for(e=0;e<c.length;e++)c[e].prototype[a]&&c[e].prototype[a].apply(this,arguments);return b.apply(this,arguments)}},i=function(a){for(var c in a)b.prototype[c]=h(c,a[c])};if(arguments.length>2)for(e=2;e<arguments.length;e++)i(arguments[e]);return b}function w(){var a=4294967295*Math.random()|0,b=4294967295*Math.random()|0,c=4294967295*Math.random()|0,d=4294967295*Math.random()|0;return H[255&a]+H[a>>8&255]+H[a>>16&255]+H[a>>24&255]+"-"+H[255&b]+H[b>>8&255]+"-"+H[b>>16&15|64]+H[b>>24&255]+"-"+H[63&c|128]+H[c>>8&255]+"-"+H[c>>16&255]+H[c>>24&255]+H[255&d]+H[d>>8&255]+H[d>>16&255]+H[d>>24&255]}function x(a){if(null==a)return null;for(var b=a.replace(/^\s\s*/,""),c=/\s/,d=b.length;c.test(b.charAt(--d)););return b.slice(0,d+1)}function y(a,b){a=null==a.length||"string"==typeof a?[a]:a;for(var c=0;c<a.length;c++)b(a[c])}function z(a,b){for(var c=[],d=0;d<a.length;d++)c.push(b(a[d]));return c}function A(a,b,c){c=c||"parent";var d=function(a){return a?b[a]:null},e=function(a){return a?d(a[c]):null},f=function(a,b){if(null==a)return b;var c=["anchor","anchors","cssClass","connector","paintStyle","hoverPaintStyle","endpoint","endpoints"];"override"===b.mergeStrategy&&Array.prototype.push.apply(c,["events","overlays"]);var d=l(a,b,[],c);return f(e(a),d)},g=function(a){if(null==a)return{};if("string"==typeof a)return d(a);if(a.length){for(var b=!1,c=0,e=void 0;!b&&c<a.length;)e=g(a[c]),e?b=!0:c++;return e}},h=g(a);return h?f(e(h),h):{}}function B(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];if(G.logEnabled&&"undefined"!=typeof console)try{var c=arguments[arguments.length-1];console.log(c)}catch(d){}}function C(a,b,c){return function(){var d=null;try{null!=b&&(d=b.apply(this,arguments))}catch(e){B("jsPlumb function failed : "+e)}if(null!=a&&(null==c||d!==c))try{d=a.apply(this,arguments)}catch(e){B("wrapped function failed : "+e)}return d}}function D(a,b,c){var d=[a[0]-b[0],a[1]-b[1]],e=Math.cos(c/360*Math.PI*2),f=Math.sin(c/360*Math.PI*2);return[d[0]*e-d[1]*f+b[0],d[1]*e+d[0]*f+b[1],e,f]}function E(a,b){var c=D(a,[0,0],b);return[Math.round(c[0]),Math.round(c[1])]}var F=this;F.jsPlumbUtil=F.jsPlumbUtil||{};var G=F.jsPlumbUtil;"undefined"!=typeof exports&&(exports.jsPlumbUtil=G),G.isArray=a,G.isNumber=b,G.isString=c,G.isBoolean=d,G.isNull=e,G.isObject=f,G.isDate=g,G.isFunction=h,G.isNamedFunction=i,G.isEmpty=j,G.clone=k,G.merge=l,G.replace=m,G.functionChain=n,G.populate=o,G.findWithFunction=p,G.removeWithFunction=q,G.remove=r,G.addWithFunction=s,G.addToList=t,G.suggest=u,G.extend=v;for(var H=[],I=0;256>I;I++)H[I]=(16>I?"0":"")+I.toString(16);G.uuid=w,G.fastTrim=x,G.each=y,G.map=z,G.mergeWithParents=A,G.logEnabled=!0,G.log=B,G.wrap=C;var J=function(){function a(){var a=this;this._listeners={},this.eventsSuspended=!1,this.tick=!1,this.eventsToDieOn={ready:!0},this.queue=[],this.bind=function(b,c,d){var e=function(b){t(a._listeners,b,c,d),c.__jsPlumb=c.__jsPlumb||{},c.__jsPlumb[w()]=b};if("string"==typeof b)e(b);else if(null!=b.length)for(var f=0;f<b.length;f++)e(b[f]);return a},this.fire=function(a,b,c){if(this.tick)this.queue.unshift(arguments);else{if(this.tick=!0,!this.eventsSuspended&&this._listeners[a]){var d=this._listeners[a].length,e=0,f=!1,g=null;if(!this.shouldFireEvent||this.shouldFireEvent(a,b,c))for(;!f&&d>e&&g!==!1;){if(this.eventsToDieOn[a])this._listeners[a][e].apply(this,[b,c]);else try{g=this._listeners[a][e].apply(this,[b,c])}catch(h){B("jsPlumb: fire failed for event "+a+" : "+h)}e++,(null==this._listeners||null==this._listeners[a])&&(f=!0)}}this.tick=!1,this._drain()}return this},this._drain=function(){var b=a.queue.pop();b&&a.fire.apply(a,b)},this.unbind=function(a,b){if(0===arguments.length)this._listeners={};else if(1===arguments.length){if("string"==typeof a)delete this._listeners[a];else if(a.__jsPlumb){var c=void 0;for(var d in a.__jsPlumb)c=a.__jsPlumb[d],r(this._listeners[c]||[],a)}}else 2===arguments.length&&r(this._listeners[a]||[],b);return this},this.getListener=function(b){return a._listeners[b]},this.setSuspendEvents=function(b){a.eventsSuspended=b},this.isSuspendEvents=function(){return a.eventsSuspended},this.silently=function(b){a.setSuspendEvents(!0);try{b()}catch(c){B("Cannot execute silent function "+c)}a.setSuspendEvents(!1)},this.cleanupListeners=function(){for(var b in a._listeners)a._listeners[b]=null}}return a}();G.EventGenerator=J,G.rotatePoint=D,G.rotateAnchorOrientation=E}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this;a.jsPlumbUtil.matchesSelector=function(a,b,c){c=c||a.parentNode;for(var d=c.querySelectorAll(b),e=0;e<d.length;e++)if(d[e]===a)return!0;return!1},a.jsPlumbUtil.consume=function(a,b){a.stopPropagation?a.stopPropagation():a.returnValue=!1,!b&&a.preventDefault&&a.preventDefault()},a.jsPlumbUtil.sizeElement=function(a,b,c,d,e){a&&(a.style.height=e+"px",a.height=e,a.style.width=d+"px",a.width=d,a.style.left=b+"px",a.style.top=c+"px")}}.call("undefined"!=typeof window?window:this),function(){var a={deriveAnchor:function(a,b,c,d){return{top:["TopRight","TopLeft"],bottom:["BottomRight","BottomLeft"]}[a][b]}},b=this,c=function(a,b){this.count=0,this.instance=a,this.lists={},this.options=b||{},this.instance.addList=function(a,b){return this.listManager.addList(a,b)},this.instance.removeList=function(a){this.listManager.removeList(a)},this.instance.bind("manageElement",function(a){for(var b=this.instance.getSelector(a.el,"[jtk-scrollable-list]"),c=0;c<b.length;c++)this.addList(b[c])}.bind(this)),this.instance.bind("unmanageElement",function(a){this.removeList(a.el)}),this.instance.bind("connection",function(a,b){null==b&&(this._maybeUpdateParentList(a.source),this._maybeUpdateParentList(a.target))}.bind(this))};b.jsPlumbListManager=c,c.prototype={addList:function(b,c){var e=this.instance.extend({},a);this.instance.extend(e,this.options),c=this.instance.extend(e,c||{});var f=[this.instance.getInstanceIndex(),this.count++].join("_");this.lists[f]=new d(this.instance,b,c,f)},removeList:function(a){var b=this.lists[a._jsPlumbList];b&&(b.destroy(),delete this.lists[a._jsPlumbList])},_maybeUpdateParentList:function(a){for(var b=a.parentNode,c=this.instance.getContainer();null!=b&&b!==c;){if(null!=b._jsPlumbList&&null!=this.lists[b._jsPlumbList])return void b._jsPlumbScrollHandler();b=b.parentNode}}};var d=function(a,b,c,d){function e(a,b,d,e){return c.anchor?c.anchor:c.deriveAnchor(a,b,d,e)}function f(a,b,d,e){return c.deriveEndpoint?c.deriveEndpoint(a,b,d,e):c.endpoint?c.endpoint:d.type}function g(b){for(var c=b.parentNode,d=a.getContainer();null!=c&&c!==d;){if(a.hasClass(c,"jtk-managed"))return void a.recalculateOffsets(c);c=c.parentNode}}b._jsPlumbList=d;var h=function(c){for(var d=a.getSelector(b,".jtk-managed"),h=a.getId(b),i=0;i<d.length;i++){if(d[i].offsetTop<b.scrollTop)d[i]._jsPlumbProxies||(d[i]._jsPlumbProxies=d[i]._jsPlumbProxies||[],a.select({source:d[i]}).each(function(c){a.proxyConnection(c,0,b,h,function(){return f("top",0,c.endpoints[0],c)},function(){return e("top",0,c.endpoints[0],c)}),d[i]._jsPlumbProxies.push([c,0])}),a.select({target:d[i]}).each(function(c){a.proxyConnection(c,1,b,h,function(){return f("top",1,c.endpoints[1],c)},function(){return e("top",1,c.endpoints[1],c)}),d[i]._jsPlumbProxies.push([c,1])}));else if(d[i].offsetTop+d[i].offsetHeight>b.scrollTop+b.offsetHeight)d[i]._jsPlumbProxies||(d[i]._jsPlumbProxies=d[i]._jsPlumbProxies||[],a.select({source:d[i]}).each(function(c){a.proxyConnection(c,0,b,h,function(){return f("bottom",0,c.endpoints[0],c)},function(){return e("bottom",0,c.endpoints[0],c)}),d[i]._jsPlumbProxies.push([c,0])}),a.select({target:d[i]}).each(function(c){a.proxyConnection(c,1,b,h,function(){return f("bottom",1,c.endpoints[1],c)},function(){return e("bottom",1,c.endpoints[1],c)}),d[i]._jsPlumbProxies.push([c,1])}));else if(d[i]._jsPlumbProxies){for(var j=0;j<d[i]._jsPlumbProxies.length;j++)a.unproxyConnection(d[i]._jsPlumbProxies[j][0],d[i]._jsPlumbProxies[j][1],h);delete d[i]._jsPlumbProxies}a.revalidate(d[i])}g(b)};a.setAttribute(b,"jtk-scrollable-list","true"),b._jsPlumbScrollHandler=h,a.on(b,"scroll",h),h(),this.destroy=function(){a.off(b,"scroll",h),delete b._jsPlumbScrollHandler;for(var c=a.getSelector(b,".jtk-managed"),d=a.getId(b),e=0;e<c.length;e++)if(c[e]._jsPlumbProxies){for(var f=0;f<c[e]._jsPlumbProxies.length;f++)a.unproxyConnection(c[e]._jsPlumbProxies[f][0],c[e]._jsPlumbProxies[f][1],d);delete c[e]._jsPlumbProxies}}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumbUtil,c=function(a){if(a._jsPlumb.paintStyle&&a._jsPlumb.hoverPaintStyle){var b={};n.extend(b,a._jsPlumb.paintStyle),n.extend(b,a._jsPlumb.hoverPaintStyle),delete a._jsPlumb.hoverPaintStyle,b.gradient&&a._jsPlumb.paintStyle.fill&&delete b.gradient,a._jsPlumb.hoverPaintStyle=b}},d=["tap","dbltap","click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","contextmenu"],e=function(a,b,c,d){var e=a.getAttachedElements();if(e)for(var f=0,g=e.length;g>f;f++)d&&d===e[f]||e[f].setHover(b,!0,c)},f=function(a){return null==a?null:a.split(" ")},g=function(a,b,c){for(var d in b)a[d]=c},h=function(a,c,d){if(a.getDefaultType){var e=a.getTypeDescriptor(),f={},h=a.getDefaultType(),i=b.merge({},h);g(f,h,"__default");for(var j=0,k=a._jsPlumb.types.length;k>j;j++){var l=a._jsPlumb.types[j];if("__default"!==l){var m=a._jsPlumb.instance.getType(l,e);if(null!=m){var n=["anchor","anchors","connector","paintStyle","hoverPaintStyle","endpoint","endpoints","connectorOverlays","connectorStyle","connectorHoverStyle","endpointStyle","endpointHoverStyle"],o=[];"override"===m.mergeStrategy?Array.prototype.push.apply(n,["events","overlays","cssClass"]):o.push("cssClass"),i=b.merge(i,m,o,n),g(f,m,l)}}}c&&(i=b.populate(i,c,"_")),a.applyType(i,d,f),d||a.repaint()}},i=a.jsPlumbUIComponent=function(a){b.EventGenerator.apply(this,arguments);var c=this,d=arguments,e=c.idPrefix,f=e+(new Date).getTime();this._jsPlumb={instance:a._jsPlumb,parameters:a.parameters||{},paintStyle:null,hoverPaintStyle:null,paintStyleInUse:null,hover:!1,beforeDetach:a.beforeDetach,beforeDrop:a.beforeDrop,overlayPlacements:[],hoverClass:a.hoverClass||a._jsPlumb.Defaults.HoverClass,types:[],typeCache:{}},this.cacheTypeItem=function(a,b,c){this._jsPlumb.typeCache[c]=this._jsPlumb.typeCache[c]||{},this._jsPlumb.typeCache[c][a]=b},this.getCachedTypeItem=function(a,b){return this._jsPlumb.typeCache[b]?this._jsPlumb.typeCache[b][a]:null},this.getId=function(){return f};var g=a.overlays||[],h={};if(this.defaultOverlayKeys){for(var i=0;i<this.defaultOverlayKeys.length;i++)Array.prototype.push.apply(g,this._jsPlumb.instance.Defaults[this.defaultOverlayKeys[i]]||[]);for(i=0;i<g.length;i++){var j=n.convertToFullOverlaySpec(g[i]);h[j[1].id]=j}}var k={overlays:h,parameters:a.parameters||{},scope:a.scope||this._jsPlumb.instance.getDefaultScope()};if(this.getDefaultType=function(){return k},this.appendToDefaultType=function(a){for(var b in a)k[b]=a[b]},a.events)for(var l in a.events)c.bind(l,a.events[l]);this.clone=function(){var a=Object.create(this.constructor.prototype);return this.constructor.apply(a,d),a}.bind(this),this.isDetachAllowed=function(a){var c=!0;if(this._jsPlumb.beforeDetach)try{c=this._jsPlumb.beforeDetach(a)}catch(d){b.log("jsPlumb: beforeDetach callback failed",d)}return c},this.isDropAllowed=function(a,c,d,e,f,g,h){var i=this._jsPlumb.instance.checkCondition("beforeDrop",{sourceId:a,targetId:c,scope:d,connection:e,dropEndpoint:f,source:g,target:h});if(this._jsPlumb.beforeDrop)try{i=this._jsPlumb.beforeDrop({sourceId:a,targetId:c,scope:d,connection:e,dropEndpoint:f,source:g,target:h})}catch(j){b.log("jsPlumb: beforeDrop callback failed",j)}return i};var m=[];this.setListenerComponent=function(a){for(var b=0;b<m.length;b++)m[b][3]=a}},j=function(a,b){var c=a._jsPlumb.types[b],d=a._jsPlumb.instance.getType(c,a.getTypeDescriptor());null!=d&&d.cssClass&&a.canvas&&a._jsPlumb.instance.removeClass(a.canvas,d.cssClass)};b.extend(a.jsPlumbUIComponent,b.EventGenerator,{getParameter:function(a){return this._jsPlumb.parameters[a]},setParameter:function(a,b){this._jsPlumb.parameters[a]=b},getParameters:function(){return this._jsPlumb.parameters},setParameters:function(a){this._jsPlumb.parameters=a},getClass:function(){return n.getClass(this.canvas)},hasClass:function(a){return n.hasClass(this.canvas,a)},addClass:function(a){n.addClass(this.canvas,a)},removeClass:function(a){n.removeClass(this.canvas,a)},updateClasses:function(a,b){n.updateClasses(this.canvas,a,b)},setType:function(a,b,c){this.clearTypes(),this._jsPlumb.types=f(a)||[],h(this,b,c)},getType:function(){return this._jsPlumb.types},reapplyTypes:function(a,b){h(this,a,b)},hasType:function(a){return-1!==this._jsPlumb.types.indexOf(a)},addType:function(a,b,c){var d=f(a),e=!1;if(null!=d){for(var g=0,i=d.length;i>g;g++)this.hasType(d[g])||(this._jsPlumb.types.push(d[g]),e=!0);e&&h(this,b,c)}},removeType:function(a,b,c){var d=f(a),e=!1,g=function(a){var b=this._jsPlumb.types.indexOf(a);return-1!==b?(j(this,b),this._jsPlumb.types.splice(b,1),!0):!1}.bind(this);if(null!=d){for(var i=0,k=d.length;k>i;i++)e=g(d[i])||e;e&&h(this,b,c)}},clearTypes:function(a,b){for(var c=this._jsPlumb.types.length,d=0;c>d;d++)j(this,0),this._jsPlumb.types.splice(0,1);h(this,a,b)},toggleType:function(a,b,c){var d=f(a);if(null!=d){for(var e=0,g=d.length;g>e;e++){var i=this._jsPlumb.types.indexOf(d[e]);-1!==i?(j(this,i),this._jsPlumb.types.splice(i,1)):this._jsPlumb.types.push(d[e])}h(this,b,c)}},applyType:function(a,b){if(this.setPaintStyle(a.paintStyle,b),this.setHoverPaintStyle(a.hoverPaintStyle,b),a.parameters)for(var c in a.parameters)this.setParameter(c,a.parameters[c]);this._jsPlumb.paintStyleInUse=this.getPaintStyle()},setPaintStyle:function(a,b){this._jsPlumb.paintStyle=a,this._jsPlumb.paintStyleInUse=this._jsPlumb.paintStyle,c(this),b||this.repaint()},getPaintStyle:function(){return this._jsPlumb.paintStyle},setHoverPaintStyle:function(a,b){this._jsPlumb.hoverPaintStyle=a,c(this),b||this.repaint()},getHoverPaintStyle:function(){return this._jsPlumb.hoverPaintStyle},destroy:function(a){(a||null==this.typeId)&&(this.cleanupListeners(),this.clone=null,this._jsPlumb=null)},isHover:function(){return this._jsPlumb.hover},setHover:function(a,b,c){if(this._jsPlumb&&!this._jsPlumb.instance.currentlyDragging&&!this._jsPlumb.instance.isHoverSuspended()){this._jsPlumb.hover=a;var d=a?"addClass":"removeClass";null!=this.canvas&&(null!=this._jsPlumb.instance.hoverClass&&this._jsPlumb.instance[d](this.canvas,this._jsPlumb.instance.hoverClass),null!=this._jsPlumb.hoverClass&&this._jsPlumb.instance[d](this.canvas,this._jsPlumb.hoverClass)),null!=this._jsPlumb.hoverPaintStyle&&(this._jsPlumb.paintStyleInUse=a?this._jsPlumb.hoverPaintStyle:this._jsPlumb.paintStyle,this._jsPlumb.instance.isSuspendDrawing()||(c=c||jsPlumbUtil.uuid(),this.repaint({timestamp:c,recalc:!1}))),this.getAttachedElements&&!b&&e(this,a,jsPlumbUtil.uuid(),this)}}});var k=0,l=function(){var a=k+1;return k++,a},m=a.jsPlumbInstance=function(c){this.version="2.15.6",this.Defaults={Anchor:"Bottom",Anchors:[null,null],ConnectionsDetachable:!0,ConnectionOverlays:[],Connector:"Bezier",Container:null,DoNotThrowErrors:!1,DragOptions:{},DropOptions:{},Endpoint:"Dot",EndpointOverlays:[],Endpoints:[null,null],EndpointStyle:{fill:"#456"},EndpointStyles:[null,null],EndpointHoverStyle:null,EndpointHoverStyles:[null,null],HoverPaintStyle:null,LabelStyle:{color:"black"},ListStyle:{},LogEnabled:!1,Overlays:[],MaxConnections:1,PaintStyle:{"stroke-width":4,stroke:"#456"},ReattachConnections:!1,RenderMode:"svg",Scope:"jsPlumb_DefaultScope"},c&&n.extend(this.Defaults,c),this.logEnabled=this.Defaults.LogEnabled,this._connectionTypes={},this._endpointTypes={},b.EventGenerator.apply(this);var e=this,f=l(),g=e.bind,h={},j=1,k=function(a){if(null==a)return null;if(3===a.nodeType||8===a.nodeType)return{el:a,text:!0};var c=e.getElement(a);return{el:c,id:b.isString(a)&&null==c?a:S(c)}};this.getInstanceIndex=function(){return f},this.setZoom=function(a,b){return j=a,e.fire("zoom",j),b&&e.repaintEverything(),!0},this.getZoom=function(){return j};for(var m in this.Defaults)h[m]=this.Defaults[m];var o,p=[];this.unbindContainer=function(){if(null!=o&&p.length>0)for(var a=0;a<p.length;a++)e.off(o,p[a][0],p[a][1])},this.setContainer=function(a){this.unbindContainer(),a=this.getElement(a),this.select().each(function(b){b.moveParent(a)}),this.selectEndpoints().each(function(b){b.moveParent(a)});var b=o;o=a,p.length=0;for(var c={endpointclick:"endpointClick",endpointdblclick:"endpointDblClick"},f=function(a,b,d){var f=b.srcElement||b.target,g=(f&&f.parentNode?f.parentNode._jsPlumb:null)||(f?f._jsPlumb:null)||(f&&f.parentNode&&f.parentNode.parentNode?f.parentNode.parentNode._jsPlumb:null);if(g){g.fire(a,g,b);var h=d?c[d+a]||a:a;e.fire(h,g.component||g,b)}},g=function(a,b,c){p.push([a,c]),e.on(o,a,b,c)},h=function(a){g(a,".jtk-connector",function(b){f(a,b)}),g(a,".jtk-endpoint",function(b){f(a,b,"endpoint")}),g(a,".jtk-overlay",function(b){f(a,b)})},i=0;i<d.length;i++)h(d[i]);for(var j in v){var k=v[j].el;k.parentNode===b&&(b.removeChild(k),o.appendChild(k))}},this.getContainer=function(){return o},this.bind=function(a,b){"ready"===a&&r?b():g.apply(e,[a,b])},e.importDefaults=function(a){for(var b in a)e.Defaults[b]=a[b];return a.Container&&e.setContainer(a.Container),e},e.restoreDefaults=function(){return e.Defaults=n.extend({},h),e};var q=null,r=!1,s=[],t={},u={},v={},w={},x={},y=!1,z=[],A=!1,B=null,C=this.Defaults.Scope,D=1,E=function(){return""+D++},F=function(a,b){o?o.appendChild(a):b?this.getElement(b).appendChild(a):this.appendToRoot(a)}.bind(this),G=function(a,b,c,d){var f={c:[],e:[]};if(!A&&(a=e.getElement(a),null!=a)){var g=S(a),h=a.querySelectorAll(".jtk-managed");null==c&&(c=jsPlumbUtil.uuid());for(var i=(na({elId:g,offset:b,recalc:!1,timestamp:c}),0);i<h.length;i++)na({elId:h[i].getAttribute("id"),recalc:!0,timestamp:c});var j=e.router.redraw(g,b,c,null,d);if(Array.prototype.push.apply(f.c,j.c),Array.prototype.push.apply(f.e,j.e),h)for(var k=0;k<h.length;k++)j=e.router.redraw(h[k].getAttribute("id"),null,c,null,d,!0),Array.prototype.push.apply(f.c,j.c),Array.prototype.push.apply(f.e,j.e)}return f},H=function(a){return u[a]},I=function(a,b){for(var c=a.scope.split(/\s/),d=b.scope.split(/\s/),e=0;e<c.length;e++)for(var f=0;f<d.length;f++)if(d[f]===c[e])return!0;return!1},J=function(a,b){var c=n.extend({},a);for(var d in b)b[d]&&(c[d]=b[d]);return c},K=function(a,c){var d=n.extend({},a);if(c&&n.extend(d,c),d.source&&(d.source.endpoint?d.sourceEndpoint=d.source:d.source=e.getElement(d.source)),d.target&&(d.target.endpoint?d.targetEndpoint=d.target:d.target=e.getElement(d.target)),a.uuids&&(d.sourceEndpoint=H(a.uuids[0]),d.targetEndpoint=H(a.uuids[1])),d.sourceEndpoint&&d.sourceEndpoint.isFull())return void b.log(e,"could not add connection; source endpoint is full");if(d.targetEndpoint&&d.targetEndpoint.isFull())return void b.log(e,"could not add connection; target endpoint is full");if(!d.type&&d.sourceEndpoint&&(d.type=d.sourceEndpoint.connectionType),d.sourceEndpoint&&d.sourceEndpoint.connectorOverlays){d.overlays=d.overlays||[];for(var f=0,g=d.sourceEndpoint.connectorOverlays.length;g>f;f++)d.overlays.push(d.sourceEndpoint.connectorOverlays[f])}d.sourceEndpoint&&d.sourceEndpoint.scope&&(d.scope=d.sourceEndpoint.scope),!d["pointer-events"]&&d.sourceEndpoint&&d.sourceEndpoint.connectorPointerEvents&&(d["pointer-events"]=d.sourceEndpoint.connectorPointerEvents);var h=function(a,b,c){var f=J(b,{anchor:d.anchors?d.anchors[c]:d.anchor,endpoint:d.endpoints?d.endpoints[c]:d.endpoint,paintStyle:d.endpointStyles?d.endpointStyles[c]:d.endpointStyle,hoverPaintStyle:d.endpointHoverStyles?d.endpointHoverStyles[c]:d.endpointHoverStyle});return e.addEndpoint(a,f)},i=function(a,b,c,e){if(d[a]&&!d[a].endpoint&&!d[a+"Endpoint"]&&!d.newConnection){var f=S(d[a]),g=c[f];if(g=g?g[e]:null){if(!g.enabled)return!1;var i=n.extend({},g.def);delete i.label;var j=null!=g.endpoint&&g.endpoint._jsPlumb?g.endpoint:h(d[a],i,b);if(j.isFull())return!1;d[a+"Endpoint"]=j,!d.scope&&i.scope&&(d.scope=i.scope),g.uniqueEndpoint?g.endpoint?j.finalEndpoint=g.endpoint:(g.endpoint=j,j.setDeleteOnEmpty(!1)):j.setDeleteOnEmpty(!0),0===b&&g.def.connectorOverlays&&(d.overlays=d.overlays||[],Array.prototype.push.apply(d.overlays,g.def.connectorOverlays))}}};return i("source",0,this.sourceEndpointDefinitions,d.type||"default")!==!1&&i("target",1,this.targetEndpointDefinitions,d.type||"default")!==!1?(d.sourceEndpoint&&d.targetEndpoint&&(I(d.sourceEndpoint,d.targetEndpoint)||(d=null)),d):void 0}.bind(e),L=function(a){var b=e.Defaults.ConnectionType||e.getDefaultConnectionType();a._jsPlumb=e,a.newConnection=L,a.newEndpoint=N,a.endpointsByUUID=u,a.endpointsByElement=t,a.finaliseConnection=M,a.id="con_"+E();var c=new b(a);return c.isDetachable()&&(c.endpoints[0].initDraggable("_jsPlumbSource"),c.endpoints[1].initDraggable("_jsPlumbTarget")),c},M=e.finaliseConnection=function(a,b,c,d){if(b=b||{},a.suspendedEndpoint||s.push(a),a.pending=null,a.endpoints[0].isTemporarySource=!1,d!==!1&&e.router.newConnection(a),G(a.source),!b.doNotFireConnectionEvent&&b.fireEvent!==!1){var f={connection:a,source:a.source,target:a.target,sourceId:a.sourceId,targetId:a.targetId,sourceEndpoint:a.endpoints[0],targetEndpoint:a.endpoints[1]};e.fire("connection",f,c)}},N=function(a,b){var c=e.Defaults.EndpointType||n.Endpoint,d=n.extend({},a);d._jsPlumb=e,d.newConnection=L,d.newEndpoint=N,d.endpointsByUUID=u,d.endpointsByElement=t,d.fireDetachEvent=V,d.elementId=b||S(d.source);var f=new c(d);return f.id="ep_"+E(),ma(d.elementId,d.source),n.headless||e.getDragManager().endpointAdded(d.source,b),f},O=function(a,b,c){var d=t[a];if(d&&d.length)for(var e=0,f=d.length;f>e;e++){for(var g=0,h=d[e].connections.length;h>g;g++){var i=b(d[e].connections[g]);if(i)return}c&&c(d[e])}},P=function(a,b,c){b="block"===b;var d=null;c&&(d=function(a){a.setVisible(b,!0,!0)});var e=k(a);O(e.id,function(a){if(b&&c){var d=a.sourceId===e.id?1:0;a.endpoints[d].isVisible()&&a.setVisible(!0)}else a.setVisible(b)},d)},Q=function(a,b){var c=null;b&&(c=function(a){var b=a.isVisible();a.setVisible(!b)}),O(a,function(a){var b=a.isVisible();a.setVisible(!b)},c)},R=function(a){var b=w[a];return b?{o:b,s:z[a]}:na({elId:a})},S=function(a,c,d){if(b.isString(a))return a;if(null==a)return null;var g=e.getAttribute(a,"id");return g&&"undefined"!==g||(2===arguments.length&&void 0!==arguments[1]?g=c:(1===arguments.length||3===arguments.length&&!arguments[2])&&(g="jsPlumb_"+f+"_"+E()),d||e.setAttribute(a,"id",g)),g};this.setConnectionBeingDragged=function(a){y=a},this.isConnectionBeingDragged=function(){return y},this.getManagedElements=function(){return v},this.connectorClass="jtk-connector",this.connectorOutlineClass="jtk-connector-outline",this.connectedClass="jtk-connected",this.hoverClass="jtk-hover",this.endpointClass="jtk-endpoint",this.endpointConnectedClass="jtk-endpoint-connected",this.endpointFullClass="jtk-endpoint-full",this.endpointDropAllowedClass="jtk-endpoint-drop-allowed",this.endpointDropForbiddenClass="jtk-endpoint-drop-forbidden",this.overlayClass="jtk-overlay",this.draggingClass="jtk-dragging",this.elementDraggingClass="jtk-element-dragging",this.sourceElementDraggingClass="jtk-source-element-dragging",this.targetElementDraggingClass="jtk-target-element-dragging",this.endpointAnchorClassPrefix="jtk-endpoint-anchor",this.hoverSourceClass="jtk-source-hover",this.hoverTargetClass="jtk-target-hover",this.dragSelectClass="jtk-drag-select",this.Anchors={},this.Connectors={svg:{}},this.Endpoints={svg:{}},this.Overlays={svg:{}},this.ConnectorRenderers={},this.SVG="svg",this.addEndpoint=function(a,c,d){d=d||{};var f=n.extend({},d);n.extend(f,c),f.endpoint=f.endpoint||e.Defaults.Endpoint,f.paintStyle=f.paintStyle||e.Defaults.EndpointStyle;for(var g=[],h=b.isArray(a)||null!=a.length&&!b.isString(a)?a:[a],i=0,j=h.length;j>i;i++){f.source=e.getElement(h[i]),ka(f.source);var k=S(f.source),l=N(f,k),m=ma(k,f.source,null,!A).info.o;b.addToList(t,k,l),A||l.paint({anchorLoc:l.anchor.compute({xy:[m.left,m.top],wh:z[k],element:l,timestamp:B,rotation:this.getRotation(k)}),timestamp:B}),g.push(l)}return 1===g.length?g[0]:g},this.addEndpoints=function(a,c,d){for(var f=[],g=0,h=c.length;h>g;g++){var i=e.addEndpoint(a,c[g],d);b.isArray(i)?Array.prototype.push.apply(f,i):f.push(i)}return f},this.animate=function(a,c,d){if(!this.animationSupported)return!1;d=d||{};var f=e.getElement(a),g=S(f),h=n.animEvents.step,i=n.animEvents.complete;d[h]=b.wrap(d[h],function(){e.revalidate(g)}),d[i]=b.wrap(d[i],function(){e.revalidate(g)}),e.doAnimate(f,c,d)},this.checkCondition=function(a,c){var d=e.getListener(a),f=!0;if(d&&d.length>0){var g=Array.prototype.slice.call(arguments,1);try{for(var h=0,i=d.length;i>h;h++)f=f&&d[h].apply(d[h],g)}catch(j){b.log(e,"cannot check condition ["+a+"]"+j)}}return f},this.connect=function(a,c){var d,e=K(a,c);if(e){if(null==e.source&&null==e.sourceEndpoint)return void b.log("Cannot establish connection - source does not exist");if(null==e.target&&null==e.targetEndpoint)return void b.log("Cannot establish connection - target does not exist");ka(e.source),d=L(e),M(d,e)}return d};var T=[{el:"source",elId:"sourceId",epDefs:"sourceEndpointDefinitions"},{el:"target",elId:"targetId",epDefs:"targetEndpointDefinitions"}],U=function(a,b,c,d){var e,f,g,h=T[c],i=a[h.elId],j=(a[h.el],a.endpoints[c]),k={index:c,originalSourceId:0===c?i:a.sourceId,newSourceId:a.sourceId,originalTargetId:1===c?i:a.targetId,newTargetId:a.targetId,connection:a};if(b.constructor===n.Endpoint)e=b,e.addConnection(a),
b=e.element;else if(f=S(b),g=this[h.epDefs][f],f===a[h.elId])e=null;else if(g)for(var l in g){if(!g[l].enabled)return;e=null!=g[l].endpoint&&g[l].endpoint._jsPlumb?g[l].endpoint:this.addEndpoint(b,g[l].def),g[l].uniqueEndpoint&&(g[l].endpoint=e),e.addConnection(a)}else e=a.makeEndpoint(0===c,b,f);return null!=e&&(j.detachFromConnection(a),a.endpoints[c]=e,a[h.el]=e.element,a[h.elId]=e.elementId,k[0===c?"newSourceId":"newTargetId"]=e.elementId,W(k),d||a.repaint()),k.element=b,k}.bind(this);this.setSource=function(a,b,c){var d=U(a,b,0,c);this.router.sourceOrTargetChanged(d.originalSourceId,d.newSourceId,a,d.el,0)},this.setTarget=function(a,b,c){var d=U(a,b,1,c);this.router.sourceOrTargetChanged(d.originalTargetId,d.newTargetId,a,d.el,1)},this.deleteEndpoint=function(a,b,c){var d="string"==typeof a?u[a]:a;return d&&e.deleteObject({endpoint:d,dontUpdateHover:b,deleteAttachedObjects:c}),e},this.deleteEveryEndpoint=function(){var a=e.setSuspendDrawing(!0);for(var b in t){var c=t[b];if(c&&c.length)for(var d=0,f=c.length;f>d;d++)e.deleteEndpoint(c[d],!0)}t={},v={},u={},w={},x={},e.router.reset();var g=e.getDragManager();return g&&g.reset(),a||e.setSuspendDrawing(!1),e};var V=function(a,b,c){var d=e.Defaults.ConnectionType||e.getDefaultConnectionType(),f=a.constructor===d,g=f?{connection:a,source:a.source,target:a.target,sourceId:a.sourceId,targetId:a.targetId,sourceEndpoint:a.endpoints[0],targetEndpoint:a.endpoints[1]}:a;b&&e.fire("connectionDetached",g,c),e.fire("internal.connectionDetached",g,c),e.router.connectionDetached(g)},W=e.fireMoveEvent=function(a,b){e.fire("connectionMoved",a,b)};this.unregisterEndpoint=function(a){a._jsPlumb.uuid&&(u[a._jsPlumb.uuid]=null),e.router.deleteEndpoint(a);for(var b in t){var c=t[b];if(c){for(var d=[],f=0,g=c.length;g>f;f++)c[f]!==a&&d.push(c[f]);t[b]=d}t[b].length<1&&delete t[b]}};var X="isDetachAllowed",Y="beforeDetach",Z="checkCondition";this.deleteConnection=function(a,c){return null!=a&&(c=c||{},c.force||b.functionChain(!0,!1,[[a.endpoints[0],X,[a]],[a.endpoints[1],X,[a]],[a,X,[a]],[e,Z,[Y,a]]]))?(a.setHover(!1),V(a,!a.pending&&c.fireEvent!==!1,c.originalEvent),a.endpoints[0].detachFromConnection(a),a.endpoints[1].detachFromConnection(a),b.removeWithFunction(s,function(b){return a.id===b.id}),a.cleanup(),a.destroy(),!0):!1},this.deleteEveryConnection=function(a){a=a||{};var b=s.length,c=0;return e.batch(function(){for(var d=0;b>d;d++)c+=e.deleteConnection(s[0],a)?1:0}),c},this.deleteConnectionsForElement=function(a,b){b=b||{},a=e.getElement(a);var c=S(a),d=t[c];if(d&&d.length)for(var f=0,g=d.length;g>f;f++)d[f].deleteEveryConnection(b);return e},this.deleteObject=function(a){var c={endpoints:{},connections:{},endpointCount:0,connectionCount:0},d=a.deleteAttachedObjects!==!1,f=function(b){null!=b&&null==c.connections[b.id]&&(a.dontUpdateHover||null==b._jsPlumb||b.setHover(!1),c.connections[b.id]=b,c.connectionCount++)},g=function(b){if(null!=b&&null==c.endpoints[b.id]&&(a.dontUpdateHover||null==b._jsPlumb||b.setHover(!1),c.endpoints[b.id]=b,c.endpointCount++,d))for(var e=0;e<b.connections.length;e++){var g=b.connections[e];f(g)}};a.connection?f(a.connection):g(a.endpoint);for(var h in c.connections){var i=c.connections[h];if(i._jsPlumb){b.removeWithFunction(s,function(a){return i.id===a.id}),V(i,a.fireEvent===!1?!1:!i.pending,a.originalEvent);var j=null==a.deleteAttachedObjects?null:!a.deleteAttachedObjects;i.endpoints[0].detachFromConnection(i,null,j),i.endpoints[1].detachFromConnection(i,null,j),i.cleanup(!0),i.destroy(!0)}}for(var k in c.endpoints){var l=c.endpoints[k];l._jsPlumb&&(e.unregisterEndpoint(l),l.cleanup(!0),l.destroy(!0))}return c};var $=function(a,b,c,d){for(var e=0,f=a.length;f>e;e++)a[e][b].apply(a[e],c);return d(a)},_=function(a,b,c){for(var d=[],e=0,f=a.length;f>e;e++)d.push([a[e][b].apply(a[e],c),a[e]]);return d},aa=function(a,b,c){return function(){return $(a,b,arguments,c)}},ba=function(a,b){return function(){return _(a,b,arguments)}},ca=function(a,b){var c=[];if(a)if("string"==typeof a){if("*"===a)return a;c.push(a)}else if(b)c=a;else if(a.length)for(var d=0,e=a.length;e>d;d++)c.push(k(a[d]).id);else c.push(k(a).id);return c},da=function(a,b,c){return"*"===a?!0:a.length>0?-1!==a.indexOf(b):!c};this.getConnections=function(a,b){a?a.constructor===String&&(a={scope:a}):a={};for(var c=a.scope||e.getDefaultScope(),d=ca(c,!0),f=ca(a.source),g=ca(a.target),h=!b&&d.length>1?{}:[],i=function(a,c){if(!b&&d.length>1){var e=h[a];null==e&&(e=h[a]=[]),e.push(c)}else h.push(c)},j=0,k=s.length;k>j;j++){var l=s[j],m=l.proxies&&l.proxies[0]?l.proxies[0].originalEp.elementId:l.sourceId,n=l.proxies&&l.proxies[1]?l.proxies[1].originalEp.elementId:l.targetId;da(d,l.scope)&&da(f,m)&&da(g,n)&&i(l.scope,l)}return h};var ea=function(a,b){return function(c){for(var d=0,e=a.length;e>d;d++)c(a[d]);return b(a)}},fa=function(a){return function(b){return a[b]}},ga=function(a,b){var c,d,e={length:a.length,each:ea(a,b),get:fa(a)},f=["setHover","removeAllOverlays","setLabel","addClass","addOverlay","removeOverlay","removeOverlays","showOverlay","hideOverlay","showOverlays","hideOverlays","setPaintStyle","setHoverPaintStyle","setSuspendEvents","setParameter","setParameters","setVisible","repaint","addType","toggleType","removeType","removeClass","setType","bind","unbind"],g=["getLabel","getOverlay","isHover","getParameter","getParameters","getPaintStyle","getHoverPaintStyle","isVisible","hasType","getType","isSuspendEvents"];for(c=0,d=f.length;d>c;c++)e[f[c]]=aa(a,f[c],b);for(c=0,d=g.length;d>c;c++)e[g[c]]=ba(a,g[c]);return e},ha=function(a){var b=ga(a,ha);return n.extend(b,{setDetachable:aa(a,"setDetachable",ha),setReattach:aa(a,"setReattach",ha),setConnector:aa(a,"setConnector",ha),"delete":function(){for(var b=0,c=a.length;c>b;b++)e.deleteConnection(a[b])},isDetachable:ba(a,"isDetachable"),isReattach:ba(a,"isReattach")})},ia=function(a){var b=ga(a,ia);return n.extend(b,{setEnabled:aa(a,"setEnabled",ia),setAnchor:aa(a,"setAnchor",ia),isEnabled:ba(a,"isEnabled"),deleteEveryConnection:function(){for(var b=0,c=a.length;c>b;b++)a[b].deleteEveryConnection()},"delete":function(){for(var b=0,c=a.length;c>b;b++)e.deleteEndpoint(a[b])}})};this.select=function(a){return a=a||{},a.scope=a.scope||"*",ha(a.connections||e.getConnections(a,!0))},this.selectEndpoints=function(a){a=a||{},a.scope=a.scope||"*";var b=!a.element&&!a.source&&!a.target,c=b?"*":ca(a.element),d=b?"*":ca(a.source),e=b?"*":ca(a.target),f=ca(a.scope,!0),g=[];for(var h in t){var i=da(c,h,!0),j=da(d,h,!0),k="*"!==d,l=da(e,h,!0),m="*"!==e;if(i||j||l)a:for(var n=0,o=t[h].length;o>n;n++){var p=t[h][n];if(da(f,p.scope,!0)){var q=k&&d.length>0&&!p.isSource,r=m&&e.length>0&&!p.isTarget;if(q||r)continue a;g.push(p)}}}return ia(g)},this.getAllConnections=function(){return s},this.getDefaultScope=function(){return C},this.getEndpoint=H,this.getEndpoints=function(a){return t[k(a).id]||[]},this.getDefaultEndpointType=function(){return n.Endpoint},this.getDefaultConnectionType=function(){return n.Connection},this.getId=S,this.draw=G,this.info=k,this.appendElement=F;var ja=!1;this.isHoverSuspended=function(){return ja},this.setHoverSuspended=function(a){ja=a},this.hide=function(a,b){return P(a,"none",b),e},this.idstamp=E;var ka=function(a){if(!o&&a){var b=e.getElement(a);b.offsetParent&&e.setContainer(b.offsetParent)}},la=function(){e.Defaults.Container&&e.setContainer(e.Defaults.Container)},ma=e.manage=function(a,b,c,d){return v[a]?d&&(v[a].info=na({elId:a,timestamp:B,recalc:!0})):(v[a]={el:b,endpoints:[],connections:[],rotation:0},v[a].info=na({elId:a,timestamp:B}),e.addClass(b,"jtk-managed"),c||e.fire("manageElement",{id:a,info:v[a].info,el:b})),v[a]};this.unmanage=function(a){if(v[a]){var b=v[a].el;e.removeClass(b,"jtk-managed"),delete v[a],e.fire("unmanageElement",{id:a,el:b})}},this.rotate=function(a,b,c){return v[a]&&(v[a].rotation=b,v[a].el.style.transform="rotate("+b+"deg)",v[a].el.style.transformOrigin="center center",c!==!0)?this.revalidate(a):{c:[],e:[]}},this.getRotation=function(a){return v[a]?v[a].rotation||0:0};var na=function(a){var b,c=a.timestamp,d=a.recalc,f=a.offset,g=a.elId;return A&&!c&&(c=B),!d&&c&&c===x[g]?{o:a.offset||w[g],s:z[g]}:(d||!f&&null==w[g]?(b=v[g]?v[g].el:null,null!=b&&(z[g]=e.getSize(b),w[g]=e.getOffset(b),x[g]=c)):(w[g]=f||w[g],null==z[g]&&(b=v[g].el,null!=b&&(z[g]=e.getSize(b))),x[g]=c),w[g]&&!w[g].right&&(w[g].right=w[g].left+z[g][0],w[g].bottom=w[g].top+z[g][1],w[g].width=z[g][0],w[g].height=z[g][1],w[g].centerx=w[g].left+w[g].width/2,w[g].centery=w[g].top+w[g].height/2),{o:w[g],s:z[g]})};this.updateOffset=na,this.init=function(){r||(la(),e.router=new a.jsPlumb.DefaultRouter(e),e.anchorManager=e.router.anchorManager,r=!0,e.fire("ready",e))}.bind(this),this.log=q,this.jsPlumbUIComponent=i,this.makeAnchor=function(){var c,d=function(b,c){if(a.jsPlumb.Anchors[b])return new a.jsPlumb.Anchors[b](c);if(!e.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown anchor type '"+b+"'"}};if(0===arguments.length)return null;var f=arguments[0],g=arguments[1],h=(arguments[2],null);if(f.compute&&f.getOrientation)return f;if("string"==typeof f)h=d(arguments[0],{elementId:g,jsPlumbInstance:e});else if(b.isArray(f))if(b.isArray(f[0])||b.isString(f[0]))2===f.length&&b.isObject(f[1])?b.isString(f[0])?(c=a.jsPlumb.extend({elementId:g,jsPlumbInstance:e},f[1]),h=d(f[0],c)):(c=a.jsPlumb.extend({elementId:g,jsPlumbInstance:e,anchors:f[0]},f[1]),h=new a.jsPlumb.DynamicAnchor(c)):h=new n.DynamicAnchor({anchors:f,selector:null,elementId:g,jsPlumbInstance:e});else{var i={x:f[0],y:f[1],orientation:f.length>=4?[f[2],f[3]]:[0,0],offsets:f.length>=6?[f[4],f[5]]:[0,0],elementId:g,jsPlumbInstance:e,cssClass:7===f.length?f[6]:null};h=new a.jsPlumb.Anchor(i),h.clone=function(){return new a.jsPlumb.Anchor(i)}}return h.id||(h.id="anchor_"+E()),h},this.makeAnchors=function(c,d,f){for(var g=[],h=0,i=c.length;i>h;h++)"string"==typeof c[h]?g.push(a.jsPlumb.Anchors[c[h]]({elementId:d,jsPlumbInstance:f})):b.isArray(c[h])&&g.push(e.makeAnchor(c[h],d,f));return g},this.makeDynamicAnchor=function(b,c){return new a.jsPlumb.DynamicAnchor({anchors:b,selector:c,elementId:null,jsPlumbInstance:e})},this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={};var oa=function(a,b,c,d,e){for(var f=a.target||a.srcElement,g=!1,h=d.getSelector(b,c),i=0;i<h.length;i++)if(h[i]===f){g=!0;break}return e?!g:g},pa=function(c,d,f,g,h){var j=new i(d),k=d._jsPlumb.EndpointDropHandler({jsPlumb:e,enabled:function(){return c.def.enabled},isFull:function(){var a=e.select({target:c.id}).length;return c.def.maxConnections>0&&a>=c.def.maxConnections},element:c.el,elementId:c.id,isSource:g,isTarget:h,addClass:function(a){e.addClass(c.el,a)},removeClass:function(a){e.removeClass(c.el,a)},onDrop:function(a){var b=a.endpoints[0];b.anchor.locked=!1},isDropAllowed:function(){return j.isDropAllowed.apply(j,arguments)},isRedrop:function(a){return null!=a.suspendedElement&&null!=a.suspendedEndpoint&&a.suspendedEndpoint.element===c.el},getEndpoint:function(b){var f=c.def.endpoint;if(null==f||null==f._jsPlumb){var g=e.deriveEndpointAndAnchorSpec(b.getType().join(" "),!0),h=g.endpoints?a.jsPlumb.extend(d,{endpoint:c.def.def.endpoint||g.endpoints[1]}):d;g.anchors&&(h=a.jsPlumb.extend(h,{anchor:c.def.def.anchor||g.anchors[1]})),f=e.addEndpoint(c.el,h),f._mtNew=!0}if(d.uniqueEndpoint&&(c.def.endpoint=f),f.setDeleteOnEmpty(!0),b.isDetachable()&&f.initDraggable(),null!=f.anchor.positionFinder){var i=e.getUIPosition(arguments,e.getZoom()),j=e.getOffset(c.el),k=e.getSize(c.el),l=null==i?[0,0]:f.anchor.positionFinder(i,j,k,f.anchor.constructorParams);f.anchor.x=l[0],f.anchor.y=l[1]}return f},maybeCleanup:function(a){a._mtNew&&0===a.connections.length?e.deleteObject({endpoint:a}):delete a._mtNew}}),l=a.jsPlumb.dragEvents.drop;return f.scope=f.scope||d.scope||e.Defaults.Scope,f[l]=b.wrap(f[l],k,!0),f.rank=d.rank||0,h&&(f[a.jsPlumb.dragEvents.over]=function(){return!0}),d.allowLoopback===!1&&(f.canDrop=function(a){var b=a.getDragElement()._jsPlumbRelatedElement;return b!==c.el}),e.initDroppable(c.el,f,"internal"),k};this.makeTarget=function(b,c,d){var f=a.jsPlumb.extend({_jsPlumb:this},d);a.jsPlumb.extend(f,c);for(var g=f.maxConnections||-1,h=function(b){var c=k(b),d=c.id,h=a.jsPlumb.extend({},f.dropOptions||{}),i=f.connectionType||"default";this.targetEndpointDefinitions[d]=this.targetEndpointDefinitions[d]||{},ka(d),c.el._isJsPlumbGroup&&null==h.rank&&(h.rank=-1);var j={def:a.jsPlumb.extend({},f),uniqueEndpoint:f.uniqueEndpoint,maxConnections:g,enabled:!0};f.createEndpoint&&(j.uniqueEndpoint=!0,j.endpoint=e.addEndpoint(b,j.def),j.endpoint.setDeleteOnEmpty(!1)),c.def=j,this.targetEndpointDefinitions[d][i]=j,pa(c,f,h,f.isSource===!0,!0),c.el._katavorioDrop[c.el._katavorioDrop.length-1].targetDef=j}.bind(this),i=b.length&&b.constructor!==String?b:[b],j=0,l=i.length;l>j;j++)h(i[j]);return this},this.unmakeTarget=function(a,b){var c=k(a);return e.destroyDroppable(c.el,"internal"),b||delete this.targetEndpointDefinitions[c.id],this},this.makeSource=function(c,d,f){var g=a.jsPlumb.extend({_jsPlumb:this},f);a.jsPlumb.extend(g,d);var h=g.connectionType||"default",i=e.deriveEndpointAndAnchorSpec(h);g.endpoint=g.endpoint||i.endpoints[0],g.anchor=g.anchor||i.anchors[0];for(var l=g.maxConnections||-1,m=g.onMaxConnections,n=function(d){var f=d.id,i=this.getElement(d.el);this.sourceEndpointDefinitions[f]=this.sourceEndpointDefinitions[f]||{},ka(f);var k={def:a.jsPlumb.extend({},g),uniqueEndpoint:g.uniqueEndpoint,maxConnections:l,enabled:!0};g.createEndpoint&&(k.uniqueEndpoint=!0,k.endpoint=e.addEndpoint(c,k.def),k.endpoint.setDeleteOnEmpty(!1)),this.sourceEndpointDefinitions[f][h]=k,d.def=k;var n=a.jsPlumb.dragEvents.stop,o=a.jsPlumb.dragEvents.drag,p=a.jsPlumb.extend({},g.dragOptions||{}),q=p.drag,r=p.stop,s=null,t=!1;p.scope=p.scope||g.scope,p[o]=b.wrap(p[o],function(){q&&q.apply(this,arguments),t=!1}),p[n]=b.wrap(p[n],function(){if(r&&r.apply(this,arguments),this.currentlyDragging=!1,null!=s._jsPlumb){var a=g.anchor||this.Defaults.Anchor,b=s.anchor,c=s.connections[0],d=this.makeAnchor(a,f,this),h=s.element;if(null!=d.positionFinder){var i=e.getOffset(h),j=this.getSize(h),k={left:i.left+b.x*j[0],top:i.top+b.y*j[1]},l=d.positionFinder(k,i,j,d.constructorParams);d.x=l[0],d.y=l[1]}s.setAnchor(d,!0),s.repaint(),this.repaint(s.elementId),null!=c&&this.repaint(c.targetId)}}.bind(this));var u=function(c){if(3!==c.which&&2!==c.button){f=this.getId(this.getElement(d.el));var k=this.sourceEndpointDefinitions[f][h];if(k.enabled){if(g.filter){var n=b.isString(g.filter)?oa(c,d.el,g.filter,this,g.filterExclude):g.filter(c,d.el);if(n===!1)return}var o=this.select({source:f}).length;if(k.maxConnections>=0&&o>=k.maxConnections)return m&&m({element:d.el,maxConnections:l},c),!1;var q=a.jsPlumb.getPositionOnElement(c,i,j),r={};a.jsPlumb.extend(r,k.def),r.isTemporarySource=!0,r.anchor=[q[0],q[1],0,0],r.dragOptions=p,k.def.scope&&(r.scope=k.def.scope),s=this.addEndpoint(f,r),t=!0,s.setDeleteOnEmpty(!0),k.uniqueEndpoint&&(k.endpoint?s.finalEndpoint=k.endpoint:(k.endpoint=s,s.setDeleteOnEmpty(!1)));var u=function(){e.off(s.canvas,"mouseup",u),e.off(d.el,"mouseup",u),t&&(t=!1,e.deleteEndpoint(s))};e.on(s.canvas,"mouseup",u),e.on(d.el,"mouseup",u);var v={};if(k.def.extract)for(var w in k.def.extract){var x=(c.srcElement||c.target).getAttribute(w);x&&(v[k.def.extract[w]]=x)}e.trigger(s.canvas,"mousedown",c,v),b.consume(c)}}}.bind(this);this.on(d.el,"mousedown",u),k.trigger=u,g.filter&&(b.isString(g.filter)||b.isFunction(g.filter))&&e.setDragFilter(d.el,g.filter);var v=a.jsPlumb.extend({},g.dropOptions||{});pa(d,g,v,!0,g.isTarget===!0)}.bind(this),o=c.length&&c.constructor!==String?c:[c],p=0,q=o.length;q>p;p++)n(k(o[p]));return this},this.unmakeSource=function(a,b,c){var d=k(a);e.destroyDroppable(d.el,"internal");var f=this.sourceEndpointDefinitions[d.id];if(f)for(var g in f)if(null==b||b===g){var h=f[g].trigger;h&&e.off(d.el,"mousedown",h),c||delete this.sourceEndpointDefinitions[d.id][g]}return this},this.unmakeEverySource=function(){for(var a in this.sourceEndpointDefinitions)e.unmakeSource(a,null,!0);return this.sourceEndpointDefinitions={},this};var qa=function(a,c,d){c=b.isArray(c)?c:[c];var e=S(a);d=d||"default";for(var f=0;f<c.length;f++){var g=this[c[f]][e];if(g&&g[d])return g[d].def.scope||this.Defaults.Scope}}.bind(this),ra=function(a,c,d,e){d=b.isArray(d)?d:[d];var f=S(a);e=e||"default";for(var g=0;g<d.length;g++){var h=this[d[g]][f];h&&h[e]&&(h[e].def.scope=c)}}.bind(this);this.getScope=function(a,b){return qa(a,["sourceEndpointDefinitions","targetEndpointDefinitions"])},this.getSourceScope=function(a){return qa(a,"sourceEndpointDefinitions")},this.getTargetScope=function(a){return qa(a,"targetEndpointDefinitions")},this.setScope=function(a,b,c){this.setSourceScope(a,b,c),this.setTargetScope(a,b,c)},this.setSourceScope=function(a,b,c){ra(a,b,"sourceEndpointDefinitions",c),this.setDragScope(a,b)},this.setTargetScope=function(a,b,c){ra(a,b,"targetEndpointDefinitions",c),this.setDropScope(a,b)},this.unmakeEveryTarget=function(){for(var a in this.targetEndpointDefinitions)e.unmakeTarget(a,!0);return this.targetEndpointDefinitions={},this};var sa=function(a,c,d,f,g){var h,i,j,l="source"===a?this.sourceEndpointDefinitions:this.targetEndpointDefinitions;if(g=g||"default",c.length&&!b.isString(c)){h=[];for(var m=0,n=c.length;n>m;m++)i=k(c[m]),l[i.id]&&l[i.id][g]&&(h[m]=l[i.id][g].enabled,j=f?!h[m]:d,l[i.id][g].enabled=j,e[j?"removeClass":"addClass"](i.el,"jtk-"+a+"-disabled"))}else{i=k(c);var o=i.id;l[o]&&l[o][g]&&(h=l[o][g].enabled,j=f?!h:d,l[o][g].enabled=j,e[j?"removeClass":"addClass"](i.el,"jtk-"+a+"-disabled"))}return h}.bind(this),ta=function(a,c){if(null!=a){if(b.isString(a)||!a.length)return c.apply(this,[a]);if(a.length)return c.apply(this,[a[0]])}}.bind(this);this.toggleSourceEnabled=function(a,b){return sa("source",a,null,!0,b),this.isSourceEnabled(a,b)},this.setSourceEnabled=function(a,b,c){return sa("source",a,b,null,c)},this.isSource=function(a,b){return b=b||"default",ta(a,function(a){var c=this.sourceEndpointDefinitions[k(a).id];return null!=c&&null!=c[b]}.bind(this))},this.isSourceEnabled=function(a,b){return b=b||"default",ta(a,function(a){var c=this.sourceEndpointDefinitions[k(a).id];return c&&c[b]&&c[b].enabled===!0}.bind(this))},this.toggleTargetEnabled=function(a,b){return sa("target",a,null,!0,b),this.isTargetEnabled(a,b)},this.isTarget=function(a,b){return b=b||"default",ta(a,function(a){var c=this.targetEndpointDefinitions[k(a).id];return null!=c&&null!=c[b]}.bind(this))},this.isTargetEnabled=function(a,b){return b=b||"default",ta(a,function(a){var c=this.targetEndpointDefinitions[k(a).id];return c&&c[b]&&c[b].enabled===!0}.bind(this))},this.setTargetEnabled=function(a,b,c){return sa("target",a,b,null,c)},this.ready=function(a){e.bind("ready",a)};var ua=function(a,b){if("object"==typeof a&&a.length)for(var c=0,d=a.length;d>c;c++)b(a[c]);else b(a);return e};this.repaint=function(a,b,c){return ua(a,function(a){G(a,b,c)})},this.revalidate=function(a,b,c){var d=c?a:e.getId(a);e.updateOffset({elId:d,recalc:!0,timestamp:b});var f=e.getDragManager();return f&&f.updateOffsets(d),G(a,null,b)},this.repaintEverything=function(){var a,b=jsPlumbUtil.uuid();for(a in t)e.updateOffset({elId:a,recalc:!0,timestamp:b});for(a in t)G(a,null,b);return this},this.removeAllEndpoints=function(a,b,c){c=c||[];var d=function(a){var f,g,h=k(a),i=t[h.id];if(i)for(c.push(h),f=0,g=i.length;g>f;f++)e.deleteEndpoint(i[f],!1);if(delete t[h.id],b&&h.el&&3!==h.el.nodeType&&8!==h.el.nodeType)for(f=0,g=h.el.childNodes.length;g>f;f++)d(h.el.childNodes[f])};return d(a),this};var va=function(a,b){e.removeAllEndpoints(a.id,!0,b);for(var c=e.getDragManager(),d=function(a){c&&c.elementRemoved(a.id),e.router.elementRemoved(a.id),e.isSource(a.el)&&e.unmakeSource(a.el),e.isTarget(a.el)&&e.unmakeTarget(a.el),e.destroyDraggable(a.el),e.destroyDroppable(a.el),delete e.floatingConnections[a.id],delete v[a.id],delete w[a.id],a.el&&(e.removeElement(a.el),a.el._jsPlumb=null)},f=1;f<b.length;f++)d(b[f]);d(a)};this.remove=function(a,b){var c=k(a),d=[];return c.text&&c.el.parentNode?c.el.parentNode.removeChild(c.el):c.id&&e.batch(function(){va(c,d)},b===!0),e},this.empty=function(a,b){var c=[],d=function(a,b){var e=k(a);if(e.text)e.el.parentNode.removeChild(e.el);else if(e.el){for(;e.el.childNodes.length>0;)d(e.el.childNodes[0]);b||va(e,c)}};return e.batch(function(){d(a,!0)},b===!1),e},this.reset=function(a){e.silently(function(){ja=!1,e.removeAllGroups(),e.removeGroupManager(),e.deleteEveryEndpoint(),a||e.unbind(),this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={},s.length=0,this.doReset&&this.doReset()}.bind(this))},this.destroy=function(){this.reset(),o=null,p=null};var wa=function(a){a.canvas&&a.canvas.parentNode&&a.canvas.parentNode.removeChild(a.canvas),a.cleanup(),a.destroy()};this.clear=function(){e.select().each(wa),e.selectEndpoints().each(wa),t={},u={}},this.setDefaultScope=function(a){return C=a,e},this.deriveEndpointAndAnchorSpec=function(a,b){for(var c=((b?"":"default ")+a).split(/[\s]/),d=null,f=null,g=null,h=null,i=0;i<c.length;i++){var j=e.getType(c[i],"connection");j&&(j.endpoints&&(d=j.endpoints),j.endpoint&&(f=j.endpoint),j.anchors&&(h=j.anchors),j.anchor&&(g=j.anchor))}return{endpoints:d?d:[f,f],anchors:h?h:[g,g]}},this.setId=function(a,c,d){var e;b.isString(a)?e=a:(a=this.getElement(a),e=this.getId(a));var f=this.getConnections({source:e,scope:"*"},!0),g=this.getConnections({target:e,scope:"*"},!0);c=""+c,d?a=this.getElement(c):(a=this.getElement(e),this.setAttribute(a,"id",c)),t[c]=t[e]||[];for(var h=0,i=t[c].length;i>h;h++)t[c][h].setElementId(c),t[c][h].setReferenceElement(a);delete t[e],this.sourceEndpointDefinitions[c]=this.sourceEndpointDefinitions[e],delete this.sourceEndpointDefinitions[e],this.targetEndpointDefinitions[c]=this.targetEndpointDefinitions[e],delete this.targetEndpointDefinitions[e],this.router.changeId(e,c);var j=this.getDragManager();j&&j.changeId(e,c),v[c]=v[e],delete v[e];var k=function(b,d,e){for(var f=0,g=b.length;g>f;f++)b[f].endpoints[d].setElementId(c),b[f].endpoints[d].setReferenceElement(a),b[f][e+"Id"]=c,b[f][e]=a};k(f,0,"source"),k(g,1,"target"),this.repaint(c)},this.setDebugLog=function(a){q=a},this.setSuspendDrawing=function(a,b){var c=A;return A=a,B=a?(new Date).getTime():null,b&&this.repaintEverything(),c},this.isSuspendDrawing=function(){return A},this.getSuspendedAt=function(){return B},this.batch=function(a,c){var d=this.isSuspendDrawing();d||this.setSuspendDrawing(!0);try{a()}catch(e){b.log("Function run while suspended failed",e)}d||this.setSuspendDrawing(!1,!c)},this.doWhileSuspended=this.batch,this.getCachedData=R,this.show=function(a,b){return P(a,"block",b),e},this.toggleVisible=Q,this.addListener=this.bind;var xa=[];this.registerFloatingConnection=function(a,c,d){xa[a.id]=c,b.addToList(t,a.id,d)},this.getFloatingConnectionFor=function(a){return xa[a]},this.listManager=new a.jsPlumbListManager(this,this.Defaults.ListStyle)};b.extend(a.jsPlumbInstance,b.EventGenerator,{setAttribute:function(a,b,c){this.setAttribute(a,b,c)},getAttribute:function(b,c){return this.getAttribute(a.jsPlumb.getElement(b),c)},convertToFullOverlaySpec:function(a){return b.isString(a)&&(a=[a,{}]),a[1].id=a[1].id||b.uuid(),a},registerConnectionType:function(b,c){if(this._connectionTypes[b]=a.jsPlumb.extend({},c),c.overlays){for(var d={},e=0;e<c.overlays.length;e++){var f=this.convertToFullOverlaySpec(c.overlays[e]);d[f[1].id]=f}this._connectionTypes[b].overlays=d}},registerConnectionTypes:function(a){for(var b in a)this.registerConnectionType(b,a[b])},registerEndpointType:function(b,c){if(this._endpointTypes[b]=a.jsPlumb.extend({},c),c.overlays){for(var d={},e=0;e<c.overlays.length;e++){var f=this.convertToFullOverlaySpec(c.overlays[e]);d[f[1].id]=f}this._endpointTypes[b].overlays=d}},registerEndpointTypes:function(a){for(var b in a)this.registerEndpointType(b,a[b])},getType:function(a,b){return"connection"===b?this._connectionTypes[a]:this._endpointTypes[a]},setIdChanged:function(a,b){this.setId(a,b,!0)},setParent:function(a,b){var c=this.getElement(a),d=this.getId(c),e=this.getElement(b),f=this.getId(e),g=this.getDragManager();c.parentNode.removeChild(c),e.appendChild(c),g&&g.setParent(c,d,e,f)},extend:function(a,b,c){var d;if(c)for(d=0;d<c.length;d++)a[c[d]]=b[c[d]];else for(d in b)a[d]=b[d];return a},floatingConnections:{},getFloatingAnchorIndex:function(a){return a.endpoints[0].isFloating()?0:a.endpoints[1].isFloating()?1:-1},proxyConnection:function(a,b,c,d,e,f){var g,h=a.endpoints[b].elementId,i=a.endpoints[b];a.proxies=a.proxies||[],g=a.proxies[b]?a.proxies[b].ep:this.addEndpoint(c,{endpoint:e(a,b),anchor:f(a,b),parameters:{isProxyEndpoint:!0}}),g.setDeleteOnEmpty(!0),a.proxies[b]={ep:g,originalEp:i},0===b?this.router.sourceOrTargetChanged(h,d,a,c,0):this.router.sourceOrTargetChanged(h,d,a,c,1),i.detachFromConnection(a,null,!0),g.connections=[a],a.endpoints[b]=g,i.setVisible(!1),a.setVisible(!0),this.revalidate(c)},unproxyConnection:function(a,b,c){if(null!=a._jsPlumb&&null!=a.proxies&&null!=a.proxies[b]){var d=a.proxies[b].originalEp.element,e=a.proxies[b].originalEp.elementId;a.endpoints[b]=a.proxies[b].originalEp,0===b?this.router.sourceOrTargetChanged(c,e,a,d,0):this.router.sourceOrTargetChanged(c,e,a,d,1),a.proxies[b].ep.detachFromConnection(a,null),a.proxies[b].originalEp.addConnection(a),a.isVisible()&&a.proxies[b].originalEp.setVisible(!0),delete a.proxies[b]}}});var n=new m;a.jsPlumb=n,n.getInstance=function(a,b){var c=new m(a);if(b)for(var d in b)c[d]=b[d];return c.init(),c},n.each=function(a,b){if(null!=a)if("string"==typeof a)b(n.getElement(a));else if(null!=a.length)for(var c=0;c<a.length;c++)b(n.getElement(a[c]));else b(a)},"undefined"!=typeof exports&&(exports.jsPlumb=n)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d="__label",e=function(a,c){var e={cssClass:c.cssClass,labelStyle:a.labelStyle,id:d,component:a,_jsPlumb:a._jsPlumb.instance},f=b.extend(e,c);return new(b.Overlays[a._jsPlumb.instance.getRenderMode()].Label)(f)},f=function(a,d){var e=null;if(c.isArray(d)){var f=d[0],g=b.extend({component:a,_jsPlumb:a._jsPlumb.instance},d[1]);3===d.length&&b.extend(g,d[2]),e=new(b.Overlays[a._jsPlumb.instance.getRenderMode()][f])(g)}else e=d.constructor===String?new(b.Overlays[a._jsPlumb.instance.getRenderMode()][d])({component:a,_jsPlumb:a._jsPlumb.instance}):d;return e.id=e.id||c.uuid(),a.cacheTypeItem("overlay",e,e.id),a._jsPlumb.overlays[e.id]=e,e};b.OverlayCapableJsPlumbUIComponent=function(b){a.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.overlays={},this._jsPlumb.overlayPositions={},b.label&&(this.getDefaultType().overlays[d]=["Label",{label:b.label,location:b.labelLocation||this.defaultLabelLocation||.5,labelStyle:b.labelStyle||this._jsPlumb.instance.Defaults.LabelStyle,id:d}]),this.setListenerComponent=function(a){if(this._jsPlumb)for(var b in this._jsPlumb.overlays)this._jsPlumb.overlays[b].setListenerComponent(a)}},b.OverlayCapableJsPlumbUIComponent.applyType=function(a,b){if(b.overlays){var c,d={};for(c in b.overlays){var e=a._jsPlumb.overlays[b.overlays[c][1].id];if(e)e.updateFrom(b.overlays[c][1]),d[b.overlays[c][1].id]=!0,e.reattach(a._jsPlumb.instance,a);else{var f=a.getCachedTypeItem("overlay",b.overlays[c][1].id);null!=f?(f.reattach(a._jsPlumb.instance,a),f.setVisible(!0),f.updateFrom(b.overlays[c][1]),a._jsPlumb.overlays[f.id]=f):f=a.addOverlay(b.overlays[c],!0),d[f.id]=!0}}for(c in a._jsPlumb.overlays)null==d[a._jsPlumb.overlays[c].id]&&a.removeOverlay(a._jsPlumb.overlays[c].id,!0)}},c.extend(b.OverlayCapableJsPlumbUIComponent,a.jsPlumbUIComponent,{setHover:function(a,b){if(this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged())for(var c in this._jsPlumb.overlays)this._jsPlumb.overlays[c][a?"addClass":"removeClass"](this._jsPlumb.instance.hoverClass)},addOverlay:function(a,b){var d=f(this,a);if(this.getData&&"Label"===d.type&&c.isArray(a)){var e=this.getData(),g=a[1];if(e){var h=g.labelLocationAttribute||"labelLocation",i=e?e[h]:null;i&&(d.loc=i)}}return b||this.repaint(),d},getOverlay:function(a){return this._jsPlumb.overlays[a]},getOverlays:function(){return this._jsPlumb.overlays},hideOverlay:function(a){var b=this.getOverlay(a);b&&b.hide()},hideOverlays:function(){for(var a in this._jsPlumb.overlays)this._jsPlumb.overlays[a].hide()},showOverlay:function(a){var b=this.getOverlay(a);b&&b.show()},showOverlays:function(){for(var a in this._jsPlumb.overlays)this._jsPlumb.overlays[a].show()},removeAllOverlays:function(a){for(var b in this._jsPlumb.overlays)this._jsPlumb.overlays[b].cleanup&&this._jsPlumb.overlays[b].cleanup();this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null,this._jsPlumb.overlayPlacements={},a||this.repaint()},removeOverlay:function(a,b){var c=this._jsPlumb.overlays[a];c&&(c.setVisible(!1),!b&&c.cleanup&&c.cleanup(),delete this._jsPlumb.overlays[a],this._jsPlumb.overlayPositions&&delete this._jsPlumb.overlayPositions[a],this._jsPlumb.overlayPlacements&&delete this._jsPlumb.overlayPlacements[a])},removeOverlays:function(){for(var a=0,b=arguments.length;b>a;a++)this.removeOverlay(arguments[a])},moveParent:function(a){if(this.bgCanvas&&(this.bgCanvas.parentNode.removeChild(this.bgCanvas),a.appendChild(this.bgCanvas)),this.canvas&&this.canvas.parentNode){this.canvas.parentNode.removeChild(this.canvas),a.appendChild(this.canvas);for(var b in this._jsPlumb.overlays)if(this._jsPlumb.overlays[b].isAppendedAtTopLevel){var c=this._jsPlumb.overlays[b].getElement();c.parentNode.removeChild(c),a.appendChild(c)}}},getLabel:function(){var a=this.getOverlay(d);return null!=a?a.getLabel():null},getLabelOverlay:function(){return this.getOverlay(d)},setLabel:function(a){var b=this.getOverlay(d);if(b)a.constructor===String||a.constructor===Function?b.setLabel(a):(a.label&&b.setLabel(a.label),a.location&&b.setLocation(a.location));else{var c=a.constructor===String||a.constructor===Function?{label:a}:a;b=e(this,c),this._jsPlumb.overlays[d]=b}this._jsPlumb.instance.isSuspendDrawing()||this.repaint()},cleanup:function(a){for(var b in this._jsPlumb.overlays)this._jsPlumb.overlays[b].cleanup(a),this._jsPlumb.overlays[b].destroy(a);a&&(this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null)},setVisible:function(a){this[a?"showOverlays":"hideOverlays"]()},setAbsoluteOverlayPosition:function(a,b){this._jsPlumb.overlayPositions[a.id]=b},getAbsoluteOverlayPosition:function(a){return this._jsPlumb.overlayPositions?this._jsPlumb.overlayPositions[a.id]:null},_clazzManip:function(a,b,c){if(!c)for(var d in this._jsPlumb.overlays)this._jsPlumb.overlays[d][a+"Class"](b)},addClass:function(a,b){this._clazzManip("add",a,b)},removeClass:function(a,b){this._clazzManip("remove",a,b)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d=function(a,b,c){var d=!1;return{drag:function(){if(d)return d=!1,!0;if(b.element){var e=c.getUIPosition(arguments,c.getZoom());null!=e&&c.setPosition(b.element,e),c.repaint(b.element,e),a.paint({anchorPoint:a.anchor.getCurrentLocation({element:a})})}},stopDrag:function(){d=!0}}},e=function(a,b,c,d){var e=b.createElement("div",{position:"absolute"});b.appendElement(e);var f=b.getId(e);b.setPosition(e,c),e.style.width=d[0]+"px",e.style.height=d[1]+"px",b.manage(f,e,!0),a.id=f,a.element=e},f=function(a,c,d,e,f,g,h,i){var j=new b.FloatingAnchor({reference:c,referenceCanvas:e,jsPlumbInstance:g});return h({paintStyle:a,endpoint:d,anchor:j,source:f,scope:i})},g=["connectorStyle","connectorHoverStyle","connectorOverlays","connector","connectionType","connectorClass","connectorHoverClass"],h=function(a,b){var c=0;if(null!=b)for(var d=0;d<a.connections.length;d++)if(a.connections[d].sourceId===b||a.connections[d].targetId===b){c=d;break}return a.connections[c]};b.Endpoint=function(a){var i=a._jsPlumb,j=a.newConnection,k=a.newEndpoint;this.idPrefix="_jsplumb_e_",this.defaultLabelLocation=[.5,.5],
this.defaultOverlayKeys=["Overlays","EndpointOverlays"],b.OverlayCapableJsPlumbUIComponent.apply(this,arguments),this.appendToDefaultType({connectionType:a.connectionType,maxConnections:null==a.maxConnections?this._jsPlumb.instance.Defaults.MaxConnections:a.maxConnections,paintStyle:a.endpointStyle||a.paintStyle||a.style||this._jsPlumb.instance.Defaults.EndpointStyle||b.Defaults.EndpointStyle,hoverPaintStyle:a.endpointHoverStyle||a.hoverPaintStyle||this._jsPlumb.instance.Defaults.EndpointHoverStyle||b.Defaults.EndpointHoverStyle,connectorStyle:a.connectorStyle,connectorHoverStyle:a.connectorHoverStyle,connectorClass:a.connectorClass,connectorHoverClass:a.connectorHoverClass,connectorOverlays:a.connectorOverlays,connector:a.connector,connectorTooltip:a.connectorTooltip}),this._jsPlumb.enabled=!(a.enabled===!1),this._jsPlumb.visible=!0,this.element=b.getElement(a.source),this._jsPlumb.uuid=a.uuid,this._jsPlumb.floatingEndpoint=null;var l=null;this._jsPlumb.uuid&&(a.endpointsByUUID[this._jsPlumb.uuid]=this),this.elementId=a.elementId,this.dragProxy=a.dragProxy,this._jsPlumb.connectionCost=a.connectionCost,this._jsPlumb.connectionsDirected=a.connectionsDirected,this._jsPlumb.currentAnchorClass="",this._jsPlumb.events={};var m=a.deleteOnEmpty===!0;this.setDeleteOnEmpty=function(a){m=a};var n=function(){var a=i.endpointAnchorClassPrefix+"-"+this._jsPlumb.currentAnchorClass;this._jsPlumb.currentAnchorClass=this.anchor.getCssClass();var c=i.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");this.removeClass(a),this.addClass(c),b.updateClasses(this.element,c,a)}.bind(this);this.prepareAnchor=function(a){var b=this._jsPlumb.instance.makeAnchor(a,this.elementId,i);return b.bind("anchorChanged",function(a){this.fire("anchorChanged",{endpoint:this,anchor:a}),n()}.bind(this)),b},this.setPreparedAnchor=function(a,b){return this._jsPlumb.instance.continuousAnchorFactory.clear(this.elementId),this.anchor=a,n(),b||this._jsPlumb.instance.repaint(this.elementId),this},this.setAnchor=function(a,b){var c=this.prepareAnchor(a);return this.setPreparedAnchor(c,b),this};var o=function(a){if(this.connections.length>0)for(var b=0;b<this.connections.length;b++)this.connections[b].setHover(a,!1);else this.setHover(a)}.bind(this);this.bind("mouseover",function(){o(!0)}),this.bind("mouseout",function(){o(!1)}),a._transient||this._jsPlumb.instance.router.addEndpoint(this,this.elementId),this.prepareEndpoint=function(d,e){var f,g=function(a,c){var d=i.getRenderMode();if(b.Endpoints[d][a])return new b.Endpoints[d][a](c);if(!i.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown endpoint type '"+a+"'"}},h={_jsPlumb:this._jsPlumb.instance,cssClass:a.cssClass,container:a.container,tooltip:a.tooltip,connectorTooltip:a.connectorTooltip,endpoint:this};return c.isString(d)?f=g(d,h):c.isArray(d)?(h=c.merge(d[1],h),f=g(d[0],h)):f=d.clone(),f.clone=function(){return c.isString(d)?g(d,h):c.isArray(d)?(h=c.merge(d[1],h),g(d[0],h)):void 0}.bind(this),f.typeId=e,f},this.setEndpoint=function(a,b){var c=this.prepareEndpoint(a);this.setPreparedEndpoint(c,!0)},this.setPreparedEndpoint=function(a,b){null!=this.endpoint&&(this.endpoint.cleanup(),this.endpoint.destroy()),this.endpoint=a,this.type=this.endpoint.type,this.canvas=this.endpoint.canvas},b.extend(this,a,g),this.isSource=a.isSource||!1,this.isTemporarySource=a.isTemporarySource||!1,this.isTarget=a.isTarget||!1,this.connections=a.connections||[],this.connectorPointerEvents=a["connector-pointer-events"],this.scope=a.scope||i.getDefaultScope(),this.timestamp=null,this.reattachConnections=a.reattach||i.Defaults.ReattachConnections,this.connectionsDetachable=i.Defaults.ConnectionsDetachable,(a.connectionsDetachable===!1||a.detachable===!1)&&(this.connectionsDetachable=!1),this.dragAllowedWhenFull=a.dragAllowedWhenFull!==!1,a.onMaxConnections&&this.bind("maxConnections",a.onMaxConnections),this.addConnection=function(a){this.connections.push(a),this[(this.connections.length>0?"add":"remove")+"Class"](i.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](i.endpointFullClass)},this.detachFromConnection=function(a,b,c){b=null==b?this.connections.indexOf(a):b,b>=0&&(this.connections.splice(b,1),this[(this.connections.length>0?"add":"remove")+"Class"](i.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](i.endpointFullClass)),!c&&m&&0===this.connections.length&&i.deleteObject({endpoint:this,fireEvent:!1,deleteAttachedObjects:c!==!0})},this.deleteEveryConnection=function(a){for(var b=this.connections.length,c=0;b>c;c++)i.deleteConnection(this.connections[0],a)},this.detachFrom=function(a,b,c){for(var d=[],e=0;e<this.connections.length;e++)(this.connections[e].endpoints[1]===a||this.connections[e].endpoints[0]===a)&&d.push(this.connections[e]);for(var f=0,g=d.length;g>f;f++)i.deleteConnection(d[0]);return this},this.getElement=function(){return this.element},this.setElement=function(d){var e=this._jsPlumb.instance.getId(d),f=this.elementId;return c.removeWithFunction(a.endpointsByElement[this.elementId],function(a){return a.id===this.id}.bind(this)),this.element=b.getElement(d),this.elementId=i.getId(this.element),i.router.rehomeEndpoint(this,f,this.element),i.dragManager.endpointAdded(this.element),c.addToList(a.endpointsByElement,e,this),this},this.makeInPlaceCopy=function(){var b=this.anchor.getCurrentLocation({element:this}),c=this.anchor.getOrientation(this),d=this.anchor.getCssClass(),e={bind:function(){},compute:function(){return[b[0],b[1]]},getCurrentLocation:function(){return[b[0],b[1]]},getOrientation:function(){return c},getCssClass:function(){return d}};return k({dropOptions:a.dropOptions,anchor:e,source:this.element,paintStyle:this.getPaintStyle(),endpoint:a.hideOnDrag?"Blank":this.endpoint,_transient:!0,scope:this.scope,reference:this})},this.connectorSelector=function(){return this.connections[0]},this.setStyle=this.setPaintStyle,this.paint=function(a){a=a||{};var b=a.timestamp,c=!(a.recalc===!1);if(!b||this.timestamp!==b){var d=i.updateOffset({elId:this.elementId,timestamp:b}),e=a.offset?a.offset.o:d.o;if(null!=e){var f=a.anchorPoint,g=a.connectorPaintStyle;if(null==f){var j=a.dimensions||d.s,k={xy:[e.left,e.top],wh:j,element:this,timestamp:b};if(c&&this.anchor.isDynamic&&this.connections.length>0){var l=h(this,a.elementWithPrecedence),m=l.endpoints[0]===this?1:0,n=0===m?l.sourceId:l.targetId,o=i.getCachedData(n),p=o.o,q=o.s;k.index=0===m?1:0,k.connection=l,k.txy=[p.left,p.top],k.twh=q,k.tElement=l.endpoints[m],k.tRotation=i.getRotation(n)}else this.connections.length>0&&(k.connection=this.connections[0]);k.rotation=i.getRotation(this.elementId),f=this.anchor.compute(k)}this.endpoint.compute(f,this.anchor.getOrientation(this),this._jsPlumb.paintStyleInUse,g||this.paintStyleInUse),this.endpoint.paint(this._jsPlumb.paintStyleInUse,this.anchor),this.timestamp=b;for(var r in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(r)){var s=this._jsPlumb.overlays[r];s.isVisible()&&(this._jsPlumb.overlayPlacements[r]=s.draw(this.endpoint,this._jsPlumb.paintStyleInUse),s.paint(this._jsPlumb.overlayPlacements[r]))}}}},this.getTypeDescriptor=function(){return"endpoint"},this.isVisible=function(){return this._jsPlumb.visible},this.repaint=this.paint;var p=!1;this.initDraggable=function(){if(!p&&b.isDragSupported(this.element)){var g,h={id:null,element:null},m=null,n=!1,o=null,q=d(this,h,i),r=a.dragOptions||{},s={},t=b.dragEvents.start,u=b.dragEvents.stop,v=b.dragEvents.drag,w=b.dragEvents.beforeStart,x=function(a){g=a.e.payload||{}},y=function(c){m=this.connectorSelector();var d=!0;this.isEnabled()||(d=!1),null!=m||this.isSource||this.isTemporarySource||(d=!1),!this.isSource||!this.isFull()||null!=m&&this.dragAllowedWhenFull||(d=!1),null==m||m.isDetachable(this)||(this.isFull()?d=!1:m=null);var l=i.checkCondition(null==m?"beforeDrag":"beforeStartDetach",{endpoint:this,source:this.element,sourceId:this.elementId,connection:m});if(l===!1?d=!1:"object"==typeof l?b.extend(l,g||{}):l=g||{},d===!1)return i.stopDrag&&i.stopDrag(this.canvas),q.stopDrag(),!1;for(var p=0;p<this.connections.length;p++)this.connections[p].setHover(!1);this.addClass("endpointDrag"),i.setConnectionBeingDragged(!0),m&&!this.isFull()&&this.isSource&&(m=null),i.updateOffset({elId:this.elementId});var r=this._jsPlumb.instance.getOffset(this.canvas),s=this.canvas,t=this._jsPlumb.instance.getSize(this.canvas);e(h,i,r,t),i.setAttributes(this.canvas,{dragId:h.id,elId:this.elementId});var u=this.dragProxy||this.endpoint;if(null==this.dragProxy&&null!=this.connectionType){var v=this._jsPlumb.instance.deriveEndpointAndAnchorSpec(this.connectionType);v.endpoints[1]&&(u=v.endpoints[1])}var w=this._jsPlumb.instance.makeAnchor("Center");w.isFloating=!0,this._jsPlumb.floatingEndpoint=f(this.getPaintStyle(),w,u,this.canvas,h.element,i,k,this.scope);var x=this._jsPlumb.floatingEndpoint.anchor;if(null==m)this.setHover(!1,!1),m=j({sourceEndpoint:this,targetEndpoint:this._jsPlumb.floatingEndpoint,source:this.element,target:h.element,anchors:[this.anchor,this._jsPlumb.floatingEndpoint.anchor],paintStyle:a.connectorStyle,hoverPaintStyle:a.connectorHoverStyle,connector:a.connector,overlays:a.connectorOverlays,type:this.connectionType,cssClass:this.connectorClass,hoverClass:this.connectorHoverClass,scope:a.scope,data:l}),m.pending=!0,m.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.anchor=x,i.fire("connectionDrag",m),i.router.newConnection(m);else{n=!0,m.setHover(!1);var y=m.endpoints[0].id===this.id?0:1;this.detachFromConnection(m,null,!0);var z=i.getDragScope(s);i.setAttribute(this.canvas,"originalScope",z),i.fire("connectionDrag",m),0===y?(o=[m.source,m.sourceId,s,z],i.router.sourceOrTargetChanged(m.endpoints[y].elementId,h.id,m,h.element,0)):(o=[m.target,m.targetId,s,z],i.router.sourceOrTargetChanged(m.endpoints[y].elementId,h.id,m,h.element,1)),m.suspendedEndpoint=m.endpoints[y],m.suspendedElement=m.endpoints[y].getElement(),m.suspendedElementId=m.endpoints[y].elementId,m.suspendedElementType=0===y?"source":"target",m.suspendedEndpoint.setHover(!1),this._jsPlumb.floatingEndpoint.referenceEndpoint=m.suspendedEndpoint,m.endpoints[y]=this._jsPlumb.floatingEndpoint,m.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.addClass(i.draggingClass)}i.registerFloatingConnection(h,m,this._jsPlumb.floatingEndpoint),i.currentlyDragging=!0}.bind(this),z=function(){if(i.setConnectionBeingDragged(!1),m&&null!=m.endpoints){var a=i.getDropEvent(arguments),b=i.getFloatingAnchorIndex(m);if(m.endpoints[0===b?1:0].anchor.locked=!1,m.removeClass(i.draggingClass),this._jsPlumb&&(m.deleteConnectionNow||m.endpoints[b]===this._jsPlumb.floatingEndpoint)&&n&&m.suspendedEndpoint){0===b?(m.floatingElement=m.source,m.floatingId=m.sourceId,m.floatingEndpoint=m.endpoints[0],m.floatingIndex=0,m.source=o[0],m.sourceId=o[1]):(m.floatingElement=m.target,m.floatingId=m.targetId,m.floatingEndpoint=m.endpoints[1],m.floatingIndex=1,m.target=o[0],m.targetId=o[1]);var c=this._jsPlumb.floatingEndpoint;i.setDragScope(o[2],o[3]),m.endpoints[b]=m.suspendedEndpoint,m.isReattach()||m._forceReattach||m._forceDetach||!i.deleteConnection(m,{originalEvent:a})?(m.setHover(!1),m._forceDetach=null,m._forceReattach=null,this._jsPlumb.floatingEndpoint.detachFromConnection(m),m.suspendedEndpoint.addConnection(m),1===b?i.router.sourceOrTargetChanged(m.floatingId,m.targetId,m,m.target,b):i.router.sourceOrTargetChanged(m.floatingId,m.sourceId,m,m.source,b),i.repaint(o[1])):i.deleteObject({endpoint:c})}this.deleteAfterDragStop?i.deleteObject({endpoint:this}):this._jsPlumb&&this.paint({recalc:!1}),i.fire("connectionDragStop",m,a),m.pending&&i.fire("connectionAborted",m,a),i.currentlyDragging=!1,m.suspendedElement=null,m.suspendedEndpoint=null,m=null}h&&h.element&&i.remove(h.element,!1,!1),l&&i.deleteObject({endpoint:l}),this._jsPlumb&&(this.canvas.style.visibility="visible",this.anchor.locked=!1,this._jsPlumb.floatingEndpoint=null)}.bind(this);r=b.extend(s,r),r.scope=this.scope||r.scope,r[w]=c.wrap(r[w],x,!1),r[t]=c.wrap(r[t],y,!1),r[v]=c.wrap(r[v],q.drag),r[u]=c.wrap(r[u],z),r.multipleDrop=!1,r.canDrag=function(){return this.isSource||this.isTemporarySource||this.connections.length>0&&this.connectionsDetachable!==!1}.bind(this),i.initDraggable(this.canvas,r,"internal"),this.canvas._jsPlumbRelatedElement=this.element,p=!0}};var q=a.endpoint||this._jsPlumb.instance.Defaults.Endpoint||b.Defaults.Endpoint;this.setEndpoint(q,!0);var r=a.anchor?a.anchor:a.anchors?a.anchors:i.Defaults.Anchor||"Top";this.setAnchor(r,!0);var s=["default",a.type||""].join(" ");this.addType(s,a.data,!0),this.canvas=this.endpoint.canvas,this.canvas._jsPlumb=this,this.initDraggable();var t=function(d,e,f,g){if(b.isDropSupported(this.element)){var h=a.dropOptions||i.Defaults.DropOptions||b.Defaults.DropOptions;h=b.extend({},h),h.scope=h.scope||this.scope;var j=b.dragEvents.drop,k=b.dragEvents.over,l=b.dragEvents.out,m=this,n=i.EndpointDropHandler({getEndpoint:function(){return m},jsPlumb:i,enabled:function(){return null!=f?f.isEnabled():!0},isFull:function(){return f.isFull()},element:this.element,elementId:this.elementId,isSource:this.isSource,isTarget:this.isTarget,addClass:function(a){m.addClass(a)},removeClass:function(a){m.removeClass(a)},isDropAllowed:function(){return m.isDropAllowed.apply(m,arguments)},reference:g,isRedrop:function(a,b){return a.suspendedEndpoint&&b.reference&&a.suspendedEndpoint.id===b.reference.id}});h[j]=c.wrap(h[j],n,!0),h[k]=c.wrap(h[k],function(){var a=b.getDragObject(arguments),c=i.getAttribute(b.getElement(a),"dragId"),d=i.getFloatingConnectionFor(c);if(null!=d){var e=i.getFloatingAnchorIndex(d),f=this.isTarget&&0!==e||d.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===d.suspendedEndpoint.id;if(f){var g=i.checkCondition("checkDropAllowed",{sourceEndpoint:d.endpoints[e],targetEndpoint:this,connection:d});this[(g?"add":"remove")+"Class"](i.endpointDropAllowedClass),this[(g?"remove":"add")+"Class"](i.endpointDropForbiddenClass),d.endpoints[e].anchor.over(this.anchor,this)}}}.bind(this)),h[l]=c.wrap(h[l],function(){var a=b.getDragObject(arguments),c=null==a?null:i.getAttribute(b.getElement(a),"dragId"),d=c?i.getFloatingConnectionFor(c):null;if(null!=d){var e=i.getFloatingAnchorIndex(d),f=this.isTarget&&0!==e||d.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===d.suspendedEndpoint.id;f&&(this.removeClass(i.endpointDropAllowedClass),this.removeClass(i.endpointDropForbiddenClass),d.endpoints[e].anchor.out())}}.bind(this)),i.initDroppable(d,h,"internal",e)}}.bind(this);return this.anchor.isFloating||t(this.canvas,!(a._transient||this.anchor.isFloating),this,a.reference),this},c.extend(b.Endpoint,b.OverlayCapableJsPlumbUIComponent,{setVisible:function(a,b,c){if(this._jsPlumb.visible=a,this.canvas&&(this.canvas.style.display=a?"block":"none"),this[a?"showOverlays":"hideOverlays"](),!b)for(var d=0;d<this.connections.length;d++)if(this.connections[d].setVisible(a),!c){var e=this===this.connections[d].endpoints[0]?1:0;1===this.connections[d].endpoints[e].connections.length&&this.connections[d].endpoints[e].setVisible(a,!0,!0)}},getAttachedElements:function(){return this.connections},applyType:function(a,c){this.setPaintStyle(a.endpointStyle||a.paintStyle,c),this.setHoverPaintStyle(a.endpointHoverStyle||a.hoverPaintStyle,c),null!=a.maxConnections&&(this._jsPlumb.maxConnections=a.maxConnections),a.scope&&(this.scope=a.scope),b.extend(this,a,g),null!=a.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,a.cssClass),b.OverlayCapableJsPlumbUIComponent.applyType(this,a)},isEnabled:function(){return this._jsPlumb.enabled},setEnabled:function(a){this._jsPlumb.enabled=a},cleanup:function(){var a=this._jsPlumb.instance.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");b.removeClass(this.element,a),this.anchor=null,this.endpoint.cleanup(!0),this.endpoint.destroy(),this.endpoint=null,this._jsPlumb.instance.destroyDraggable(this.canvas,"internal"),this._jsPlumb.instance.destroyDroppable(this.canvas,"internal")},setHover:function(a){this.endpoint&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&this.endpoint.setHover(a)},isFull:function(){return 0===this._jsPlumb.maxConnections?!0:!(this.isFloating()||this._jsPlumb.maxConnections<0||this.connections.length<this._jsPlumb.maxConnections)},isFloating:function(){return null!=this.anchor&&this.anchor.isFloating},isConnectedTo:function(a){var b=!1;if(a)for(var c=0;c<this.connections.length;c++)if(this.connections[c].endpoints[1]===a||this.connections[c].endpoints[0]===a){b=!0;break}return b},getConnectionCost:function(){return this._jsPlumb.connectionCost},setConnectionCost:function(a){this._jsPlumb.connectionCost=a},areConnectionsDirected:function(){return this._jsPlumb.connectionsDirected},setConnectionsDirected:function(a){this._jsPlumb.connectionsDirected=a},setElementId:function(a){this.elementId=a,this.anchor.elementId=a},setReferenceElement:function(a){this.element=b.getElement(a)},setDragAllowedWhenFull:function(a){this.dragAllowedWhenFull=a},equals:function(a){return this.anchor.equals(a.anchor)},getUuid:function(){return this._jsPlumb.uuid},computeAnchor:function(a){return this.anchor.compute(a)}}),a.jsPlumbInstance.prototype.EndpointDropHandler=function(a){return function(b){var d=a.jsPlumb;a.removeClass(d.endpointDropAllowedClass),a.removeClass(d.endpointDropForbiddenClass);var e=d.getDropEvent(arguments),f=d.getDragObject(arguments),g=d.getAttribute(f,"dragId"),h=(d.getAttribute(f,"elId"),d.getAttribute(f,"originalScope")),i=d.getFloatingConnectionFor(g);if(null!=i){var j=null!=i.suspendedEndpoint;if(!j||null!=i.suspendedEndpoint._jsPlumb){var k=a.getEndpoint(i);if(null!=k){if(a.isRedrop(i,a))return i._forceReattach=!0,i.setHover(!1),void(a.maybeCleanup&&a.maybeCleanup(k));var l=d.getFloatingAnchorIndex(i);if(0===l&&!a.isSource||1===l&&!a.isTarget)return void(a.maybeCleanup&&a.maybeCleanup(k));a.onDrop&&a.onDrop(i),h&&d.setDragScope(f,h);var m=a.isFull(b);if(m&&k.fire("maxConnections",{endpoint:this,connection:i,maxConnections:k._jsPlumb.maxConnections},e),!m&&a.enabled()){var n=!0;0===l?(i.floatingElement=i.source,i.floatingId=i.sourceId,i.floatingEndpoint=i.endpoints[0],i.floatingIndex=0,i.source=a.element,i.sourceId=d.getId(a.element)):(i.floatingElement=i.target,i.floatingId=i.targetId,i.floatingEndpoint=i.endpoints[1],i.floatingIndex=1,i.target=a.element,i.targetId=d.getId(a.element)),j&&i.suspendedEndpoint.id!==k.id&&(i.isDetachAllowed(i)&&i.endpoints[l].isDetachAllowed(i)&&i.suspendedEndpoint.isDetachAllowed(i)&&d.checkCondition("beforeDetach",i)||(n=!1));var o=function(b){i.endpoints[l].detachFromConnection(i),i.suspendedEndpoint&&i.suspendedEndpoint.detachFromConnection(i),i.endpoints[l]=k,k.addConnection(i);var f=k.getParameters();for(var g in f)i.setParameter(g,f[g]);if(j){var h=i.suspendedEndpoint.elementId;d.fireMoveEvent({index:l,originalSourceId:0===l?h:i.sourceId,newSourceId:0===l?k.elementId:i.sourceId,originalTargetId:1===l?h:i.targetId,newTargetId:1===l?k.elementId:i.targetId,originalSourceEndpoint:0===l?i.suspendedEndpoint:i.endpoints[0],newSourceEndpoint:0===l?k:i.endpoints[0],originalTargetEndpoint:1===l?i.suspendedEndpoint:i.endpoints[1],newTargetEndpoint:1===l?k:i.endpoints[1],connection:i},e)}else f.draggable&&d.initDraggable(this.element,a.dragOptions,"internal",d);if(1===l?d.router.sourceOrTargetChanged(i.floatingId,i.targetId,i,i.target,1):d.router.sourceOrTargetChanged(i.floatingId,i.sourceId,i,i.source,0),i.endpoints[0].finalEndpoint){var m=i.endpoints[0];m.detachFromConnection(i),i.endpoints[0]=i.endpoints[0].finalEndpoint,i.endpoints[0].addConnection(i)}c.isObject(b)&&i.mergeData(b),d.finaliseConnection(i,null,e,!1),i.setHover(!1),d.revalidate(i.endpoints[0].element)}.bind(this),p=function(){i.suspendedEndpoint&&(i.endpoints[l]=i.suspendedEndpoint,i.setHover(!1),i._forceDetach=!0,0===l?(i.source=i.suspendedEndpoint.element,i.sourceId=i.suspendedEndpoint.elementId):(i.target=i.suspendedEndpoint.element,i.targetId=i.suspendedEndpoint.elementId),i.suspendedEndpoint.addConnection(i),1===l?d.router.sourceOrTargetChanged(i.floatingId,i.targetId,i,i.target,1):d.router.sourceOrTargetChanged(i.floatingId,i.sourceId,i,i.source,0),d.repaint(i.sourceId),i._forceDetach=!1)};if(n=n&&a.isDropAllowed(i.sourceId,i.targetId,i.scope,i,k))return o(n),!0;p()}a.maybeCleanup&&a.maybeCleanup(k),d.currentlyDragging=!1}}}}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d=function(a,d,e,f,g){if(b.Connectors[d]=b.Connectors[d]||{},null==b.Connectors[d][e]){if(null==b.Connectors[e]){if(a.Defaults.DoNotThrowErrors)return null;throw new TypeError("jsPlumb: unknown connector type '"+e+"'")}b.Connectors[d][e]=function(){b.Connectors[e].apply(this,arguments),b.ConnectorRenderers[d].apply(this,arguments)},c.extend(b.Connectors[d][e],[b.Connectors[e],b.ConnectorRenderers[d]])}return new b.Connectors[d][e](f,g)},e=function(a,b,c){return a?c.makeAnchor(a,b,c):null},f=function(a,b,d,e){null!=b&&(b._jsPlumbConnections=b._jsPlumbConnections||{},e?delete b._jsPlumbConnections[a.id]:b._jsPlumbConnections[a.id]=!0,c.isEmpty(b._jsPlumbConnections)?d.removeClass(b,d.connectedClass):d.addClass(b,d.connectedClass))};b.Connection=function(a){var d=a.newEndpoint;this.id=a.id,this.connector=null,this.idPrefix="_jsplumb_c_",this.defaultLabelLocation=.5,this.defaultOverlayKeys=["Overlays","ConnectionOverlays"],this.previousConnection=a.previousConnection,this.source=b.getElement(a.source),this.target=b.getElement(a.target),b.OverlayCapableJsPlumbUIComponent.apply(this,arguments),a.sourceEndpoint?(this.source=a.sourceEndpoint.getElement(),this.sourceId=a.sourceEndpoint.elementId):this.sourceId=this._jsPlumb.instance.getId(this.source),a.targetEndpoint?(this.target=a.targetEndpoint.getElement(),this.targetId=a.targetEndpoint.elementId):this.targetId=this._jsPlumb.instance.getId(this.target),this.scope=a.scope,this.endpoints=[],this.endpointStyles=[];var e=this._jsPlumb.instance;e.manage(this.sourceId,this.source),e.manage(this.targetId,this.target),this._jsPlumb.visible=!0,this._jsPlumb.params={cssClass:a.cssClass,container:a.container,"pointer-events":a["pointer-events"],editorParams:a.editorParams,overlays:a.overlays},this._jsPlumb.lastPaintedAt=null,this.bind("mouseover",function(){this.setHover(!0)}.bind(this)),this.bind("mouseout",function(){this.setHover(!1)}.bind(this)),this.makeEndpoint=function(b,c,f,g,h){return f=f||this._jsPlumb.instance.getId(c),this.prepareEndpoint(e,d,this,g,b?0:1,a,c,f,h)},a.type&&(a.endpoints=a.endpoints||this._jsPlumb.instance.deriveEndpointAndAnchorSpec(a.type).endpoints);var f=this.makeEndpoint(!0,this.source,this.sourceId,a.sourceEndpoint),g=this.makeEndpoint(!1,this.target,this.targetId,a.targetEndpoint);f&&c.addToList(a.endpointsByElement,this.sourceId,f),g&&c.addToList(a.endpointsByElement,this.targetId,g),this.scope||(this.scope=this.endpoints[0].scope),null!=a.deleteEndpointsOnEmpty&&(this.endpoints[0].setDeleteOnEmpty(a.deleteEndpointsOnEmpty),this.endpoints[1].setDeleteOnEmpty(a.deleteEndpointsOnEmpty));var h=e.Defaults.ConnectionsDetachable;a.detachable===!1&&(h=!1),this.endpoints[0].connectionsDetachable===!1&&(h=!1),this.endpoints[1].connectionsDetachable===!1&&(h=!1);var i=a.reattach||this.endpoints[0].reattachConnections||this.endpoints[1].reattachConnections||e.Defaults.ReattachConnections;this.appendToDefaultType({detachable:h,reattach:i,paintStyle:this.endpoints[0].connectorStyle||this.endpoints[1].connectorStyle||a.paintStyle||e.Defaults.PaintStyle||b.Defaults.PaintStyle,hoverPaintStyle:this.endpoints[0].connectorHoverStyle||this.endpoints[1].connectorHoverStyle||a.hoverPaintStyle||e.Defaults.HoverPaintStyle||b.Defaults.HoverPaintStyle});var j=e.getSuspendedAt();if(!e.isSuspendDrawing()){var k=e.getCachedData(this.sourceId),l=k.o,m=k.s,n=e.getCachedData(this.targetId),o=n.o,p=n.s,q=j||jsPlumbUtil.uuid(),r=this.endpoints[0].anchor.compute({xy:[l.left,l.top],wh:m,element:this.endpoints[0],elementId:this.endpoints[0].elementId,txy:[o.left,o.top],twh:p,tElement:this.endpoints[1],timestamp:q,rotation:e.getRotation(this.endpoints[0].elementId)});this.endpoints[0].paint({anchorLoc:r,timestamp:q}),r=this.endpoints[1].anchor.compute({xy:[o.left,o.top],wh:p,element:this.endpoints[1],elementId:this.endpoints[1].elementId,txy:[l.left,l.top],twh:m,tElement:this.endpoints[0],timestamp:q,rotation:e.getRotation(this.endpoints[1].elementId)}),this.endpoints[1].paint({anchorLoc:r,timestamp:q})}this.getTypeDescriptor=function(){return"connection"},this.getAttachedElements=function(){return this.endpoints},this.isDetachable=function(a){return this._jsPlumb.detachable===!1?!1:null!=a?a.connectionsDetachable===!0:this._jsPlumb.detachable===!0},this.setDetachable=function(a){this._jsPlumb.detachable=a===!0},this.isReattach=function(){return this._jsPlumb.reattach===!0||this.endpoints[0].reattachConnections===!0||this.endpoints[1].reattachConnections===!0},this.setReattach=function(a){this._jsPlumb.reattach=a===!0},this._jsPlumb.cost=a.cost||this.endpoints[0].getConnectionCost(),this._jsPlumb.directed=a.directed,null==a.directed&&(this._jsPlumb.directed=this.endpoints[0].areConnectionsDirected());var s=b.extend({},this.endpoints[1].getParameters());b.extend(s,this.endpoints[0].getParameters()),b.extend(s,this.getParameters()),this.setParameters(s),this.setConnector(this.endpoints[0].connector||this.endpoints[1].connector||a.connector||e.Defaults.Connector||b.Defaults.Connector,!0);var t=null!=a.data&&c.isObject(a.data)?a.data:{};this.getData=function(){return t},this.setData=function(a){t=a||{}},this.mergeData=function(a){t=b.extend(t,a)};var u=["default",this.endpoints[0].connectionType,this.endpoints[1].connectionType,a.type].join(" ");/[^\s]/.test(u)&&this.addType(u,a.data,!0),this.updateConnectedClass()},c.extend(b.Connection,b.OverlayCapableJsPlumbUIComponent,{applyType:function(a,c,d){var e=null;null!=a.connector&&(e=this.getCachedTypeItem("connector",d.connector),null==e&&(e=this.prepareConnector(a.connector,d.connector),this.cacheTypeItem("connector",e,d.connector)),this.setPreparedConnector(e)),null!=a.detachable&&this.setDetachable(a.detachable),null!=a.reattach&&this.setReattach(a.reattach),a.scope&&(this.scope=a.scope),null!=a.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,a.cssClass);var f=null;a.anchor?(f=this.getCachedTypeItem("anchors",d.anchor),null==f&&(f=[this._jsPlumb.instance.makeAnchor(a.anchor),this._jsPlumb.instance.makeAnchor(a.anchor)],this.cacheTypeItem("anchors",f,d.anchor))):a.anchors&&(f=this.getCachedTypeItem("anchors",d.anchors),null==f&&(f=[this._jsPlumb.instance.makeAnchor(a.anchors[0]),this._jsPlumb.instance.makeAnchor(a.anchors[1])],this.cacheTypeItem("anchors",f,d.anchors))),null!=f&&(this.endpoints[0].anchor=f[0],this.endpoints[1].anchor=f[1],this.endpoints[1].anchor.isDynamic&&this._jsPlumb.instance.repaint(this.endpoints[1].elementId)),b.OverlayCapableJsPlumbUIComponent.applyType(this,a)},addClass:function(a,b){b&&(this.endpoints[0].addClass(a),this.endpoints[1].addClass(a),this.suspendedEndpoint&&this.suspendedEndpoint.addClass(a)),this.connector&&this.connector.addClass(a)},removeClass:function(a,b){b&&(this.endpoints[0].removeClass(a),this.endpoints[1].removeClass(a),this.suspendedEndpoint&&this.suspendedEndpoint.removeClass(a)),this.connector&&this.connector.removeClass(a)},isVisible:function(){return this._jsPlumb.visible},setVisible:function(a){this._jsPlumb.visible=a,this.connector&&this.connector.setVisible(a),this.repaint()},cleanup:function(){this.updateConnectedClass(!0),this.endpoints=null,this.source=null,this.target=null,null!=this.connector&&(this.connector.cleanup(!0),this.connector.destroy(!0)),this.connector=null},updateConnectedClass:function(a){this._jsPlumb&&(f(this,this.source,this._jsPlumb.instance,a),f(this,this.target,this._jsPlumb.instance,a))},setHover:function(b){this.connector&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&(this.connector.setHover(b),a.jsPlumb[b?"addClass":"removeClass"](this.source,this._jsPlumb.instance.hoverSourceClass),a.jsPlumb[b?"addClass":"removeClass"](this.target,this._jsPlumb.instance.hoverTargetClass))},getUuids:function(){return[this.endpoints[0].getUuid(),this.endpoints[1].getUuid()]},getCost:function(){return this._jsPlumb?this._jsPlumb.cost:-(1/0)},setCost:function(a){this._jsPlumb.cost=a},isDirected:function(){return this._jsPlumb.directed},getConnector:function(){return this.connector},prepareConnector:function(a,b){var e,f={_jsPlumb:this._jsPlumb.instance,cssClass:this._jsPlumb.params.cssClass,container:this._jsPlumb.params.container,"pointer-events":this._jsPlumb.params["pointer-events"]},g=this._jsPlumb.instance.getRenderMode();return c.isString(a)?e=d(this._jsPlumb.instance,g,a,f,this):c.isArray(a)&&(e=1===a.length?d(this._jsPlumb.instance,g,a[0],f,this):d(this._jsPlumb.instance,g,a[0],c.merge(a[1],f),this)),null!=b&&(e.typeId=b),e},setPreparedConnector:function(a,b,c,d){if(this.connector!==a){var e,f="";if(null!=this.connector&&(e=this.connector,f=e.getClass(),this.connector.cleanup(),this.connector.destroy()),this.connector=a,d&&this.cacheTypeItem("connector",a,d),this.canvas=this.connector.canvas,this.bgCanvas=this.connector.bgCanvas,this.connector.reattach(this._jsPlumb.instance),this.addClass(f),this.canvas&&(this.canvas._jsPlumb=this),this.bgCanvas&&(this.bgCanvas._jsPlumb=this),null!=e)for(var g=this.getOverlays(),h=0;h<g.length;h++)g[h].transfer&&g[h].transfer(this.connector);c||this.setListenerComponent(this.connector),b||this.repaint()}},setConnector:function(a,b,c,d){var e=this.prepareConnector(a,d);this.setPreparedConnector(e,b,c,d)},paint:function(a){if(!this._jsPlumb.instance.isSuspendDrawing()&&this._jsPlumb.visible){a=a||{};var b=a.timestamp,c=!1,d=c?this.sourceId:this.targetId,e=c?this.targetId:this.sourceId,f=c?0:1,g=c?1:0;if(null==b||b!==this._jsPlumb.lastPaintedAt){var h=this._jsPlumb.instance.updateOffset({elId:e}).o,i=this._jsPlumb.instance.updateOffset({elId:d}).o,j=this.endpoints[g],k=this.endpoints[f],l=j.anchor.getCurrentLocation({xy:[h.left,h.top],wh:[h.width,h.height],element:j,timestamp:b,rotation:this._jsPlumb.instance.getRotation(this.sourceId)}),m=k.anchor.getCurrentLocation({xy:[i.left,i.top],wh:[i.width,i.height],element:k,timestamp:b,rotation:this._jsPlumb.instance.getRotation(this.targetId)});this.connector.resetBounds(),this.connector.compute({sourcePos:l,targetPos:m,sourceOrientation:j.anchor.getOrientation(j),targetOrientation:k.anchor.getOrientation(k),sourceEndpoint:this.endpoints[g],targetEndpoint:this.endpoints[f],"stroke-width":this._jsPlumb.paintStyleInUse.strokeWidth,sourceInfo:h,targetInfo:i});var n={minX:1/0,minY:1/0,maxX:-(1/0),maxY:-(1/0)};for(var o in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(o)){var p=this._jsPlumb.overlays[o];p.isVisible()&&(this._jsPlumb.overlayPlacements[o]=p.draw(this.connector,this._jsPlumb.paintStyleInUse,this.getAbsoluteOverlayPosition(p)),n.minX=Math.min(n.minX,this._jsPlumb.overlayPlacements[o].minX),n.maxX=Math.max(n.maxX,this._jsPlumb.overlayPlacements[o].maxX),n.minY=Math.min(n.minY,this._jsPlumb.overlayPlacements[o].minY),n.maxY=Math.max(n.maxY,this._jsPlumb.overlayPlacements[o].maxY))}var q=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||1)/2,r=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||0),s={xmin:Math.min(this.connector.bounds.minX-(q+r),n.minX),ymin:Math.min(this.connector.bounds.minY-(q+r),n.minY),xmax:Math.max(this.connector.bounds.maxX+(q+r),n.maxX),ymax:Math.max(this.connector.bounds.maxY+(q+r),n.maxY)};this.connector.paintExtents=s,this.connector.paint(this._jsPlumb.paintStyleInUse,null,s);for(var t in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(t)){var u=this._jsPlumb.overlays[t];u.isVisible()&&u.paint(this._jsPlumb.overlayPlacements[t],s);
}}this._jsPlumb.lastPaintedAt=b}},repaint:function(a){var b=jsPlumb.extend(a||{},{});b.elId=this.sourceId,this.paint(b)},prepareEndpoint:function(a,c,d,f,g,h,i,j,k){var l;if(f)d.endpoints[g]=f,f.addConnection(d);else{h.endpoints||(h.endpoints=[null,null]);var m=k||h.endpoints[g]||h.endpoint||a.Defaults.Endpoints[g]||b.Defaults.Endpoints[g]||a.Defaults.Endpoint||b.Defaults.Endpoint;h.endpointStyles||(h.endpointStyles=[null,null]),h.endpointHoverStyles||(h.endpointHoverStyles=[null,null]);var n=h.endpointStyles[g]||h.endpointStyle||a.Defaults.EndpointStyles[g]||b.Defaults.EndpointStyles[g]||a.Defaults.EndpointStyle||b.Defaults.EndpointStyle;null==n.fill&&null!=h.paintStyle&&(n.fill=h.paintStyle.stroke),null==n.outlineStroke&&null!=h.paintStyle&&(n.outlineStroke=h.paintStyle.outlineStroke),null==n.outlineWidth&&null!=h.paintStyle&&(n.outlineWidth=h.paintStyle.outlineWidth);var o=h.endpointHoverStyles[g]||h.endpointHoverStyle||a.Defaults.EndpointHoverStyles[g]||b.Defaults.EndpointHoverStyles[g]||a.Defaults.EndpointHoverStyle||b.Defaults.EndpointHoverStyle;null!=h.hoverPaintStyle&&(null==o&&(o={}),null==o.fill&&(o.fill=h.hoverPaintStyle.stroke));var p=h.anchors?h.anchors[g]:h.anchor?h.anchor:e(a.Defaults.Anchors[g],j,a)||e(b.Defaults.Anchors[g],j,a)||e(a.Defaults.Anchor,j,a)||e(b.Defaults.Anchor,j,a),q=h.uuids?h.uuids[g]:null;l=c({paintStyle:n,hoverPaintStyle:o,endpoint:m,connections:[d],uuid:q,anchor:p,source:i,scope:h.scope,reattach:h.reattach||a.Defaults.ReattachConnections,detachable:h.detachable||a.Defaults.ConnectionsDetachable}),null==f&&l.setDeleteOnEmpty(!0),d.endpoints[g]=l,h.drawEndpoints===!1&&l.setVisible(!1,!0,!0)}return l},replaceEndpoint:function(a,b){var c=this.endpoints[a],d=c.elementId,e=this._jsPlumb.instance.getEndpoints(d),f=e.indexOf(c),g=this.makeEndpoint(0===a,c.element,d,null,b);this.endpoints[a]=g,e.splice(f,1,g),this._jsPlumb.instance.deleteObject({endpoint:c,deleteAttachedObjects:!1}),this._jsPlumb.instance.fire("endpointReplaced",{previous:c,current:g}),this._jsPlumb.instance.router.sourceOrTargetChanged(this.endpoints[1].elementId,this.endpoints[1].elementId,this,this.endpoints[1].element,1)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumbUtil,c=a.jsPlumb;c.AnchorManager=function(a){var d={},e={},f={},g={},h=this,i={},j=a.jsPlumbInstance,k={},l=function(a,b,c,d,e,f,g,h){for(var i=[],j=b[e?0:1]/(d.length+1),k=0;k<d.length;k++){var l=(k+1)*j,m=f*b[e?1:0];g&&(l=b[e?0:1]-l);var n=e?l:m,o=c.left+n,p=n/b[0],q=e?m:l,r=c.top+q,s=q/b[1];if(0!==h){var t=jsPlumbUtil.rotatePoint([o,r],[c.centerx,c.centery],h);o=t[0],r=t[1]}i.push([o,r,p,s,d[k][1],d[k][2]])}return i},m=function(a,b){return b[0][0]-a[0][0]},n=function(a,b){var c=a[0][0]<0?-Math.PI-a[0][0]:Math.PI-a[0][0],d=b[0][0]<0?-Math.PI-b[0][0]:Math.PI-b[0][0];return c-d},o={top:n,right:m,bottom:m,left:n},p=function(a,b){return a.sort(b)},q=function(a,b){var c=j.getCachedData(a),d=c.s,g=c.o,h=function(b,c,d,g,h,i,k){if(g.length>0)for(var m=p(g,o[b]),n="right"===b||"top"===b,q=j.getRotation(a),r=l(b,c,d,m,h,i,n,q),s=function(a,b){e[a.id]=[b[0],b[1],b[2],b[3]],f[a.id]=k},t=0;t<r.length;t++){var u=r[t][4],v=u.endpoints[0].elementId===a,w=u.endpoints[1].elementId===a;v&&s(u.endpoints[0],r[t]),w&&s(u.endpoints[1],r[t])}};h("bottom",d,g,b.bottom,!0,1,[0,1]),h("top",d,g,b.top,!0,0,[0,-1]),h("left",d,g,b.left,!1,0,[-1,0]),h("right",d,g,b.right,!1,1,[1,0])};this.reset=function(){d={},g={},i={}},this.addFloatingConnection=function(a,b){k[a]=b},this.newConnection=function(a){var d=a.sourceId,e=a.targetId,f=a.endpoints,h=!0,i=function(i,j,k,l,m){d===e&&k.isContinuous&&(a._jsPlumb.instance.removeElement(f[1].canvas),h=!1),b.addToList(g,l,[m,j,k.constructor===c.DynamicAnchor])};i(0,f[0],f[0].anchor,e,a),h&&i(1,f[1],f[1].anchor,d,a)};var r=function(a){!function(a,c){if(a){var d=function(a){return a[4]===c};b.removeWithFunction(a.top,d),b.removeWithFunction(a.left,d),b.removeWithFunction(a.bottom,d),b.removeWithFunction(a.right,d)}}(i[a.elementId],a.id)};this.connectionDetached=function(a,c){var d=a.connection||a,e=a.sourceId,f=a.targetId,i=d.endpoints,j=function(a,c,d,e,f){b.removeWithFunction(g[e],function(a){return a[0].id===f.id})};j(1,i[1],i[1].anchor,e,d),j(0,i[0],i[0].anchor,f,d),d.floatingId&&(j(d.floatingIndex,d.floatingEndpoint,d.floatingEndpoint.anchor,d.floatingId,d),r(d.floatingEndpoint)),r(d.endpoints[0]),r(d.endpoints[1]),c||(h.redraw(d.sourceId),d.targetId!==d.sourceId&&h.redraw(d.targetId))},this.addEndpoint=function(a,c){b.addToList(d,c,a)},this.changeId=function(a,b){g[b]=g[a],d[b]=d[a],delete g[a],delete d[a]},this.getConnectionsFor=function(a){return g[a]||[]},this.getEndpointsFor=function(a){return d[a]||[]},this.deleteEndpoint=function(a){b.removeWithFunction(d[a.elementId],function(b){return b.id===a.id}),r(a)},this.elementRemoved=function(a){delete k[a],delete d[a],d[a]=[]};var s=function(c,d,e,f,g,h,i,j,k,l,m,n){var o,p,q=-1,r=-1,s=f.endpoints[i],t=s.id,u=[1,0][i],v=[[d,e],f,g,h,t],w=c[k],x=s._continuousAnchorEdge?c[s._continuousAnchorEdge]:null;if(x){var y=b.findWithFunction(x,function(a){return a[4]===t});if(-1!==y)for(x.splice(y,1),o=0;o<x.length;o++)p=x[o][1],b.addWithFunction(m,p,function(a){return a.id===p.id}),b.addWithFunction(n,x[o][1].endpoints[i],function(a){return a.id===p.endpoints[i].id}),b.addWithFunction(n,x[o][1].endpoints[u],function(a){return a.id===p.endpoints[u].id})}for(o=0;o<w.length;o++)p=w[o][1],1===a.idx&&w[o][3]===h&&-1===r&&(r=o),b.addWithFunction(m,p,function(a){return a.id===p.id}),b.addWithFunction(n,w[o][1].endpoints[i],function(a){return a.id===p.endpoints[i].id}),b.addWithFunction(n,w[o][1].endpoints[u],function(a){return a.id===p.endpoints[u].id});if(-1!==q)w[q]=v;else{var z=j?-1!==r?r:0:w.length;w.splice(z,0,v)}s._continuousAnchorEdge=k};this.sourceOrTargetChanged=function(a,d,e,f,h){if(0===h){if(a!==d){e.sourceId=d,e.source=f,b.removeWithFunction(g[a],function(a){return a[0].id===e.id});var i=b.findWithFunction(g[e.targetId],function(a){return a[0].id===e.id});i>-1&&(g[e.targetId][i][0]=e,g[e.targetId][i][1]=e.endpoints[0],g[e.targetId][i][2]=e.endpoints[0].anchor.constructor===c.DynamicAnchor),b.addToList(g,d,[e,e.endpoints[1],e.endpoints[1].anchor.constructor===c.DynamicAnchor]),e.endpoints[1].anchor.isContinuous&&(e.source===e.target?e._jsPlumb.instance.removeElement(e.endpoints[1].canvas):null==e.endpoints[1].canvas.parentNode&&e._jsPlumb.instance.appendElement(e.endpoints[1].canvas)),e.updateConnectedClass()}}else if(1===h){var j=e.endpoints[0].elementId;e.target=f,e.targetId=d;var k=b.findWithFunction(g[j],function(a){return a[0].id===e.id}),l=b.findWithFunction(g[a],function(a){return a[0].id===e.id});-1!==k&&(g[j][k][0]=e,g[j][k][1]=e.endpoints[1],g[j][k][2]=e.endpoints[1].anchor.constructor===c.DynamicAnchor),l>-1&&(g[a].splice(l,1),b.addToList(g,d,[e,e.endpoints[0],e.endpoints[0].anchor.constructor===c.DynamicAnchor])),e.updateConnectedClass()}},this.rehomeEndpoint=function(a,b,c){var e=d[b]||[],f=j.getId(c);if(f!==b){var g=e.indexOf(a);if(g>-1){var i=e.splice(g,1)[0];h.add(i,f)}}for(var k=0;k<a.connections.length;k++)a.connections[k].sourceId===b?h.sourceOrTargetChanged(b,a.elementId,a.connections[k],a.element,0):a.connections[k].targetId===b&&h.sourceOrTargetChanged(b,a.elementId,a.connections[k],a.element,1)},this.redraw=function(a,e,f,h,l,m){var n=[],o=[],p=[];if(!j.isSuspendDrawing()){var r=d[a]||[],t=g[a]||[];f=f||jsPlumbUtil.uuid(),h=h||{left:0,top:0},e&&(e={left:e.left+h.left,top:e.top+h.top});for(var u=j.updateOffset({elId:a,offset:e,recalc:!1,timestamp:f}),v={},w=0;w<t.length;w++){var x=t[w][0],y=x.sourceId,z=x.targetId,A=x.endpoints[0].anchor.isContinuous,B=x.endpoints[1].anchor.isContinuous;if(A||B){var C=y+"_"+z,D=v[C],E=x.sourceId===a?1:0,F=j.getRotation(z),G=j.getRotation(y);A&&!i[y]&&(i[y]={top:[],right:[],bottom:[],left:[]}),B&&!i[z]&&(i[z]={top:[],right:[],bottom:[],left:[]}),a!==z&&j.updateOffset({elId:z,timestamp:f}),a!==y&&j.updateOffset({elId:y,timestamp:f});var H=j.getCachedData(z),I=j.getCachedData(y);z===y&&(A||B)?(s(i[y],-Math.PI/2,0,x,!1,z,0,!1,"top",y,n,o),s(i[z],-Math.PI/2,0,x,!1,y,1,!1,"top",z,n,o)):(D||(D=this.calculateOrientation(y,z,I.o,H.o,x.endpoints[0].anchor,x.endpoints[1].anchor,x,G,F),v[C]=D),A&&s(i[y],D.theta,0,x,!1,z,0,!1,D.a[0],y,n,o),B&&s(i[z],D.theta2,-1,x,!0,y,1,!0,D.a[1],z,n,o)),A&&b.addWithFunction(p,y,function(a){return a===y}),B&&b.addWithFunction(p,z,function(a){return a===z}),b.addWithFunction(n,x,function(a){return a.id===x.id}),(A&&0===E||B&&1===E)&&b.addWithFunction(o,x.endpoints[E],function(a){return a.id===x.endpoints[E].id})}}for(w=0;w<r.length;w++)0===r[w].connections.length&&r[w].anchor.isContinuous&&(i[a]||(i[a]={top:[],right:[],bottom:[],left:[]}),s(i[a],-Math.PI/2,0,{endpoints:[r[w],r[w]],paint:function(){}},!1,a,0,!1,r[w].anchor.getDefaultFace(),a,n,o),b.addWithFunction(p,a,function(b){return b===a}));for(w=0;w<p.length;w++)q(p[w],i[p[w]]);for(w=0;w<r.length;w++)r[w].paint({timestamp:f,offset:u,dimensions:u.s,recalc:m!==!0});for(w=0;w<o.length;w++){var J=j.getCachedData(o[w].elementId);o[w].paint({timestamp:null,offset:J,dimensions:J.s})}for(w=0;w<t.length;w++){var K=t[w][1];if(K.anchor.constructor===c.DynamicAnchor){K.paint({elementWithPrecedence:a,timestamp:f}),b.addWithFunction(n,t[w][0],function(a){return a.id===t[w][0].id});for(var L=0;L<K.connections.length;L++)K.connections[L]!==t[w][0]&&b.addWithFunction(n,K.connections[L],function(a){return a.id===K.connections[L].id})}else b.addWithFunction(n,t[w][0],function(a){return a.id===t[w][0].id})}var M=k[a];for(M&&M.paint({timestamp:f,recalc:!1,elId:a}),w=0;w<n.length;w++)n[w].paint({elId:a,timestamp:null,recalc:!1,clearEdits:l})}return{c:n,e:o}};var t=function(a){b.EventGenerator.apply(this),this.type="Continuous",this.isDynamic=!0,this.isContinuous=!0;for(var c=a.faces||["top","right","bottom","left"],d=!(a.clockwise===!1),g={},h={top:"bottom",right:"left",left:"right",bottom:"top"},i={top:"right",right:"bottom",left:"top",bottom:"left"},j={top:"left",right:"top",left:"bottom",bottom:"right"},k=d?i:j,l=d?j:i,m=a.cssClass||"",n=null,o=null,p=["left","right"],q=["top","bottom"],r=null,s=0;s<c.length;s++)g[c[s]]=!0;this.getDefaultFace=function(){return 0===c.length?"top":c[0]},this.isRelocatable=function(){return!0},this.isSnapOnRelocate=function(){return!0},this.verifyEdge=function(a){return g[a]?a:g[h[a]]?h[a]:g[k[a]]?k[a]:g[l[a]]?l[a]:a},this.isEdgeSupported=function(a){return null==r?null==o?g[a]===!0:o===a:-1!==r.indexOf(a)},this.setCurrentFace=function(a,b){n=a,b&&null!=o&&(o=n)},this.getCurrentFace=function(){return n},this.getSupportedFaces=function(){var a=[];for(var b in g)g[b]&&a.push(b);return a},this.lock=function(){o=n},this.unlock=function(){o=null},this.isLocked=function(){return null!=o},this.lockCurrentAxis=function(){null!=n&&(r="left"===n||"right"===n?p:q)},this.unlockCurrentAxis=function(){r=null},this.compute=function(a){return e[a.element.id]||[0,0]},this.getCurrentLocation=function(a){return e[a.element.id]||[0,0]},this.getOrientation=function(a){return f[a.id]||[0,0]},this.getCssClass=function(){return m}};j.continuousAnchorFactory={get:function(a){return new t(a)},clear:function(a){delete e[a]}}},c.AnchorManager.prototype.calculateOrientation=function(a,b,c,d,e,f,g,h,i){var j={HORIZONTAL:"horizontal",VERTICAL:"vertical",DIAGONAL:"diagonal",IDENTITY:"identity"},k=["left","top","right","bottom"];if(a===b)return{orientation:j.IDENTITY,a:["top","top"]};var l=Math.atan2(d.centery-c.centery,d.centerx-c.centerx),m=Math.atan2(c.centery-d.centery,c.centerx-d.centerx),n=[],o={};!function(a,b){for(var c=0;c<a.length;c++)if(o[a[c]]={left:[b[c][0].left,b[c][0].centery],right:[b[c][0].right,b[c][0].centery],top:[b[c][0].centerx,b[c][0].top],bottom:[b[c][0].centerx,b[c][0].bottom]},0!==b[c][1])for(var d in o[a[c]])o[a[c]][d]=jsPlumbUtil.rotatePoint(o[a[c]][d],[b[c][0].centerx,b[c][0].centery],b[c][1])}(["source","target"],[[c,h],[d,i]]);for(var p=0;p<k.length;p++)for(var q=0;q<k.length;q++)n.push({source:k[p],target:k[q],dist:Biltong.lineLength(o.source[k[p]],o.target[k[q]])});n.sort(function(a,b){return a.dist<b.dist?-1:a.dist>b.dist?1:0});for(var r=n[0].source,s=n[0].target,t=0;t<n.length&&(r=e.isContinuous&&e.locked?e.getCurrentFace():!e.isContinuous||e.isEdgeSupported(n[t].source)?n[t].source:null,s=f.isContinuous&&f.locked?f.getCurrentFace():!f.isContinuous||f.isEdgeSupported(n[t].target)?n[t].target:null,null==r||null==s);t++);return e.isContinuous&&e.setCurrentFace(r),f.isContinuous&&f.setCurrentFace(s),{a:[r,s],theta:l,theta2:m}},c.Anchor=function(a){this.x=a.x||0,this.y=a.y||0,this.elementId=a.elementId,this.cssClass=a.cssClass||"",this.orientation=a.orientation||[0,0],this.lastReturnValue=null,this.offsets=a.offsets||[0,0],this.timestamp=null,this._unrotatedOrientation=[this.orientation[0],this.orientation[1]],this.relocatable=a.relocatable!==!1,this.snapOnRelocate=a.snapOnRelocate!==!1,this.locked=!1,b.EventGenerator.apply(this),this.compute=function(a){var b=a.xy,c=a.wh,d=a.timestamp;if(d&&d===this.timestamp)return this.lastReturnValue;var e=[b[0]+this.x*c[0]+this.offsets[0],b[1]+this.y*c[1]+this.offsets[1],this.x,this.y],f=a.rotation;if(null!=f&&0!==f){var g=jsPlumbUtil.rotatePoint(e,[b[0]+c[0]/2,b[1]+c[1]/2],f);this.orientation[0]=Math.round(this._unrotatedOrientation[0]*g[2]-this._unrotatedOrientation[1]*g[3]),this.orientation[1]=Math.round(this._unrotatedOrientation[1]*g[2]+this._unrotatedOrientation[0]*g[3]),this.lastReturnValue=[g[0],g[1],this.x,this.y]}else this.orientation[0]=this._unrotatedOrientation[0],this.orientation[1]=this._unrotatedOrientation[1],this.lastReturnValue=e;return this.timestamp=d,this.lastReturnValue},this.getCurrentLocation=function(a){return a=a||{},null==this.lastReturnValue||null!=a.timestamp&&this.timestamp!==a.timestamp?this.compute(a):this.lastReturnValue},this.setPosition=function(a,b,c,d,e){(!this.locked||e)&&(this.x=a,this.y=b,this.orientation=[c,d],this.lastReturnValue=null)}},b.extend(c.Anchor,b.EventGenerator,{equals:function(a){if(!a)return!1;var b=a.getOrientation(),c=this.getOrientation();return this.x===a.x&&this.y===a.y&&this.offsets[0]===a.offsets[0]&&this.offsets[1]===a.offsets[1]&&c[0]===b[0]&&c[1]===b[1]},getOrientation:function(){return this.orientation},getCssClass:function(){return this.cssClass}}),c.FloatingAnchor=function(a){c.Anchor.apply(this,arguments);var b=a.reference,d=a.referenceCanvas,e=c.getSize(d),f=0,g=0,h=null,i=null;this.orientation=null,this.x=0,this.y=0,this.isFloating=!0,this.compute=function(a){var b=a.xy,c=[b[0]+e[0]/2,b[1]+e[1]/2];return i=c,c},this.getOrientation=function(a){if(h)return h;var c=b.getOrientation(a);return[Math.abs(c[0])*f*-1,Math.abs(c[1])*g*-1]},this.over=function(a,b){h=a.getOrientation(b)},this.out=function(){h=null},this.getCurrentLocation=function(a){return null==i?this.compute(a):i}},b.extend(c.FloatingAnchor,c.Anchor);var d=function(a,b,d){return a.constructor===c.Anchor?a:b.makeAnchor(a,d,b)};c.DynamicAnchor=function(a){c.Anchor.apply(this,arguments),this.isDynamic=!0,this.anchors=[],this.elementId=a.elementId,this.jsPlumbInstance=a.jsPlumbInstance;for(var b=0;b<a.anchors.length;b++)this.anchors[b]=d(a.anchors[b],this.jsPlumbInstance,this.elementId);this.getAnchors=function(){return this.anchors};var e=this.anchors.length>0?this.anchors[0]:null,f=e,g=function(a,b,c,d,e,f,g){var h=d[0]+a.x*e[0],i=d[1]+a.y*e[1],j=d[0]+e[0]/2,k=d[1]+e[1]/2;if(null!=f&&0!==f){var l=jsPlumbUtil.rotatePoint([h,i],[j,k],f);h=l[0],i=l[1]}return Math.sqrt(Math.pow(b-h,2)+Math.pow(c-i,2))+Math.sqrt(Math.pow(j-h,2)+Math.pow(k-i,2))},h=a.selector||function(a,b,c,d,e,f,h){for(var i=c[0]+d[0]/2,j=c[1]+d[1]/2,k=-1,l=1/0,m=0;m<h.length;m++){var n=g(h[m],i,j,a,b,e,f);l>n&&(k=m+0,l=n)}return h[k]};this.compute=function(a){var b=a.xy,c=a.wh,d=a.txy,g=a.twh,i=a.rotation,j=a.tRotation;return this.timestamp=a.timestamp,this.locked||null==d||null==g?(this.lastReturnValue=e.compute(a),this.lastReturnValue):(a.timestamp=null,e=h(b,c,d,g,i,j,this.anchors),this.x=e.x,this.y=e.y,e!==f&&this.fire("anchorChanged",e),f=e,this.lastReturnValue=e.compute(a),this.lastReturnValue)},this.getCurrentLocation=function(a){return null!=e?e.getCurrentLocation(a):null},this.getOrientation=function(a){return null!=e?e.getOrientation(a):[0,0]},this.over=function(a,b){null!=e&&e.over(a,b)},this.out=function(){null!=e&&e.out()},this.setAnchor=function(a){e=a},this.getCssClass=function(){return e&&e.getCssClass()||""},this.setAnchorCoordinates=function(a){var b=jsPlumbUtil.findWithFunction(this.anchors,function(b){return b.x===a[0]&&b.y===a[1]});return-1!==b?(this.setAnchor(this.anchors[b]),!0):!1}},b.extend(c.DynamicAnchor,c.Anchor);var e=function(a,b,d,e,f,g){c.Anchors[f]=function(c){var h=c.jsPlumbInstance.makeAnchor([a,b,d,e,0,0],c.elementId,c.jsPlumbInstance);return h.type=f,g&&g(h,c),h}};e(.5,0,0,-1,"TopCenter"),e(.5,1,0,1,"BottomCenter"),e(0,.5,-1,0,"LeftMiddle"),e(1,.5,1,0,"RightMiddle"),e(.5,0,0,-1,"Top"),e(.5,1,0,1,"Bottom"),e(0,.5,-1,0,"Left"),e(1,.5,1,0,"Right"),e(.5,.5,0,0,"Center"),e(1,0,0,-1,"TopRight"),e(1,1,0,1,"BottomRight"),e(0,0,0,-1,"TopLeft"),e(0,1,0,1,"BottomLeft"),c.Defaults.DynamicAnchors=function(a){return a.jsPlumbInstance.makeAnchors(["TopCenter","RightMiddle","BottomCenter","LeftMiddle"],a.elementId,a.jsPlumbInstance)},c.Anchors.AutoDefault=function(a){var b=a.jsPlumbInstance.makeDynamicAnchor(c.Defaults.DynamicAnchors(a));return b.type="AutoDefault",b};var f=function(a,b){c.Anchors[a]=function(c){var d=c.jsPlumbInstance.makeAnchor(["Continuous",{faces:b}],c.elementId,c.jsPlumbInstance);return d.type=a,d}};c.Anchors.Continuous=function(a){return a.jsPlumbInstance.continuousAnchorFactory.get(a)},f("ContinuousLeft",["left"]),f("ContinuousTop",["top"]),f("ContinuousBottom",["bottom"]),f("ContinuousRight",["right"]),e(0,0,0,0,"Assign",function(a,b){var c=b.position||"Fixed";a.positionFinder=c.constructor===String?b.jsPlumbInstance.AnchorPositionFinders[c]:c,a.constructorParams=b}),a.jsPlumbInstance.prototype.AnchorPositionFinders={Fixed:function(a,b,c){return[(a.left-b.left)/c[0],(a.top-b.top)/c[1]]},Grid:function(a,b,c,d){var e=a.left-b.left,f=a.top-b.top,g=c[0]/d.grid[0],h=c[1]/d.grid[1],i=Math.floor(e/g),j=Math.floor(f/h);return[(i*g+g/2)/c[0],(j*h+h/2)/c[1]]}},c.Anchors.Perimeter=function(a){a=a||{};var b=a.anchorCount||60,c=a.shape;if(!c)throw new Error("no shape supplied to Perimeter Anchor type");var d=function(){for(var a=.5,c=2*Math.PI/b,d=0,e=[],f=0;b>f;f++){var g=a+a*Math.sin(d),h=a+a*Math.cos(d);e.push([g,h,0,0]),d+=c}return e},e=function(a){for(var c=b/a.length,d=[],e=function(a,e,f,g,h,i,j){c=b*h;for(var k=(f-a)/c,l=(g-e)/c,m=0;c>m;m++)d.push([a+k*m,e+l*m,null==i?0:i,null==j?0:j])},f=0;f<a.length;f++)e.apply(null,a[f]);return d},f=function(a){for(var b=[],c=0;c<a.length;c++)b.push([a[c][0],a[c][1],a[c][2],a[c][3],1/a.length,a[c][4],a[c][5]]);return e(b)},g=function(){return f([[0,0,1,0,0,-1],[1,0,1,1,1,0],[1,1,0,1,0,1],[0,1,0,0,-1,0]])},h={Circle:d,Ellipse:d,Diamond:function(){return f([[.5,0,1,.5],[1,.5,.5,1],[.5,1,0,.5],[0,.5,.5,0]])},Rectangle:g,Square:g,Triangle:function(){return f([[.5,0,1,1],[1,1,0,1],[0,1,.5,0]])},Path:function(a){for(var b=a.points,c=[],d=0,f=0;f<b.length-1;f++){var g=Math.sqrt(Math.pow(b[f][2]-b[f][0])+Math.pow(b[f][3]-b[f][1]));d+=g,c.push([b[f][0],b[f][1],b[f+1][0],b[f+1][1],g])}for(var h=0;h<c.length;h++)c[h][4]=c[h][4]/d;return e(c)}},i=function(a,b){for(var c=[],d=b/180*Math.PI,e=0;e<a.length;e++){var f=a[e][0]-.5,g=a[e][1]-.5;c.push([.5+(f*Math.cos(d)-g*Math.sin(d)),.5+(f*Math.sin(d)+g*Math.cos(d)),a[e][2],a[e][3]])}return c};if(!h[c])throw new Error("Shape ["+c+"] is unknown by Perimeter Anchor type");var j=h[c](a);a.rotation&&(j=i(j,a.rotation));var k=a.jsPlumbInstance.makeDynamicAnchor(j);return k.type="Perimeter",k}}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=(a.jsPlumbUtil,a.jsPlumb);b.DefaultRouter=function(a){this.jsPlumbInstance=a,this.anchorManager=new b.AnchorManager({jsPlumbInstance:a}),this.sourceOrTargetChanged=function(a,b,c,d,e){this.anchorManager.sourceOrTargetChanged(a,b,c,d,e)},this.reset=function(){this.anchorManager.reset()},this.changeId=function(a,b){this.anchorManager.changeId(a,b)},this.elementRemoved=function(a){this.anchorManager.elementRemoved(a)},this.newConnection=function(a){this.anchorManager.newConnection(a)},this.connectionDetached=function(a,b){this.anchorManager.connectionDetached(a,b)},this.redraw=function(a,b,c,d,e,f){return this.anchorManager.redraw(a,b,c,d,e,f)},this.deleteEndpoint=function(a){this.anchorManager.deleteEndpoint(a)},this.rehomeEndpoint=function(a,b,c){this.anchorManager.rehomeEndpoint(a,b,c)},this.addEndpoint=function(a,b){this.anchorManager.addEndpoint(a,b)}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d=a.Biltong;b.Segments={AbstractSegment:function(a){this.params=a,this.findClosestPointOnPath=function(a,b){return{d:1/0,x:null,y:null,l:null}},this.getBounds=function(){return{minX:Math.min(a.x1,a.x2),minY:Math.min(a.y1,a.y2),maxX:Math.max(a.x1,a.x2),maxY:Math.max(a.y1,a.y2)}},this.lineIntersection=function(a,b,c,d){return[]},this.boxIntersection=function(a,b,c,d){var e=[];return e.push.apply(e,this.lineIntersection(a,b,a+c,b)),e.push.apply(e,this.lineIntersection(a+c,b,a+c,b+d)),e.push.apply(e,this.lineIntersection(a+c,b+d,a,b+d)),e.push.apply(e,this.lineIntersection(a,b+d,a,b)),e},this.boundingBoxIntersection=function(a){return this.boxIntersection(a.x,a.y,a.w,a.y)}},Straight:function(a){var c,e,f,g,h,i,j,k=(b.Segments.AbstractSegment.apply(this,arguments),function(){c=Math.sqrt(Math.pow(h-g,2)+Math.pow(j-i,2)),e=d.gradient({x:g,y:i},{x:h,y:j}),f=-1/e});this.type="Straight",this.getLength=function(){return c},this.getGradient=function(){return e},this.getCoordinates=function(){return{x1:g,y1:i,x2:h,y2:j}},this.setCoordinates=function(a){g=a.x1,i=a.y1,h=a.x2,j=a.y2,k()},this.setCoordinates({x1:a.x1,y1:a.y1,x2:a.x2,y2:a.y2}),this.getBounds=function(){return{minX:Math.min(g,h),minY:Math.min(i,j),maxX:Math.max(g,h),maxY:Math.max(i,j)}},this.pointOnPath=function(a,b){if(0!==a||b){if(1!==a||b){var e=b?a>0?a:c+a:a*c;return d.pointOnLine({x:g,y:i},{x:h,y:j},e)}return{x:h,y:j}}return{x:g,y:i}},this.gradientAtPoint=function(a){return e},this.pointAlongPathFrom=function(a,b,c){var e=this.pointOnPath(a,c),f=0>=b?{x:g,y:i}:{x:h,y:j};return 0>=b&&Math.abs(b)>1&&(b*=-1),d.pointOnLine(e,f,b)};var l=function(a,b,c){return c>=Math.min(a,b)&&c<=Math.max(a,b)},m=function(a,b,c){return Math.abs(c-a)<Math.abs(c-b)?a:b};this.findClosestPointOnPath=function(a,b){var k={d:1/0,x:null,y:null,l:null,x1:g,x2:h,y1:i,y2:j};if(0===e)k.y=i,k.x=l(g,h,a)?a:m(g,h,a);else if(e===1/0||e===-(1/0))k.x=g,k.y=l(i,j,b)?b:m(i,j,b);else{var n=i-e*g,o=b-f*a,p=(o-n)/(e-f),q=e*p+n;k.x=l(g,h,p)?p:m(g,h,p),k.y=l(i,j,q)?q:m(i,j,q)}var r=d.lineLength([k.x,k.y],[g,i]);return k.d=d.lineLength([a,b],[k.x,k.y]),k.l=r/c,k};var n=function(a,b,c){return c>b?a>=b&&c>=a:b>=a&&a>=c},o=n;this.lineIntersection=function(a,b,c,f){var k=Math.abs(d.gradient({x:a,y:b},{x:c,y:f})),l=Math.abs(e),m=l===1/0?g:i-l*g,n=[],p=k===1/0?a:b-k*a;if(k!==l)if(k===1/0&&0===l)o(a,g,h)&&o(i,b,f)&&(n=[a,i]);else if(0===k&&l===1/0)o(b,i,j)&&o(g,a,c)&&(n=[g,b]);else{var q,r;k===1/0?(q=a,o(q,g,h)&&(r=l*a+m,o(r,b,f)&&(n=[q,r]))):0===k?(r=b,o(r,i,j)&&(q=(b-m)/l,o(q,a,c)&&(n=[q,r]))):(q=(p-m)/(l-k),r=l*q+m,o(q,g,h)&&o(r,i,j)&&(n=[q,r]))}return n},this.boxIntersection=function(a,b,c,d){var e=[];return e.push.apply(e,this.lineIntersection(a,b,a+c,b)),e.push.apply(e,this.lineIntersection(a+c,b,a+c,b+d)),e.push.apply(e,this.lineIntersection(a+c,b+d,a,b+d)),e.push.apply(e,this.lineIntersection(a,b+d,a,b)),e},this.boundingBoxIntersection=function(a){return this.boxIntersection(a.x,a.y,a.w,a.h)}},Arc:function(a){var c=(b.Segments.AbstractSegment.apply(this,arguments),function(b,c){return d.theta([a.cx,a.cy],[b,c])}),e=function(a,b){if(a.anticlockwise){var c=a.startAngle<a.endAngle?a.startAngle+f:a.startAngle,d=Math.abs(c-a.endAngle);return c-d*b}var e=a.endAngle<a.startAngle?a.endAngle+f:a.endAngle,g=Math.abs(e-a.startAngle);return a.startAngle+g*b},f=2*Math.PI;this.radius=a.r,this.anticlockwise=a.ac,this.type="Arc",a.startAngle&&a.endAngle?(this.startAngle=a.startAngle,this.endAngle=a.endAngle,this.x1=a.cx+this.radius*Math.cos(a.startAngle),this.y1=a.cy+this.radius*Math.sin(a.startAngle),this.x2=a.cx+this.radius*Math.cos(a.endAngle),this.y2=a.cy+this.radius*Math.sin(a.endAngle)):(this.startAngle=c(a.x1,a.y1),this.endAngle=c(a.x2,a.y2),this.x1=a.x1,this.y1=a.y1,this.x2=a.x2,this.y2=a.y2),this.endAngle<0&&(this.endAngle+=f),this.startAngle<0&&(this.startAngle+=f);var g=this.endAngle<this.startAngle?this.endAngle+f:this.endAngle;this.sweep=Math.abs(g-this.startAngle),this.anticlockwise&&(this.sweep=f-this.sweep);var h=2*Math.PI*this.radius,i=this.sweep/f,j=h*i;this.getLength=function(){return j},this.getBounds=function(){return{minX:a.cx-a.r,maxX:a.cx+a.r,minY:a.cy-a.r,maxY:a.cy+a.r}};var k=1e-10,l=function(a){var b=Math.floor(a),c=Math.ceil(a);return k>a-b?b:k>c-a?c:a};this.pointOnPath=function(b,c){if(0===b)return{x:this.x1,y:this.y1,theta:this.startAngle};if(1===b)return{x:this.x2,y:this.y2,theta:this.endAngle};c&&(b/=j);var d=e(this,b),f=a.cx+a.r*Math.cos(d),g=a.cy+a.r*Math.sin(d);return{x:l(f),y:l(g),theta:d}},this.gradientAtPoint=function(b,c){var e=this.pointOnPath(b,c),f=d.normal([a.cx,a.cy],[e.x,e.y]);return this.anticlockwise||f!==1/0&&f!==-(1/0)||(f*=-1),f},this.pointAlongPathFrom=function(b,c,d){var e=this.pointOnPath(b,d),f=c/h*2*Math.PI,g=this.anticlockwise?-1:1,i=e.theta+g*f,j=a.cx+this.radius*Math.cos(i),k=a.cy+this.radius*Math.sin(i);return{x:j,y:k}}},Bezier:function(c){this.curve=[{x:c.x1,y:c.y1},{x:c.cp1x,y:c.cp1y},{x:c.cp2x,y:c.cp2y},{x:c.x2,y:c.y2}];var d=function(a){return a[0].x===a[1].x&&a[0].y===a[1].y},e=function(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))},f=function(a){var b={x:0,y:0};if(0===a)return this.curve[0];var c=this.curve.length-1;if(1===a)return this.curve[c];var d=this.curve,e=1-a;if(0===c)return this.curve[0];if(1===c)return{x:e*d[0].x+a*d[1].x,y:e*d[0].y+a*d[1].y};if(4>c){var f,g,h,i=e*e,j=a*a,k=0;return 2===c?(d=[d[0],d[1],d[2],b],f=i,g=2*(e*a),h=j):3===c&&(f=i*e,g=3*(i*a),h=3*(e*j),k=a*j),{x:f*d[0].x+g*d[1].x+h*d[2].x+k*d[3].x,y:f*d[0].y+g*d[1].y+h*d[2].y+k*d[3].y}}return b}.bind(this),g=function(a){var b=[];a--;for(var c=0;a>=c;c++)b.push(f(c/a));return b},h=function(){d(this.curve)&&(this.length=0);var a=16,b=g(a);this.length=0;for(var c=0;a-1>c;c++){var f=b[c],h=b[c+1];this.length+=e(f,h)}}.bind(this);b.Segments.AbstractSegment.apply(this,arguments);this.bounds={minX:Math.min(c.x1,c.x2,c.cp1x,c.cp2x),minY:Math.min(c.y1,c.y2,c.cp1y,c.cp2y),maxX:Math.max(c.x1,c.x2,c.cp1x,c.cp2x),maxY:Math.max(c.y1,c.y2,c.cp1y,c.cp2y)},this.type="Bezier",h();var i=function(b,c,d){return d&&(c=a.jsBezier.locationAlongCurveFrom(b,c>0?0:1,c)),c};this.pointOnPath=function(b,c){return b=i(this.curve,b,c),a.jsBezier.pointOnCurve(this.curve,b)},this.gradientAtPoint=function(b,c){return b=i(this.curve,b,c),a.jsBezier.gradientAtPoint(this.curve,b)},this.pointAlongPathFrom=function(b,c,d){return b=i(this.curve,b,d),a.jsBezier.pointAlongCurveFrom(this.curve,b,c)},this.getLength=function(){return this.length},this.getBounds=function(){return this.bounds},this.findClosestPointOnPath=function(b,c){var d=a.jsBezier.nearestPointOnCurve({x:b,y:c},this.curve);return{d:Math.sqrt(Math.pow(d.point.x-b,2)+Math.pow(d.point.y-c,2)),x:d.point.x,y:d.point.y,l:1-d.location,s:this}},this.lineIntersection=function(b,c,d,e){return a.jsBezier.lineIntersection(b,c,d,e,this.curve)}}},b.SegmentRenderer={getPath:function(a,b){return{Straight:function(b){var c=a.getCoordinates();return(b?"M "+c.x1+" "+c.y1+" ":"")+"L "+c.x2+" "+c.y2},Bezier:function(b){var c=a.params;return(b?"M "+c.x2+" "+c.y2+" ":"")+"C "+c.cp2x+" "+c.cp2y+" "+c.cp1x+" "+c.cp1y+" "+c.x1+" "+c.y1},Arc:function(b){var c=a.params,d=a.sweep>Math.PI?1:0,e=a.anticlockwise?0:1;return(b?"M"+a.x1+" "+a.y1+" ":"")+"A "+a.radius+" "+c.r+" 0 "+d+","+e+" "+a.x2+" "+a.y2}}[a.type](b)}};var e=function(){this.resetBounds=function(){this.bounds={minX:1/0,minY:1/0,maxX:-(1/0),maxY:-(1/0)}},this.resetBounds()};b.Connectors.AbstractConnector=function(a){e.apply(this,arguments);var f=[],g=0,h=[],i=[],j=a.stub||0,k=c.isArray(j)?j[0]:j,l=c.isArray(j)?j[1]:j,m=a.gap||0,n=c.isArray(m)?m[0]:m,o=c.isArray(m)?m[1]:m,p=null,q=null;this.getPathData=function(){for(var a="",c=0;c<f.length;c++)a+=b.SegmentRenderer.getPath(f[c],0===c),a+=" ";return a},this.findSegmentForPoint=function(a,b){for(var c={d:1/0,s:null,x:null,y:null,l:null},d=0;d<f.length;d++){var e=f[d].findClosestPointOnPath(a,b);e.d<c.d&&(c.d=e.d,c.l=e.l,c.x=e.x,c.y=e.y,c.s=f[d],c.x1=e.x1,c.x2=e.x2,c.y1=e.y1,c.y2=e.y2,c.index=d,c.connectorLocation=h[d][0]+e.l*(h[d][1]-h[d][0]))}return c},this.lineIntersection=function(a,b,c,d){for(var e=[],g=0;g<f.length;g++)e.push.apply(e,f[g].lineIntersection(a,b,c,d));return e},this.boxIntersection=function(a,b,c,d){for(var e=[],g=0;g<f.length;g++)e.push.apply(e,f[g].boxIntersection(a,b,c,d));return e},this.boundingBoxIntersection=function(a){for(var b=[],c=0;c<f.length;c++)b.push.apply(b,f[c].boundingBoxIntersection(a));return b};var r=function(){for(var a=0,b=0;b<f.length;b++){var c=f[b].getLength();i[b]=c/g,h[b]=[a,a+=c/g]}},s=function(a,b){var c,d,e;if(b&&(a=a>0?a/g:(g+a)/g),1===a)c=f.length-1,e=1;else if(0===a)e=0,c=0;else if(a>=.5){for(c=0,e=0,d=h.length-1;d>-1;d--)if(h[d][1]>=a&&h[d][0]<=a){c=d,e=(a-h[d][0])/i[d];break}}else for(c=h.length-1,e=1,d=0;d<h.length;d++)if(h[d][1]>=a){c=d,e=(a-h[d][0])/i[d];break}return{segment:f[c],proportion:e,index:c}},t=function(a,c,d){if(d.x1!==d.x2||d.y1!==d.y2){var e=new b.Segments[c](d);f.push(e),g+=e.getLength(),a.updateBounds(e)}},u=function(){g=f.length=h.length=i.length=0};this.setSegments=function(a){p=[],g=0;for(var b=0;b<a.length;b++)p.push(a[b]),g+=a[b].getLength()},this.getLength=function(){return g};var v=function(a){this.strokeWidth=a.strokeWidth;var b=d.quadrant(a.sourcePos,a.targetPos),c=a.targetPos[0]<a.sourcePos[0],e=a.targetPos[1]<a.sourcePos[1],f=a.strokeWidth||1,g=a.sourceEndpoint.anchor.getOrientation(a.sourceEndpoint),h=a.targetEndpoint.anchor.getOrientation(a.targetEndpoint),i=c?a.targetPos[0]:a.sourcePos[0],j=e?a.targetPos[1]:a.sourcePos[1],m=Math.abs(a.targetPos[0]-a.sourcePos[0]),p=Math.abs(a.targetPos[1]-a.sourcePos[1]);if(0===g[0]&&0===g[1]||0===h[0]&&0===h[1]){var q=m>p?0:1,r=[1,0][q];g=[],h=[],g[q]=a.sourcePos[q]>a.targetPos[q]?-1:1,h[q]=a.sourcePos[q]>a.targetPos[q]?1:-1,g[r]=0,h[r]=0}var s=c?m+n*g[0]:n*g[0],t=e?p+n*g[1]:n*g[1],u=c?o*h[0]:m+o*h[0],v=e?o*h[1]:p+o*h[1],w=g[0]*h[0]+g[1]*h[1],x={sx:s,sy:t,tx:u,ty:v,lw:f,xSpan:Math.abs(u-s),ySpan:Math.abs(v-t),mx:(s+u)/2,my:(t+v)/2,so:g,to:h,x:i,y:j,w:m,h:p,segment:b,startStubX:s+g[0]*k,startStubY:t+g[1]*k,endStubX:u+h[0]*l,endStubY:v+h[1]*l,isXGreaterThanStubTimes2:Math.abs(s-u)>k+l,isYGreaterThanStubTimes2:Math.abs(t-v)>k+l,opposite:-1===w,perpendicular:0===w,orthogonal:1===w,sourceAxis:0===g[0]?"y":"x",points:[i,j,m,p,s,t,u,v],stubs:[k,l]};return x.anchorOrientation=x.opposite?"opposite":x.orthogonal?"orthogonal":"perpendicular",x};this.getSegments=function(){return f},this.updateBounds=function(a){var b=a.getBounds();this.bounds.minX=Math.min(this.bounds.minX,b.minX),this.bounds.maxX=Math.max(this.bounds.maxX,b.maxX),this.bounds.minY=Math.min(this.bounds.minY,b.minY),this.bounds.maxY=Math.max(this.bounds.maxY,b.maxY)};return this.pointOnPath=function(a,b){var c=s(a,b);return c.segment&&c.segment.pointOnPath(c.proportion,!1)||[0,0]},this.gradientAtPoint=function(a,b){var c=s(a,b);return c.segment&&c.segment.gradientAtPoint(c.proportion,!1)||0},this.pointAlongPathFrom=function(a,b,c){var d=s(a,c);return d.segment&&d.segment.pointAlongPathFrom(d.proportion,b,!1)||[0,0]},this.compute=function(a){q=v.call(this,a),u(),this._compute(q,a),this.x=q.points[0],this.y=q.points[1],
this.w=q.points[2],this.h=q.points[3],this.segment=q.segment,r()},{addSegment:t,prepareCompute:v,sourceStub:k,targetStub:l,maxStub:Math.max(k,l),sourceGap:n,targetGap:o,maxGap:Math.max(n,o)}},c.extend(b.Connectors.AbstractConnector,e),b.Endpoints.AbstractEndpoint=function(a){e.apply(this,arguments);var b=this.compute=function(a,b,c,d){var e=this._compute.apply(this,arguments);return this.x=e[0],this.y=e[1],this.w=e[2],this.h=e[3],this.bounds.minX=this.x,this.bounds.minY=this.y,this.bounds.maxX=this.x+this.w,this.bounds.maxY=this.y+this.h,e};return{compute:b,cssClass:a.cssClass}},c.extend(b.Endpoints.AbstractEndpoint,e),b.Endpoints.Dot=function(a){this.type="Dot";b.Endpoints.AbstractEndpoint.apply(this,arguments);a=a||{},this.radius=a.radius||10,this.defaultOffset=.5*this.radius,this.defaultInnerRadius=this.radius/3,this._compute=function(a,b,c,d){this.radius=c.radius||this.radius;var e=a[0]-this.radius,f=a[1]-this.radius,g=2*this.radius,h=2*this.radius;if(c.stroke){var i=c.strokeWidth||1;e-=i,f-=i,g+=2*i,h+=2*i}return[e,f,g,h,this.radius]}},c.extend(b.Endpoints.Dot,b.Endpoints.AbstractEndpoint),b.Endpoints.Rectangle=function(a){this.type="Rectangle";b.Endpoints.AbstractEndpoint.apply(this,arguments);a=a||{},this.width=a.width||20,this.height=a.height||20,this._compute=function(a,b,c,d){var e=c.width||this.width,f=c.height||this.height,g=a[0]-e/2,h=a[1]-f/2;return[g,h,e,f]}},c.extend(b.Endpoints.Rectangle,b.Endpoints.AbstractEndpoint);var f=function(a){b.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.displayElements=[]};c.extend(f,b.jsPlumbUIComponent,{getDisplayElements:function(){return this._jsPlumb.displayElements},appendDisplayElement:function(a){this._jsPlumb.displayElements.push(a)}}),b.Endpoints.Image=function(d){this.type="Image",f.apply(this,arguments),b.Endpoints.AbstractEndpoint.apply(this,arguments);var e=d.onload,g=d.src||d.url,h=d.cssClass?" "+d.cssClass:"";this._jsPlumb.img=new Image,this._jsPlumb.ready=!1,this._jsPlumb.initialized=!1,this._jsPlumb.deleted=!1,this._jsPlumb.widthToUse=d.width,this._jsPlumb.heightToUse=d.height,this._jsPlumb.endpoint=d.endpoint,this._jsPlumb.img.onload=function(){null!=this._jsPlumb&&(this._jsPlumb.ready=!0,this._jsPlumb.widthToUse=this._jsPlumb.widthToUse||this._jsPlumb.img.width,this._jsPlumb.heightToUse=this._jsPlumb.heightToUse||this._jsPlumb.img.height,e&&e(this))}.bind(this),this._jsPlumb.endpoint.setImage=function(a,b){var c=a.constructor===String?a:a.src;e=b,this._jsPlumb.img.src=c,null!=this.canvas&&this.canvas.setAttribute("src",this._jsPlumb.img.src)}.bind(this),this._jsPlumb.endpoint.setImage(g,e),this._compute=function(a,b,c,d){return this.anchorPoint=a,this._jsPlumb.ready?[a[0]-this._jsPlumb.widthToUse/2,a[1]-this._jsPlumb.heightToUse/2,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse]:[0,0,0,0]},this.canvas=b.createElement("img",{position:"absolute",margin:0,padding:0,outline:0},this._jsPlumb.instance.endpointClass+h),this._jsPlumb.widthToUse&&this.canvas.setAttribute("width",this._jsPlumb.widthToUse),this._jsPlumb.heightToUse&&this.canvas.setAttribute("height",this._jsPlumb.heightToUse),this._jsPlumb.instance.appendElement(this.canvas),this.actuallyPaint=function(a,b,d){if(!this._jsPlumb.deleted){this._jsPlumb.initialized||(this.canvas.setAttribute("src",this._jsPlumb.img.src),this.appendDisplayElement(this.canvas),this._jsPlumb.initialized=!0);var e=this.anchorPoint[0]-this._jsPlumb.widthToUse/2,f=this.anchorPoint[1]-this._jsPlumb.heightToUse/2;c.sizeElement(this.canvas,e,f,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse)}},this.paint=function(b,c){null!=this._jsPlumb&&(this._jsPlumb.ready?this.actuallyPaint(b,c):a.setTimeout(function(){this.paint(b,c)}.bind(this),200))}},c.extend(b.Endpoints.Image,[f,b.Endpoints.AbstractEndpoint],{cleanup:function(a){a&&(this._jsPlumb.deleted=!0,this.canvas&&this.canvas.parentNode.removeChild(this.canvas),this.canvas=null)}}),b.Endpoints.Blank=function(a){b.Endpoints.AbstractEndpoint.apply(this,arguments);this.type="Blank",f.apply(this,arguments),this._compute=function(a,b,c,d){return[a[0],a[1],10,0]};var d=a.cssClass?" "+a.cssClass:"";this.canvas=b.createElement("div",{display:"block",width:"1px",height:"1px",background:"transparent",position:"absolute"},this._jsPlumb.instance.endpointClass+d),this._jsPlumb.instance.appendElement(this.canvas),this.paint=function(a,b){c.sizeElement(this.canvas,this.x,this.y,this.w,this.h)}},c.extend(b.Endpoints.Blank,[b.Endpoints.AbstractEndpoint,f],{cleanup:function(){this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas)}}),b.Endpoints.Triangle=function(a){this.type="Triangle",b.Endpoints.AbstractEndpoint.apply(this,arguments);var c=this;a=a||{},a.width=a.width||55,a.height=a.height||55,this.width=a.width,this.height=a.height,this._compute=function(a,b,d,e){var f=d.width||c.width,g=d.height||c.height,h=a[0]-f/2,i=a[1]-g/2;return[h,i,f,g]}};var g=b.Overlays.AbstractOverlay=function(a){this.visible=!0,this.isAppendedAtTopLevel=!0,this.component=a.component,this.loc=null==a.location?.5:a.location,this.endpointLoc=null==a.endpointLocation?[.5,.5]:a.endpointLocation,this.visible=a.visible!==!1};g.prototype={cleanup:function(a){a&&(this.component=null,this.canvas=null,this.endpointLoc=null)},reattach:function(a,b){},setVisible:function(a){this.visible=a,this.component.repaint()},isVisible:function(){return this.visible},hide:function(){this.setVisible(!1)},show:function(){this.setVisible(!0)},incrementLocation:function(a){this.loc+=a,this.component.repaint()},setLocation:function(a){this.loc=a,this.component.repaint()},getLocation:function(){return this.loc},updateFrom:function(){}},b.Overlays.Arrow=function(a){this.type="Arrow",g.apply(this,arguments),this.isAppendedAtTopLevel=!1,a=a||{};var e=this;this.length=a.length||20,this.width=a.width||20,this.id=a.id,this.direction=(a.direction||1)<0?-1:1;var f=a.paintStyle||{"stroke-width":1},h=a.foldback||.623;this.computeMaxSize=function(){return 1.5*e.width},this.elementCreated=function(c,d){if(this.path=c,a.events)for(var e in a.events)b.on(c,e,a.events[e])},this.draw=function(a,b){var e,g,i,j,k;if(a.pointAlongPathFrom){if(c.isString(this.loc)||this.loc>1||this.loc<0){var l=parseInt(this.loc,10),m=this.loc<0?1:0;e=a.pointAlongPathFrom(m,l,!1),g=a.pointAlongPathFrom(m,l-this.direction*this.length/2,!1),i=d.pointOnLine(e,g,this.length)}else if(1===this.loc){if(e=a.pointOnPath(this.loc),g=a.pointAlongPathFrom(this.loc,-this.length),i=d.pointOnLine(e,g,this.length),-1===this.direction){var n=i;i=e,e=n}}else if(0===this.loc){if(i=a.pointOnPath(this.loc),g=a.pointAlongPathFrom(this.loc,this.length),e=d.pointOnLine(i,g,this.length),-1===this.direction){var o=i;i=e,e=o}}else e=a.pointAlongPathFrom(this.loc,this.direction*this.length/2),g=a.pointOnPath(this.loc),i=d.pointOnLine(e,g,this.length);j=d.perpendicularLineTo(e,i,this.width),k=d.pointOnLine(e,i,h*this.length);var p={hxy:e,tail:j,cxy:k},q=f.stroke||b.stroke,r=f.fill||b.stroke,s=f.strokeWidth||b.strokeWidth;return{component:a,d:p,"stroke-width":s,stroke:q,fill:r,minX:Math.min(e.x,j[0].x,j[1].x),maxX:Math.max(e.x,j[0].x,j[1].x),minY:Math.min(e.y,j[0].y,j[1].y),maxY:Math.max(e.y,j[0].y,j[1].y)}}return{component:a,minX:0,maxX:0,minY:0,maxY:0}}},c.extend(b.Overlays.Arrow,g,{updateFrom:function(a){this.length=a.length||this.length,this.width=a.width||this.width,this.direction=null!=a.direction?a.direction:this.direction,this.foldback=a.foldback||this.foldback},cleanup:function(){this.path&&this.path.parentNode&&this.path.parentNode.removeChild(this.path)}}),b.Overlays.PlainArrow=function(a){a=a||{};var c=b.extend(a,{foldback:1});b.Overlays.Arrow.call(this,c),this.type="PlainArrow"},c.extend(b.Overlays.PlainArrow,b.Overlays.Arrow),b.Overlays.Diamond=function(a){a=a||{};var c=a.length||40,d=b.extend(a,{length:c/2,foldback:2});b.Overlays.Arrow.call(this,d),this.type="Diamond"},c.extend(b.Overlays.Diamond,b.Overlays.Arrow);var h=function(a,b){return(null==a._jsPlumb.cachedDimensions||b)&&(a._jsPlumb.cachedDimensions=a.getDimensions()),a._jsPlumb.cachedDimensions},i=function(a){b.jsPlumbUIComponent.apply(this,arguments),g.apply(this,arguments);var d=this.fire;this.fire=function(){d.apply(this,arguments),this.component&&this.component.fire.apply(this.component,arguments)},this.detached=!1,this.id=a.id,this._jsPlumb.div=null,this._jsPlumb.initialised=!1,this._jsPlumb.component=a.component,this._jsPlumb.cachedDimensions=null,this._jsPlumb.create=a.create,this._jsPlumb.initiallyInvisible=a.visible===!1,this.getElement=function(){if(null==this._jsPlumb.div){var c=this._jsPlumb.div=b.getElement(this._jsPlumb.create(this._jsPlumb.component));c.style.position="absolute",jsPlumb.addClass(c,this._jsPlumb.instance.overlayClass+" "+(this.cssClass?this.cssClass:a.cssClass?a.cssClass:"")),this._jsPlumb.instance.appendElement(c),this._jsPlumb.instance.getId(c),this.canvas=c;var d="translate(-50%, -50%)";c.style.webkitTransform=d,c.style.mozTransform=d,c.style.msTransform=d,c.style.oTransform=d,c.style.transform=d,c._jsPlumb=this,a.visible===!1&&(c.style.display="none")}return this._jsPlumb.div},this.draw=function(a,b,d){var e=h(this);if(null!=e&&2===e.length){var f={x:0,y:0};if(d)f={x:d[0],y:d[1]};else if(a.pointOnPath){var g=this.loc,i=!1;(c.isString(this.loc)||this.loc<0||this.loc>1)&&(g=parseInt(this.loc,10),i=!0),f=a.pointOnPath(g,i)}else{var j=this.loc.constructor===Array?this.loc:this.endpointLoc;f={x:j[0]*a.w,y:j[1]*a.h}}var k=f.x-e[0]/2,l=f.y-e[1]/2;return{component:a,d:{minx:k,miny:l,td:e,cxy:f},minX:k,maxX:k+e[0],minY:l,maxY:l+e[1]}}return{minX:0,maxX:0,minY:0,maxY:0}}};c.extend(i,[b.jsPlumbUIComponent,g],{getDimensions:function(){return[1,1]},setVisible:function(a){this._jsPlumb.div&&(this._jsPlumb.div.style.display=a?"block":"none",a&&this._jsPlumb.initiallyInvisible&&(h(this,!0),this.component.repaint(),this._jsPlumb.initiallyInvisible=!1))},clearCachedDimensions:function(){this._jsPlumb.cachedDimensions=null},cleanup:function(a){a?null!=this._jsPlumb.div&&(this._jsPlumb.div._jsPlumb=null,this._jsPlumb.instance.removeElement(this._jsPlumb.div)):(this._jsPlumb&&this._jsPlumb.div&&this._jsPlumb.div.parentNode&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div),this.detached=!0)},reattach:function(a,b){null!=this._jsPlumb.div&&a.getContainer().appendChild(this._jsPlumb.div),this.detached=!1},computeMaxSize:function(){var a=h(this);return Math.max(a[0],a[1])},paint:function(a,b){this._jsPlumb.initialised||(this.getElement(),a.component.appendDisplayElement(this._jsPlumb.div),this._jsPlumb.initialised=!0,this.detached&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div)),this._jsPlumb.div.style.left=a.component.x+a.d.minx+"px",this._jsPlumb.div.style.top=a.component.y+a.d.miny+"px"}}),b.Overlays.Custom=function(a){this.type="Custom",i.apply(this,arguments)},c.extend(b.Overlays.Custom,i),b.Overlays.GuideLines=function(){var a=this;a.length=50,a.strokeWidth=5,this.type="GuideLines",g.apply(this,arguments),b.jsPlumbUIComponent.apply(this,arguments),this.draw=function(b,c){var e=b.pointAlongPathFrom(a.loc,a.length/2),f=b.pointOnPath(a.loc),g=d.pointOnLine(e,f,a.length),h=d.perpendicularLineTo(e,g,40),i=d.perpendicularLineTo(g,e,20);return{connector:b,head:e,tail:g,headLine:i,tailLine:h,minX:Math.min(e.x,g.x,i[0].x,i[1].x),minY:Math.min(e.y,g.y,i[0].y,i[1].y),maxX:Math.max(e.x,g.x,i[0].x,i[1].x),maxY:Math.max(e.y,g.y,i[0].y,i[1].y)}}},b.Overlays.Label=function(a){this.labelStyle=a.labelStyle;this.cssClass=null!=this.labelStyle?this.labelStyle.cssClass:null;var c=b.extend({create:function(){return b.createElement("div")}},a);if(b.Overlays.Custom.call(this,c),this.type="Label",this.label=a.label||"",this.labelText=null,this.labelStyle){var d=this.getElement();if(this.labelStyle.font=this.labelStyle.font||"12px sans-serif",d.style.font=this.labelStyle.font,d.style.color=this.labelStyle.color||"black",this.labelStyle.fill&&(d.style.background=this.labelStyle.fill),this.labelStyle.borderWidth>0){var e=this.labelStyle.borderStyle?this.labelStyle.borderStyle:"black";d.style.border=this.labelStyle.borderWidth+"px solid "+e}this.labelStyle.padding&&(d.style.padding=this.labelStyle.padding)}},c.extend(b.Overlays.Label,b.Overlays.Custom,{cleanup:function(a){a&&(this.div=null,this.label=null,this.labelText=null,this.cssClass=null,this.labelStyle=null)},getLabel:function(){return this.label},setLabel:function(a){this.label=a,this.labelText=null,this.clearCachedDimensions(),this.update(),this.component.repaint()},getDimensions:function(){return this.update(),i.prototype.getDimensions.apply(this,arguments)},update:function(){if("function"==typeof this.label){var a=this.label(this);this.getElement().innerHTML=a.replace(/\r\n/g,"<br/>")}else null==this.labelText&&(this.labelText=this.label,this.getElement().innerHTML=this.labelText.replace(/\r\n/g,"<br/>"))},updateFrom:function(a){null!=a.label&&this.setLabel(a.label)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumbUtil,c=a.jsPlumbInstance,d="jtk-group-collapsed",e="jtk-group-expanded",f="[jtk-group-content]",g="elementDraggable",h="stop",i="revert",j="_groupManager",k="_jsPlumbGroup",l="_jsPlumbGroupDrag",m="group:addMember",n="group:removeMember",o="group:add",p="group:remove",q="group:expand",r="group:collapse",s="groupDragStop",t="connectionMoved",u="internal.connectionDetached",v="removeAll",w="orphanAll",x="show",y="hide",z=function(a){function c(b,c){for(var d=a.getContainer(),e=!1;!e;){if(null==b||b===d)return!1;if(b===c)return!0;b=b.parentNode}}function f(a){delete a.proxies;var c,d=j[a.id];null!=d&&(c=function(b){return b.id===a.id},b.removeWithFunction(d.connections.source,c),b.removeWithFunction(d.connections.target,c),delete j[a.id]),d=l[a.id],null!=d&&(c=function(b){return b.id===a.id},b.removeWithFunction(d.connections.source,c),b.removeWithFunction(d.connections.target,c),delete l[a.id])}function g(b,c){for(var d=b.getEl().querySelectorAll(".jtk-managed"),e=0;e<d.length;e++)a[c?x:y](d[e],!0)}function h(b){for(var c=b.getMembers().slice(),d=[],e=0;e<c.length;e++)Array.prototype.push.apply(d,c[e].querySelectorAll(".jtk-managed"));Array.prototype.push.apply(c,d);var f=a.getConnections({source:c,scope:"*"},!0),g=a.getConnections({target:c,scope:"*"},!0),h={};b.connections.source.length=0,b.connections.target.length=0;var i=function(c){for(var d=0;d<c.length;d++)if(!h[c[d].id]){h[c[d].id]=!0;var e=a.getGroupFor(c[d].source),f=a.getGroupFor(c[d].target);e===b?(f!==b&&b.connections.source.push(c[d]),j[c[d].id]=b):f===b&&(b.connections.target.push(c[d]),l[c[d].id]=b)}};i(f),i(g)}var i={},j={},l={},n=this;a.bind("connection",function(c){var d=a.getGroupFor(c.source),e=a.getGroupFor(c.target);null!=d&&null!=e&&d===e?(j[c.connection.id]=d,l[c.connection.id]=d):(null!=d&&(b.suggest(d.connections.source,c.connection),j[c.connection.id]=d),null!=e&&(b.suggest(e.connections.target,c.connection),l[c.connection.id]=e))}),a.bind(u,function(a){f(a.connection)}),a.bind(t,function(a){var b=0===a.index?j:l,c=b[a.connection.id];if(c){var d=c.connections[0===a.index?"source":"target"],e=d.indexOf(a.connection);-1!==e&&d.splice(e,1)}}),this.addGroup=function(b){a.addClass(b.getEl(),e),i[b.id]=b,b.manager=this,h(b),a.fire(o,{group:b})},this.addToGroup=function(b,c,d){if(b=this.getGroup(b)){var e=b.getEl();if(c._isJsPlumbGroup)return;var f=c._jsPlumbGroup;if(f!==b){a.removeFromDragSelection(c);var g=a.getOffset(c,!0),h=b.collapsed?a.getOffset(e,!0):a.getOffset(b.getDragArea(),!0);null!=f&&(f.remove(c,!1,d,!1,b),n.updateConnectionsForGroup(f)),b.add(c,d);var i=function(a,c){var d=0===c?1:0;a.each(function(a){a.setVisible(!1),a.endpoints[d].element._jsPlumbGroup===b?(a.endpoints[d].setVisible(!1),z(a,d,b)):(a.endpoints[c].setVisible(!1),s(a,c,b))})};b.collapsed&&(i(a.select({source:c}),0),i(a.select({target:c}),1));var j=a.getId(c);a.dragManager.setParent(c,j,e,a.getId(e),g);var k={left:g.left-h.left,top:g.top-h.top};if(a.setPosition(c,k),a.dragManager.revalidateParent(c,j,g),n.updateConnectionsForGroup(b),a.revalidate(j),!d){var l={group:b,el:c,pos:k};f&&(l.sourceGroup=f),a.fire(m,l)}}}},this.removeFromGroup=function(a,b,d){if(a=this.getGroup(a)){if(a.collapsed){var e=function(d,e){for(var f=0;f<d.length;f++){var g=d[f];if(g.proxies)for(var h=0;h<g.proxies.length;h++)if(null!=g.proxies[h]){var i=g.proxies[h].originalEp.element;(i===b||c(i,b))&&z(g,e,a)}}};e(a.connections.source.slice(),0),e(a.connections.target.slice(),1)}a.remove(b,null,d)}},this.getGroup=function(a){var c=a;if(b.isString(a)&&(c=i[a],null==c))throw new TypeError("No such group ["+a+"]");return c},this.getGroups=function(){var a=[];for(var b in i)a.push(i[b]);return a},this.removeGroup=function(b,c,d,e){b=this.getGroup(b),this.expandGroup(b,!0);var f=b[c?v:w](d,e);return a.remove(b.getEl()),delete i[b.id],delete a._groups[b.id],a.fire(p,{group:b}),f},this.removeAllGroups=function(a,b,c){for(var d in i)this.removeGroup(i[d],a,b,c)};var s=function(b,c,d){var e=b.endpoints[0===c?1:0].element;if(!e[k]||e[k].shouldProxy()||!e[k].collapsed){var f=d.getEl(),g=a.getId(f);a.proxyConnection(b,c,f,g,function(a,b){return d.getEndpoint(a,b)},function(a,b){return d.getAnchor(a,b)})}};this.collapseGroup=function(b){if(b=this.getGroup(b),null!=b&&!b.collapsed){var c=b.getEl();if(g(b,!1),b.shouldProxy()){var f=function(a,c){for(var d=0;d<a.length;d++){var e=a[d];s(e,c,b)}};f(b.connections.source,0),f(b.connections.target,1)}b.collapsed=!0,a.removeClass(c,e),a.addClass(c,d),a.revalidate(c),a.fire(r,{group:b})}};var z=function(b,c,d){a.unproxyConnection(b,c,a.getId(d.getEl()))};this.expandGroup=function(b,c){if(b=this.getGroup(b),null!=b&&b.collapsed){var f=b.getEl();if(g(b,!0),b.shouldProxy()){var h=function(a,c){for(var d=0;d<a.length;d++){var e=a[d];z(e,c,b)}};h(b.connections.source,0),h(b.connections.target,1)}b.collapsed=!1,a.addClass(f,e),a.removeClass(f,d),a.revalidate(f),this.repaintGroup(b),c||a.fire(q,{group:b})}},this.repaintGroup=function(b){b=this.getGroup(b);for(var c=b.getMembers(),d=0;d<c.length;d++)a.revalidate(c[d])},this.updateConnectionsForGroup=h,this.refreshAllGroups=function(){for(var b in i)h(i[b]),a.dragManager.updateOffsets(a.getId(i[b].getEl()))}},A=function(c,d){function e(a){return a.offsetParent}function j(a,b){var d=e(a),f=c.getSize(d),g=c.getSize(a),h=b[0],i=h+g[0],j=b[1],k=j+g[1];return i>0&&h<f[0]&&k>0&&j<f[1]}function m(a){var b=c.getId(a),d=c.getOffset(a);return a.parentNode.removeChild(a),c.getContainer().appendChild(a),c.setPosition(a,d),q(a),c.dragManager.clearParent(a,b),[b,d]}function o(a){function b(a,b,d){var e=null;if(!j(a,[b,d])){var f=a._jsPlumbGroup;A?c.remove(a):e=m(a),f.remove(a)}return e}for(var d=[],e=0;e<a.selection.length;e++)d.push(b(a.selection[e][0],a.selection[e][1].left,a.selection[e][1].top));return 1===d.length?d[0]:d}function p(a){var b=c.getId(a);c.revalidate(a),c.dragManager.revalidateParent(a,b)}function q(a){a._katavorioDrag&&((A||z)&&a._katavorioDrag.off(h,o),A||z||!y||(a._katavorioDrag.off(i,p),a._katavorioDrag.setRevert(null)))}function r(a){a._katavorioDrag&&((A||z)&&a._katavorioDrag.on(h,o),x&&a._katavorioDrag.setConstrain(!0),w&&a._katavorioDrag.setUseGhostProxy(!0),A||z||!y||(a._katavorioDrag.on(i,p),a._katavorioDrag.setRevert(function(a,b){return!j(a,b)})))}var t=this,u=d.el;this.getEl=function(){return u},this.id=d.id||b.uuid(),u._isJsPlumbGroup=!0;var v=this.getDragArea=function(){var a=c.getSelector(u,f);return a&&a.length>0?a[0]:u},w=d.ghost===!0,x=w||d.constrain===!0,y=d.revert!==!1,z=d.orphan===!0,A=d.prune===!0,B=d.dropOverride===!0,C=d.proxied!==!1,D=[];if(this.connections={source:[],target:[],internal:[]},this.getAnchor=function(a,b){return d.anchor||"Continuous"},this.getEndpoint=function(a,b){return d.endpoint||["Dot",{radius:10}]},this.collapsed=!1,d.draggable!==!1){var E={drag:function(){for(var a=0;a<D.length;a++)c.draw(D[a])},stop:function(a){c.fire(s,jsPlumb.extend(a,{group:t}))},scope:l};d.dragOptions&&a.jsPlumb.extend(E,d.dragOptions),c.draggable(d.el,E)}d.droppable!==!1&&c.droppable(d.el,{drop:function(a){var b=a.drag.el;if(!b._isJsPlumbGroup){var d=b._jsPlumbGroup;if(d!==t){if(null!=d&&d.overrideDrop(b,t))return;c.getGroupManager().addToGroup(t,b,!1)}}}});var F=function(a,b){for(var c=null==a.nodeType?a:[a],d=0;d<c.length;d++)b(c[d])};this.overrideDrop=function(a,b){return B&&(y||A||z)},this.add=function(a,b){var d=v();F(a,function(a){if(null!=a._jsPlumbGroup){if(a._jsPlumbGroup===t)return;a._jsPlumbGroup.remove(a,!0,b,!1)}a._jsPlumbGroup=t,D.push(a),c.isAlreadyDraggable(a)&&r(a),a.parentNode!==d&&d.appendChild(a)}),c.getGroupManager().updateConnectionsForGroup(t)},this.remove=function(a,d,e,f,g){F(a,function(a){if(a._jsPlumbGroup===t){if(delete a._jsPlumbGroup,b.removeWithFunction(D,function(b){return b===a}),d)try{t.getDragArea().removeChild(a)}catch(f){jsPlumbUtil.log("Could not remove element from Group "+f)}if(q(a),!e){var h={group:t,el:a};g&&(h.targetGroup=g),c.fire(n,h)}}}),f||c.getGroupManager().updateConnectionsForGroup(t)},this.removeAll=function(a,b){for(var d=0,e=D.length;e>d;d++){var f=D[0];t.remove(f,a,b,!0),c.remove(f,!0)}D.length=0,c.getGroupManager().updateConnectionsForGroup(t)},this.orphanAll=function(){for(var a={},b=0;b<D.length;b++){var c=m(D[b]);a[c[0]]=c[1]}return D.length=0,a},this.getMembers=function(){return D},u[k]=this,c.bind(g,function(a){a.el._jsPlumbGroup===this&&r(a.el)}.bind(this)),this.shouldProxy=function(){return C},c.getGroupManager().addGroup(this)};c.prototype.addGroup=function(a){var b=this;if(b._groups=b._groups||{},null!=b._groups[a.id])throw new TypeError("cannot create Group ["+a.id+"]; a Group with that ID exists");if(null!=a.el[k])throw new TypeError("cannot create Group ["+a.id+"]; the given element is already a Group");var c=new A(b,a);return b._groups[c.id]=c,a.collapsed&&this.collapseGroup(c),c},c.prototype.addToGroup=function(a,b,c){var d=function(b){var d=this.getId(b);this.manage(d,b),this.getGroupManager().addToGroup(a,b,c)}.bind(this);if(Array.isArray(b))for(var e=0;e<b.length;e++)d(b[e]);else d(b)},c.prototype.removeFromGroup=function(a,b,c){this.getGroupManager().removeFromGroup(a,b,c),this.getContainer().appendChild(b)},c.prototype.removeGroup=function(a,b,c,d){return this.getGroupManager().removeGroup(a,b,c,d)},c.prototype.removeAllGroups=function(a,b,c){this.getGroupManager().removeAllGroups(a,b,c)},c.prototype.getGroup=function(a){return this.getGroupManager().getGroup(a)},c.prototype.getGroups=function(){return this.getGroupManager().getGroups()},c.prototype.expandGroup=function(a){this.getGroupManager().expandGroup(a)},c.prototype.collapseGroup=function(a){this.getGroupManager().collapseGroup(a)},c.prototype.repaintGroup=function(a){this.getGroupManager().repaintGroup(a)},c.prototype.toggleGroup=function(a){a=this.getGroupManager().getGroup(a),null!=a&&this.getGroupManager()[a.collapsed?"expandGroup":"collapseGroup"](a)},c.prototype.getGroupManager=function(){var a=this[j];return null==a&&(a=this[j]=new z(this)),a},c.prototype.removeGroupManager=function(){delete this[j]},c.prototype.getGroupFor=function(a){if(a=this.getElement(a)){for(var b=this.getContainer(),c=!1,d=null,e=null;!c;)null==a||a===b?c=!0:a[k]?(d=a[k],e=a,c=!0):a=a.parentNode;return d}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d="Straight",e="Arc",f=function(a){this.type="Flowchart",a=a||{},a.stub=null==a.stub?30:a.stub;var c,f,g=b.Connectors.AbstractConnector.apply(this,arguments),h=null==a.midpoint||isNaN(a.midpoint)?.5:a.midpoint,i=a.alwaysRespectStubs===!0,j=null,k=null,l=null!=a.cornerRadius?a.cornerRadius:0,m=(a.loopbackRadius||25,function(a){return 0>a?-1:0===a?0:1}),n=function(a){return[m(a[2]-a[0]),m(a[3]-a[1])]},o=function(a,b,c,d){if(j!==b||k!==c){var e=null==j?d.sx:j,f=null==k?d.sy:k,g=e===b?"v":"h";j=b,k=c,a.push([e,f,b,c,g])}},p=function(a){return Math.sqrt(Math.pow(a[0]-a[2],2)+Math.pow(a[1]-a[3],2))},q=function(a){var b=[];return b.push.apply(b,a),b},r=function(a,b,c){for(var f,h,i,j=null,k=0;k<b.length-1;k++){if(j=j||q(b[k]),f=q(b[k+1]),h=n(j),i=n(f),l>0&&j[4]!==f[4]){var m=Math.min(p(j),p(f)),o=Math.min(l,m/2);j[2]-=h[0]*o,j[3]-=h[1]*o,f[0]+=i[0]*o,f[1]+=i[1]*o;var r=h[1]===i[0]&&1===i[0]||h[1]===i[0]&&0===i[0]&&h[0]!==i[1]||h[1]===i[0]&&-1===i[0],s=f[1]>j[3]?1:-1,t=f[0]>j[2]?1:-1,u=s===t,v=u&&r||!u&&!r?f[0]:j[2],w=u&&r||!u&&!r?j[3]:f[1];g.addSegment(a,d,{x1:j[0],y1:j[1],x2:j[2],y2:j[3]}),g.addSegment(a,e,{r:o,x1:j[2],y1:j[3],x2:f[0],y2:f[1],cx:v,cy:w,ac:r})}else{var x=j[2]===j[0]?0:j[2]>j[0]?c.lw/2:-(c.lw/2),y=j[3]===j[1]?0:j[3]>j[1]?c.lw/2:-(c.lw/2);g.addSegment(a,d,{x1:j[0]-x,y1:j[1]-y,x2:j[2]+x,y2:j[3]+y})}j=f}null!=f&&g.addSegment(a,d,{x1:f[0],y1:f[1],x2:f[2],y2:f[3]})};this.midpoint=h,this._compute=function(a,b){c=[],j=null,k=null,f=null;var d=function(){return[a.startStubX,a.startStubY,a.endStubX,a.endStubY]},e={perpendicular:d,orthogonal:d,opposite:function(b){var c=a,d="x"===b?0:1,e={x:function(){return 1===c.so[d]&&(c.startStubX>c.endStubX&&c.tx>c.startStubX||c.sx>c.endStubX&&c.tx>c.sx)||-1===c.so[d]&&(c.startStubX<c.endStubX&&c.tx<c.startStubX||c.sx<c.endStubX&&c.tx<c.sx)},y:function(){return 1===c.so[d]&&(c.startStubY>c.endStubY&&c.ty>c.startStubY||c.sy>c.endStubY&&c.ty>c.sy)||-1===c.so[d]&&(c.startStubY<c.endStubY&&c.ty<c.startStubY||c.sy<c.endStubY&&c.ty<c.sy)}};return!i&&e[b]()?{x:[(a.sx+a.tx)/2,a.startStubY,(a.sx+a.tx)/2,a.endStubY],y:[a.startStubX,(a.sy+a.ty)/2,a.endStubX,(a.sy+a.ty)/2]}[b]:[a.startStubX,a.startStubY,a.endStubX,a.endStubY]}},l=e[a.anchorOrientation](a.sourceAxis),m="x"===a.sourceAxis?0:1,n="x"===a.sourceAxis?1:0,p=l[m],q=l[n],s=l[m+2],t=l[n+2];o(c,l[0],l[1],a);var u=a.startStubX+(a.endStubX-a.startStubX)*h,v=a.startStubY+(a.endStubY-a.startStubY)*h,w={x:[0,1],y:[1,0]},x={perpendicular:function(b){var c=a,d={x:[[[1,2,3,4],null,[2,1,4,3]],null,[[4,3,2,1],null,[3,4,1,2]]],y:[[[3,2,1,4],null,[2,3,4,1]],null,[[4,1,2,3],null,[1,4,3,2]]]},e={x:[[c.startStubX,c.endStubX],null,[c.endStubX,c.startStubX]],y:[[c.startStubY,c.endStubY],null,[c.endStubY,c.startStubY]]},f={x:[[u,c.startStubY],[u,c.endStubY]],y:[[c.startStubX,v],[c.endStubX,v]]},g={x:[[c.endStubX,c.startStubY]],y:[[c.startStubX,c.endStubY]]},h={x:[[c.startStubX,c.endStubY],[c.endStubX,c.endStubY]],y:[[c.endStubX,c.startStubY],[c.endStubX,c.endStubY]]},i={x:[[c.startStubX,v],[c.endStubX,v],[c.endStubX,c.endStubY]],y:[[u,c.startStubY],[u,c.endStubY],[c.endStubX,c.endStubY]]},j={x:[c.startStubY,c.endStubY],y:[c.startStubX,c.endStubX]},k=w[b][0],l=w[b][1],m=c.so[k]+1,n=c.to[l]+1,o=-1===c.to[l]&&j[b][1]<j[b][0]||1===c.to[l]&&j[b][1]>j[b][0],p=e[b][m][0],q=e[b][m][1],r=d[b][m][n];return c.segment===r[3]||c.segment===r[2]&&o?f[b]:c.segment===r[2]&&p>q?g[b]:c.segment===r[2]&&q>=p||c.segment===r[1]&&!o?i[b]:c.segment===r[0]||c.segment===r[1]&&o?h[b]:void 0},orthogonal:function(b,c,d,e,f){var g=a,h={x:-1===g.so[0]?Math.min(c,e):Math.max(c,e),y:-1===g.so[1]?Math.min(c,e):Math.max(c,e)}[b];return{x:[[h,d],[h,f],[e,f]],y:[[d,h],[f,h],[f,e]]}[b]},opposite:function(c,d,e,f){var h=a,i={x:"y",y:"x"}[c],j={x:"height",y:"width"}[c],k=h["is"+c.toUpperCase()+"GreaterThanStubTimes2"];if(b.sourceEndpoint.elementId===b.targetEndpoint.elementId){var l=e+(1-b.sourceEndpoint.anchor[i])*b.sourceInfo[j]+g.maxStub;return{x:[[d,l],[f,l]],y:[[l,d],[l,f]]}[c]}return!k||1===h.so[m]&&d>f||-1===h.so[m]&&f>d?{x:[[d,v],[f,v]],y:[[u,d],[u,f]]}[c]:1===h.so[m]&&f>d||-1===h.so[m]&&d>f?{x:[[u,h.sy],[u,h.ty]],y:[[h.sx,v],[h.tx,v]]}[c]:void 0}},y=x[a.anchorOrientation](a.sourceAxis,p,q,s,t);if(y)for(var z=0;z<y.length;z++)o(c,y[z][0],y[z][1],a);o(c,l[2],l[3],a),o(c,a.tx,a.ty,a),r(this,c,a)}};b.Connectors.Flowchart=f,c.extend(b.Connectors.Flowchart,b.Connectors.AbstractConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil;b.Connectors.AbstractBezierConnector=function(a){a=a||{};var c,d=a.showLoopback!==!1,e=(a.curviness||10,a.margin||5),f=(a.proximityLimit||80,a.orientation&&"clockwise"===a.orientation),g=a.loopbackRadius||25,h=!1;return this._compute=function(a,b){var i=b.sourcePos,j=b.targetPos,k=Math.abs(i[0]-j[0]),l=Math.abs(i[1]-j[1]);if(d&&b.sourceEndpoint.elementId===b.targetEndpoint.elementId){h=!0;var m=b.sourcePos[0],n=b.sourcePos[1]-e,o=m,p=n-g,q=o-g,r=p-g;k=2*g,l=2*g,a.points[0]=q,a.points[1]=r,a.points[2]=k,a.points[3]=l,c.addSegment(this,"Arc",{loopback:!0,x1:m-q+4,y1:n-r,startAngle:0,endAngle:2*Math.PI,r:g,ac:!f,x2:m-q-4,y2:n-r,cx:o-q,cy:p-r})}else h=!1,this._computeBezier(a,b,i,j,k,l)},c=b.Connectors.AbstractConnector.apply(this,arguments)},c.extend(b.Connectors.AbstractBezierConnector,b.Connectors.AbstractConnector);var d=function(a){a=a||{},this.type="Bezier";var c=b.Connectors.AbstractBezierConnector.apply(this,arguments),d=a.curviness||150,e=10;this.getCurviness=function(){return d},this._findControlPoint=function(a,b,c,f,g,h,i){var j=h[0]!==i[0]||h[1]===i[1],k=[];return j?(0===i[0]?k.push(c[0]<b[0]?a[0]+e:a[0]-e):k.push(a[0]+d*i[0]),0===i[1]?k.push(c[1]<b[1]?a[1]+e:a[1]-e):k.push(a[1]+d*h[1])):(0===h[0]?k.push(b[0]<c[0]?a[0]+e:a[0]-e):k.push(a[0]-d*h[0]),0===h[1]?k.push(b[1]<c[1]?a[1]+e:a[1]-e):k.push(a[1]+d*i[1])),k},this._computeBezier=function(a,b,d,e,f,g){var h,i,j=d[0]<e[0]?f:0,k=d[1]<e[1]?g:0,l=d[0]<e[0]?0:f,m=d[1]<e[1]?0:g;h=this._findControlPoint([j,k],d,e,b.sourceEndpoint,b.targetEndpoint,a.so,a.to),i=this._findControlPoint([l,m],e,d,b.targetEndpoint,b.sourceEndpoint,a.to,a.so),c.addSegment(this,"Bezier",{x1:j,y1:k,x2:l,y2:m,cp1x:h[0],cp1y:h[1],cp2x:i[0],cp2y:i[1]})}};b.Connectors.Bezier=d,c.extend(d,b.Connectors.AbstractBezierConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d=function(a,b,c,d){return c>=a&&b>=d?1:c>=a&&d>=b?2:a>=c&&d>=b?3:4},e=function(a,b,c,d,e,f,g,h,i){return i>=h?[a,b]:1===c?d[3]<=0&&e[3]>=1?[a+(d[2]<.5?-1*f:f),b]:d[2]>=1&&e[2]<=0?[a,b+(d[3]<.5?-1*g:g)]:[a+-1*f,b+-1*g]:2===c?d[3]>=1&&e[3]<=0?[a+(d[2]<.5?-1*f:f),b]:d[2]>=1&&e[2]<=0?[a,b+(d[3]<.5?-1*g:g)]:[a+f,b+-1*g]:3===c?d[3]>=1&&e[3]<=0?[a+(d[2]<.5?-1*f:f),b]:d[2]<=0&&e[2]>=1?[a,b+(d[3]<.5?-1*g:g)]:[a+-1*f,b+-1*g]:4===c?d[3]<=0&&e[3]>=1?[a+(d[2]<.5?-1*f:f),b]:d[2]<=0&&e[2]>=1?[a,b+(d[3]<.5?-1*g:g)]:[a+f,b+-1*g]:void 0},f=function(a){a=a||{},this.type="StateMachine";var c,f=b.Connectors.AbstractBezierConnector.apply(this,arguments),g=a.curviness||10,h=a.margin||5,i=a.proximityLimit||80;a.orientation&&"clockwise"===a.orientation;this._computeBezier=function(a,b,j,k,l,m){var n=b.sourcePos[0]<b.targetPos[0]?0:l,o=b.sourcePos[1]<b.targetPos[1]?0:m,p=b.sourcePos[0]<b.targetPos[0]?l:0,q=b.sourcePos[1]<b.targetPos[1]?m:0;0===b.sourcePos[2]&&(n-=h),1===b.sourcePos[2]&&(n+=h),0===b.sourcePos[3]&&(o-=h),1===b.sourcePos[3]&&(o+=h),0===b.targetPos[2]&&(p-=h),1===b.targetPos[2]&&(p+=h),0===b.targetPos[3]&&(q-=h),1===b.targetPos[3]&&(q+=h);var r,s,t,u,v=(n+p)/2,w=(o+q)/2,x=d(n,o,p,q),y=Math.sqrt(Math.pow(p-n,2)+Math.pow(q-o,2));c=e(v,w,x,b.sourcePos,b.targetPos,g,g,y,i),r=c[0],s=c[0],t=c[1],u=c[1],f.addSegment(this,"Bezier",{x1:p,y1:q,x2:n,y2:o,cp1x:r,cp1y:t,cp2x:s,cp2y:u})}};b.Connectors.StateMachine=f,c.extend(f,b.Connectors.AbstractBezierConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d="Straight",e=function(a){this.type=d;var c=b.Connectors.AbstractConnector.apply(this,arguments);this._compute=function(a,b){c.addSegment(this,d,{x1:a.sx,y1:a.sy,x2:a.startStubX,y2:a.startStubY}),c.addSegment(this,d,{x1:a.startStubX,y1:a.startStubY,x2:a.endStubX,y2:a.endStubY}),c.addSegment(this,d,{x1:a.endStubX,y1:a.endStubY,x2:a.tx,y2:a.ty})}};b.Connectors.Straight=e,c.extend(e,b.Connectors.AbstractConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d={"stroke-linejoin":"stroke-linejoin","stroke-dashoffset":"stroke-dashoffset","stroke-linecap":"stroke-linecap"},e="stroke-dasharray",f="dashstyle",g="linearGradient",h="radialGradient",i="defs",j="fill",k="stop",l="stroke",m="stroke-width",n="style",o="none",p="jsplumb_gradient_",q="strokeWidth",r={
svg:"http://www.w3.org/2000/svg"},s=function(a,b){for(var c in b)a.setAttribute(c,""+b[c])},t=function(a,c){return c=c||{},c.version="1.1",c.xmlns=r.svg,b.createElementNS(r.svg,a,null,null,c)},u=function(a){return"position:absolute;left:"+a[0]+"px;top:"+a[1]+"px"},v=function(a){for(var b=a.querySelectorAll(" defs,linearGradient,radialGradient"),c=0;c<b.length;c++)b[c].parentNode.removeChild(b[c])},w=function(a,b,c,d,e){var f=p+e._jsPlumb.instance.idstamp();v(a);var m;m=c.gradient.offset?t(h,{id:f}):t(g,{id:f,gradientUnits:"userSpaceOnUse"});var n=t(i);a.appendChild(n),n.appendChild(m);for(var o=0;o<c.gradient.stops.length;o++){var q=1===e.segment||2===e.segment?o:c.gradient.stops.length-1-o,r=c.gradient.stops[q][1],s=t(k,{offset:Math.floor(100*c.gradient.stops[o][0])+"%","stop-color":r});m.appendChild(s)}var u=c.stroke?l:j;b.setAttribute(u,"url(#"+f+")")},x=function(a,b,c,g,h){if(b.setAttribute(j,c.fill?c.fill:o),b.setAttribute(l,c.stroke?c.stroke:o),c.gradient?w(a,b,c,g,h):(v(a),b.setAttribute(n,"")),c.strokeWidth&&b.setAttribute(m,c.strokeWidth),c[f]&&c[q]&&!c[e]){var i=-1===c[f].indexOf(",")?" ":",",k=c[f].split(i),p="";k.forEach(function(a){p+=Math.floor(a*c.strokeWidth)+i}),b.setAttribute(e,p)}else c[e]&&b.setAttribute(e,c[e]);for(var r in d)c[r]&&b.setAttribute(d[r],c[r])},y=function(a,b,c){a.childNodes.length>c?a.insertBefore(b,a.childNodes[c]):a.appendChild(b)};c.svg={node:t,attr:s,pos:u};var z=function(a){var d=a.pointerEventsSpec||"all",e={};b.jsPlumbUIComponent.apply(this,a.originalArgs),this.canvas=null,this.path=null,this.svg=null,this.bgCanvas=null;var f=a.cssClass+" "+(a.originalArgs[0].cssClass||""),g={style:"",width:0,height:0,"pointer-events":d,position:"absolute"};this.svg=t("svg",g),a.useDivWrapper?(this.canvas=b.createElement("div",{position:"absolute"}),c.sizeElement(this.canvas,0,0,1,1),this.canvas.className=f):(s(this.svg,{"class":f}),this.canvas=this.svg),a._jsPlumb.appendElement(this.canvas,a.originalArgs[0].parent),a.useDivWrapper&&this.canvas.appendChild(this.svg);var h=[this.canvas];return this.getDisplayElements=function(){return h},this.appendDisplayElement=function(a){h.push(a)},this.paint=function(b,d,f){if(null!=b){var g,h=[this.x,this.y],i=[this.w,this.h];null!=f&&(f.xmin<0&&(h[0]+=f.xmin),f.ymin<0&&(h[1]+=f.ymin),i[0]=f.xmax+(f.xmin<0?-f.xmin:0),i[1]=f.ymax+(f.ymin<0?-f.ymin:0)),a.useDivWrapper?(c.sizeElement(this.canvas,h[0],h[1],i[0]>0?i[0]:1,i[1]>0?i[1]:1),h[0]=0,h[1]=0,g=u([0,0])):g=u([h[0],h[1]]),e.paint.apply(this,arguments),s(this.svg,{style:g,width:i[0]||1,height:i[1]||1})}},{renderer:e}};c.extend(z,b.jsPlumbUIComponent,{cleanup:function(a){a||null==this.typeId?(this.canvas&&(this.canvas._jsPlumb=null),this.svg&&(this.svg._jsPlumb=null),this.bgCanvas&&(this.bgCanvas._jsPlumb=null),this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.svg=null,this.canvas=null,this.path=null,this.group=null,this._jsPlumb=null):(this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.bgCanvas.parentNode.removeChild(this.bgCanvas))},reattach:function(a){var b=a.getContainer();this.canvas&&null==this.canvas.parentNode&&b.appendChild(this.canvas),this.bgCanvas&&null==this.bgCanvas.parentNode&&b.appendChild(this.bgCanvas)},setVisible:function(a){this.canvas&&(this.canvas.style.display=a?"block":"none")}}),b.ConnectorRenderers.svg=function(a){var c=this,d=z.apply(this,[{cssClass:a._jsPlumb.connectorClass,originalArgs:arguments,pointerEventsSpec:"none",_jsPlumb:a._jsPlumb}]);d.renderer.paint=function(d,e,f){var g=c.getSegments(),h="",i=[0,0];if(f.xmin<0&&(i[0]=-f.xmin),f.ymin<0&&(i[1]=-f.ymin),g.length>0){h=c.getPathData();var j={d:h,transform:"translate("+i[0]+","+i[1]+")","pointer-events":a["pointer-events"]||"visibleStroke"},k=null,l=[c.x,c.y,c.w,c.h];if(d.outlineStroke){var m=d.outlineWidth||1,n=d.strokeWidth+2*m;k=b.extend({},d),delete k.gradient,k.stroke=d.outlineStroke,k.strokeWidth=n,null==c.bgPath?(c.bgPath=t("path",j),b.addClass(c.bgPath,b.connectorOutlineClass),y(c.svg,c.bgPath,0)):s(c.bgPath,j),x(c.svg,c.bgPath,k,l,c)}null==c.path?(c.path=t("path",j),y(c.svg,c.path,d.outlineStroke?1:0)):s(c.path,j),x(c.svg,c.path,d,l,c)}}},c.extend(b.ConnectorRenderers.svg,z);var A=b.SvgEndpoint=function(a){var c=z.apply(this,[{cssClass:a._jsPlumb.endpointClass,originalArgs:arguments,pointerEventsSpec:"all",useDivWrapper:!0,_jsPlumb:a._jsPlumb}]);c.renderer.paint=function(a){var c=b.extend({},a);c.outlineStroke&&(c.stroke=c.outlineStroke),null==this.node?(this.node=this.makeNode(c),this.svg.appendChild(this.node)):null!=this.updateNode&&this.updateNode(this.node),x(this.svg,this.node,c,[this.x,this.y,this.w,this.h],this),u(this.node,[this.x,this.y])}.bind(this)};c.extend(A,z),b.Endpoints.svg.Dot=function(){b.Endpoints.Dot.apply(this,arguments),A.apply(this,arguments),this.makeNode=function(a){return t("circle",{cx:this.w/2,cy:this.h/2,r:this.radius})},this.updateNode=function(a){s(a,{cx:this.w/2,cy:this.h/2,r:this.radius})}},c.extend(b.Endpoints.svg.Dot,[b.Endpoints.Dot,A]),b.Endpoints.svg.Rectangle=function(){b.Endpoints.Rectangle.apply(this,arguments),A.apply(this,arguments),this.makeNode=function(a){return t("rect",{width:this.w,height:this.h})},this.updateNode=function(a){s(a,{width:this.w,height:this.h})}},c.extend(b.Endpoints.svg.Rectangle,[b.Endpoints.Rectangle,A]),b.Endpoints.svg.Image=b.Endpoints.Image,b.Endpoints.svg.Blank=b.Endpoints.Blank,b.Overlays.svg.Label=b.Overlays.Label,b.Overlays.svg.Custom=b.Overlays.Custom;var B=function(a,c){a.apply(this,c),b.jsPlumbUIComponent.apply(this,c),this.isAppendedAtTopLevel=!1;this.path=null,this.paint=function(a,b){if(a.component.svg&&b){null==this.path&&(this.path=t("path",{"pointer-events":"all"}),a.component.svg.appendChild(this.path),this.elementCreated&&this.elementCreated(this.path,a.component),this.canvas=a.component.svg);var e=c&&1===c.length?c[0].cssClass||"":"",f=[0,0];b.xmin<0&&(f[0]=-b.xmin),b.ymin<0&&(f[1]=-b.ymin),s(this.path,{d:d(a.d),"class":e,stroke:a.stroke?a.stroke:null,fill:a.fill?a.fill:null,transform:"translate("+f[0]+","+f[1]+")"})}};var d=function(a){return isNaN(a.cxy.x)||isNaN(a.cxy.y)?"":"M"+a.hxy.x+","+a.hxy.y+" L"+a.tail[0].x+","+a.tail[0].y+" L"+a.cxy.x+","+a.cxy.y+" L"+a.tail[1].x+","+a.tail[1].y+" L"+a.hxy.x+","+a.hxy.y};this.transfer=function(a){a.canvas&&this.path&&this.path.parentNode&&(this.path.parentNode.removeChild(this.path),a.canvas.appendChild(this.path))}},C={cleanup:function(a){null!=this.path&&(a?this._jsPlumb.instance.removeElement(this.path):this.path.parentNode&&this.path.parentNode.removeChild(this.path))},reattach:function(a,b){this.path&&b.canvas&&b.canvas.appendChild(this.path)},setVisible:function(a){null!=this.path&&(this.path.style.display=a?"block":"none")}};c.extend(B,[b.jsPlumbUIComponent,b.Overlays.AbstractOverlay]),b.Overlays.svg.Arrow=function(){B.apply(this,[b.Overlays.Arrow,arguments])},c.extend(b.Overlays.svg.Arrow,[b.Overlays.Arrow,B],C),b.Overlays.svg.PlainArrow=function(){B.apply(this,[b.Overlays.PlainArrow,arguments])},c.extend(b.Overlays.svg.PlainArrow,[b.Overlays.PlainArrow,B],C),b.Overlays.svg.Diamond=function(){B.apply(this,[b.Overlays.Diamond,arguments])},c.extend(b.Overlays.svg.Diamond,[b.Overlays.Diamond,B],C),b.Overlays.svg.GuideLines=function(){var a,c,d=null,e=this;b.Overlays.GuideLines.apply(this,arguments),this.paint=function(b,g){null==d&&(d=t("path"),b.connector.svg.appendChild(d),e.attachListeners(d,b.connector),e.attachListeners(d,e),a=t("path"),b.connector.svg.appendChild(a),e.attachListeners(a,b.connector),e.attachListeners(a,e),c=t("path"),b.connector.svg.appendChild(c),e.attachListeners(c,b.connector),e.attachListeners(c,e));var h=[0,0];g.xmin<0&&(h[0]=-g.xmin),g.ymin<0&&(h[1]=-g.ymin),s(d,{d:f(b.head,b.tail),stroke:"red",fill:null,transform:"translate("+h[0]+","+h[1]+")"}),s(a,{d:f(b.tailLine[0],b.tailLine[1]),stroke:"blue",fill:null,transform:"translate("+h[0]+","+h[1]+")"}),s(c,{d:f(b.headLine[0],b.headLine[1]),stroke:"green",fill:null,transform:"translate("+h[0]+","+h[1]+")"})};var f=function(a,b){return"M "+a.x+","+a.y+" L"+b.x+","+b.y}},c.extend(b.Overlays.svg.GuideLines,b.Overlays.GuideLines)}.call("undefined"!=typeof window?window:this),function(){"use strict";var a=this,b=a.jsPlumb,c=a.jsPlumbUtil,d=a.Katavorio,e=a.Biltong,f=function(b){var c=b._mottle;return c||(c=b._mottle=new a.Mottle),c},g=function(a,c){c=c||"main";var f="_katavorio_"+c,g=a[f],h=a.getEventManager();return g||(g=new d({bind:h.on,unbind:h.off,getSize:b.getSize,getConstrainingRectangle:function(a){return[a.parentNode.scrollWidth,a.parentNode.scrollHeight]},getPosition:function(b,c){var d=a.getOffset(b,c,b._katavorioDrag?b.offsetParent:null);return[d.left,d.top]},setPosition:function(a,b){a.style.left=b[0]+"px",a.style.top=b[1]+"px"},addClass:b.addClass,removeClass:b.removeClass,intersects:e.intersects,indexOf:function(a,b){return a.indexOf(b)},scope:a.getDefaultScope(),css:{noSelect:a.dragSelectClass,droppable:"jtk-droppable",draggable:"jtk-draggable",drag:"jtk-drag",selected:"jtk-drag-selected",active:"jtk-drag-active",hover:"jtk-drag-hover",ghostProxy:"jtk-ghost-proxy"}}),g.setZoom(a.getZoom()),a[f]=g,a.bind("zoom",g.setZoom)),g},h=function(a){var b=a.el._jsPlumbDragOptions,c=!0;return b.canDrag&&(c=b.canDrag()),c&&(this.setHoverSuspended(!0),this.select({source:a.el}).addClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:a.el}).addClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),this.setConnectionBeingDragged(!0)),c},i=function(a){var b=this.getUIPosition(arguments,this.getZoom());if(null!=b){var c=a.el._jsPlumbDragOptions;this.draw(a.el,b,null,!0),c._dragging&&this.addClass(a.el,"jtk-dragged"),c._dragging=!0}},j=function(a){for(var b,c=a.selection,d=function(c){var d;null!=c[1]&&(b=this.getUIPosition([{el:c[2].el,pos:[c[1].left,c[1].top]}]),d=this.draw(c[2].el,b)),null!=c[0]._jsPlumbDragOptions&&delete c[0]._jsPlumbDragOptions._dragging,this.removeClass(c[0],"jtk-dragged"),this.select({source:c[2].el}).removeClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:c[2].el}).removeClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),a.e._drawResult=a.e._drawResult||{c:[],e:[],a:[]},Array.prototype.push.apply(a.e._drawResult.c,d.c),Array.prototype.push.apply(a.e._drawResult.e,d.e),Array.prototype.push.apply(a.e._drawResult.a,d.a),this.getDragManager().dragEnded(c[2].el)}.bind(this),e=0;e<c.length;e++)d(c[e]);this.setHoverSuspended(!1),this.setConnectionBeingDragged(!1)},k=function(a,b){var d=function(d){if(null!=b[d]){if(c.isString(b[d])){var e=b[d].match(/-=/)?-1:1,f=b[d].substring(2);return a[d]+e*f}return b[d]}return a[d]};return[d("left"),d("top")]},l=function(a,b){if(null==b)return[0,0];var c=q(b),d=p(c,0);return[d[a+"X"],d[a+"Y"]]},m=l.bind(this,"page"),n=l.bind(this,"screen"),o=l.bind(this,"client"),p=function(a,b){return a.item?a.item(b):a[b]},q=function(a){return a.touches&&a.touches.length>0?a.touches:a.changedTouches&&a.changedTouches.length>0?a.changedTouches:a.targetTouches&&a.targetTouches.length>0?a.targetTouches:[a]},r=function(a){var b={},c=[],d={},e={},f={};this.register=function(g){var h,i=a.getId(g);b[i]||(b[i]=g,c.push(g),d[i]={});var j=function(b){if(b)for(var c=0;c<b.childNodes.length;c++)if(3!==b.childNodes[c].nodeType&&8!==b.childNodes[c].nodeType){var k=jsPlumb.getElement(b.childNodes[c]),l=a.getId(b.childNodes[c],null,!0);if(l&&e[l]&&e[l]>0){h||(h=a.getOffset(g));var m=a.getOffset(k);d[i][l]={id:l,offset:{left:m.left-h.left,top:m.top-h.top}},f[l]=i}j(b.childNodes[c])}};j(g)},this.updateOffsets=function(b,c){if(null!=b){c=c||{};var e,g=jsPlumb.getElement(b),h=a.getId(g),i=d[h];if(i)for(var j in i)if(i.hasOwnProperty(j)){var k=jsPlumb.getElement(j),l=c[j]||a.getOffset(k);if(null==k.offsetParent&&null!=d[h][j])continue;e||(e=a.getOffset(g)),d[h][j]={id:j,offset:{left:l.left-e.left,top:l.top-e.top}},f[j]=h}}},this.endpointAdded=function(c,g){g=g||a.getId(c);var h=document.body,i=c.parentNode;for(e[g]=e[g]?e[g]+1:1;null!=i&&i!==h;){var j=a.getId(i,null,!0);if(j&&b[j]){var k=a.getOffset(i);if(null==d[j][g]){var l=a.getOffset(c);d[j][g]={id:g,offset:{left:l.left-k.left,top:l.top-k.top}},f[g]=j}break}i=i.parentNode}},this.endpointDeleted=function(a){if(e[a.elementId]&&(e[a.elementId]--,e[a.elementId]<=0))for(var b in d)d.hasOwnProperty(b)&&d[b]&&(delete d[b][a.elementId],delete f[a.elementId])},this.changeId=function(a,b){d[b]=d[a],d[a]={},f[b]=f[a],f[a]=null},this.getElementsForDraggable=function(a){return d[a]},this.elementRemoved=function(a){var b=f[a];b&&(d[b]&&delete d[b][a],delete f[a])},this.reset=function(){b={},c=[],d={},e={}},this.dragEnded=function(b){if(null!=b.offsetParent){var c=a.getId(b),d=f[c];d&&this.updateOffsets(d)}},this.setParent=function(b,c,e,g,h){var i=f[c];d[g]||(d[g]={});var j=a.getOffset(e),k=h||a.getOffset(b);i&&d[i]&&delete d[i][c],d[g][c]={id:c,offset:{left:k.left-j.left,top:k.top-j.top}},f[c]=g},this.clearParent=function(a,b){var c=f[b];c&&(delete d[c][b],delete f[b])},this.revalidateParent=function(b,c,d){var e=f[c];if(e){var g={};g[c]=d,this.updateOffsets(e,g),a.revalidate(e)}},this.getDragAncestor=function(b){var c=jsPlumb.getElement(b),d=a.getId(c),e=f[d];return e?jsPlumb.getElement(e):null}},s=function(a,b,d){b=c.fastTrim(b),"undefined"!=typeof a.className.baseVal?a.className.baseVal=b:a.className=b;try{var e=a.classList;if(null!=e){for(;e.length>0;)e.remove(e.item(0));for(var f=0;f<d.length;f++)d[f]&&e.add(d[f])}}catch(g){c.log("JSPLUMB: cannot set class list",g)}},t=function(a){return"undefined"==typeof a.className.baseVal?a.className:a.className.baseVal},u=function(a,b,d){b=null==b?[]:c.isArray(b)?b:b.split(/\s+/),d=null==d?[]:c.isArray(d)?d:d.split(/\s+/);var e=t(a),f=e.split(/\s+/),g=function(a,b){for(var c=0;c<b.length;c++)if(a)-1===f.indexOf(b[c])&&f.push(b[c]);else{var d=f.indexOf(b[c]);-1!==d&&f.splice(d,1)}};g(!0,b),g(!1,d),s(a,f.join(" "),f)};a.jsPlumb.extend(a.jsPlumbInstance.prototype,{headless:!1,pageLocation:m,screenLocation:n,clientLocation:o,getDragManager:function(){return null==this.dragManager&&(this.dragManager=new r(this)),this.dragManager},recalculateOffsets:function(a){this.getDragManager().updateOffsets(a)},createElement:function(a,b,c,d){return this.createElementNS(null,a,b,c,d)},createElementNS:function(a,b,c,d,e){var f,g=null==a?document.createElement(b):document.createElementNS(a,b);c=c||{};for(f in c)g.style[f]=c[f];d&&(g.className=d),e=e||{};for(f in e)g.setAttribute(f,""+e[f]);return g},getAttribute:function(a,b){return null!=a.getAttribute?a.getAttribute(b):null},setAttribute:function(a,b,c){null!=a.setAttribute&&a.setAttribute(b,c)},setAttributes:function(a,b){for(var c in b)b.hasOwnProperty(c)&&a.setAttribute(c,b[c])},appendToRoot:function(a){document.body.appendChild(a)},getRenderModes:function(){return["svg"]},getClass:t,addClass:function(a,b){jsPlumb.each(a,function(a){u(a,b)})},hasClass:function(a,b){return a=jsPlumb.getElement(a),a.classList?a.classList.contains(b):-1!==t(a).indexOf(b)},removeClass:function(a,b){jsPlumb.each(a,function(a){u(a,null,b)})},toggleClass:function(a,b){jsPlumb.hasClass(a,b)?jsPlumb.removeClass(a,b):jsPlumb.addClass(a,b)},updateClasses:function(a,b,c){jsPlumb.each(a,function(a){u(a,b,c)})},setClass:function(a,b){null!=b&&jsPlumb.each(a,function(a){s(a,b,b.split(/\s+/))})},setPosition:function(a,b){a.style.left=b.left+"px",a.style.top=b.top+"px"},getPosition:function(a){var b=function(b){var c=a.style[b];return c?c.substring(0,c.length-2):0};return{left:b("left"),top:b("top")}},getStyle:function(a,b){return"undefined"!=typeof window.getComputedStyle?getComputedStyle(a,null).getPropertyValue(b):a.currentStyle[b]},getSelector:function(a,b){var c=null;return c=1===arguments.length?null!=a.nodeType?a:document.querySelectorAll(a):a.querySelectorAll(b)},getOffset:function(a,b,c){a=jsPlumb.getElement(a),c=c||this.getContainer();for(var d={left:a.offsetLeft,top:a.offsetTop},e=b||null!=c&&a!==c&&a.offsetParent!==c?a.offsetParent:null,f=function(a){null!=a&&a!==document.body&&(a.scrollTop>0||a.scrollLeft>0)&&(d.left-=a.scrollLeft,d.top-=a.scrollTop)}.bind(this);null!=e;)d.left+=e.offsetLeft,d.top+=e.offsetTop,f(e),e=b?e.offsetParent:e.offsetParent===c?null:e.offsetParent;if(null!=c&&!b&&(c.scrollTop>0||c.scrollLeft>0)){var g=null!=a.offsetParent?this.getStyle(a.offsetParent,"position"):"static",h=this.getStyle(a,"position");"absolute"!==h&&"fixed"!==h&&"absolute"!==g&&"fixed"!==g&&(d.left-=c.scrollLeft,d.top-=c.scrollTop)}return d},getPositionOnElement:function(a,b,c){var d="undefined"!=typeof b.getBoundingClientRect?b.getBoundingClientRect():{left:0,top:0,width:0,height:0},e=document.body,f=document.documentElement,g=window.pageYOffset||f.scrollTop||e.scrollTop,h=window.pageXOffset||f.scrollLeft||e.scrollLeft,i=f.clientTop||e.clientTop||0,j=f.clientLeft||e.clientLeft||0,k=0,l=0,m=d.top+g-i+k*c,n=d.left+h-j+l*c,o=jsPlumb.pageLocation(a),p=d.width||b.offsetWidth*c,q=d.height||b.offsetHeight*c,r=(o[0]-n)/p,s=(o[1]-m)/q;return[r,s]},getAbsolutePosition:function(a){var b=function(b){var c=a.style[b];return c?parseFloat(c.substring(0,c.length-2)):void 0};return[b("left"),b("top")]},setAbsolutePosition:function(a,b,c,d){c?this.animate(a,{left:"+="+(b[0]-c[0]),top:"+="+(b[1]-c[1])},d):(a.style.left=b[0]+"px",a.style.top=b[1]+"px")},getSize:function(a){return[a.offsetWidth,a.offsetHeight]},getWidth:function(a){return a.offsetWidth},getHeight:function(a){return a.offsetHeight},getRenderMode:function(){return"svg"},draggable:function(a,b){var d;return a=c.isArray(a)||null!=a.length&&!c.isString(a)?a:[a],Array.prototype.slice.call(a).forEach(function(a){d=this.info(a),d.el&&this._initDraggableIfNecessary(d.el,!0,b,d.id,!0)}.bind(this)),this},snapToGrid:function(a,b,c){var d=[],e=function(a){var e=this.info(a);if(null!=e.el&&e.el._katavorioDrag){var f=e.el._katavorioDrag.snap(b,c);this.revalidate(e.el),d.push([e.el,f])}}.bind(this);if(1===arguments.length||3===arguments.length)e(a,b,c);else{var f=this.getManagedElements();for(var g in f)e(g,arguments[0],arguments[1])}return d},initDraggable:function(a,b,c){g(this,c).draggable(a,b),a._jsPlumbDragOptions=b},destroyDraggable:function(a,b){g(this,b).destroyDraggable(a),a._jsPlumbDragOptions=null,a._jsPlumbRelatedElement=null},unbindDraggable:function(a,b,c,d){g(this,d).destroyDraggable(a,b,c)},setDraggable:function(a,b){return jsPlumb.each(a,function(a){this.isDragSupported(a)&&(this._draggableStates[this.getAttribute(a,"id")]=b,this.setElementDraggable(a,b))}.bind(this))},_draggableStates:{},toggleDraggable:function(a){var b;return jsPlumb.each(a,function(a){var c=this.getAttribute(a,"id");return b=null==this._draggableStates[c]?!1:this._draggableStates[c],b=!b,this._draggableStates[c]=b,this.setDraggable(a,b),b}.bind(this)),b},_initDraggableIfNecessary:function(a,b,d,e,f){if(!jsPlumb.headless){var g=null==b?!1:b;if(g&&jsPlumb.isDragSupported(a,this)){var k=d||this.Defaults.DragOptions;if(k=jsPlumb.extend({},k),jsPlumb.isAlreadyDraggable(a,this))d.force&&this.initDraggable(a,k);else{var l=jsPlumb.dragEvents.drag,m=jsPlumb.dragEvents.stop,n=jsPlumb.dragEvents.start;this.manage(e,a),k[n]=c.wrap(k[n],h.bind(this)),k[l]=c.wrap(k[l],i.bind(this)),k[m]=c.wrap(k[m],j.bind(this));var o=this.getId(a);this._draggableStates[o]=!0;var p=this._draggableStates[o];k.disabled=null==p?!1:!p,this.initDraggable(a,k),this.getDragManager().register(a),f&&this.fire("elementDraggable",{el:a,options:k})}}}},animationSupported:!0,getElement:function(a){return null==a?null:(a="string"==typeof a?a:null==a.tagName&&null!=a.length&&null==a.enctype?a[0]:a,"string"==typeof a?document.getElementById(a):a)},removeElement:function(a){g(this).elementRemoved(a),this.getEventManager().remove(a)},doAnimate:function(a,c,d){d=d||{};var e=this.getOffset(a),f=k(e,c),g=f[0]-e.left,h=f[1]-e.top,i=d.duration||250,j=15,l=i/j,m=j/i*g,n=j/i*h,o=0,p=setInterval(function(){b.setPosition(a,{left:e.left+m*(o+1),top:e.top+n*(o+1)}),null!=d.step&&d.step(o,Math.ceil(l)),o++,o>=l&&(window.clearInterval(p),null!=d.complete&&d.complete())},j)},destroyDroppable:function(a,b){g(this,b).destroyDroppable(a)},unbindDroppable:function(a,b,c,d){g(this,d).destroyDroppable(a,b,c)},droppable:function(a,b){a=c.isArray(a)||null!=a.length&&!c.isString(a)?a:[a];var d;return b=b||{},b.allowLoopback=!1,Array.prototype.slice.call(a).forEach(function(a){d=this.info(a),d.el&&this.initDroppable(d.el,b)}.bind(this)),this},initDroppable:function(a,b,c){g(this,c).droppable(a,b)},isAlreadyDraggable:function(a){return null!=a._katavorioDrag},isDragSupported:function(a,b){return!0},isDropSupported:function(a,b){return!0},isElementDraggable:function(a){return a=b.getElement(a),a._katavorioDrag&&a._katavorioDrag.isEnabled()},getDragObject:function(a){return a[0].drag.getDragElement()},getDragScope:function(a){return a._katavorioDrag&&a._katavorioDrag.scopes.join(" ")||""},getDropEvent:function(a){return a[0].e},getUIPosition:function(a,b){var c=a[0].el;if(null==c.offsetParent)return null;var d=a[0].finalPos||a[0].pos,e={left:d[0],top:d[1]};if(c._katavorioDrag&&c.offsetParent!==this.getContainer()){var f=this.getOffset(c.offsetParent);e.left+=f.left,e.top+=f.top}return e},setDragFilter:function(a,b,c){a._katavorioDrag&&a._katavorioDrag.setFilter(b,c)},setElementDraggable:function(a,c){a=b.getElement(a),a._katavorioDrag&&a._katavorioDrag.setEnabled(c)},setDragScope:function(a,b){a._katavorioDrag&&a._katavorioDrag.k.setDragScope(a,b)},setDropScope:function(a,b){a._katavorioDrop&&a._katavorioDrop.length>0&&a._katavorioDrop[0].k.setDropScope(a,b)},addToPosse:function(a,c){var d=Array.prototype.slice.call(arguments,1),e=g(this);b.each(a,function(a){a=[b.getElement(a)],a.push.apply(a,d),e.addToPosse.apply(e,a)})},setPosse:function(a,c){var d=Array.prototype.slice.call(arguments,1),e=g(this);b.each(a,function(a){a=[b.getElement(a)],a.push.apply(a,d),e.setPosse.apply(e,a)})},removeFromPosse:function(a,c){var d=Array.prototype.slice.call(arguments,1),e=g(this);b.each(a,function(a){a=[b.getElement(a)],a.push.apply(a,d),e.removeFromPosse.apply(e,a)})},removeFromAllPosses:function(a){var c=g(this);b.each(a,function(a){c.removeFromAllPosses(b.getElement(a))})},setPosseState:function(a,c,d){var e=g(this);b.each(a,function(a){e.setPosseState(b.getElement(a),c,d)})},dragEvents:{start:"start",stop:"stop",drag:"drag",step:"step",over:"over",out:"out",drop:"drop",complete:"complete",beforeStart:"beforeStart"},animEvents:{step:"step",complete:"complete"},stopDrag:function(a){a._katavorioDrag&&a._katavorioDrag.abort()},addToDragSelection:function(a){var b=this.getElement(a);null==b||!b._isJsPlumbGroup&&null!=b._jsPlumbGroup||g(this).select(a)},removeFromDragSelection:function(a){g(this).deselect(a)},getDragSelection:function(){return g(this).getSelection()},clearDragSelection:function(){g(this).deselectAll()},trigger:function(a,b,c,d){this.getEventManager().trigger(a,b,c,d)},doReset:function(){for(var a in this)0===a.indexOf("_katavorio_")&&this[a].reset()},getEventManager:function(){return f(this)},on:function(a,b,c){return this.getEventManager().on.apply(this,arguments),this},off:function(a,b,c){return this.getEventManager().off.apply(this,arguments),this}});var v=function(a){var b=function(){/complete|loaded|interactive/.test(document.readyState)&&"undefined"!=typeof document.body&&null!=document.body?a():setTimeout(b,9)};b()};v(b.init)}.call("undefined"!=typeof window?window:this);