<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技感动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
        }
        
        .container {
            width: 100%;
            height: 100%;
            padding: 3%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tech-border-box {
            position: relative;
            width: 100%;
            height: 100%;
            min-width: 80px;
            min-height: 40px;
            background: transparent;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            overflow: hidden;
        }
        
        /* 角落装饰 - 使用vh单位和百分比 */
        .tech-border-box::before,
        .tech-border-box::after {
            content: '';
            position: absolute;
            width: 2vh;
            height: 2vh;
            min-width: 8px;
            min-height: 8px;
            max-width: 20px;
            max-height: 20px;
            border: 2px solid #00ffff;
            z-index: 2;
        }
        
        .tech-border-box::before {
            top: 1vh;
            left: 1vh;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }
        
        .tech-border-box::after {
            bottom: 1vh;
            right: 1vh;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }
        
        /* 扫描线效果 */
        .scan-line {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(0, 255, 255, 0.1) 20%, 
                rgba(0, 255, 255, 0.3) 50%, 
                rgba(0, 255, 255, 0.1) 80%, 
                transparent 100%);
            animation: scan 3s linear infinite;
            z-index: 1;
        }
        
        /* 边框发光脉冲 */
        .glow-border {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px solid #00d4ff;
            border-radius: 8px;
            animation: border-pulse 2s ease-in-out infinite;
            z-index: 0;
        }
        
        /* 数据流线条 - 响应式尺寸 */
        .data-line {
            position: absolute;
            width: 2px;
            height: 3vh;
            min-height: 12px;
            max-height: 30px;
            background: linear-gradient(to bottom, transparent, #00ff88, transparent);
            animation: data-flow 4s linear infinite;
        }
        
        .data-line:nth-child(1) {
            top: 2vh;
            right: 2vh;
            animation-delay: 0s;
        }
        
        .data-line:nth-child(2) {
            bottom: 2vh;
            left: 2vh;
            animation-delay: 2s;
        }
        
        .content {
            background: transparent;
            border: none;
            border-radius: 6px;
            padding: 2%;
            text-align: center;
            color: #00d4ff;
            width: calc(100% - 4vh);
            height: calc(100% - 4vh);
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 3;
        }
        
        h1 {
            font-size: 4vh;
            margin-bottom: 1vh;
            background: linear-gradient(90deg, #00d4ff, #00ffff, #00ff88);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            animation: text-glow 3s ease-in-out infinite alternate;
        }
        
        p {
            color: #66ddff;
            line-height: 1.6;
            font-size: 2.5vh;
            opacity: 1;
            text-shadow: 0 0 8px rgba(102, 221, 255, 0.6);
        }
        
        /* 动画定义 */
        @keyframes corner-glow {
            0% { 
                box-shadow: 0 0 5px #00ffff;
                opacity: 0.7;
            }
            100% { 
                box-shadow: 0 0 15px #00ffff, 0 0 25px #00ffff;
                opacity: 1;
            }
        }
        
        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        @keyframes border-pulse {
            0%, 100% { 
                opacity: 0.5;
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
            }
            50% { 
                opacity: 1;
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
            }
        }
        
        @keyframes data-flow {
            0% { opacity: 0; transform: translateY(-10px); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateY(10px); }
        }
        
        @keyframes text-glow {
            0% { filter: brightness(1); }
            100% { filter: brightness(1.2); }
        }
        
        /* 悬停效果 */
        .tech-border-box:hover {
            box-shadow: 
                0 0 30px rgba(0, 212, 255, 0.5),
                inset 0 0 30px rgba(0, 212, 255, 0.2);
        }
        
        .tech-border-box:hover .scan-line {
            animation-duration: 1.5s;
        }
        
        /* 响应式设计 - 基于高度的断点 */
        @media (max-height: 50px) {
            h1 { font-size: 2vh; margin-bottom: 0.5vh; }
            p { font-size: 1.5vh; }
            .tech-border-box::before, .tech-border-box::after {
                width: 1vh; height: 1vh;
                min-width: 4px; min-height: 4px;
                max-width: 8px; max-height: 8px;
            }
            .data-line { height: 2vh; min-height: 6px; max-height: 12px; }
        }
        
        @media (min-height: 50px) and (max-height: 100px) {
            h1 { font-size: 3vh; }
            p { font-size: 2vh; }
        }
        
        @media (min-height: 100px) and (max-height: 200px) {
            h1 { font-size: 4vh; }
            p { font-size: 2.5vh; }
        }
        
        @media (min-height: 200px) {
            h1 { font-size: 5vh; }
            p { font-size: 3vh; }
        }
        
        /* 极小容器的特殊处理 */
        @media (max-width: 80px) or (max-height: 40px) {
            h1 { font-size: 10px; }
            p { font-size: 8px; }
            .tech-border-box::before, .tech-border-box::after {
                width: 6px; height: 6px;
                border-width: 1px;
            }
            .data-line { width: 1px; height: 8px; }
        }
        
        /* 宽度补充调整 */
        @media (max-width: 100px) {
            .content { padding: 1%; }
            h1, p { letter-spacing: 0.02em; }
        }
        
        @media (min-width: 200px) {
            h1, p { letter-spacing: 0.05em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="tech-border-box">
            <div class="scan-line"></div>
            <div class="glow-border"></div>
            <div class="data-line"></div>
            <div class="data-line"></div>
            
            <div class="content">
                <!-- 内容区域 - 完全透明，可放置任何内容 -->
            </div>
        </div>
    </div>
</body>
</html>
