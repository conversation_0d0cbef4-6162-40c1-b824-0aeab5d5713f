<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期格式化排序修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .sql-comparison {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .sql-before, .sql-after {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        
        .sql-before {
            border-left: 4px solid #dc3545;
        }
        
        .sql-after {
            border-left: 4px solid #28a745;
        }
        
        .issue-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .issue-wrong {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .issue-fixed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .data-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-calendar-date"></i>
                日期格式化排序修复验证
            </h2>
            
            <!-- 问题说明 -->
            <div class="alert alert-danger">
                <h6><i class="bi bi-exclamation-triangle"></i> 问题描述</h6>
                <p class="mb-0">配置日期格式化后，输出的记录变成早期记录而不是最新记录。原因是ORDER BY引用了格式化后的字符串别名，导致按字符串排序而不是时间排序。</p>
            </div>
            
            <!-- 问题分析 -->
            <div class="test-section">
                <h5><i class="bi bi-bug"></i> 问题分析</h5>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-x-circle text-danger"></i> 修复前（错误排序）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>生成的SQL：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                            <span class="issue-badge issue-wrong">ORDER BY引用格式化后的别名</span>
                        </div>
                        <div class="col-md-6">
                            <strong>实际排序结果：</strong>
                            <div class="data-example">12-25 14:30  ← 字符串排序最大
12-24 09:15
07-23 16:45
07-22 11:20
01-15 08:30  ← 实际是最新时间</div>
                            <p class="small text-danger mb-0">❌ 按格式化字符串排序，'12-25'比'07-23'大</p>
                        </div>
                    </div>
                </div>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-check-circle text-success"></i> 修复后（正确排序）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>生成的SQL：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                            <span class="issue-badge issue-fixed">ORDER BY使用原始时间字段</span>
                        </div>
                        <div class="col-md-6">
                            <strong>实际排序结果：</strong>
                            <div class="data-example">01-15 08:30  ← 最新时间
12-25 14:30
12-24 09:15
07-23 16:45
07-22 11:20  ← 最早时间</div>
                            <p class="small text-success mb-0">✅ 按原始时间戳排序，显示最新记录</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修复原理 -->
            <div class="test-section">
                <h5><i class="bi bi-gear"></i> 修复原理</h5>
                <p>修复的核心是确保ORDER BY使用原始时间字段而不是格式化后的别名：</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="sql-comparison">
                            <h6>问题根因</h6>
                            <div class="sql-before">-- SELECT子句中的别名覆盖了原始字段
SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp
-- ORDER BY引用的是格式化后的字符串
ORDER BY timestamp DESC</div>
                            <p class="small text-muted">ORDER BY timestamp 实际引用的是格式化后的字符串</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="sql-comparison">
                            <h6>修复方案</h6>
                            <div class="sql-after">-- 检测到日期格式化配置
if (dateField && dateFormat && timeField === dateField) {
    // ORDER BY使用原始字段名
    return sql + `\nORDER BY ${originalTimeField} DESC\nLIMIT ${limit}`;
}</div>
                            <p class="small text-muted">确保ORDER BY使用原始时间字段进行排序</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修复详情 -->
            <div class="test-section">
                <h5><i class="bi bi-code-slash"></i> 修复详情</h5>
                
                <div class="sql-comparison">
                    <h6>1. 新增方法：getOriginalTimeFieldForOrderBy</h6>
                    <div class="sql-after">getOriginalTimeFieldForOrderBy(timeField) {
    // 检查是否配置了日期格式化
    const dateField = document.getElementById('dateField')?.value;
    const dateFormat = document.getElementById('dateFormat')?.value;
    
    // 如果配置了日期格式化，且时间字段就是被格式化的字段
    if (dateField && dateFormat && timeField === dateField) {
        console.log(`时间字段 ${timeField} 被日期格式化，ORDER BY 使用原始字段名`);
        // 返回原始字段名，确保ORDER BY使用原始时间字段
        return timeField;
    }
    
    return timeField;
}</div>
                </div>
                
                <div class="sql-comparison">
                    <h6>2. 修改简单查询排序逻辑</h6>
                    <div class="sql-after">addOrderByLimitToSimpleQuery(sql, timeField, limit) {
    // 获取用于ORDER BY的原始时间字段名
    const originalTimeField = this.getOriginalTimeFieldForOrderBy(timeField);
    
    // 检查是否需要特殊处理日期格式化的情况
    const dateField = document.getElementById('dateField')?.value;
    const dateFormat = document.getElementById('dateFormat')?.value;
    
    if (dateField && dateFormat && timeField === dateField) {
        // 当时间字段被DATE_FORMAT格式化时，确保ORDER BY使用原始字段
        if (sql.includes(`DATE_FORMAT(${timeField},`)) {
            console.log(`检测到日期格式化，ORDER BY 使用原始字段 ${originalTimeField}`);
            return sql + `\nORDER BY ${originalTimeField} DESC\nLIMIT ${limit}`;
        }
    }
    
    return sql + `\nORDER BY ${originalTimeField} DESC\nLIMIT ${limit}`;
}</div>
                </div>
                
                <div class="sql-comparison">
                    <h6>3. 修改聚合查询排序逻辑</h6>
                    <div class="sql-after">addOrderByLimitToAggregation(sql, timeField, limit) {
    // 获取用于ORDER BY的原始时间字段名
    const originalTimeField = this.getOriginalTimeFieldForOrderBy(timeField);
    
    // 在聚合查询中，使用聚合函数对原始时间字段排序
    const orderByClause = `\nORDER BY MAX(${originalTimeField}) DESC`;
    console.log(`聚合查询ORDER BY使用: MAX(${originalTimeField})`);
    return sql + orderByClause + `\nLIMIT ${limit}`;
}</div>
                </div>
            </div>
            
            <!-- 测试场景 -->
            <div class="test-section">
                <h5><i class="bi bi-list-check"></i> 测试场景</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="sql-comparison">
                            <h6>场景1：简单查询 + 日期格式化</h6>
                            <div class="sql-after">-- 配置：日期字段=timestamp，格式=%m-%d %H:%i
-- 期望：显示格式化日期，但按原始时间排序

SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
ORDER BY timestamp DESC  -- 使用原始timestamp排序
LIMIT 5</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="sql-comparison">
                            <h6>场景2：聚合查询 + 日期格式化</h6>
                            <div class="sql-after">-- 配置：聚合=MAX，日期格式化=timestamp
-- 期望：聚合函数使用原始时间字段

SELECT name, MAX(value) as max_value
FROM data_history
GROUP BY name
ORDER BY MAX(timestamp) DESC  -- 使用原始timestamp
LIMIT 5</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 验证结果 -->
            <div class="test-section">
                <h5><i class="bi bi-check-circle"></i> 验证结果</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="sql-comparison text-center">
                            <h6>排序正确性</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">按原始时间字段排序</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="sql-comparison text-center">
                            <h6>显示格式化</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">保持日期格式化显示</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="sql-comparison text-center">
                            <h6>最新记录</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">获取最新数据记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
