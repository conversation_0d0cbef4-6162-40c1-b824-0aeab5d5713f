-- 添加标准化字段到bi_widgets表（参考report项目）
-- 执行时间：2025-06-15

-- 添加setup字段（样式配置JSON）
ALTER TABLE bi_widgets ADD COLUMN setup TEXT COMMENT '组件的样式配置json';

-- 添加data字段（数据配置JSON）
ALTER TABLE bi_widgets ADD COLUMN data TEXT COMMENT '组件的数据配置json';

-- 添加position字段（位置配置JSON）
ALTER TABLE bi_widgets ADD COLUMN position TEXT COMMENT '组件的位置配置json';

-- 添加options字段（选项配置JSON）
ALTER TABLE bi_widgets ADD COLUMN options TEXT COMMENT '组件的选项配置json';

-- 添加z_index字段（图层顺序）
ALTER TABLE bi_widgets ADD COLUMN z_index INT DEFAULT 1000 COMMENT '图层顺序';

-- 添加refresh_seconds字段（自动刷新间隔）
ALTER TABLE bi_widgets ADD COLUMN refresh_seconds INT COMMENT '自动刷新间隔秒';

-- 添加sort字段（排序）
ALTER TABLE bi_widgets ADD COLUMN sort BIGINT COMMENT '排序，图层的概念';

-- 添加enable_flag字段（启用标志）
ALTER TABLE bi_widgets ADD COLUMN enable_flag INT DEFAULT 1 COMMENT '0--已禁用 1--已启用';

-- 添加delete_flag字段（删除标志）
ALTER TABLE bi_widgets ADD COLUMN delete_flag INT DEFAULT 0 COMMENT '0--未删除 1--已删除';

-- 为新字段创建索引
CREATE INDEX idx_bi_widgets_z_index ON bi_widgets(z_index);
CREATE INDEX idx_bi_widgets_sort ON bi_widgets(sort);
CREATE INDEX idx_bi_widgets_enable_flag ON bi_widgets(enable_flag);
CREATE INDEX idx_bi_widgets_delete_flag ON bi_widgets(delete_flag);

-- 更新现有数据，将旧格式转换为新格式
-- 注意：这个脚本会在应用启动时自动执行
UPDATE bi_widgets SET 
    z_index = 1000 + id,
    sort = id,
    enable_flag = 1,
    delete_flag = 0
WHERE z_index IS NULL;
