# 序列号许可系统规范文档

## 1. 概述

本文档详细描述了SDPLC系统的序列号许可功能实现规范，确保生成器和验证程序的完全一致性。

## 2. 序列号格式规范

### 2.1 序列号结构
```
格式：SDPLC-{Base64加密数据}
示例：SDPLC-A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0
```

### 2.2 原始数据结构（加密前）
```json
{
  "userId": "USER001",
  "startDate": "20250726",
  "endDate": "20250727", 
  "checksum": "ABC123"
}
```

### 2.3 数据字段说明
- **userId**: 用户标识，字符串格式，如"USER001"
- **startDate**: 激活开始日期，格式YYYYMMDD
- **endDate**: 激活结束日期，格式YYYYMMDD
- **checksum**: 校验码，MD5(userId+startDate+endDate)的前6位

## 3. 加密算法规范

### 3.1 AES加密参数
- **算法**: AES/ECB/PKCS5Padding
- **密钥**: "SDPLC2024LICENSE" (15字节固定密钥)
- **编码**: Base64

### 3.2 加密流程
1. 构造JSON字符串
2. 使用固定密钥进行AES加密
3. 对加密结果进行Base64编码
4. 添加"SDPLC-"前缀

### 3.3 解密流程
1. 去除"SDPLC-"前缀
2. Base64解码
3. AES解密得到JSON字符串
4. 解析JSON获取原始数据

## 4. 校验码算法

```java
// 校验码生成
String input = userId + startDate + endDate;
String md5 = MD5(input);
String checksum = md5.substring(0, 6).toUpperCase();
```

## 5. 硬件绑定规范

### 5.1 硬件ID生成
```java
String computerName = System.getProperty("user.name");
String userName = System.getProperty("user.name");
String hardwareId = MD5(computerName + userName).substring(0, 16).toUpperCase();
```

## 6. 许可文件格式

### 6.1 文件位置
- 文件名：`license.json`
- 位置：应用程序根目录

### 6.2 文件内容
```json
{
  "userId": "USER001",
  "serialNumber": "SDPLC-A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6",
  "installTime": "2025-07-26T10:30:00",
  "hardwareId": "HW123456789ABC"
}
```

## 7. 验证流程

### 7.1 启动时验证
1. 检查license.json文件是否存在
2. 读取并解析许可文件
3. 验证硬件ID是否匹配
4. 解密序列号验证有效期
5. 验证通过则允许正常登录，否则跳转激活页面

### 7.2 激活时验证
1. 用户输入用户ID和序列号
2. 解密序列号获取原始数据
3. 验证用户ID是否匹配
4. 验证当前日期是否在有效期内
5. 验证校验码是否正确
6. 生成许可文件并重定向到登录页面

## 8. 错误处理

### 8.1 常见错误类型
- 序列号格式错误：前缀不正确或Base64解码失败
- 解密失败：密钥不匹配或数据损坏
- 用户ID不匹配：输入的用户ID与序列号中的不一致
- 日期超出范围：当前日期不在有效期内
- 校验码错误：数据被篡改
- 硬件不匹配：许可文件与当前硬件不匹配

### 8.2 错误信息
- "序列号格式不正确"
- "序列号已过期"
- "用户ID不匹配"
- "序列号无效"
- "硬件环境已变更，请重新激活"

## 9. 关键常量

```java
public static final String SERIAL_PREFIX = "SDPLC-";
public static final String AES_KEY = "SDPLC2024LICENSE";
public static final String LICENSE_FILE = "license.json";
public static final String DATE_FORMAT = "yyyyMMdd";
public static final String DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
```

## 10. 测试用例

### 10.1 有效序列号示例
- 用户ID: USER001
- 有效期: 2025-07-26 到 2025-07-27
- 预期序列号: 根据加密算法生成

### 10.2 测试场景
1. 正常激活流程
2. 序列号格式错误
3. 用户ID不匹配
4. 日期过期
5. 硬件变更后重新激活

---

**重要提醒**: 生成器和验证程序必须使用完全相同的算法、密钥和格式，任何不一致都会导致验证失败。
