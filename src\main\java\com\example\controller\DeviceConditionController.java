package com.example.controller;

import com.example.model.Device;
import com.example.model.DeviceCondition;
import com.example.model.DataItem;
import com.example.service.DeviceService;
import com.example.service.DeviceConditionService;
import com.example.service.DataItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/conditions")
public class DeviceConditionController {
    
    private final DeviceService deviceService;
    private final DeviceConditionService deviceConditionService;
    private final DataItemService dataItemService;
    
    public DeviceConditionController(
            DeviceService deviceService,
            DeviceConditionService deviceConditionService,
            DataItemService dataItemService) {
        this.deviceService = deviceService;
        this.deviceConditionService = deviceConditionService;
        this.dataItemService = dataItemService;
    }
    
    @PostMapping("/device/{deviceId}")
    public ResponseEntity<?> createCondition(
            @PathVariable String deviceId,
            @RequestBody Map<String, Object> params) {
        try {
            // 获取设备
            Device device = deviceService.getDevice(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));
            
            // 获取源数据项
            String sourceItemId = (String) params.get("sourceItemId");
            DataItem sourceItem = dataItemService.getDataItem(sourceItemId)
                .orElseThrow(() -> new IllegalArgumentException("源数据项不存在"));
            
            // 获取目标数据项
            String targetItemId = (String) params.get("targetItemId");
            DataItem targetItem = dataItemService.getDataItem(targetItemId)
                .orElseThrow(() -> new IllegalArgumentException("目标数据项不存在"));
            
            // 获取其他参数
            String operator = (String) params.get("operator");
            Integer compareValue = (Integer) params.get("compareValue");
            Integer writeValue = (Integer) params.get("writeValue");
            Integer writeInterval = (Integer) params.get("writeInterval");
            String remark = (String) params.get("remark");
            
            // 创建条件
            DeviceCondition condition = deviceConditionService.createCondition(
                device, sourceItem, operator, compareValue, targetItem, writeValue, 
                writeInterval, remark);
            
            return ResponseEntity.ok(condition);
        } catch (Exception e) {
            log.error("创建条件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("创建条件失败", e.getMessage()));
        }
    }
    
    @GetMapping("/device/{deviceId}")
    public ResponseEntity<?> getDeviceConditions(@PathVariable String deviceId) {
        try {
            Device device = deviceService.getDevice(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));
            
            List<DeviceCondition> conditions = deviceConditionService.getDeviceConditions(device);
            return ResponseEntity.ok(conditions);
        } catch (Exception e) {
            log.error("获取设备条件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取设备条件失败", e.getMessage()));
        }
    }
    
    @GetMapping("/{conditionId}")
    public ResponseEntity<?> getCondition(@PathVariable Long conditionId) {
        try {
            DeviceCondition condition = deviceConditionService.getCondition(conditionId)
                .orElseThrow(() -> new IllegalArgumentException("条件不存在"));
            return ResponseEntity.ok(condition);
        } catch (Exception e) {
            log.error("获取条件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取条件失败", e.getMessage()));
        }
    }
    
    @PutMapping("/{conditionId}")
    public ResponseEntity<?> updateCondition(
            @PathVariable Long conditionId,
            @RequestBody Map<String, Object> params) {
        try {
            // 获取源数据项
            String sourceItemId = (String) params.get("sourceItemId");
            DataItem sourceItem = dataItemService.getDataItem(sourceItemId)
                .orElseThrow(() -> new IllegalArgumentException("源数据项不存在"));
            
            // 获取目标数据项
            String targetItemId = (String) params.get("targetItemId");
            DataItem targetItem = dataItemService.getDataItem(targetItemId)
                .orElseThrow(() -> new IllegalArgumentException("目标数据项不存在"));
            
            // 获取其他参数
            String operator = (String) params.get("operator");
            Integer compareValue = (Integer) params.get("compareValue");
            Integer writeValue = (Integer) params.get("writeValue");
            Integer writeInterval = (Integer) params.get("writeInterval");
            String remark = (String) params.get("remark");
            
            // 更新条件
            DeviceCondition condition = deviceConditionService.updateCondition(
                conditionId, sourceItem, operator, compareValue, targetItem, writeValue, 
                writeInterval, remark);
            
            return ResponseEntity.ok(condition);
        } catch (Exception e) {
            log.error("更新条件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("更新条件失败", e.getMessage()));
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCondition(@PathVariable Long id) {
        try {
            deviceConditionService.deleteCondition(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除条件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("删除条件失败", e.getMessage()));
        }
    }
    
    @PutMapping("/{id}/toggle")
    public ResponseEntity<?> toggleCondition(
            @PathVariable Long id,
            @RequestBody Map<String, Boolean> params) {
        try {
            Boolean enabled = params.get("enabled");
            if (enabled == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "缺少enabled参数"));
            }
            
            deviceConditionService.toggleCondition(id, enabled);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("切换条件状态失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("切换条件状态失败", e.getMessage()));
        }
    }
}

class ErrorResponse {
    private final String error;
    private final String message;
    
    public ErrorResponse(String error, String message) {
        this.error = error;
        this.message = message;
    }
    
    public String getError() {
        return error;
    }
    
    public String getMessage() {
        return message;
    }
} 