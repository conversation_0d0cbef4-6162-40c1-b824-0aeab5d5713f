<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完全响应式动态按钮</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .button-container {
            width: 100%;
            height: 100%;
            padding: 3%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dynamic-button {
            position: relative;
            width: 100%;
            height: 100%;
            font-size: 4vh;  /* 基于容器高度的字体大小 */
            font-weight: bold;
            color: #00d4ff;
            background: transparent;
            border: 0.1em solid #00d4ff;
            border-radius: 0.3em;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;  /* 最小高度保证可见性 */
            min-width: 80px;   /* 最小宽度保证可见性 */
        }
        
        /* 背景发光效果 */
        .dynamic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(0, 212, 255, 0.2), 
                transparent);
            transition: left 0.5s;
        }
        
        /* 边框发光动画 */
        .dynamic-button::after {
            content: '';
            position: absolute;
            top: -0.1em;
            left: -0.1em;
            right: -0.1em;
            bottom: -0.1em;
            background: linear-gradient(45deg, 
                #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff);
            background-size: 400%;
            border-radius: 0.4em;
            z-index: -1;
            opacity: 0;
            animation: border-flow 3s linear infinite;
            transition: opacity 0.3s;
        }
        
        /* 悬停效果 */
        .dynamic-button:hover::before {
            left: 100%;
        }
        
        .dynamic-button:hover::after {
            opacity: 1;
        }
        
        .dynamic-button:hover {
            color: #ffffff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 
                0 0 1em rgba(0, 212, 255, 0.4),
                0 0 2em rgba(0, 212, 255, 0.2);
            transform: translateY(-0.05em);
            text-shadow: 
                0 0 0.3em rgba(0, 212, 255, 0.8),
                0 0 0.6em rgba(0, 212, 255, 0.6),
                0 0 1em rgba(0, 212, 255, 0.4);
            border-color: #66ffff;
        }
        
        /* 点击效果 */
        .dynamic-button:active {
            transform: translateY(0);
            box-shadow: 
                0 0 0.5em rgba(0, 212, 255, 0.6),
                0 0 1em rgba(0, 212, 255, 0.3);
        }
        
        /* 边框流动动画 */
        @keyframes border-flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 按钮内部装饰 */
        .button-decoration {
            position: absolute;
            width: 1vh;
            height: 1vh;
            background: #00ffff;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            min-width: 4px;
            min-height: 4px;
            max-width: 12px;
            max-height: 12px;
        }
        
        .decoration-1 {
            top: 1vh;
            left: 1vh;
            animation: dot-pulse 2s ease-in-out infinite;
        }
        
        .decoration-2 {
            top: 1vh;
            right: 1vh;
            animation: dot-pulse 2s ease-in-out infinite 0.5s;
        }
        
        .decoration-3 {
            bottom: 1vh;
            left: 1vh;
            animation: dot-pulse 2s ease-in-out infinite 1s;
        }
        
        .decoration-4 {
            bottom: 1vh;
            right: 1vh;
            animation: dot-pulse 2s ease-in-out infinite 1.5s;
        }
        
        .dynamic-button:hover .button-decoration {
            opacity: 1;
        }
        
        @keyframes dot-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* 按钮文字 */
        .button-text {
            position: relative;
            z-index: 2;
            pointer-events: none;
            font-size: inherit;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 响应式字体大小调整 */
        @media (max-height: 50px) {
            .dynamic-button {
                font-size: 2vh;
                border-width: 0.05em;
            }
            
            .decoration-1, .decoration-2 { top: 0.5vh; }
            .decoration-3, .decoration-4 { bottom: 0.5vh; }
            .decoration-1, .decoration-3 { left: 0.5vh; }
            .decoration-2, .decoration-4 { right: 0.5vh; }
        }
        
        @media (min-height: 50px) and (max-height: 100px) {
            .dynamic-button {
                font-size: 3vh;
                border-width: 0.08em;
            }
        }
        
        @media (min-height: 100px) and (max-height: 200px) {
            .dynamic-button {
                font-size: 4vh;
                border-width: 0.1em;
            }
        }
        
        @media (min-height: 200px) {
            .dynamic-button {
                font-size: 5vh;
                border-width: 0.12em;
            }
        }
        
        /* 宽度响应式调整 */
        @media (max-width: 100px) {
            .dynamic-button {
                letter-spacing: 0.02em;
            }
            
            .button-text {
                font-size: 0.8em;
            }
        }
        
        @media (min-width: 100px) and (max-width: 200px) {
            .dynamic-button {
                letter-spacing: 0.03em;
            }
        }
        
        @media (min-width: 200px) {
            .dynamic-button {
                letter-spacing: 0.05em;
            }
        }
        
        /* 确保在极小容器中仍然可见 */
        @media (max-width: 80px) or (max-height: 40px) {
            .dynamic-button {
                font-size: 10px;
                border-width: 1px;
                letter-spacing: 0;
            }
            
            .button-decoration {
                width: 3px;
                height: 3px;
            }
            
            .decoration-1, .decoration-2 { top: 3px; }
            .decoration-3, .decoration-4 { bottom: 3px; }
            .decoration-1, .decoration-3 { left: 3px; }
            .decoration-2, .decoration-4 { right: 3px; }
        }
    </style>
</head>
<body>
    <div class="button-container">
        <button class="dynamic-button">
            <span class="button-decoration decoration-1"></span>
            <span class="button-decoration decoration-2"></span>
            <span class="button-decoration decoration-3"></span>
            <span class="button-decoration decoration-4"></span>
            <span class="button-text">点击我</span>
        </button>
    </div>
</body>
</html>
