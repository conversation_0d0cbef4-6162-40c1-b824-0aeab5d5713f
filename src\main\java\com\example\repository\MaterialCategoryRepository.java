package com.example.repository;

import com.example.entity.MaterialCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialCategoryRepository extends JpaRepository<MaterialCategory, Long> {
    
    /**
     * 根据类型查找分类，按排序顺序排列
     */
    List<MaterialCategory> findByTypeOrderBySortOrderAsc(MaterialCategory.MaterialType type);
    
    /**
     * 查找所有分类，按类型和排序顺序排列
     */
    @Query("SELECT mc FROM MaterialCategory mc ORDER BY mc.type, mc.sortOrder ASC")
    List<MaterialCategory> findAllOrderByTypeAndSortOrder();
    
    /**
     * 根据名称和类型查找分类（用于检查重复）
     */
    boolean existsByNameAndType(String name, MaterialCategory.MaterialType type);
    
    /**
     * 统计指定类型的分类数量
     */
    long countByType(MaterialCategory.MaterialType type);
    
    /**
     * 查找指定类型中排序顺序最大的分类
     */
    @Query("SELECT MAX(mc.sortOrder) FROM MaterialCategory mc WHERE mc.type = :type")
    Integer findMaxSortOrderByType(@Param("type") MaterialCategory.MaterialType type);
}
