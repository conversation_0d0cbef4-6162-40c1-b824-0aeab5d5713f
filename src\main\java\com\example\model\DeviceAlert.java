package com.example.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "device_alerts")
@Data
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DeviceAlert {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "device_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private Device device;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "data_item_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private DataItem dataItem;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "normal_min")
    private Integer normalMin;
    
    @Column(name = "normal_max")
    private Integer normalMax;
    
    @Column(name = "warning_min")
    private Integer warningMin;
    
    @Column(name = "warning_max")
    private Integer warningMax;
    
    @Column(name = "danger_min")
    private Integer dangerMin;
    
    @Column(name = "danger_max")
    private Integer dangerMax;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "remark", length = 500)
    private String remark;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
} 