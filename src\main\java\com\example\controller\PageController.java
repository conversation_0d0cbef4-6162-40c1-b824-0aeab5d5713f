package com.example.controller;

import com.example.model.Device;
import com.example.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
public class PageController {
    
    @Autowired
    private DeviceService deviceService;
    
    @GetMapping("/device/{deviceId}/config")
    public String deviceConfig(@PathVariable String deviceId, Model model) {
        Device device = deviceService.getDevice(deviceId)
            .orElseThrow(() -> new IllegalArgumentException("设备不存在: " + deviceId));
            
        model.addAttribute("deviceId", deviceId);
        model.addAttribute("device", device);
        
        return "device/config";
    }
    
    @GetMapping("/device/{deviceId}/logs")
    public String deviceLogs(@PathVariable String deviceId, Model model) {
        Device device = deviceService.getDevice(deviceId)
            .orElseThrow(() -> new IllegalArgumentException("设备不存在: " + deviceId));
            
        model.addAttribute("deviceId", deviceId);
        model.addAttribute("device", device);
        
        return "device/logs";
    }
} 