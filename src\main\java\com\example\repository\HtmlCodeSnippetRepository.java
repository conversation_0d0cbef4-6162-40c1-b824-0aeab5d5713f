package com.example.repository;

import com.example.entity.HtmlCodeSnippet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * HTML代码片段数据访问接口
 */
@Repository
public interface HtmlCodeSnippetRepository extends JpaRepository<HtmlCodeSnippet, Long> {
    
    /**
     * 按创建时间倒序查询所有代码片段
     */
    List<HtmlCodeSnippet> findAllByOrderByCreatedAtDesc();
    
    /**
     * 根据分类ID查询代码片段，按排序顺序排列
     */
    List<HtmlCodeSnippet> findByCategoryIdOrderBySortOrderAsc(Long categoryId);
    

    
    /**
     * 根据标题或标签搜索代码片段
     */
    @Query("SELECT s FROM HtmlCodeSnippet s WHERE " +
           "s.title LIKE %:keyword% OR " +
           "s.description LIKE %:keyword% OR " +
           "s.tags LIKE %:keyword% " +
           "ORDER BY s.createdAt DESC")
    List<HtmlCodeSnippet> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * 获取指定分类下的最大排序顺序
     */
    @Query("SELECT MAX(s.sortOrder) FROM HtmlCodeSnippet s WHERE s.category.id = :categoryId")
    Integer findMaxSortOrderByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 统计分类下的代码片段数量
     */
    long countByCategoryId(Long categoryId);
}
