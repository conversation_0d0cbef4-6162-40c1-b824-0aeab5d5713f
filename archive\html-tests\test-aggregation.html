<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聚合功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .filter-condition {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
        }
        .filter-condition-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .filter-condition-content {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }
        .logic-operator {
            text-align: center;
            font-weight: bold;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .sql-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            min-height: 150px;
            font-size: 0.9em;
        }
        .test-case {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
        }
        .test-result {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>聚合功能测试</h2>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试用例</h5>
                    </div>
                    <div class="card-body">
                        <div class="test-case">
                            <h6>测试1: 基础聚合 - 获取每个产品的最大销量</h6>
                            <button class="btn btn-primary btn-sm" onclick="runTest1()">运行测试</button>
                            <div id="test1Result" class="test-result" style="display: none;"></div>
                        </div>
                        
                        <div class="test-case">
                            <h6>测试2: 时间聚合 - 获取今天每个产品的最大销量（最新记录）</h6>
                            <button class="btn btn-primary btn-sm" onclick="runTest2()">运行测试</button>
                            <div id="test2Result" class="test-result" style="display: none;"></div>
                        </div>
                        
                        <div class="test-case">
                            <h6>测试3: 复合条件 - 获取本周每个产品的最大销量（最新记录）</h6>
                            <button class="btn btn-primary btn-sm" onclick="runTest3()">运行测试</button>
                            <div id="test3Result" class="test-result" style="display: none;"></div>
                        </div>
                        
                        <div class="test-case">
                            <h6>测试4: 不同聚合类型 - 获取每个产品的平均销量</h6>
                            <button class="btn btn-primary btn-sm" onclick="runTest4()">运行测试</button>
                            <div id="test4Result" class="test-result" style="display: none;"></div>
                        </div>

                        <div class="test-case">
                            <h6>测试5: 验证时间字段不输出 - 只输出标签和数值</h6>
                            <button class="btn btn-primary btn-sm" onclick="runTest5()">运行测试</button>
                            <div id="test5Result" class="test-result" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>实时SQL生成器</h5>
                    </div>
                    <div class="card-body">
                        <!-- 字段配置区域 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">字段配置</label>
                            <div id="fieldConfigList">
                                <!-- 字段列表将动态加载 -->
                            </div>
                        </div>
                        
                        <!-- 筛选条件构建器 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">筛选条件</label>
                            <div class="filter-builder">
                                <div class="filter-conditions" id="filterConditions">
                                    <!-- 筛选条件将动态加载 -->
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addFilterBtn">
                                    <i class="bi bi-plus"></i> 添加条件
                                </button>
                            </div>
                        </div>
                        
                        <!-- 聚合配置 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="aggregation-config">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAggregation">
                                    <label class="form-check-label" for="enableAggregation">
                                        启用聚合查询
                                    </label>
                                </div>
                                <div id="aggregationOptions" style="display: none;">
                                    <div class="mb-2">
                                        <label for="aggregationType" class="form-label form-label-sm">聚合类型</label>
                                        <select class="form-select form-select-sm" id="aggregationType">
                                            <option value="MAX">最大值</option>
                                            <option value="MIN">最小值</option>
                                            <option value="AVG">平均值</option>
                                            <option value="SUM">求和</option>
                                            <option value="COUNT">计数</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="timeField" class="form-label form-label-sm">时间字段</label>
                                        <select class="form-select form-select-sm" id="timeField">
                                            <option value="">选择时间字段（用于排序）</option>
                                            <!-- 时间字段选项将动态加载 -->
                                        </select>
                                        <div class="form-text small">当有多个相同聚合值时，按此字段排序取最新记录</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm" id="refreshSqlBtn">
                                <i class="bi bi-arrow-clockwise"></i> 重新生成SQL
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" id="validateSqlBtn">
                                <i class="bi bi-check-circle"></i> 验证SQL
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="resetFieldsBtn">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置字段
                            </button>
                        </div>
                        
                        <!-- SQL输出 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">生成的SQL</label>
                            <div class="sql-output" id="customSql">
                                <!-- SQL将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="src/main/resources/static/js/dataset-field-configurator.js"></script>
    <script>
        // 模拟测试数据
        const mockTable = {
            name: 'sales_data',
            comment: '销售数据表'
        };
        
        const mockFields = [
            { name: 'id', type: 'INT', size: 11, remarks: '主键ID' },
            { name: 'product_name', type: 'VARCHAR', size: 100, remarks: '产品名称' },
            { name: 'sales_amount', type: 'DECIMAL', size: '10,2', remarks: '销售金额' },
            { name: 'sales_quantity', type: 'INT', size: 11, remarks: '销售数量' },
            { name: 'sale_date', type: 'DATE', remarks: '销售日期' },
            { name: 'created_time', type: 'DATETIME', remarks: '创建时间' },
            { name: 'updated_time', type: 'TIMESTAMP', remarks: '更新时间' },
            { name: 'region', type: 'VARCHAR', size: 50, remarks: '销售区域' },
            { name: 'salesperson', type: 'VARCHAR', size: 50, remarks: '销售员' }
        ];
        
        let fieldConfigurator;
        
        // 初始化字段配置器
        document.addEventListener('DOMContentLoaded', function() {
            fieldConfigurator = new DataSetFieldConfigurator();
            fieldConfigurator.init();
            fieldConfigurator.setTableAndFields(mockTable, mockFields);
            
            console.log('聚合功能测试页面已加载');
            console.log('可用字段:', mockFields);
        });
        
        // 测试用例函数
        function runTest1() {
            // 基础聚合测试
            resetConfiguration();
            
            // 设置字段角色
            fieldConfigurator.fieldRoles = {
                'product_name': 'label',
                'sales_amount': 'value'
            };
            
            // 启用聚合
            fieldConfigurator.aggregationConfig = {
                enabled: true,
                type: 'MAX',
                timeField: '',
                groupByLabel: true
            };
            
            fieldConfigurator.generateSQL();
            const sql = document.getElementById('customSql').textContent;
            
            showTestResult('test1Result', '基础聚合SQL', sql);
        }
        
        function runTest2() {
            // 时间聚合测试
            resetConfiguration();
            
            // 设置字段角色
            fieldConfigurator.fieldRoles = {
                'product_name': 'label',
                'sales_amount': 'value'
            };
            
            // 添加时间筛选条件
            fieldConfigurator.filterConditions = [{
                id: Date.now(),
                field: 'sale_date',
                operator: 'TODAY',
                value: '',
                logicOperator: null
            }];
            
            // 启用聚合
            fieldConfigurator.aggregationConfig = {
                enabled: true,
                type: 'MAX',
                timeField: 'created_time',
                groupByLabel: true
            };
            
            fieldConfigurator.generateSQL();
            const sql = document.getElementById('customSql').textContent;
            
            showTestResult('test2Result', '时间聚合SQL（今天+最新记录）', sql);
        }
        
        function runTest3() {
            // 复合条件测试
            resetConfiguration();
            
            // 设置字段角色
            fieldConfigurator.fieldRoles = {
                'product_name': 'label',
                'sales_amount': 'value'
            };
            
            // 添加时间筛选条件
            fieldConfigurator.filterConditions = [{
                id: Date.now(),
                field: 'sale_date',
                operator: 'THIS_WEEK',
                value: '',
                logicOperator: null
            }];
            
            // 启用聚合
            fieldConfigurator.aggregationConfig = {
                enabled: true,
                type: 'MAX',
                timeField: 'created_time',
                groupByLabel: true
            };
            
            fieldConfigurator.generateSQL();
            const sql = document.getElementById('customSql').textContent;
            
            showTestResult('test3Result', '复合条件SQL（本周+最新记录）', sql);
        }
        
        function runTest4() {
            // 不同聚合类型测试
            resetConfiguration();

            // 设置字段角色
            fieldConfigurator.fieldRoles = {
                'product_name': 'label',
                'sales_amount': 'value'
            };

            // 启用聚合
            fieldConfigurator.aggregationConfig = {
                enabled: true,
                type: 'AVG',
                timeField: '',
                groupByLabel: true
            };

            fieldConfigurator.generateSQL();
            const sql = document.getElementById('customSql').textContent;

            showTestResult('test4Result', '平均值聚合SQL', sql);
        }

        function runTest5() {
            // 验证时间字段不输出测试
            resetConfiguration();

            // 设置字段角色
            fieldConfigurator.fieldRoles = {
                'product_name': 'label',
                'sales_amount': 'value'
            };

            // 添加时间筛选条件
            fieldConfigurator.filterConditions = [{
                id: Date.now(),
                field: 'sale_date',
                operator: 'TODAY',
                value: '',
                logicOperator: null
            }];

            // 启用聚合
            fieldConfigurator.aggregationConfig = {
                enabled: true,
                type: 'MAX',
                timeField: 'created_time',
                groupByLabel: true
            };

            fieldConfigurator.generateSQL();
            const sql = document.getElementById('customSql').textContent;

            // 验证SQL中只包含标签和数值字段，不包含时间字段
            const hasTimeFieldInSelect = sql.includes('SELECT product_name, sales_amount, created_time');
            const hasCorrectSelect = sql.includes('SELECT product_name, sales_amount\n');

            let resultText = `验证时间字段不输出SQL:\n${sql}\n\n`;
            resultText += `验证结果:\n`;
            resultText += `- 外层SELECT包含时间字段: ${hasTimeFieldInSelect ? '❌ 错误' : '✅ 正确'}\n`;
            resultText += `- 外层SELECT只包含标签和数值: ${hasCorrectSelect ? '✅ 正确' : '❌ 错误'}\n`;
            resultText += `- 内层仍使用时间字段排序: ${sql.includes('ORDER BY sales_amount DESC, created_time DESC') ? '✅ 正确' : '❌ 错误'}`;

            showTestResult('test5Result', '时间字段输出验证', resultText);
        }
        
        function resetConfiguration() {
            fieldConfigurator.fieldRoles = {};
            fieldConfigurator.filterConditions = [];
            fieldConfigurator.aggregationConfig = {
                enabled: false,
                type: 'MAX',
                timeField: '',
                groupByLabel: true
            };
        }
        
        function showTestResult(elementId, title, sql) {
            const resultElement = document.getElementById(elementId);
            resultElement.innerHTML = `<strong>${title}:</strong><br><pre>${sql}</pre>`;
            resultElement.style.display = 'block';
        }
    </script>
</body>
</html>
