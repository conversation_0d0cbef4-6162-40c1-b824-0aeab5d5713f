import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试许可解析功能
 */
public class TestLicenseParser {
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String LICENSE_FILE = "C:\\ProgramData\\SHENGDA-PLC\\license.json";
    
    public static void main(String[] args) {
        System.out.println("=== 许可文件解析测试 ===\n");
        
        try {
            // 检查许可文件是否存在
            File licenseFile = new File(LICENSE_FILE);
            if (!licenseFile.exists()) {
                System.out.println("许可文件不存在: " + LICENSE_FILE);
                return;
            }
            
            // 读取加密的许可文件
            String encryptedContent = new String(Files.readAllBytes(licenseFile.toPath()), 
                                               StandardCharsets.UTF_8);
            
            System.out.println("许可文件路径: " + LICENSE_FILE);
            System.out.println("加密内容长度: " + encryptedContent.length() + " 字符");
            System.out.println("加密内容预览: " + encryptedContent.substring(0, Math.min(50, encryptedContent.length())) + "...\n");
            
            // 解密许可证内容
            String licenseJson = decryptLicenseFile(encryptedContent);
            System.out.println("解密后的JSON: " + licenseJson + "\n");
            
            // 解析JSON
            Map<String, String> license = parseJsonManually(licenseJson);
            
            // 显示解析结果
            displayLicenseInfo(license);
            
        } catch (Exception e) {
            System.out.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void displayLicenseInfo(Map<String, String> license) {
        System.out.println("=== 许可文件信息 ===");
        
        // 基本信息
        System.out.println("用户ID: " + license.getOrDefault("userId", "未知"));
        System.out.println("安装时间: " + license.getOrDefault("installTime", "未知"));
        System.out.println("硬件ID: " + license.getOrDefault("hardwareId", "未知"));
        
        // 许可期限信息
        String licenseStartDate = license.get("licenseStartDate");
        String licenseEndDate = license.get("licenseEndDate");
        
        System.out.println("\n=== 许可使用期限 ===");
        if (licenseStartDate != null && licenseEndDate != null) {
            System.out.println("开始日期: " + formatDate(licenseStartDate));
            System.out.println("结束日期: " + formatDate(licenseEndDate));
            
            // 计算剩余天数
            try {
                LocalDate today = LocalDate.now();
                LocalDate endDate = LocalDate.parse(licenseEndDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                long daysRemaining = java.time.temporal.ChronoUnit.DAYS.between(today, endDate);
                
                if (daysRemaining > 0) {
                    System.out.println("剩余天数: " + daysRemaining + " 天");
                    System.out.println("状态: ✅ 有效");
                } else if (daysRemaining == 0) {
                    System.out.println("状态: ⚠️ 今天到期");
                } else {
                    System.out.println("状态: ❌ 已过期 (" + (-daysRemaining) + " 天前)");
                }
            } catch (Exception e) {
                System.out.println("状态: ❓ 无法计算");
            }
        } else {
            System.out.println("⚠️ 未找到许可期限信息 (可能是旧格式许可文件)");
        }
        
        // 硬件绑定验证
        System.out.println("\n=== 硬件绑定验证 ===");
        try {
            String currentHardwareId = generateHardwareId();
            String fileHardwareId = license.getOrDefault("hardwareId", "");
            
            System.out.println("当前硬件ID: " + currentHardwareId);
            System.out.println("许可硬件ID: " + fileHardwareId);
            
            if (currentHardwareId.equals(fileHardwareId)) {
                System.out.println("硬件绑定: ✅ 匹配");
            } else {
                System.out.println("硬件绑定: ❌ 不匹配");
            }
        } catch (Exception e) {
            System.out.println("硬件绑定: ❓ 验证失败");
        }
        
        // 序列号信息
        String serialNumber = license.get("serialNumber");
        if (serialNumber != null) {
            System.out.println("\n=== 序列号信息 ===");
            System.out.println("序列号: " + serialNumber);
            
            try {
                Map<String, String> serialData = parseSerialNumber(serialNumber);
                System.out.println("序列号用户ID: " + serialData.getOrDefault("userId", "未知"));
                System.out.println("激活时间窗口: " + formatDate(serialData.get("startDate")) + 
                                 " 至 " + formatDate(serialData.get("endDate")));
                
                if (serialData.containsKey("licenseStartDate")) {
                    System.out.println("序列号许可期限: " + formatDate(serialData.get("licenseStartDate")) + 
                                     " 至 " + formatDate(serialData.get("licenseEndDate")));
                }
            } catch (Exception e) {
                System.out.println("序列号解析: ❌ 失败 - " + e.getMessage());
            }
        }
    }
    
    private static String formatDate(String dateStr) {
        if (dateStr == null || dateStr.length() != 8) {
            return dateStr;
        }
        try {
            return dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8);
        } catch (Exception e) {
            return dateStr;
        }
    }
    
    // 许可文件解密方法
    private static String decryptLicenseFile(String encryptedData) throws Exception {
        byte[] encrypted = Base64.getDecoder().decode(encryptedData);
        return aesDecrypt(encrypted);
    }
    
    private static String aesDecrypt(byte[] encryptedData) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(encryptedData);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    // 手动JSON解析方法
    private static Map<String, String> parseJsonManually(String json) {
        Map<String, String> result = new HashMap<>();
        
        json = json.trim();
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1);
            String[] pairs = json.split(",");
            
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim().replaceAll("\"", "");
                    result.put(key, value);
                }
            }
        }
        
        return result;
    }
    
    // 序列号解析方法
    private static Map<String, String> parseSerialNumber(String serialNumber) throws Exception {
        if (!serialNumber.startsWith(SERIAL_PREFIX)) {
            throw new IllegalArgumentException("序列号格式不正确");
        }
        
        String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
        byte[] encrypted = Base64.getDecoder().decode(base64Data);
        String json = aesDecrypt(encrypted);
        
        return parseJsonManually(json);
    }
    
    // 硬件ID生成方法
    private static String generateHardwareId() {
        try {
            String computerName = System.getProperty("user.name");
            String userName = System.getProperty("user.name");
            String input = computerName + userName;
            return md5(input).substring(0, 16).toUpperCase();
        } catch (Exception e) {
            return "UNKNOWN";
        }
    }
    
    // MD5哈希方法
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5计算失败", e);
        }
    }
}
