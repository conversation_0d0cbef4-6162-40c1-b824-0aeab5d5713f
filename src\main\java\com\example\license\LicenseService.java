package com.example.license;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 许可证服务类
 * 处理许可文件的生成、读取和验证
 */
@Service
public class LicenseService {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 检查许可文件是否存在且有效
     * @return true表示许可有效，false表示需要激活
     */
    public boolean isLicenseValid() {
        try {
            // 确保许可证目录存在
            if (!LicenseUtils.ensureLicenseDirectoryExists()) {
                return false;
            }

            File licenseFile = new File(LicenseUtils.LICENSE_FILE);
            if (!licenseFile.exists()) {
                return false;
            }
            
            // 读取加密的许可文件
            String encryptedContent = new String(Files.readAllBytes(licenseFile.toPath()), StandardCharsets.UTF_8);

            // 解密许可证内容
            String licenseJson = LicenseUtils.decryptLicenseFile(encryptedContent);

            // 解析JSON
            @SuppressWarnings("unchecked")
            Map<String, String> license = objectMapper.readValue(licenseJson, Map.class);
            
            // 验证必要字段
            String userId = license.get("userId");
            String serialNumber = license.get("serialNumber");
            String hardwareId = license.get("hardwareId");

            if (userId == null || serialNumber == null || hardwareId == null) {
                return false;
            }

            // 验证硬件ID
            String currentHardwareId = LicenseUtils.generateHardwareId();
            if (!hardwareId.equals(currentHardwareId)) {
                return false;
            }

            // 验证许可使用期限（优先使用许可文件中的期限信息）
            String licenseStartDate = license.get("licenseStartDate");
            String licenseEndDate = license.get("licenseEndDate");

            if (licenseStartDate != null && licenseEndDate != null) {
                // 验证许可文件中的使用期限
                try {
                    LocalDate today = LocalDate.now();
                    LocalDate start = LocalDate.parse(licenseStartDate, DateTimeFormatter.ofPattern(LicenseUtils.DATE_FORMAT));
                    LocalDate end = LocalDate.parse(licenseEndDate, DateTimeFormatter.ofPattern(LicenseUtils.DATE_FORMAT));

                    if (today.isBefore(start) || today.isAfter(end)) {
                        return false;
                    }
                } catch (Exception e) {
                    return false;
                }
            } else {
                // 回退到验证序列号（向后兼容）
                return LicenseUtils.validateSerialNumber(userId, serialNumber);
            }

            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 激活许可证
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 激活结果消息
     */
    public String activateLicense(String userId, String serialNumber) {
        try {
            // 添加调试日志
            System.out.println("DEBUG: Activating license for userId=" + userId);
            System.out.println("DEBUG: Serial number=" + serialNumber);

            // 验证序列号（激活时验证）
            boolean isValid = LicenseUtils.validateSerialNumberForActivation(userId, serialNumber);
            System.out.println("DEBUG: Activation validation result=" + isValid);

            if (!isValid) {
                // 尝试解析序列号以获取更详细的错误信息
                try {
                    java.util.Map<String, String> data = LicenseUtils.parseSerialNumber(serialNumber);
                    System.out.println("DEBUG: Parsed data=" + data);
                    String serialUserId = data.get("userId");
                    System.out.println("DEBUG: Serial userId=" + serialUserId + ", Input userId=" + userId);
                    System.out.println("DEBUG: User ID match=" + userId.equals(serialUserId));
                } catch (Exception e) {
                    System.out.println("DEBUG: Parse error=" + e.getMessage());
                }
                return "序列号无效或用户ID不匹配";
            }
            
            // 解析序列号获取许可期限信息
            Map<String, String> serialData = LicenseUtils.parseSerialNumber(serialNumber);

            // 生成许可文件
            Map<String, String> license = new HashMap<>();
            license.put("userId", userId);
            license.put("serialNumber", serialNumber);
            license.put("installTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern(LicenseUtils.DATETIME_FORMAT)));
            license.put("hardwareId", LicenseUtils.generateHardwareId());

            // 添加许可使用期限信息
            if (serialData.containsKey("licenseStartDate") && serialData.containsKey("licenseEndDate")) {
                // 新格式序列号，直接使用序列号中的许可期限
                license.put("licenseStartDate", serialData.get("licenseStartDate"));
                license.put("licenseEndDate", serialData.get("licenseEndDate"));
            } else {
                // 旧格式序列号，使用激活时间作为许可期限（保持向后兼容）
                license.put("licenseStartDate", serialData.get("startDate"));
                license.put("licenseEndDate", serialData.get("endDate"));
            }
            
            // 确保许可证目录存在
            if (!LicenseUtils.ensureLicenseDirectoryExists()) {
                return "许可证目录创建失败";
            }

            // 将许可证对象转换为JSON字符串
            String licenseJson = objectMapper.writeValueAsString(license);

            // 加密许可证内容
            String encryptedLicense = LicenseUtils.encryptLicenseFile(licenseJson);

            // 保存加密的许可文件
            File licenseFile = new File(LicenseUtils.LICENSE_FILE);
            Files.write(licenseFile.toPath(), encryptedLicense.getBytes(StandardCharsets.UTF_8));
            
            return "激活成功";
            
        } catch (IllegalArgumentException e) {
            return "序列号格式不正确";
        } catch (IOException e) {
            return "许可文件保存失败";
        } catch (Exception e) {
            return "激活失败：" + e.getMessage();
        }
    }
    
    /**
     * 获取许可信息
     * @return 许可信息，如果无效则返回null
     */
    public Map<String, String> getLicenseInfo() {
        try {
            File licenseFile = new File(LicenseUtils.LICENSE_FILE);
            if (!licenseFile.exists()) {
                return null;
            }
            
            @SuppressWarnings("unchecked")
            Map<String, String> license = objectMapper.readValue(licenseFile, Map.class);
            
            // 验证许可有效性
            if (isLicenseValid()) {
                return license;
            } else {
                return null;
            }
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 删除许可文件（用于重新激活）
     * @return 删除结果
     */
    public boolean deleteLicense() {
        try {
            File licenseFile = new File(LicenseUtils.LICENSE_FILE);
            if (licenseFile.exists()) {
                return licenseFile.delete();
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取序列号的详细信息（用于调试）
     * @param serialNumber 序列号
     * @return 序列号信息
     */
    public Map<String, String> getSerialNumberInfo(String serialNumber) {
        try {
            return LicenseUtils.parseSerialNumber(serialNumber);
        } catch (Exception e) {
            return null;
        }
    }
}
