package com.example.repository;

import com.example.entity.MaterialFile;
import com.example.entity.MaterialCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialFileRepository extends JpaRepository<MaterialFile, Long> {
    
    /**
     * 根据分类查找素材文件，按排序顺序排列
     */
    List<MaterialFile> findByCategoryOrderBySortOrderAsc(MaterialCategory category);
    
    /**
     * 根据分类ID查找素材文件，按排序顺序排列
     */
    @Query("SELECT mf FROM MaterialFile mf WHERE mf.category.id = :categoryId ORDER BY mf.sortOrder ASC")
    List<MaterialFile> findByCategoryIdOrderBySortOrderAsc(@Param("categoryId") Long categoryId);
    
    /**
     * 根据文件名查找素材文件
     */
    MaterialFile findByFilename(String filename);
    
    /**
     * 查找默认素材文件
     */
    List<MaterialFile> findByIsDefaultTrueOrderBySortOrderAsc();
    
    /**
     * 根据文件类型查找素材文件
     */
    List<MaterialFile> findByFileTypeOrderByCreatedAtDesc(String fileType);
    
    /**
     * 查找指定分类中的默认素材
     */
    @Query("SELECT mf FROM MaterialFile mf WHERE mf.category.id = :categoryId AND mf.isDefault = true ORDER BY mf.sortOrder ASC")
    List<MaterialFile> findDefaultMaterialsByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 统计指定分类的素材文件数量
     */
    long countByCategory(MaterialCategory category);
    
    /**
     * 查找指定分类中排序顺序最大的素材文件
     */
    @Query("SELECT MAX(mf.sortOrder) FROM MaterialFile mf WHERE mf.category.id = :categoryId")
    Integer findMaxSortOrderByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据素材类型查找所有素材文件
     */
    @Query("SELECT mf FROM MaterialFile mf WHERE mf.category.type = :type ORDER BY mf.category.sortOrder, mf.sortOrder ASC")
    List<MaterialFile> findByMaterialTypeOrderBySortOrder(@Param("type") MaterialCategory.MaterialType type);
    
    /**
     * 检查文件名是否已存在
     */
    boolean existsByFilename(String filename);

    /**
     * 获取所有素材文件，按创建时间倒序排列
     */
    List<MaterialFile> findAllByOrderByCreatedAtDesc();
}
