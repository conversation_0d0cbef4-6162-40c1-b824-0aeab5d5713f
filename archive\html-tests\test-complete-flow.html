<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输出限制功能完整测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1000px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-check2-all"></i>
                输出限制功能完整测试
            </h2>
            
            <!-- 测试说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 测试说明</h6>
                <p class="mb-0">此页面用于完整测试数据集创建过程中的输出限制功能，包括前端UI、SQL生成、数据保存等各个环节。</p>
            </div>
            
            <!-- 前端UI测试 -->
            <div class="test-section">
                <h5><i class="bi bi-display"></i> 前端UI测试</h5>
                <p>测试步骤2中输出限制配置UI的显示和交互功能。</p>
                
                <!-- 模拟步骤2的UI -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-section">
                            <label class="form-label fw-bold">输出限制</label>
                            <div class="mb-2">
                                <input type="number" class="form-control form-control-sm" id="outputLimit" 
                                       placeholder="最大记录数" min="1" max="10000" value="50">
                                <div class="form-text small">限制查询结果的最大记录数，提高查询性能</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-section">
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="aggregation-config">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAggregation">
                                    <label class="form-check-label" for="enableAggregation">
                                        启用聚合查询
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="uiTestResult" class="test-result" style="display: none;"></div>
            </div>
            
            <!-- SQL生成测试 -->
            <div class="test-section">
                <h5><i class="bi bi-code-square"></i> SQL生成测试</h5>
                <p>测试输出限制在SQL生成过程中的应用。</p>
                
                <div class="mb-3">
                    <label class="form-label">测试表名:</label>
                    <input type="text" class="form-control" id="testTableName" value="data_items">
                </div>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-primary me-2" onclick="testSimpleSQL()">
                        <i class="bi bi-play"></i> 测试简单查询
                    </button>
                    <button type="button" class="btn btn-success me-2" onclick="testAggregationSQL()">
                        <i class="bi bi-bar-chart"></i> 测试聚合查询
                    </button>
                    <button type="button" class="btn btn-info" onclick="testWindowSQL()">
                        <i class="bi bi-window"></i> 测试窗口函数
                    </button>
                </div>
                
                <div class="code-block" id="sqlOutput">点击上方按钮生成SQL...</div>
                <div id="sqlTestResult" class="test-result" style="display: none;"></div>
            </div>
            
            <!-- 数据保存测试 -->
            <div class="test-section">
                <h5><i class="bi bi-database"></i> 数据保存测试</h5>
                <p>测试输出限制信息在数据集保存时的处理。</p>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-warning" onclick="testDataSave()">
                        <i class="bi bi-save"></i> 测试数据保存逻辑
                    </button>
                </div>
                
                <div class="code-block" id="saveOutput">点击上方按钮测试保存逻辑...</div>
                <div id="saveTestResult" class="test-result" style="display: none;"></div>
            </div>
            
            <!-- 综合测试结果 -->
            <div class="test-section">
                <h5><i class="bi bi-clipboard-check"></i> 综合测试结果</h5>
                <div id="overallResult">
                    <p class="text-muted">请先运行上述各项测试...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试结果记录
        let testResults = {
            ui: false,
            sql: false,
            save: false
        };
        
        // 模拟SQL生成器
        class TestSQLGenerator {
            addLimitClause(sql) {
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement) {
                    const outputLimit = parseInt(outputLimitElement.value);
                    if (outputLimit && outputLimit > 0) {
                        if (!sql.toLowerCase().includes('limit')) {
                            sql += `\nLIMIT ${outputLimit}`;
                        }
                    }
                }
                return sql;
            }
            
            generateSimpleSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_items';
                let sql = `SELECT *\nFROM ${tableName}`;
                return this.addLimitClause(sql);
            }
            
            generateAggregationSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_items';
                let sql = `SELECT name, MAX(latest_value) as latest_value\nFROM ${tableName}\nGROUP BY name`;
                return this.addLimitClause(sql);
            }
            
            generateWindowSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_items';
                let sql = `SELECT name, latest_value\nFROM (\n    SELECT *, ROW_NUMBER() OVER (\n        PARTITION BY name \n        ORDER BY latest_value DESC, id DESC\n    ) as rn\n    FROM ${tableName}\n) t\nWHERE rn = 1`;
                return this.addLimitClause(sql);
            }
        }
        
        const generator = new TestSQLGenerator();
        
        // UI测试
        document.getElementById('outputLimit').addEventListener('input', function() {
            const value = this.value;
            const resultDiv = document.getElementById('uiTestResult');
            
            if (value && parseInt(value) > 0) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<i class="bi bi-check-circle"></i> UI测试通过：输出限制值 ${value} 已正确设置`;
                testResults.ui = true;
            } else {
                resultDiv.className = 'test-result warning';
                resultDiv.innerHTML = `<i class="bi bi-exclamation-triangle"></i> UI测试警告：输出限制值为空或无效`;
                testResults.ui = false;
            }
            
            resultDiv.style.display = 'block';
            updateOverallResult();
        });
        
        // SQL测试函数
        function testSimpleSQL() {
            const sql = generator.generateSimpleSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('简单查询', sql);
        }
        
        function testAggregationSQL() {
            const sql = generator.generateAggregationSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('聚合查询', sql);
        }
        
        function testWindowSQL() {
            const sql = generator.generateWindowSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('窗口函数查询', sql);
        }
        
        function validateSQL(type, sql) {
            const outputLimit = document.getElementById('outputLimit').value;
            const hasLimit = sql.toLowerCase().includes('limit');
            const limitValue = hasLimit ? sql.match(/limit\s+(\d+)/i)?.[1] : null;
            const resultDiv = document.getElementById('sqlTestResult');
            
            if (outputLimit && hasLimit && limitValue == outputLimit) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<i class="bi bi-check-circle"></i> ${type}SQL测试通过：LIMIT ${limitValue} 已正确添加`;
                testResults.sql = true;
            } else if (!outputLimit && !hasLimit) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<i class="bi bi-check-circle"></i> ${type}SQL测试通过：无输出限制时未添加LIMIT`;
                testResults.sql = true;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<i class="bi bi-x-circle"></i> ${type}SQL测试失败：期望LIMIT ${outputLimit}，实际${hasLimit ? 'LIMIT ' + limitValue : '无LIMIT'}`;
                testResults.sql = false;
            }
            
            resultDiv.style.display = 'block';
            updateOverallResult();
        }
        
        // 数据保存测试
        function testDataSave() {
            const outputLimit = document.getElementById('outputLimit').value;
            const mockDataSet = {
                name: '测试数据集',
                description: '用于测试输出限制功能',
                dataSourceId: 'test-datasource-id',
                queryConfig: JSON.stringify({ sql: 'SELECT * FROM data_items LIMIT 50' }),
                transformConfig: JSON.stringify({ labelField: 'name', valueField: 'latest_value' }),
                defaultOutputLimit: outputLimit ? parseInt(outputLimit) : null,
                dataType: 'realtime',
                enabled: true
            };
            
            document.getElementById('saveOutput').textContent = JSON.stringify(mockDataSet, null, 2);
            
            const resultDiv = document.getElementById('saveTestResult');
            if (mockDataSet.defaultOutputLimit === parseInt(outputLimit)) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<i class="bi bi-check-circle"></i> 数据保存测试通过：defaultOutputLimit 字段值为 ${mockDataSet.defaultOutputLimit}`;
                testResults.save = true;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<i class="bi bi-x-circle"></i> 数据保存测试失败：defaultOutputLimit 字段值不匹配`;
                testResults.save = false;
            }
            
            resultDiv.style.display = 'block';
            updateOverallResult();
        }
        
        // 更新综合测试结果
        function updateOverallResult() {
            const overallDiv = document.getElementById('overallResult');
            const passedTests = Object.values(testResults).filter(result => result).length;
            const totalTests = Object.keys(testResults).length;
            
            if (passedTests === totalTests) {
                overallDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="bi bi-check-circle"></i> 所有测试通过！</h6>
                        <p class="mb-0">输出限制功能已正确实现，包括前端UI、SQL生成和数据保存各个环节。</p>
                    </div>
                `;
            } else if (passedTests > 0) {
                overallDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> 部分测试通过</h6>
                        <p class="mb-0">已通过 ${passedTests}/${totalTests} 项测试，请检查未通过的测试项。</p>
                    </div>
                `;
            } else {
                overallDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-x-circle"></i> 测试未通过</h6>
                        <p class="mb-0">请运行各项测试并确保功能正常工作。</p>
                    </div>
                `;
            }
        }
        
        // 初始化
        document.getElementById('outputLimit').dispatchEvent(new Event('input'));
    </script>
</body>
</html>
