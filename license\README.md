# 🔑 SDPLC许可证管理系统

## 📋 目录说明

本目录包含SDPLC项目的完整许可证管理系统，包括序列号生成器、许可证验证工具和相关文档。

## 📁 文件结构

### 🔧 序列号生成器
- **`SDPLCSerialGenerator.java`** - 主要的图形界面序列号生成器（推荐使用）
- **`SimpleSerialGenerator.java`** - 命令行版本序列号生成器（备用）

### 🛠️ 调试工具
- **`DebugLicense.java`** - 许可证调试工具

### 📄 文档
- **`序列号生成器使用说明.md`** - 详细的使用指南（中文）
- **`测试序列号生成器.md`** - 测试指南和检查清单
- **`license-spec.md`** - 许可证技术规范
- **`test-license.md`** - 许可证测试文档

### 📊 数据文件
- **`license.json`** - 许可证配置文件示例

### 🚀 启动脚本
- **`start-generator.bat`** - Windows启动脚本
- **`start-generator.sh`** - Linux/Mac启动脚本

### 📊 编译文件
- **`SDPLCSerialGenerator.class`** - 主程序编译文件
- **`SDPLCSerialGenerator$*.class`** - 内部类编译文件
- **`SimpleSerialGenerator.class`** - 命令行版本编译文件
- **`DebugLicense.class`** - 调试工具编译文件

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）

**Windows系统：**
```bash
# 双击运行或在命令行执行
start-generator.bat
```

**Linux/Mac系统：**
```bash
# 在终端执行
./start-generator.sh
```

### 方法2：手动编译运行

**图形界面版本（推荐）：**
```bash
# 进入license目录
cd license

# 编译程序
javac -encoding UTF-8 SDPLCSerialGenerator.java

# 运行程序
java SDPLCSerialGenerator
```

**命令行版本：**
```bash
# 编译
javac SimpleSerialGenerator.java

# 运行
java SimpleSerialGenerator
```

## 🎯 主要功能

### 序列号生成器特性
- ✅ **中文图形界面** - 直观的Windows风格界面
- ✅ **用户ID验证** - 严格的格式检查（仅支持大写字母和数字）
- ✅ **日期管理** - 灵活的安装有效期设置
- ✅ **快捷操作** - 一键设置常用有效期（1天/7天/30天/365天）
- ✅ **自动转换** - 输入自动转换为大写
- ✅ **复制功能** - 一键复制到剪贴板
- ✅ **格式提示** - 实时输入格式指导

### 许可证系统特性
- 🔒 **AES加密** - 安全的序列号加密算法
- 📅 **时间控制** - 精确的安装有效期管理
- 🔍 **格式验证** - 严格的输入格式检查
- 📋 **JSON结构** - 标准化的许可证数据格式

## 📖 使用指南

### 基本使用流程
1. **启动生成器** - 运行`java SDPLCSerialGenerator`
2. **设置参数** - 输入用户ID和设置有效期
3. **生成序列号** - 点击"🔧 生成序列号"按钮
4. **复制使用** - 点击"📋 复制到剪贴板"按钮

### 重要概念
- **安装有效期** ≠ 软件使用期限
- **安装有效期** = 序列号可以用于激活的时间窗口
- 激活成功后，软件可以正常使用，没有时间限制

## ⚠️ 注意事项

### 用户ID格式要求
- ✅ **支持**: `USER001`, `ADMIN`, `TEST123`, `LICENSE_001`
- ❌ **不支持**: `user001`, `User001`, `用户001`, `test@123`

### 日期格式要求
- ✅ **正确**: `20250728`
- ❌ **错误**: `2025-07-28`, `28/07/2025`

### 系统要求
- Java 8 或更高版本
- Windows系统（推荐）
- 支持Swing GUI的环境

## 🔧 开发说明

### 编译要求
```bash
# 使用UTF-8编码编译（支持中文）
javac -encoding UTF-8 SDPLCSerialGenerator.java
```

### 核心算法
- **加密算法**: AES/ECB/PKCS5Padding
- **密钥**: SDPLC2024LICENSE
- **编码**: Base64
- **校验**: MD5哈希

### 数据结构
```json
{
  "userId": "USER001",
  "startDate": "20250728",
  "endDate": "20250827",
  "checksum": "7E179F"
}
```

## 📚 相关文档

- 📖 [序列号生成器使用说明](./序列号生成器使用说明.md) - 详细使用指南
- 🧪 [测试序列号生成器](./测试序列号生成器.md) - 测试指南
- 📋 [许可证技术规范](./license-spec.md) - 技术规范文档
- 🔍 [许可证测试文档](./test-license.md) - 测试相关文档

## 🆘 故障排除

### 常见问题
1. **编译错误** - 确保使用UTF-8编码编译
2. **界面显示异常** - 检查Java版本和Swing支持
3. **序列号无效** - 验证用户ID格式和日期格式
4. **激活失败** - 确认在安装有效期内使用

### 获取帮助
- 查看使用说明文档
- 运行测试脚本验证功能
- 检查系统环境配置

---

**版本**: v2.0  
**更新日期**: 2025-07-28  
**维护者**: SDPLC开发团队  
**许可证**: 内部使用
