// 组件状态管理测试脚本
// 用于验证组件设置丢失问题的修复效果

console.log('=== 组件状态管理测试开始 ===');

// 测试1: 组件修改状态跟踪
function testWidgetModifiedStateTracking() {
    console.log('\n--- 测试1: 组件修改状态跟踪 ---');
    
    // 创建测试组件
    const testWidget = {
        id: 999,
        type: 'text-label',
        x: 100,
        y: 100,
        width: 200,
        height: 100,
        config: { text: '测试文本', fontSize: 16 },
        isModified: false,
        modifiedPaths: new Set()
    };
    
    console.log('初始状态:', testWidget.isModified);
    
    // 标记为已修改
    markWidgetAsModified(testWidget, 'config.text');
    console.log('标记修改后:', testWidget.isModified);
    console.log('修改路径:', Array.from(testWidget.modifiedPaths));
    
    // 检查状态正确性
    ensureWidgetModifiedState(testWidget);
    console.log('状态确保后:', testWidget.isModified);
    
    return testWidget.isModified === true;
}

// 测试2: 配置缓存一致性
function testConfigCacheConsistency() {
    console.log('\n--- 测试2: 配置缓存一致性 ---');
    
    const testWidget = {
        id: 998,
        type: 'chart-widget',
        config: { title: '测试图表', chartType: 'line' },
        styleConfig: '{"color": "blue"}',
        isModified: true
    };
    
    // 缓存配置
    syncWidgetConfigCache(testWidget);
    console.log('配置已缓存');
    
    // 验证一致性
    const isConsistent = validateConfigCacheConsistency(testWidget);
    console.log('缓存一致性:', isConsistent);
    
    // 修改配置后验证
    testWidget.config.title = '修改后的标题';
    const isConsistentAfterChange = validateConfigCacheConsistency(testWidget);
    console.log('修改后一致性:', isConsistentAfterChange);
    
    // 修复不一致
    if (!isConsistentAfterChange) {
        repairConfigCacheInconsistency(testWidget);
        console.log('已修复缓存不一致');
    }
    
    return true;
}

// 测试3: 智能配置合并
function testSmartConfigMerging() {
    console.log('\n--- 测试3: 智能配置合并 ---');
    
    const defaultConfig = {
        text: '默认文本',
        fontSize: 14,
        color: '#000000',
        backgroundColor: '#ffffff'
    };
    
    const userConfig = {
        text: '用户文本',
        fontSize: 18
    };
    
    // 测试已修改组件的配置合并
    const modifiedWidget = {
        id: 997,
        type: 'text-label',
        isModified: true,
        config: userConfig
    };
    
    const mergedConfigModified = mergeWidgetConfigSmart(userConfig, defaultConfig, modifiedWidget);
    console.log('已修改组件合并结果:', mergedConfigModified);
    
    // 测试未修改组件的配置合并
    const unmodifiedWidget = {
        id: 996,
        type: 'text-label',
        isModified: false,
        config: userConfig
    };
    
    const mergedConfigUnmodified = mergeWidgetConfigSmart(userConfig, defaultConfig, unmodifiedWidget);
    console.log('未修改组件合并结果:', mergedConfigUnmodified);
    
    // 验证已修改组件保持用户配置
    const preservedUserConfig = mergedConfigModified.text === '用户文本' && 
                               mergedConfigModified.fontSize === 18;
    console.log('用户配置保持:', preservedUserConfig);
    
    return preservedUserConfig;
}

// 测试4: 配置差异检测
function testConfigDifferenceDetection() {
    console.log('\n--- 测试4: 配置差异检测 ---');
    
    const config1 = {
        text: '文本1',
        fontSize: 14,
        color: '#000000'
    };
    
    const config2 = {
        text: '文本2',
        fontSize: 16,
        color: '#000000'
    };
    
    const difference = calculateConfigDifference(config1, config2);
    console.log('配置差异程度:', difference);
    
    // 测试相同配置
    const sameDifference = calculateConfigDifference(config1, config1);
    console.log('相同配置差异:', sameDifference);
    
    return difference > 0 && sameDifference === 0;
}

// 测试5: 组件状态检查
function testWidgetStateCheck() {
    console.log('\n--- 测试5: 组件状态检查 ---');
    
    // 测试应该被标记为已修改的组件
    const shouldBeModifiedWidget = {
        id: 995,
        type: 'text-label',
        x: 50,  // 非默认位置
        y: 50,
        config: { text: '自定义文本', fontSize: 20 },
        dataSourceConfig: '{"dataItemId": "test123"}',
        isModified: false  // 错误的状态
    };
    
    const shouldBeModified = checkIfWidgetShouldBeModified(shouldBeModifiedWidget);
    console.log('应该被标记为已修改:', shouldBeModified);
    
    // 测试不应该被标记为已修改的组件
    const shouldNotBeModifiedWidget = {
        id: 994,
        type: 'text-label',
        x: 0,
        y: 0,
        config: {},
        dataSourceConfig: '{}',
        isModified: false
    };
    
    const shouldNotBeModified = checkIfWidgetShouldBeModified(shouldNotBeModifiedWidget);
    console.log('不应该被标记为已修改:', shouldNotBeModified);
    
    return shouldBeModified === true && shouldNotBeModified === false;
}

// 运行所有测试
function runAllTests() {
    console.log('开始运行组件状态管理测试...\n');
    
    const tests = [
        { name: '组件修改状态跟踪', fn: testWidgetModifiedStateTracking },
        { name: '配置缓存一致性', fn: testConfigCacheConsistency },
        { name: '智能配置合并', fn: testSmartConfigMerging },
        { name: '配置差异检测', fn: testConfigDifferenceDetection },
        { name: '组件状态检查', fn: testWidgetStateCheck }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    tests.forEach(test => {
        try {
            const result = test.fn();
            if (result) {
                console.log(`✅ ${test.name}: 通过`);
                passedTests++;
            } else {
                console.log(`❌ ${test.name}: 失败`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: 错误 - ${error.message}`);
        }
    });
    
    console.log(`\n=== 测试结果: ${passedTests}/${totalTests} 通过 ===`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！组件状态管理修复成功。');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步检查。');
    }
}

// 在页面加载完成后运行测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.addEventListener('load', () => {
        setTimeout(runAllTests, 1000); // 延迟1秒确保所有脚本加载完成
    });
} else {
    // Node.js环境
    runAllTests();
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllTests,
        testWidgetModifiedStateTracking,
        testConfigCacheConsistency,
        testSmartConfigMerging,
        testConfigDifferenceDetection,
        testWidgetStateCheck
    };
}
