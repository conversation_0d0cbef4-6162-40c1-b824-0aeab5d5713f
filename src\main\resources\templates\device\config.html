<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备配置</title>
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            padding: 0;
        }
        
        /* 标题样式 */
        h2 {
            font-size: 2rem;
            font-weight: 500;
            line-height: 1.2;
            color: #212529;
        }
        
        .mb-4 {
            margin-bottom: 1.5rem;
        }
        
        /* 容器 */
        .container {
            width: 100%;
            padding: 0;
        }
        
        /* 卡片样式 */
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            background: #fff;
        }
        
        .card-header {
            padding: 12px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-body {
            padding: 15px;
        }
        
        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .table th,
        .table td {
            padding: 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 6px 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            border-radius: 4px;
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #bb2d3b;
            border-color: #b02a37;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: block;
        }
        
        .modal-dialog {
            position: relative;
            width: 500px;
            margin: 30px auto;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
        }
        
        .modal-header {
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 15px;
        }
        
        .modal-footer {
            padding: 15px;
            border-top: 1px solid #ddd;
            text-align: right;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
        }
        
        .form-select {
            display: block;
            width: 100%;
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            appearance: none;
        }
        
        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2196F3;
        }
        
        input:checked + .slider:before {
            transform: translateX(16px);
        }
        
        /* 筛选表单样式 */
        .filter-form {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: -8px;
        }
        
        .form-col {
            flex: 1;
            padding: 8px;
            min-width: 200px;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            padding-left: 0;
            list-style: none;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .page-item {
            margin: 0 2px;
        }
        
        .page-link {
            display: block;
            padding: 0.5rem 0.75rem;
            color: #0d6efd;
            background-color: #fff;
            border: 1px solid #dee2e6;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .page-item.active .page-link {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        
        /* 状态标签 */
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #198754;
        }
        
        .badge-warning {
            color: #000;
            background-color: #ffc107;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
        
        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mb-4">设备配置</h2>
        
        <!-- 预警配置 -->
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">预警配置</h5>
                <button class="btn btn-sm" onclick="showAlertModal()">添加预警</button>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>数据项</th>
                            <th>实时数值</th>
                            <th>正常范围</th>
                            <th>预警范围</th>
                            <th>异常范围</th>
                            <th>当前状态</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="alertTableBody">
                        <!-- 动态加载预警数据 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 条件配置 -->
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">条件配置</h5>
                <button class="btn btn-sm" onclick="showConditionModal()">添加条件</button>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>源数据项</th>
                            <th>实时数值</th>
                            <th>条件</th>
                            <th>目标数据项</th>
                            <th>写入值</th>
                            <th>写入间隔</th>
                            <th>状态</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="conditionTableBody">
                        <!-- 动态加载条件数据 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">操作日志</h5>
            </div>
            <div class="card-body">
                <!-- 筛选表单 -->
                <div class="filter-form">
                    <form id="logFilterForm">
                        <div class="form-row">
                            <div class="form-col">
                                <label class="form-label">条件</label>
                                <select class="form-select" name="conditionId">
                                    <option value="">全部</option>
                                    <!-- 动态加载条件选项 -->
                                </select>
                            </div>
                            <div class="form-col">
                                <label class="form-label">时间范围</label>
                                <div style="display: flex; gap: 8px;">
                                    <input type="datetime-local" class="form-control" name="startTime">
                                    <input type="datetime-local" class="form-control" name="endTime">
                                </div>
                            </div>
                            <div class="form-col" style="flex: 0 0 auto; align-self: flex-end;">
                                <button type="submit" class="btn">查询</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 日志列表 -->
                <table class="table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>条件</th>
                            <th>源值</th>
                            <th>目标值</th>
                            <th>状态</th>
                            <th>消息</th>
                        </tr>
                    </thead>
                    <tbody id="logTableBody">
                        <!-- 动态加载日志数据 -->
                    </tbody>
                </table>

                <!-- 分页 -->
                <nav>
                    <ul class="pagination" id="pagination">
                        <!-- 动态加载分页 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- 添加预警模态框 -->
    <div class="modal" id="addAlertModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h5 style="margin: 0;">添加预警</h5>
                <button class="btn btn-sm" onclick="hideModal('addAlertModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="alertForm">
                    <div class="form-group">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">数据项</label>
                        <select class="form-select" name="dataItemId" required>
                            <!-- 动态加载数据项选项 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">正常范围</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="number" class="form-control" name="normalMin" placeholder="最小值">
                            <span style="align-self: center;">-</span>
                            <input type="number" class="form-control" name="normalMax" placeholder="最大值">
                        </div>
                        <small class="form-text text-muted">可以只设置最小值或最大值</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">预警范围</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="number" class="form-control" name="warningMin" placeholder="最小值">
                            <span style="align-self: center;">-</span>
                            <input type="number" class="form-control" name="warningMax" placeholder="最大值">
                        </div>
                        <small class="form-text text-muted">可以只设置最小值或最大值</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">异常范围</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="number" class="form-control" name="dangerMin" placeholder="最小值">
                            <span style="align-self: center;">-</span>
                            <input type="number" class="form-control" name="dangerMax" placeholder="最大值">
                        </div>
                        <small class="form-text text-muted">可以只设置最小值或最大值</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" name="remark" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideModal('addAlertModal')">取消</button>
                <button class="btn" onclick="saveAlert()">保存</button>
            </div>
        </div>
    </div>
    
    <!-- 添加条件模态框 -->
    <div class="modal" id="addConditionModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h5 style="margin: 0;">添加条件</h5>
                <button class="btn btn-sm" onclick="hideModal('addConditionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="conditionForm">
                    <div class="form-group">
                        <label class="form-label">源数据项</label>
                        <select class="form-select" name="sourceItemId" required>
                            <!-- 动态加载数据项选项 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">操作符</label>
                        <select class="form-select" name="operator" required>
                            <option value=">">&gt;</option>
                            <option value="<">&lt;</option>
                            <option value="=">=</option>
                            <option value=">=">&gt;=</option>
                            <option value="<=">&lt;=</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">比较值</label>
                        <input type="number" class="form-control" name="compareValue" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">目标数据项</label>
                        <select class="form-select" name="targetItemId" required>
                            <!-- 动态加载数据项选项 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">写入值</label>
                        <input type="number" class="form-control" name="writeValue" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">写入间隔（毫秒）</label>
                        <input type="number" class="form-control" name="writeInterval" value="1000" min="100" required>
                        <small class="form-text text-muted">最小间隔100毫秒，默认1000毫秒（1秒）</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" name="remark" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideModal('addConditionModal')">取消</button>
                <button class="btn" onclick="saveCondition()">保存</button>
            </div>
        </div>
    </div>
    
    <script th:inline="javascript">
        const deviceId = [[${deviceId}]];
        let updateTimer = null;
        let currentPage = 0;
        let pageSize = 10;
        
        // 工具函数
        function showModal(id) {
            document.getElementById(id).classList.add('show');
        }
        
        function hideModal(id) {
            document.getElementById(id).classList.remove('show');
        }
        
        // API 请求函数
        async function fetchApi(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const error = await response.json();
                        throw new Error(error.message || '请求失败');
                    } else {
                        const text = await response.text();
                        throw new Error(text || `HTTP错误 ${response.status}`);
                    }
                }
                
                if (response.status === 204) {
                    return null;
                }
                
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                }
                
                return await response.text();
            } catch (error) {
                console.error('API请求失败:', error);
                alert(error.message || '操作失败');
                throw error;
            }
        }
        
        // 加载数据
        async function loadData() {
            await Promise.all([
                loadAlerts(),
                loadConditions(),
                loadDataItems(),
                loadLogs(),
            ]);
            
            // 启动实时更新
            startRealTimeUpdate();
        }
        
        // 启动实时更新
        function startRealTimeUpdate() {
            if (updateTimer) {
                clearInterval(updateTimer);
            }
            
            // 每秒更新一次当前值
            updateTimer = setInterval(updateLatestValues, 1000);
        }
        
        // 停止实时更新
        function stopRealTimeUpdate() {
            if (updateTimer) {
                clearInterval(updateTimer);
                updateTimer = null;
            }
        }
        
        // 更新最新值
        async function updateLatestValues() {
            try {
                // 获取设备连接状态
                const device = await fetchApi(`/api/device/${deviceId}`);
                if (!device.connected) {
                    // 如果设备未连接，更新所有实时值为"未连接"，状态为"未知"
                    const alertTableBody = document.getElementById('alertTableBody');
                    const alertRows = alertTableBody.getElementsByTagName('tr');
                    for (let i = 0; i < alertRows.length; i++) {
                        const cells = alertRows[i].getElementsByTagName('td');
                        if (cells.length >= 3) {
                            cells[2].textContent = '未连接';
                            cells[6].innerHTML = getStatusBadge(0); // 0 表示未知状态
                        }
                    }
                    
                    // 更新条件表的实时数值
                    const conditionTableBody = document.getElementById('conditionTableBody');
                    const conditionRows = conditionTableBody.getElementsByTagName('tr');
                    for (let i = 0; i < conditionRows.length; i++) {
                        const cells = conditionRows[i].getElementsByTagName('td');
                        if (cells.length >= 2) {
                            cells[1].textContent = '未连接';
                        }
                    }
                    return;
                }

                // 设备已连接，正常更新数据
                // 更新预警表的实时数值和状态
                const alerts = await fetchApi(`/api/alerts/device/${deviceId}`);
                const alertTableBody = document.getElementById('alertTableBody');
                const alertRows = alertTableBody.getElementsByTagName('tr');
                
                for (let i = 0; i < alertRows.length; i++) {
                    const alert = alerts[i];
                    if (alert && alert.dataItem) {
                        const latestValue = await fetchApi(`/api/data-items/${alert.dataItem.id}/latest-value`);
                        const cells = alertRows[i].getElementsByTagName('td');
                        if (cells.length >= 3) {
                            cells[2].textContent = latestValue.value !== null && latestValue.value !== undefined ? latestValue.value : '-';
                            
                            // 更新状态
                            const statusResponse = await fetchApi(`/api/alerts/${alert.id}/status`);
                            const statusCell = cells[6];
                            statusCell.innerHTML = getStatusBadge(statusResponse.status);
                        }
                    }
                }
                
                // 更新条件表的实时数值
                const conditions = await fetchApi(`/api/conditions/device/${deviceId}`);
                const conditionTableBody = document.getElementById('conditionTableBody');
                const conditionRows = conditionTableBody.getElementsByTagName('tr');
                
                for (let i = 0; i < conditionRows.length; i++) {
                    const condition = conditions[i];
                    if (condition && condition.sourceItem) {
                        const latestValue = await fetchApi(`/api/data-items/${condition.sourceItem.id}/latest-value`);
                        const cells = conditionRows[i].getElementsByTagName('td');
                        if (cells.length >= 2) {
                            cells[1].textContent = latestValue.value !== null && latestValue.value !== undefined ? latestValue.value : '-';
                        }
                    }
                }
            } catch (error) {
                console.error('更新当前值失败:', error);
                // 如果更新失败，停止实时更新并在3秒后重试
                stopRealTimeUpdate();
                setTimeout(startRealTimeUpdate, 3000);
            }
        }
        
        // 获取状态徽章
        function getStatusBadge(status) {
            switch (status) {
                case 1:
                    return '<span class="badge badge-success">正常</span>';
                case 2:
                    return '<span class="badge badge-warning">预警</span>';
                case 3:
                    return '<span class="badge badge-danger">异常</span>';
                default:
                    return '<span class="badge badge-secondary">未知</span>';
            }
        }
        
        // 加载预警列表
        async function loadAlerts() {
            try {
                const alerts = await fetchApi(`/api/alerts/device/${deviceId}`);
                const tbody = document.getElementById('alertTableBody');
                tbody.innerHTML = alerts.map(alert => {
                    const normalRange = formatRange(alert.normalMin, alert.normalMax);
                    const warningRange = formatRange(alert.warningMin, alert.warningMax);
                    const dangerRange = formatRange(alert.dangerMin, alert.dangerMax);
                    
                    return `
                    <tr>
                        <td>${alert.name}</td>
                        <td>${alert.dataItem.name} (${alert.dataItem.address})</td>
                        <td>-</td>
                        <td>${normalRange}</td>
                        <td>${warningRange}</td>
                        <td>${dangerRange}</td>
                        <td><span class="badge badge-secondary">未知</span></td>
                        <td>${alert.remark || '-'}</td>
                        <td>
                            <button class="btn btn-sm" onclick="showEditAlertModal(${alert.id})">编辑</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteAlert(${alert.id})">删除</button>
                        </td>
                    </tr>
                    `;
                }).join('');
            } catch (error) {
                console.error('加载预警失败:', error);
            }
        }
        
        // 格式化范围
        function formatRange(min, max) {
            if (min !== null && max !== null) {
                return `${min} - ${max}`;
            } else if (min !== null) {
                return `≥ ${min}`;
            } else if (max !== null) {
                return `≤ ${max}`;
            } else {
                return '-';
            }
        }
        
        // 显示预警模态框
        function showAlertModal() {
            const form = document.getElementById('alertForm');
            form.reset();
            delete form.dataset.editId; // 清除编辑ID
            loadDataItemOptions();
            showModal('addAlertModal');
        }
        
        // 显示编辑预警模态框
        async function showEditAlertModal(alertId) {
            try {
                // 加载数据项选项
                await loadDataItemOptions();
                
                // 获取预警数据
                const alert = await fetchApi(`/api/alerts/${alertId}`);
                
                // 填充表单数据
                const form = document.getElementById('alertForm');
                form.dataset.editId = alertId; // 保存编辑ID
                form.querySelector('[name="name"]').value = alert.name;
                form.querySelector('[name="dataItemId"]').value = alert.dataItem.id;
                form.querySelector('[name="normalMin"]').value = alert.normalMin || '';
                form.querySelector('[name="normalMax"]').value = alert.normalMax || '';
                form.querySelector('[name="warningMin"]').value = alert.warningMin || '';
                form.querySelector('[name="warningMax"]').value = alert.warningMax || '';
                form.querySelector('[name="dangerMin"]').value = alert.dangerMin || '';
                form.querySelector('[name="dangerMax"]').value = alert.dangerMax || '';
                form.querySelector('[name="remark"]').value = alert.remark || '';
                
                // 显示模态框
                showModal('addAlertModal');
            } catch (error) {
                console.error('加载预警数据失败:', error);
                alert('加载预警数据失败');
            }
        }
        
        // 保存预警（新增或更新）
        async function saveAlert() {
            try {
                const form = document.getElementById('alertForm');
                const formData = new FormData(form);
                const data = {
                    name: formData.get('name'),
                    dataItemId: formData.get('dataItemId'),
                    normalMin: formData.get('normalMin') ? parseInt(formData.get('normalMin')) : null,
                    normalMax: formData.get('normalMax') ? parseInt(formData.get('normalMax')) : null,
                    warningMin: formData.get('warningMin') ? parseInt(formData.get('warningMin')) : null,
                    warningMax: formData.get('warningMax') ? parseInt(formData.get('warningMax')) : null,
                    dangerMin: formData.get('dangerMin') ? parseInt(formData.get('dangerMin')) : null,
                    dangerMax: formData.get('dangerMax') ? parseInt(formData.get('dangerMax')) : null,
                    remark: formData.get('remark')
                };
                
                const editId = form.dataset.editId;
                const method = editId ? 'PUT' : 'POST';
                const url = editId 
                    ? `/api/alerts/${editId}`
                    : `/api/alerts/device/${deviceId}`;
                
                await fetchApi(url, {
                    method: method,
                    body: JSON.stringify(data)
                });
                
                await loadAlerts();
                hideModal('addAlertModal');
                form.reset();
                delete form.dataset.editId; // 清除编辑ID
            } catch (error) {
                console.error('保存预警失败:', error);
            }
        }
        
        // 删除预警
        async function deleteAlert(alertId) {
            if (confirm('确定要删除此预警吗？')) {
                try {
                    await fetchApi(`/api/alerts/${alertId}`, {
                        method: 'DELETE'
                    });
                    await loadAlerts();
                } catch (error) {
                    console.error('删除预警失败:', error);
                }
            }
        }
        
        // 加载条件列表
        async function loadConditions() {
            try {
                const conditions = await fetchApi(`/api/conditions/device/${deviceId}`);
                const tbody = document.getElementById('conditionTableBody');
                tbody.innerHTML = conditions.map(condition => {
                    return `
                    <tr>
                        <td>${condition.sourceItem.name} (${condition.sourceItem.address})</td>
                        <td>-</td>
                        <td>${condition.operator} ${condition.compareValue}</td>
                        <td>${condition.targetItem.name} (${condition.targetItem.address})</td>
                        <td>${condition.writeValue}</td>
                        <td>${condition.writeInterval}ms</td>
                        <td>
                            <label class="switch">
                                <input type="checkbox" ${condition.enabled ? 'checked' : ''}
                                    onchange="toggleCondition(${condition.id}, this.checked)">
                                <span class="slider"></span>
                            </label>
                        </td>
                        <td>${condition.remark || '-'}</td>
                        <td>
                            <button class="btn btn-sm" onclick="showEditConditionModal(${condition.id})">编辑</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCondition(${condition.id})">删除</button>
                        </td>
                    </tr>
                    `;
                }).join('');
            } catch (error) {
                console.error('加载条件失败:', error);
            }
        }
        
        // 加载数据项列表
        async function loadDataItems() {
            try {
                const dataItems = await fetchApi(`/api/data-items/device/${deviceId}`);
                const select = document.querySelector('select[name="dataItemId"]');
                select.innerHTML = dataItems.map(item => 
                    `<option value="${item.id}">${item.name} (${item.address})</option>`
                ).join('');
            } catch (error) {
                console.error('加载数据项失败:', error);
            }
        }
        
        // 显示条件模态框
        function showConditionModal() {
            const form = document.getElementById('conditionForm');
            form.reset();
            delete form.dataset.editId; // 清除编辑ID
            loadDataItemOptions();
            showModal('addConditionModal');
        }
        
        // 加载数据项列表到条件配置模态框
        async function loadDataItemOptions() {
            try {
                // 加载当前设备的数据项（用于源数据项）
                const sourceDataItems = await fetchApi(`/api/data-items/device/${deviceId}`);
                const sourceSelect = document.querySelector('select[name="sourceItemId"]');
                
                // 加载所有设备的数据项（用于目标数据项）
                const allDataItems = await fetchApi('/api/data-items');
                const targetSelect = document.querySelector('select[name="targetItemId"]');
                
                // 设置源数据项选项（仅当前设备）
                sourceSelect.innerHTML = sourceDataItems.map(item => 
                    `<option value="${item.id}">${item.name} (${item.address})</option>`
                ).join('');
                
                // 设置目标数据项选项（所有设备）
                targetSelect.innerHTML = allDataItems.map(item => 
                    `<option value="${item.id}">${item.device.name} - ${item.name} (${item.address})</option>`
                ).join('');
            } catch (error) {
                console.error('加载数据项选项失败:', error);
            }
        }
        
        // 显示编辑条件模态框
        async function showEditConditionModal(conditionId) {
            try {
                // 加载数据项选项
                await loadDataItemOptions();
                
                // 获取条件数据
                const condition = await fetchApi(`/api/conditions/${conditionId}`);
                
                // 填充表单数据
                const form = document.getElementById('conditionForm');
                form.dataset.editId = conditionId; // 保存编辑ID
                form.querySelector('[name="sourceItemId"]').value = condition.sourceItem.id;
                form.querySelector('[name="operator"]').value = condition.operator;
                form.querySelector('[name="compareValue"]').value = condition.compareValue;
                form.querySelector('[name="targetItemId"]').value = condition.targetItem.id;
                form.querySelector('[name="writeValue"]').value = condition.writeValue;
                form.querySelector('[name="writeInterval"]').value = condition.writeInterval;
                form.querySelector('[name="remark"]').value = condition.remark || '';
                
                // 显示模态框
                showModal('addConditionModal');
            } catch (error) {
                console.error('加载条件数据失败:', error);
                alert('加载条件数据失败');
            }
        }
        
        // 保存条件（新增或更新）
        async function saveCondition() {
            try {
                const form = document.getElementById('conditionForm');
                const formData = new FormData(form);
                const data = {
                    sourceItemId: formData.get('sourceItemId'),
                    operator: formData.get('operator'),
                    compareValue: parseInt(formData.get('compareValue')),
                    targetItemId: formData.get('targetItemId'),
                    writeValue: parseInt(formData.get('writeValue')),
                    writeInterval: parseInt(formData.get('writeInterval')),
                    remark: formData.get('remark')
                };
                
                const editId = form.dataset.editId;
                const method = editId ? 'PUT' : 'POST';
                const url = editId 
                    ? `/api/conditions/${editId}`
                    : `/api/conditions/device/${deviceId}`;
                
                await fetchApi(url, {
                    method: method,
                    body: JSON.stringify(data)
                });
                
                await loadConditions();
                hideModal('addConditionModal');
                form.reset();
                delete form.dataset.editId; // 清除编辑ID
            } catch (error) {
                console.error('保存条件失败:', error);
            }
        }
        
        // 删除条件
        async function deleteCondition(conditionId) {
            if (confirm('确定要删除此条件吗？')) {
                try {
                    await fetchApi(`/api/conditions/${conditionId}`, {
                        method: 'DELETE'
                    });
                    await loadConditions();
                } catch (error) {
                    console.error('删除条件失败:', error);
                }
            }
        }
        
        // 切换条件状态
        async function toggleCondition(conditionId, enabled) {
            try {
                await fetchApi(`/api/conditions/${conditionId}/toggle`, {
                    method: 'PUT',
                    body: JSON.stringify({ enabled })
                });
                await loadConditions();
            } catch (error) {
                console.error('切换条件状态失败:', error);
                await loadConditions(); // 刷新以恢复原状态
            }
        }
        
        // 加载日志
        async function loadLogs(page = 0) {
            try {
                const form = document.getElementById('logFilterForm');
                const formData = new FormData(form);
                const params = new URLSearchParams();
                
                params.append('page', page);
                params.append('size', pageSize);
                
                const conditionId = formData.get('conditionId');
                const startTime = formData.get('startTime');
                const endTime = formData.get('endTime');
                
                if (startTime && endTime) {
                    params.append('start', startTime);
                    params.append('end', endTime);
                }
                
                // 根据是否选择了条件使用不同的API端点
                let url = `/api/logs/device/${deviceId}/page`;
                if (conditionId) {
                    url = `/api/logs/condition/${conditionId}/page`;
                }
                
                const response = await fetchApi(`${url}?${params}`);
                const { content, totalPages, number } = response;
                currentPage = number;
                
                // 更新日志表格
                const tbody = document.getElementById('logTableBody');
                tbody.innerHTML = content.map(log => `
                    <tr>
                        <td>${formatDateTime(log.createdAt)}</td>
                        <td>${log.condition.sourceItem.name} ${log.condition.operator} ${log.condition.compareValue}</td>
                        <td>${log.sourceValue}</td>
                        <td>${log.targetValue}</td>
                        <td>
                            <span class="badge ${log.success ? 'badge-success' : 'badge-danger'}">
                                ${log.success ? '成功' : '失败'}
                            </span>
                        </td>
                        <td>${log.message}</td>
                    </tr>
                `).join('');
                
                // 更新分页
                updatePagination(totalPages, number);
            } catch (error) {
                console.error('加载日志失败:', error);
            }
        }
        
        // 更新分页
        function updatePagination(totalPages, currentPage) {
            const pagination = document.getElementById('pagination');
            let html = '';
            
            // 上一页
            html += `
                <li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${currentPage - 1})">上一页</a>
                </li>
            `;
            
            // 页码
            for (let i = 0; i < totalPages; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadLogs(${i})">${i + 1}</a>
                    </li>
                `;
            }
            
            // 下一页
            html += `
                <li class="page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${currentPage + 1})">下一页</a>
                </li>
            `;
            
            pagination.innerHTML = html;
        }
        
        // 格式化日期时间
        function formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        }
        
        // 监听日志筛选表单提交
        document.getElementById('logFilterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            loadLogs();
        });
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', loadData);
        
        // 页面卸载时清理
        window.addEventListener('unload', () => {
            stopRealTimeUpdate();
        });
    </script>
</body>
</html> 