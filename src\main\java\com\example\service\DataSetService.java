package com.example.service;

import com.example.entity.DataSet;
import com.example.entity.DataSource;
import com.example.repository.DataSetRepository;
import com.example.repository.DataSourceRepository;
import com.example.service.connector.DataSourceConnector;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据集管理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSetService {
    
    private final DataSetRepository dataSetRepository;
    private final DataSourceRepository dataSourceRepository;
    private final List<DataSourceConnector> connectors;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取所有数据集
     */
    public List<DataSet> getAllDataSets() {
        return dataSetRepository.findAll();
    }
    
    /**
     * 根据ID获取数据集
     */
    public Optional<DataSet> getDataSetById(String id) {
        return dataSetRepository.findById(id);
    }
    
    /**
     * 根据数据源ID获取数据集
     */
    public List<DataSet> getDataSetsByDataSourceId(String dataSourceId) {
        return dataSetRepository.findByDataSourceId(dataSourceId);
    }
    
    /**
     * 获取启用的数据集
     */
    public List<DataSet> getEnabledDataSets() {
        return dataSetRepository.findByEnabled(true);
    }
    
    /**
     * 创建数据集
     */
    @Transactional
    public DataSet createDataSet(DataSet dataSet) {
        // 检查名称是否已存在
        if (dataSetRepository.existsByName(dataSet.getName())) {
            throw new IllegalArgumentException("数据集名称已存在: " + dataSet.getName());
        }
        
        // 验证数据源是否存在
        DataSource dataSource = dataSourceRepository.findById(dataSet.getDataSourceId())
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + dataSet.getDataSourceId()));
        
        // 设置数据源名称
        dataSet.setDataSourceName(dataSource.getName());
        
        // 生成ID
        if (dataSet.getId() == null || dataSet.getId().isEmpty()) {
            dataSet.setId(generateDataSetId());
        }
        
        // 设置默认值
        if (dataSet.getEnabled() == null) {
            dataSet.setEnabled(true);
        }
        if (dataSet.getDataType() == null) {
            dataSet.setDataType("realtime");
        }
        
        return dataSetRepository.save(dataSet);
    }
    
    /**
     * 更新数据集
     */
    @Transactional
    public DataSet updateDataSet(String id, DataSet dataSet) {
        DataSet existing = dataSetRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据集不存在: " + id));
        
        // 检查名称是否已存在（排除当前数据集）
        if (!existing.getName().equals(dataSet.getName()) && 
            dataSetRepository.existsByName(dataSet.getName())) {
            throw new IllegalArgumentException("数据集名称已存在: " + dataSet.getName());
        }
        
        // 更新字段
        existing.setName(dataSet.getName());
        existing.setDescription(dataSet.getDescription());
        existing.setQueryConfig(dataSet.getQueryConfig());
        existing.setTransformConfig(dataSet.getTransformConfig());
        existing.setCacheConfig(dataSet.getCacheConfig());
        existing.setDataType(dataSet.getDataType());
        existing.setEnabled(dataSet.getEnabled());
        existing.setUpdatedBy(dataSet.getUpdatedBy());
        
        return dataSetRepository.save(existing);
    }
    
    /**
     * 删除数据集
     */
    @Transactional
    public void deleteDataSet(String id) {
        DataSet dataSet = dataSetRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("数据集不存在: " + id));
        
        dataSetRepository.delete(dataSet);
    }
    
    /**
     * 执行数据集查询
     */
    public DataSourceConnector.QueryResult executeDataSet(String dataSetId) {
        DataSet dataSet = dataSetRepository.findById(dataSetId)
            .orElseThrow(() -> new IllegalArgumentException("数据集不存在: " + dataSetId));
        
        if (!dataSet.getEnabled()) {
            throw new IllegalArgumentException("数据集已禁用: " + dataSetId);
        }
        
        DataSource dataSource = dataSourceRepository.findById(dataSet.getDataSourceId())
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + dataSet.getDataSourceId()));
        
        if (!dataSource.getEnabled()) {
            throw new IllegalArgumentException("数据源已禁用: " + dataSet.getDataSourceId());
        }
        
        DataSourceConnector connector = getConnector(dataSource.getType());
        DataSourceConnector.QueryResult result = connector.executeQuery(
            dataSet.getQueryConfig(), 
            dataSource.getConnectionConfig()
        );
        
        // 更新执行记录
        dataSet.setLastExecuteTime(LocalDateTime.now());
        dataSet.setLastExecuteResult(result.isSuccess() ? "成功" : result.getMessage());
        dataSet.setDataRowCount(result.getRowCount());
        dataSetRepository.save(dataSet);
        
        return result;
    }
    
    /**
     * 获取数据源的表元数据
     */
    public DataSourceConnector.MetadataResult getTableMetadata(String dataSourceId) {
        DataSource dataSource = dataSourceRepository.findById(dataSourceId)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + dataSourceId));
        
        DataSourceConnector connector = getConnector(dataSource.getType());
        return connector.getMetadata(dataSource.getConnectionConfig());
    }
    
    /**
     * 预览查询结果
     */
    public List<Map<String, Object>> previewQuery(String dataSourceId, String queryConfig, int limit) {
        DataSource dataSource = dataSourceRepository.findById(dataSourceId)
            .orElseThrow(() -> new IllegalArgumentException("数据源不存在: " + dataSourceId));
        
        try {
            // 修改查询添加LIMIT限制
            JsonNode query = objectMapper.readTree(queryConfig);
            String sql = query.get("sql").asText();
            
            // 简单的LIMIT添加（实际应该更智能地处理）
            if (!sql.toLowerCase().contains("limit")) {
                sql += " LIMIT " + Math.min(limit, 100); // 最多100行
            }
            
            // 重新构建查询配置
            Map<String, Object> limitedQuery = new HashMap<>();
            limitedQuery.put("sql", sql);
            if (query.has("parameters")) {
                limitedQuery.put("parameters", query.get("parameters"));
            }
            
            String limitedQueryConfig = objectMapper.writeValueAsString(limitedQuery);
            
            DataSourceConnector connector = getConnector(dataSource.getType());
            DataSourceConnector.QueryResult result = connector.executeQuery(
                limitedQueryConfig, 
                dataSource.getConnectionConfig()
            );
            
            if (result.isSuccess()) {
                return result.getData();
            } else {
                throw new RuntimeException("查询执行失败: " + result.getMessage());
            }
        } catch (Exception e) {
            log.error("预览查询失败", e);
            throw new RuntimeException("预览查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 格式化数据集结果为BI图表格式
     */
    public Map<String, Object> formatDataSetForChart(String dataSetId, String chartType) {
        DataSourceConnector.QueryResult result = executeDataSet(dataSetId);
        
        if (!result.isSuccess()) {
            throw new RuntimeException("数据集执行失败: " + result.getMessage());
        }
        
        List<Map<String, Object>> data = result.getData();
        if (data == null || data.isEmpty()) {
            return createEmptyChartData();
        }
        
        // 获取数据集的转换配置
        DataSet dataSet = getDataSetById(dataSetId).orElse(null);
        if (dataSet != null && dataSet.getTransformConfig() != null) {
            try {
                JsonNode transformConfig = objectMapper.readTree(dataSet.getTransformConfig());
                return formatDataWithTransform(data, transformConfig, chartType);
            } catch (Exception e) {
                log.warn("数据转换配置解析失败，使用默认格式", e);
            }
        }
        
        // 使用默认格式化
        return formatDataDefault(data, chartType);
    }
    
    /**
     * 根据类型获取连接器
     */
    private DataSourceConnector getConnector(String type) {
        return connectors.stream()
            .filter(connector -> connector.getType().equals(type))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("不支持的数据源类型: " + type));
    }
    
    /**
     * 生成数据集ID
     */
    private String generateDataSetId() {
        return "dataset_" + System.currentTimeMillis();
    }
    
    /**
     * 创建空的图表数据
     */
    private Map<String, Object> createEmptyChartData() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("labels", new ArrayList<>());
        result.put("values", new ArrayList<>());
        result.put("data", new ArrayList<>());
        return result;
    }
    
    /**
     * 使用转换配置格式化数据
     */
    private Map<String, Object> formatDataWithTransform(List<Map<String, Object>> data, 
                                                       JsonNode transformConfig, String chartType) {
        // 获取字段映射配置
        String labelField = transformConfig.path("labelField").asText();
        String valueField = transformConfig.path("valueField").asText();
        
        if (labelField.isEmpty() || valueField.isEmpty()) {
            return formatDataDefault(data, chartType);
        }
        
        List<String> labels = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        List<Map<String, Object>> chartData = new ArrayList<>();
        
        for (Map<String, Object> row : data) {
            Object labelValue = row.get(labelField);
            Object valueValue = row.get(valueField);
            
            if (labelValue != null && valueValue != null) {
                String label = labelValue.toString();
                labels.add(label);
                values.add(valueValue);
                
                // 为饼图创建特殊格式
                if ("pie-chart".equals(chartType)) {
                    Map<String, Object> pieItem = new HashMap<>();
                    pieItem.put("name", label);
                    pieItem.put("value", valueValue);
                    chartData.add(pieItem);
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("labels", labels);
        result.put("values", values);
        if ("pie-chart".equals(chartType)) {
            result.put("data", chartData);
        }
        
        return result;
    }
    
    /**
     * 默认数据格式化
     */
    private Map<String, Object> formatDataDefault(List<Map<String, Object>> data, String chartType) {
        if (data.isEmpty()) {
            return createEmptyChartData();
        }
        
        // 获取第一行的键作为字段名
        Set<String> fields = data.get(0).keySet();
        List<String> fieldList = new ArrayList<>(fields);
        
        // 假设第一个字段是标签，第二个字段是数值
        String labelField = fieldList.size() > 0 ? fieldList.get(0) : "label";
        String valueField = fieldList.size() > 1 ? fieldList.get(1) : "value";
        
        List<String> labels = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        List<Map<String, Object>> chartData = new ArrayList<>();
        
        for (Map<String, Object> row : data) {
            Object labelValue = row.get(labelField);
            Object valueValue = row.get(valueField);
            
            if (labelValue != null && valueValue != null) {
                String label = labelValue.toString();
                labels.add(label);
                values.add(valueValue);
                
                if ("pie-chart".equals(chartType)) {
                    Map<String, Object> pieItem = new HashMap<>();
                    pieItem.put("name", label);
                    pieItem.put("value", valueValue);
                    chartData.add(pieItem);
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("labels", labels);
        result.put("values", values);
        if ("pie-chart".equals(chartType)) {
            result.put("data", chartData);
        }
        
        return result;
    }
}
