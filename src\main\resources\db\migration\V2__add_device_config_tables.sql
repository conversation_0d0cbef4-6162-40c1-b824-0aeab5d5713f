-- 创建地址别名表
CREATE TABLE IF NOT EXISTS address_aliases (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    data_item_id VARCHAR(50) NOT NULL,
    alias_name VARCHAR(100) NOT NULL,
    device_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (data_item_id) REFERENCES data_items(id) ON DELETE CASCADE,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    UNIQUE KEY uk_device_alias (device_id, alias_name)
);

-- 创建设备条件表
CREATE TABLE IF NOT EXISTS device_conditions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    source_alias_id BIGINT NOT NULL,
    operator VARCHAR(10) NOT NULL,
    compare_value INT NOT NULL,
    target_alias_id BIGINT NOT NULL,
    write_value INT NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (source_alias_id) REFERENCES address_aliases(id) ON DELETE CASCADE,
    FOREIGN KEY (target_alias_id) REFERENCES address_aliases(id) ON DELETE CASCADE
);

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    condition_id BIGINT NOT NULL,
    source_value INT NOT NULL,
    target_value INT NOT NULL,
    success BOOLEAN NOT NULL,
    message VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (condition_id) REFERENCES device_conditions(id) ON DELETE CASCADE
); 