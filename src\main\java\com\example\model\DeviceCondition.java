package com.example.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "device_conditions")
@Data
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class DeviceCondition {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "device_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private Device device;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "source_item_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private DataItem sourceItem;
    
    @Column(nullable = false, length = 10)
    private String operator; // >、<、=、>=、<=
    
    @Column(nullable = false)
    private Integer compareValue;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "target_item_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private DataItem targetItem;
    
    @Column(nullable = false)
    private Integer writeValue;
    
    @Column(nullable = false)
    private Boolean enabled = true;
    
    @Column(name = "write_interval", nullable = false)
    private Integer writeInterval = 1000; // 默认写入间隔1秒
    
    @Column(name = "last_write_time")
    private LocalDateTime lastWriteTime;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "remark", length = 500)
    private String remark;  // 备注信息，最大长度500字符
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
} 