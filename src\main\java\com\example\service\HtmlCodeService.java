package com.example.service;

import com.example.entity.HtmlCodeCategory;
import com.example.entity.HtmlCodeSnippet;
import com.example.repository.HtmlCodeCategoryRepository;
import com.example.repository.HtmlCodeSnippetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * HTML代码管理服务类
 */
@Service
@Slf4j
public class HtmlCodeService {
    
    @Autowired
    private HtmlCodeCategoryRepository categoryRepository;
    
    @Autowired
    private HtmlCodeSnippetRepository snippetRepository;
    
    // ==================== 分类管理 ====================
    
    /**
     * 获取所有分类
     */
    @Transactional(readOnly = true)
    public List<HtmlCodeCategory> getAllCategories() {
        return categoryRepository.findAllByOrderBySortOrderAsc();
    }
    
    /**
     * 创建新分类
     */
    @Transactional
    public HtmlCodeCategory createCategory(String name, String description) {
        // 检查名称是否重复
        if (categoryRepository.existsByName(name)) {
            throw new RuntimeException("分类名称已存在");
        }
        
        HtmlCodeCategory category = new HtmlCodeCategory();
        category.setName(name);
        category.setDescription(description);
        
        // 设置排序顺序为最大值+1
        Integer maxSortOrder = categoryRepository.findMaxSortOrder();
        category.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        
        HtmlCodeCategory saved = categoryRepository.save(category);
        log.info("创建HTML代码分类: {}", name);
        return saved;
    }
    
    /**
     * 更新分类
     */
    @Transactional
    public HtmlCodeCategory updateCategory(Long id, String name, String description) {
        HtmlCodeCategory category = categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        // 检查名称是否与其他分类重复
        HtmlCodeCategory existingCategory = categoryRepository.findByName(name);
        if (existingCategory != null && !existingCategory.getId().equals(id)) {
            throw new RuntimeException("分类名称已存在");
        }
        
        category.setName(name);
        category.setDescription(description);
        
        HtmlCodeCategory saved = categoryRepository.save(category);
        log.info("更新HTML代码分类: {}", name);
        return saved;
    }
    
    /**
     * 删除分类
     */
    @Transactional
    public void deleteCategory(Long id) {
        HtmlCodeCategory category = categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        // 检查是否有关联的代码片段
        long snippetCount = snippetRepository.countByCategoryId(id);
        if (snippetCount > 0) {
            throw new RuntimeException("该分类下还有代码片段，无法删除");
        }
        
        categoryRepository.delete(category);
        log.info("删除HTML代码分类: {}", category.getName());
    }
    
    // ==================== 代码片段管理 ====================
    
    /**
     * 获取所有代码片段
     */
    @Transactional(readOnly = true)
    public List<HtmlCodeSnippet> getAllSnippets() {
        return snippetRepository.findAllByOrderByCreatedAtDesc();
    }
    
    /**
     * 根据分类获取代码片段
     */
    @Transactional(readOnly = true)
    public List<HtmlCodeSnippet> getSnippetsByCategory(Long categoryId) {
        return snippetRepository.findByCategoryIdOrderBySortOrderAsc(categoryId);
    }
    

    
    /**
     * 搜索代码片段
     */
    @Transactional(readOnly = true)
    public List<HtmlCodeSnippet> searchSnippets(String keyword) {
        return snippetRepository.searchByKeyword(keyword);
    }
    
    /**
     * 根据ID获取代码片段
     */
    @Transactional(readOnly = true)
    public Optional<HtmlCodeSnippet> getSnippetById(Long id) {
        return snippetRepository.findById(id);
    }
    
    /**
     * 创建代码片段
     */
    @Transactional
    public HtmlCodeSnippet createSnippet(String title, String description, String htmlContent,
                                        Long categoryId, String tags) {
        HtmlCodeSnippet snippet = new HtmlCodeSnippet();
        snippet.setTitle(title);
        snippet.setDescription(description);
        snippet.setHtmlContent(htmlContent);
        snippet.setTags(tags);
        snippet.setIsFavorite(false);  // 默认不收藏
        
        // 设置分类
        if (categoryId != null) {
            HtmlCodeCategory category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
            snippet.setCategory(category);
            
            // 设置排序顺序
            Integer maxSortOrder = snippetRepository.findMaxSortOrderByCategoryId(categoryId);
            snippet.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        }
        
        HtmlCodeSnippet saved = snippetRepository.save(snippet);
        log.info("创建HTML代码片段: {}", title);
        return saved;
    }
    
    /**
     * 更新代码片段
     */
    @Transactional
    public HtmlCodeSnippet updateSnippet(Long id, String title, String description, String htmlContent,
                                        Long categoryId, String tags) {
        HtmlCodeSnippet snippet = snippetRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("代码片段不存在"));
        
        snippet.setTitle(title);
        snippet.setDescription(description);
        snippet.setHtmlContent(htmlContent);
        snippet.setTags(tags);
        // 移除收藏功能，保持原有状态
        
        // 更新分类
        if (categoryId != null) {
            HtmlCodeCategory category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
            snippet.setCategory(category);
        } else {
            snippet.setCategory(null);
        }
        
        HtmlCodeSnippet saved = snippetRepository.save(snippet);
        log.info("更新HTML代码片段: {}", title);
        return saved;
    }
    
    /**
     * 删除代码片段
     */
    @Transactional
    public void deleteSnippet(Long id) {
        HtmlCodeSnippet snippet = snippetRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("代码片段不存在"));
        
        snippetRepository.delete(snippet);
        log.info("删除HTML代码片段: {}", snippet.getTitle());
    }
    

}
