# 胜大科技智联管理系统

这是一个基于Spring Boot的智能设备管理系统，集成了Modbus TCP通信、MQTT协议、BI大屏展示、组态设计等功能的综合性工业物联网管理平台。

**版本：8.2**

## 技术栈

- Java 8
- Spring Boot 2.3.12
- Maven 3.5.4
- J2Mod (Modbus TCP库)
- Eclipse Paho (MQTT客户端)
- Bootstrap 5.1.3
- Thymeleaf

## 功能特性

- **设备管理**
  - Modbus TCP设备连接与通信
  - 设备状态监控与数据采集
  - 设备配置与参数管理
- **数据通信**
  - Modbus TCP读写操作
  - MQTT消息发布与订阅
  - 实时数据传输与处理
- **BI大屏展示**
  - 可视化数据大屏设计
  - 多种图表组件支持
  - 实时数据展示与刷新
- **组态设计**
  - 可视化组态界面设计
  - 设备布局与连接配置
  - 动态数据绑定与显示
- **文件管理**
  - 素材文件上传与管理
  - 图片、视频等多媒体支持
  - 文件分类与组织
- **许可证管理**
  - 序列号生成与验证
  - 安装有效期控制
  - 用户授权管理

## 系统要求

- JDK 1.8
- Maven 3.5.4或更高版本
- 支持Modbus TCP的设备
- MQTT Broker（可选）

## 快速开始

1. 克隆项目到本地

2. 配置数据库
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE sdplc;

# 导入数据库结构
mysql -u root -p sdplc < sdplc.sql
```

3. 启动项目

**方法一：使用启动脚本（推荐）**
```bash
# Windows系统
run.bat
```

**方法二：使用Maven命令**
```bash
mvn clean compile
mvn spring-boot:run
```

4. 访问Web界面
```
http://localhost:8080
```

## 配置说明

### application.yml配置

```yaml
# Modbus TCP配置
modbus:
  host: localhost    # Modbus设备IP地址
  port: 502         # Modbus TCP端口
  timeout: 3000     # 超时时间（毫秒）

# MQTT配置
mqtt:
  client-id: modbus-mqtt-web
  connection-timeout: 30
  keep-alive-interval: 60
  clean-session: true
```

## API接口

### Modbus接口

- 读取寄存器
  - GET `/api/modbus/read?address={address}&count={count}`
  - 参数：
    - address: 起始地址
    - count: 读取数量

- 写入寄存器
  - POST `/api/modbus/write?address={address}`
  - 参数：
    - address: 起始地址
    - body: 要写入的值数组

### MQTT接口

- 连接MQTT Broker
  - POST `/api/mqtt/connect?brokerUrl={brokerUrl}`
  - 参数：
    - brokerUrl: MQTT Broker的URL

- 发布消息
  - POST `/api/mqtt/publish?topic={topic}&message={message}`
  - 参数：
    - topic: 发布主题
    - message: 消息内容

## 许可证管理

本项目包含完整的许可证管理系统，位于 `license/` 目录中：

### 🔑 序列号生成器
- **图形界面版本**: `license/SDPLCSerialGenerator.java`（推荐）
- **命令行版本**: `license/SimpleSerialGenerator.java`

### 📖 使用方法
```bash
# 进入license目录
cd license

# 编译并运行图形界面版本
javac -encoding UTF-8 SDPLCSerialGenerator.java
java SDPLCSerialGenerator
```

### 📚 详细文档
- [序列号生成器使用说明](./license/序列号生成器使用说明.md)
- [许可证系统README](./license/README.md)

## 注意事项

1. 确保Modbus设备可以通过TCP/IP访问
2. 使用MQTT功能时，确保有可用的MQTT Broker
3. 所有的寄存器地址都是基于0的
4. 许可证相关功能请参考 `license/` 目录中的文档

## 项目结构

```
sdplc/
├── 📄 核心文件
│   ├── README.md                    # 项目说明
│   ├── pom.xml                      # Maven配置
│   ├── run.bat                      # 启动脚本
│   └── sdplc.sql                    # 数据库脚本
├── 📂 源代码和构建
│   ├── src/                         # 源代码目录
│   │   ├── main/                    # 主要源码
│   │   └── test/                    # 测试代码
│   └── target/                      # 编译输出
├── 📚 文档和系统
│   ├── docs/                        # 项目文档
│   └── license/                     # 许可证管理系统
├── 📁 资源文件
│   └── upload/                      # 上传文件目录
└── 📦 开发归档
    └── archive/                     # 开发过程归档
        ├── html-tests/              # HTML测试文件
        ├── dev-docs/                # 开发文档
        └── scripts/                 # 测试脚本
```

## 开发说明

### 项目清理
项目已进行过结构优化，将开发过程中的临时文件和测试文件归档到 `archive/` 目录：
- **HTML测试文件**: 51个文件归档到 `archive/html-tests/`
- **开发文档**: 105个文件归档到 `archive/dev-docs/`
- **测试脚本**: 11个文件归档到 `archive/scripts/`

### 维护建议
- 日常开发专注于核心文件和目录
- 需要时可查阅archive中的历史文档
- 定期清理和整理archive目录

