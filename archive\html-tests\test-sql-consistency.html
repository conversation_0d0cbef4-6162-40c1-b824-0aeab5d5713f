<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL一致性修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .sql-comparison {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .sql-before, .sql-after {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        
        .sql-before {
            border-left: 4px solid #dc3545;
        }
        
        .sql-after {
            border-left: 4px solid #28a745;
        }
        
        .issue-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .issue-inconsistent {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .issue-fixed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .config-panel {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 4px;
            background: #f8f9fa;
        }
        
        .flow-step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            font-size: 0.75rem;
            font-weight: bold;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-code-square"></i>
                SQL一致性修复验证
            </h2>
            
            <!-- 问题说明 -->
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> 问题描述</h6>
                <p class="mb-0">数据集编辑时，保存的SQL包含DATE_FORMAT函数，但编辑加载时显示的SQL缺少DATE_FORMAT，导致配置与SQL不一致。用户不修改直接保存会产生不同的结果。</p>
            </div>
            
            <!-- 问题对比 -->
            <div class="test-section">
                <h5><i class="bi bi-bug"></i> 问题对比</h5>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-x-circle text-danger"></i> 修复前（不一致）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>保存时的SQL：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                        </div>
                        <div class="col-md-6">
                            <strong>编辑时显示的SQL：</strong>
                            <div class="sql-before">SELECT timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                            <span class="issue-badge issue-inconsistent">缺少DATE_FORMAT</span>
                        </div>
                    </div>
                </div>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-check-circle text-success"></i> 修复后（一致）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>保存时的SQL：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                        </div>
                        <div class="col-md-6">
                            <strong>编辑时显示的SQL：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY timestamp DESC
LIMIT 5</div>
                            <span class="issue-badge issue-fixed">完全一致</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修复流程 -->
            <div class="test-section">
                <h5><i class="bi bi-gear"></i> 修复流程</h5>
                <p>修复后的数据集编辑流程：</p>
                
                <div class="flow-step">
                    <div class="flow-step-number">1</div>
                    <div>
                        <strong>加载数据集</strong><br>
                        <small class="text-muted">从数据库加载数据集信息，包括保存的SQL和配置</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="flow-step-number">2</div>
                    <div>
                        <strong>恢复基础配置</strong><br>
                        <small class="text-muted">恢复表选择、字段角色等基础配置</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="flow-step-number">3</div>
                    <div>
                        <strong>设置原始SQL</strong><br>
                        <small class="text-muted">将保存的SQL设置到编辑器中（可能包含DATE_FORMAT等）</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="flow-step-number">4</div>
                    <div>
                        <strong>恢复高级配置</strong><br>
                        <small class="text-muted">恢复输出限制、日期格式化、聚合配置等</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="flow-step-number">5</div>
                    <div>
                        <strong>重新生成SQL</strong><br>
                        <small class="text-muted">🔧 <strong>新增步骤</strong>：根据恢复的配置重新生成SQL，确保一致性</small>
                    </div>
                </div>
            </div>
            
            <!-- 修复详情 -->
            <div class="test-section">
                <h5><i class="bi bi-code-slash"></i> 修复详情</h5>
                
                <div class="config-panel">
                    <h6>新增方法：regenerateSQLAfterConfigRestore</h6>
                    <div class="sql-after">async regenerateSQLAfterConfigRestore() {
    try {
        console.log('开始重新生成SQL以确保与配置一致');
        
        // 等待配置设置完成
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 检查字段配置器是否可用
        if (window.fieldConfigurator && 
            typeof window.fieldConfigurator.generateSQL === 'function') {
            
            // 检查是否有选中的表和字段配置
            if (window.fieldConfigurator.selectedTable && 
                window.fieldConfigurator.fields && 
                window.fieldConfigurator.fields.length > 0) {
                
                console.log('字段配置器可用，重新生成SQL');
                window.fieldConfigurator.generateSQL();
                console.log('SQL重新生成完成');
            }
        }
    } catch (error) {
        console.error('重新生成SQL失败:', error);
    }
}</div>
                </div>
                
                <div class="config-panel">
                    <h6>调用时机</h6>
                    <p>在 <code>restoreAdvancedConfigurations</code> 方法的最后调用：</p>
                    <div class="sql-after">// 5. 配置恢复完成后，重新生成SQL确保一致性
await this.regenerateSQLAfterConfigRestore();

console.log('高级配置恢复完成');</div>
                </div>
            </div>
            
            <!-- 验证结果 -->
            <div class="test-section">
                <h5><i class="bi bi-check-circle"></i> 验证结果</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="config-panel text-center">
                            <h6>配置一致性</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">SQL与配置完全匹配</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-panel text-center">
                            <h6>编辑体验</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">用户看到的就是实际执行的SQL</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-panel text-center">
                            <h6>结果稳定性</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">不修改直接保存结果一致</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试场景 -->
            <div class="test-section">
                <h5><i class="bi bi-list-check"></i> 测试场景</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-panel">
                            <h6>场景1：日期格式化</h6>
                            <ul class="small">
                                <li>创建数据集，设置日期格式化</li>
                                <li>保存后编辑，检查SQL是否包含DATE_FORMAT</li>
                                <li>不修改直接保存，验证结果一致性</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-panel">
                            <h6>场景2：聚合配置</h6>
                            <ul class="small">
                                <li>创建数据集，启用聚合查询</li>
                                <li>保存后编辑，检查SQL是否包含聚合函数</li>
                                <li>验证窗口函数SQL的正确性</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-panel">
                            <h6>场景3：输出限制</h6>
                            <ul class="small">
                                <li>设置输出限制和智能排序</li>
                                <li>编辑时检查ORDER BY和LIMIT的正确性</li>
                                <li>验证最新数据获取逻辑</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-panel">
                            <h6>场景4：复合配置</h6>
                            <ul class="small">
                                <li>同时设置多种配置</li>
                                <li>编辑时验证所有配置的SQL体现</li>
                                <li>确保复杂SQL的正确生成</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
