<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置恢复功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .config-demo {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .comparison-table {
            width: 100%;
            margin-top: 1rem;
        }
        
        .comparison-table th {
            background: #e9ecef;
            padding: 0.75rem;
            border: 1px solid #dee2e6;
        }
        
        .comparison-table td {
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-arrow-clockwise"></i>
                配置恢复功能测试
            </h2>
            
            <!-- 测试说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 测试说明</h6>
                <p class="mb-0">此页面用于测试数据集编辑时的配置恢复功能，包括输出限制、日期格式化、聚合配置的正确恢复显示。</p>
            </div>
            
            <!-- 恢复策略说明 -->
            <div class="test-section">
                <h5><i class="bi bi-diagram-3"></i> 恢复策略</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="config-demo">
                            <h6><i class="bi bi-1-circle text-primary"></i> 优先策略</h6>
                            <p><strong>从数据库字段恢复</strong></p>
                            <ul class="small">
                                <li>defaultOutputLimit → 输出限制</li>
                                <li>transformConfig.advancedConfig → 日期格式化和聚合配置</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-demo">
                            <h6><i class="bi bi-2-circle text-success"></i> 备选策略</h6>
                            <p><strong>从SQL解析恢复</strong></p>
                            <ul class="small">
                                <li>LIMIT子句 → 输出限制</li>
                                <li>DATE_FORMAT函数 → 日期格式化</li>
                                <li>聚合函数+GROUP BY → 聚合配置</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-demo">
                            <h6><i class="bi bi-3-circle text-warning"></i> 兼容性</h6>
                            <p><strong>向后兼容</strong></p>
                            <ul class="small">
                                <li>支持旧版本数据集</li>
                                <li>自动解析现有SQL</li>
                                <li>渐进式配置迁移</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 模拟数据集配置 -->
            <div class="test-section">
                <h5><i class="bi bi-database-gear"></i> 模拟数据集配置</h5>
                
                <!-- 配置示例1：完整配置 -->
                <div class="config-demo">
                    <h6>示例1：完整配置数据集</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>数据库存储：</strong>
                            <div class="code-block">
{
  "defaultOutputLimit": 100,
  "transformConfig": {
    "labelField": "name",
    "valueField": "latest_value",
    "advancedConfig": {
      "dateFormatting": {
        "dateField": "timestamp",
        "dateFormat": "%Y-%m-%d %H:%i:%s"
      },
      "aggregation": {
        "enabled": true,
        "type": "MAX",
        "timeField": "timestamp"
      }
    }
  },
  "queryConfig": {
    "sql": "SELECT name, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:%s') as timestamp, MAX(latest_value) as latest_value FROM data_history GROUP BY name, timestamp LIMIT 100"
  }
}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <strong>期望恢复结果：</strong>
                            <table class="comparison-table">
                                <tr>
                                    <th>配置项</th>
                                    <th>恢复值</th>
                                </tr>
                                <tr>
                                    <td>输出限制</td>
                                    <td>100</td>
                                </tr>
                                <tr>
                                    <td>日期字段</td>
                                    <td>timestamp</td>
                                </tr>
                                <tr>
                                    <td>日期格式</td>
                                    <td>%Y-%m-%d %H:%i:%s</td>
                                </tr>
                                <tr>
                                    <td>聚合启用</td>
                                    <td>true</td>
                                </tr>
                                <tr>
                                    <td>聚合类型</td>
                                    <td>MAX</td>
                                </tr>
                                <tr>
                                    <td>时间字段</td>
                                    <td>timestamp</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 配置示例2：SQL解析 -->
                <div class="config-demo">
                    <h6>示例2：仅SQL配置（需要解析）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>数据库存储：</strong>
                            <div class="code-block">
{
  "defaultOutputLimit": null,
  "transformConfig": {
    "labelField": "name",
    "valueField": "value"
  },
  "queryConfig": {
    "sql": "SELECT name, DATE_FORMAT(created_at, '%m-%d %H:%i') as created_at, AVG(value) as value FROM data_items GROUP BY name LIMIT 50"
  }
}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <strong>期望解析结果：</strong>
                            <table class="comparison-table">
                                <tr>
                                    <th>配置项</th>
                                    <th>解析值</th>
                                </tr>
                                <tr>
                                    <td>输出限制</td>
                                    <td>50 (从LIMIT解析)</td>
                                </tr>
                                <tr>
                                    <td>日期字段</td>
                                    <td>created_at (从DATE_FORMAT解析)</td>
                                </tr>
                                <tr>
                                    <td>日期格式</td>
                                    <td>%m-%d %H:%i (从DATE_FORMAT解析)</td>
                                </tr>
                                <tr>
                                    <td>聚合启用</td>
                                    <td>true (从AVG+GROUP BY解析)</td>
                                </tr>
                                <tr>
                                    <td>聚合类型</td>
                                    <td>AVG (从AVG函数解析)</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SQL解析测试 -->
            <div class="test-section">
                <h5><i class="bi bi-code-slash"></i> SQL解析测试</h5>
                <div class="mb-3">
                    <label class="form-label">测试SQL语句：</label>
                    <textarea class="form-control" id="testSQL" rows="3">SELECT name, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:%s') as timestamp, MAX(latest_value) as latest_value FROM data_history GROUP BY name LIMIT 100</textarea>
                </div>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-primary" onclick="testSQLParsing()">
                        <i class="bi bi-play"></i> 测试SQL解析
                    </button>
                </div>
                
                <div id="sqlParseResult" class="test-result" style="display: none;"></div>
            </div>
            
            <!-- 配置恢复模拟 -->
            <div class="test-section">
                <h5><i class="bi bi-arrow-repeat"></i> 配置恢复模拟</h5>
                <p>模拟数据集编辑时的配置恢复过程：</p>
                
                <div class="row mb-4">
                    <!-- 输出限制配置 -->
                    <div class="col-md-4">
                        <div class="config-demo">
                            <label class="form-label fw-bold">输出限制</label>
                            <div class="mb-2">
                                <input type="number" class="form-control form-control-sm" id="outputLimit" 
                                       placeholder="最大记录数" min="1" max="10000">
                                <div class="form-text small">限制查询结果的最大记录数，提高查询性能</div>
                            </div>
                        </div>
                    </div>

                    <!-- 日期格式化配置 -->
                    <div class="col-md-4">
                        <div class="config-demo">
                            <label class="form-label fw-bold">日期格式化</label>
                            <div class="mb-2">
                                <select class="form-select form-select-sm" id="dateField">
                                    <option value="">选择日期字段</option>
                                    <option value="timestamp">timestamp</option>
                                    <option value="created_at">created_at</option>
                                    <option value="updated_at">updated_at</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <select class="form-select form-select-sm" id="dateFormat">
                                    <option value="">不格式化</option>
                                    <option value="%Y-%m-%d %H:%i:%s">年月日时分秒</option>
                                    <option value="%m-%d %H:%i:%s">月日时分秒</option>
                                    <option value="%m-%d %H:%i">月日时分</option>
                                    <option value="%m-%d">月日</option>
                                    <option value="%H:%i:%s">时分秒</option>
                                    <option value="%H:%i">时分</option>
                                </select>
                                <div class="form-text small">对日期字段进行SQL格式化</div>
                            </div>
                        </div>
                    </div>

                    <!-- 聚合配置 -->
                    <div class="col-md-4">
                        <div class="config-demo">
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="aggregation-config">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAggregation">
                                    <label class="form-check-label" for="enableAggregation">
                                        启用聚合查询
                                    </label>
                                </div>
                                <div id="aggregationOptions" style="display: none;">
                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" id="aggregationType">
                                            <option value="MAX">最大值</option>
                                            <option value="MIN">最小值</option>
                                            <option value="AVG">平均值</option>
                                            <option value="SUM">求和</option>
                                            <option value="COUNT">计数</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" id="timeField">
                                            <option value="">选择时间字段</option>
                                            <option value="timestamp">timestamp</option>
                                            <option value="created_at">created_at</option>
                                        </select>
                                        <div class="form-text small">用于排序取最新记录</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-success me-2" onclick="testConfigRestore()">
                        <i class="bi bi-arrow-clockwise"></i> 模拟配置恢复
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearConfig()">
                        <i class="bi bi-x-circle"></i> 清空配置
                    </button>
                </div>
                
                <div id="configRestoreResult" class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟SQL解析功能
        function testSQLParsing() {
            const sql = document.getElementById('testSQL').value;
            const result = parseSQLConfiguration(sql);
            
            const resultDiv = document.getElementById('sqlParseResult');
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <h6><i class="bi bi-check-circle"></i> SQL解析结果</h6>
                <table class="comparison-table">
                    <tr><th>配置项</th><th>解析值</th><th>正则表达式</th></tr>
                    <tr><td>输出限制</td><td>${result.outputLimit || '未找到'}</td><td>LIMIT\\s+(\\d+)</td></tr>
                    <tr><td>日期字段</td><td>${result.dateField || '未找到'}</td><td rowspan="2">DATE_FORMAT\\s*\\(\\s*(\\w+)\\s*,\\s*'([^']+)'\\s*\\)</td></tr>
                    <tr><td>日期格式</td><td>${result.dateFormat || '未找到'}</td></tr>
                    <tr><td>聚合类型</td><td>${result.aggregationType || '未找到'}</td><td>(MAX|MIN|AVG|SUM|COUNT)\\s*\\(</td></tr>
                    <tr><td>GROUP BY</td><td>${result.hasGroupBy ? '是' : '否'}</td><td>GROUP\\s+BY</td></tr>
                </table>
            `;
            resultDiv.style.display = 'block';
        }
        
        function parseSQLConfiguration(sql) {
            const result = {};
            
            // 解析LIMIT
            const limitMatch = sql.match(/LIMIT\s+(\d+)/i);
            if (limitMatch) result.outputLimit = limitMatch[1];
            
            // 解析DATE_FORMAT
            const dateFormatMatch = sql.match(/DATE_FORMAT\s*\(\s*(\w+)\s*,\s*'([^']+)'\s*\)/i);
            if (dateFormatMatch) {
                result.dateField = dateFormatMatch[1];
                result.dateFormat = dateFormatMatch[2];
            }
            
            // 解析聚合函数
            const aggregationMatch = sql.match(/(MAX|MIN|AVG|SUM|COUNT)\s*\(/i);
            if (aggregationMatch) result.aggregationType = aggregationMatch[1];
            
            // 检查GROUP BY
            result.hasGroupBy = /GROUP\s+BY/i.test(sql);
            
            return result;
        }
        
        // 模拟配置恢复
        function testConfigRestore() {
            // 模拟从数据库恢复的配置
            const mockConfig = {
                defaultOutputLimit: 100,
                advancedConfig: {
                    dateFormatting: {
                        dateField: 'timestamp',
                        dateFormat: '%Y-%m-%d %H:%i:%s'
                    },
                    aggregation: {
                        enabled: true,
                        type: 'MAX',
                        timeField: 'timestamp'
                    }
                }
            };
            
            // 恢复配置到UI
            restoreConfiguration(mockConfig);
            
            const resultDiv = document.getElementById('configRestoreResult');
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <h6><i class="bi bi-check-circle"></i> 配置恢复成功</h6>
                <p>已成功恢复以下配置：</p>
                <ul>
                    <li>输出限制: ${mockConfig.defaultOutputLimit}</li>
                    <li>日期字段: ${mockConfig.advancedConfig.dateFormatting.dateField}</li>
                    <li>日期格式: ${mockConfig.advancedConfig.dateFormatting.dateFormat}</li>
                    <li>聚合启用: ${mockConfig.advancedConfig.aggregation.enabled}</li>
                    <li>聚合类型: ${mockConfig.advancedConfig.aggregation.type}</li>
                    <li>时间字段: ${mockConfig.advancedConfig.aggregation.timeField}</li>
                </ul>
            `;
            resultDiv.style.display = 'block';
        }
        
        function restoreConfiguration(config) {
            // 恢复输出限制
            if (config.defaultOutputLimit) {
                document.getElementById('outputLimit').value = config.defaultOutputLimit;
            }
            
            // 恢复日期格式化配置
            if (config.advancedConfig && config.advancedConfig.dateFormatting) {
                const dateFormatting = config.advancedConfig.dateFormatting;
                if (dateFormatting.dateField) {
                    document.getElementById('dateField').value = dateFormatting.dateField;
                }
                if (dateFormatting.dateFormat) {
                    document.getElementById('dateFormat').value = dateFormatting.dateFormat;
                }
            }
            
            // 恢复聚合配置
            if (config.advancedConfig && config.advancedConfig.aggregation) {
                const aggregation = config.advancedConfig.aggregation;
                const enableAggregation = document.getElementById('enableAggregation');
                enableAggregation.checked = aggregation.enabled || false;
                
                if (aggregation.enabled) {
                    document.getElementById('aggregationOptions').style.display = 'block';
                    if (aggregation.type) {
                        document.getElementById('aggregationType').value = aggregation.type;
                    }
                    if (aggregation.timeField) {
                        document.getElementById('timeField').value = aggregation.timeField;
                    }
                }
            }
        }
        
        function clearConfig() {
            document.getElementById('outputLimit').value = '';
            document.getElementById('dateField').value = '';
            document.getElementById('dateFormat').value = '';
            document.getElementById('enableAggregation').checked = false;
            document.getElementById('aggregationOptions').style.display = 'none';
            document.getElementById('aggregationType').value = 'MAX';
            document.getElementById('timeField').value = '';
            
            document.getElementById('configRestoreResult').style.display = 'none';
        }
        
        // 聚合选项显示控制
        document.getElementById('enableAggregation').addEventListener('change', function() {
            const aggregationOptions = document.getElementById('aggregationOptions');
            aggregationOptions.style.display = this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>
