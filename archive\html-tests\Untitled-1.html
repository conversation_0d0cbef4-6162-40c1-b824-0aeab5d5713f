<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粒子滑块效果 | Particles.js</title>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 10;
        }
        
        header {
            text-align: center;
            padding: 40px 0;
        }
        
        h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            background: linear-gradient(to right, #ff7e5f, #feb47b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            font-size: 1.4rem;
            color: #e0e0ff;
            max-width: 700px;
            margin: 0 auto 30px;
            line-height: 1.6;
        }
        
        .slider-container {
            background: rgba(25, 25, 50, 0.7);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }
        
        .slider {
            position: relative;
            height: 400px;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        
        .slide.active {
            opacity: 1;
        }
        
        .slide-content {
            text-align: center;
            padding: 30px;
            max-width: 600px;
        }
        
        .slide h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .slide p {
            font-size: 1.2rem;
            line-height: 1.7;
            margin-bottom: 25px;
            color: #e0e0ff;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(to right, #ff7e5f, #feb47b);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 126, 95, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 126, 95, 0.5);
        }
        
        .slider-nav {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .nav-dot {
            width: 14px;
            height: 14px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            margin: 0 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-dot.active {
            background: #ff7e5f;
            transform: scale(1.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 50px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.08);
        }
        
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: #ff7e5f;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .feature-card p {
            color: #b0b0d0;
            line-height: 1.6;
        }
        
        footer {
            text-align: center;
            padding: 30px 0;
            margin-top: 40px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #a0a0c0;
        }
        
        .social-icons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .social-icon {
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            color: #ff7e5f;
            font-size: 1.3rem;
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            background: #ff7e5f;
            color: white;
            transform: translateY(-5px);
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .slider {
                height: 350px;
            }
            
            .slide h2 {
                font-size: 2rem;
            }
            
            .slide p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 粒子背景容器 -->
    <div id="particles-js"></div>
    
    <div class="container">
        <header>
            <h1>粒子滑块效果</h1>
            <p class="subtitle">使用Particles.js创建的动态粒子背景与现代化滑块结合，为您的网站带来引人入胜的视觉体验</p>
        </header>
        
        <div class="slider-container">
            <div class="slider">
                <div class="slide active">
                    <div class="slide-content">
                        <h2>动态粒子背景</h2>
                        <p>使用Particles.js创建流畅而优雅的粒子动画背景，为您的网站增添深度和动感。可自定义粒子数量、大小、颜色和运动方式。</p>
                        <a href="#" class="btn">了解更多</a>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h2>响应式设计</h2>
                        <p>我们的滑块和粒子效果完全响应式，可在各种设备上完美呈现。从手机到桌面电脑，始终保持最佳视觉效果。</p>
                        <a href="#" class="btn">查看示例</a>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h2>流畅的动画过渡</h2>
                        <p>滑块切换时采用平滑的淡入淡出效果，确保用户体验流畅自然。粒子动画始终保持60fps，提供丝滑的视觉体验。</p>
                        <a href="#" class="btn">开始使用</a>
                    </div>
                </div>
            </div>
            <div class="slider-nav">
                <div class="nav-dot active"></div>
                <div class="nav-dot"></div>
                <div class="nav-dot"></div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>高性能</h3>
                <p>优化后的粒子动画确保高性能，即使在移动设备上也能流畅运行，不影响页面加载速度。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>高度可定制</h3>
                <p>提供多种配置选项，可轻松调整粒子数量、颜色、形状、大小和运动行为以满足您的设计需求。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <h3>简单集成</h3>
                <p>只需几行代码即可将粒子效果添加到任何网站，无需复杂的设置或依赖项。</p>
            </div>
        </div>
        
        <footer>
            <div class="social-icons">
                <a href="#" class="social-icon">📱</a>
                <a href="#" class="social-icon">💻</a>
                <a href="#" class="social-icon">📷</a>
                <a href="#" class="social-icon">🔗</a>
            </div>
            <p>© 2023 粒子滑块效果 | 使用Particles.js创建</p>
        </footer>
    </div>

    <script>
        // 初始化粒子背景
        particlesJS('particles-js', {
            particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.5, random: true },
                size: { value: 3, random: true },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: "#ffffff",
                    opacity: 0.3,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: "none",
                    random: true,
                    straight: false,
                    out_mode: "out",
                    bounce: false
                }
            },
            interactivity: {
                detect_on: "canvas",
                events: {
                    onhover: { enable: true, mode: "repulse" },
                    onclick: { enable: true, mode: "push" },
                    resize: true
                }
            },
            retina_detect: true
        });
        
        // 滑块功能
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.nav-dot');
        let currentSlide = 0;
        
        function showSlide(index) {
            // 隐藏所有幻灯片
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));
            
            // 显示当前幻灯片
            slides[index].classList.add('active');
            dots[index].classList.add('active');
            currentSlide = index;
        }
        
        // 点击导航点切换幻灯片
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
            });
        });
        
        // 自动播放幻灯片
        setInterval(() => {
            let nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 5000);
    </script>
</body>
</html>