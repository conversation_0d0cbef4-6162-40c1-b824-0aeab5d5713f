# HTML组件开发快速参考卡片

## 🚫 绝对禁止
```css
/* ❌ 绝对不要使用 */
width: 50vw;                    /* viewport单位 */
height: 30vh;                   /* viewport单位 */
font-size: clamp(1rem, 4vw, 2rem);  /* 包含vw的clamp */
max-width: 300px;               /* 最大宽度限制 */
max-height: 200px;              /* 最大高度限制 */
```

## ✅ 必须使用
```css
/* ✅ 标准写法 */
width: 100%;                    /* 宽度100% */
height: 100%;                   /* 高度100% */
font-size: 4vh;                 /* 字体基于容器高度 */
min-width: 80px;                /* 最小宽度保证可见 */
min-height: 40px;               /* 最小高度保证可见 */
```

## 📐 标准模板
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            background: transparent; 
            width: 100%; 
            height: 100vh; 
            overflow: hidden; 
        }
        .container { 
            width: 100%; 
            height: 100%; 
            padding: 3%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .main-element { 
            width: 100%; 
            height: 100%; 
            font-size: 4vh; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-element">
            <!-- 内容 -->
        </div>
    </div>
</body>
</html>
```

## 📱 响应式断点
```css
@media (max-height: 50px) { font-size: 2vh; }
@media (min-height: 50px) and (max-height: 100px) { font-size: 3vh; }
@media (min-height: 100px) and (max-height: 200px) { font-size: 4vh; }
@media (min-height: 200px) { font-size: 5vh; }
@media (max-width: 80px) or (max-height: 40px) { font-size: 10px; }
```

## 🎯 装饰元素
```css
.decoration {
    width: 1vh;                 /* 基于容器高度 */
    height: 1vh;
    min-width: 4px;             /* 最小尺寸 */
    min-height: 4px;
    max-width: 12px;            /* 最大尺寸 */
    max-height: 12px;
    position: absolute;
    top: 1vh;                   /* 位置也用vh */
    left: 1vh;
}
```

## ✅ 验证清单
- [ ] 没有vw单位（字体vh除外）
- [ ] 主元素100%宽高
- [ ] 没有max-width/max-height
- [ ] body高度100vh
- [ ] 设置min-width/min-height
- [ ] 测试小容器可见
- [ ] 测试大容器填满
- [ ] 响应式缩放正常

## 🔧 常用单位
| 用途 | 推荐单位 | 示例 |
|------|----------|------|
| 宽度 | % | `width: 100%` |
| 高度 | % | `height: 100%` |
| 字体 | vh | `font-size: 4vh` |
| 边框 | em/px | `border: 0.1em solid` |
| 内边距 | % | `padding: 3%` |
| 装饰尺寸 | vh | `width: 1vh` |
| 最小尺寸 | px | `min-width: 80px` |

## 🎨 组件类型速查

### 按钮
```css
.button {
    width: 100%; height: 100%;
    font-size: 4vh;
    display: flex; align-items: center; justify-content: center;
    border-width: 0.1em;
}
```

### 装饰
```css
.decoration { width: 100%; height: 100%; position: relative; }
.decoration-item { position: absolute; width: 8%; height: 8%; }
```

### 边框
```css
.border-container { width: 100%; height: 100%; padding: 3%; }
.border-element { width: 100%; height: 100%; border: 4px solid; }
```

---
**记住：100%填满 + vh字体 + 无最大限制 = 完美响应式！**
