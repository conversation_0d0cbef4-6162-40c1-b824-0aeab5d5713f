<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期格式化功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .config-section {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-calendar-date"></i>
                日期格式化功能测试
            </h2>
            
            <!-- 测试说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 测试说明</h6>
                <p class="mb-0">此页面用于测试数据集创建过程中步骤2的日期格式化功能。该功能允许在SQL查询中使用DATE_FORMAT函数对日期字段进行格式化。</p>
            </div>
            
            <!-- 模拟UI配置 -->
            <div class="test-section">
                <h5><i class="bi bi-sliders"></i> 配置测试</h5>
                <div class="row">
                    <!-- 输出限制配置 -->
                    <div class="col-md-4">
                        <div class="config-section">
                            <label class="form-label fw-bold">输出限制</label>
                            <div class="mb-2">
                                <input type="number" class="form-control form-control-sm" id="outputLimit" 
                                       placeholder="最大记录数" min="1" max="10000" value="100">
                                <div class="form-text small">限制查询结果的最大记录数，提高查询性能</div>
                            </div>
                        </div>
                    </div>

                    <!-- 日期格式化配置 -->
                    <div class="col-md-4">
                        <div class="config-section">
                            <label class="form-label fw-bold">日期格式化</label>
                            <div class="mb-2">
                                <select class="form-select form-select-sm" id="dateField">
                                    <option value="">选择日期字段</option>
                                    <option value="created_at">created_at (DATETIME)</option>
                                    <option value="updated_at">updated_at (TIMESTAMP)</option>
                                    <option value="timestamp">timestamp (DATETIME)</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <select class="form-select form-select-sm" id="dateFormat">
                                    <option value="">不格式化</option>
                                    <option value="%Y-%m-%d %H:%i:%s">年月日时分秒 (2025-07-22 17:35:55)</option>
                                    <option value="%m-%d %H:%i:%s">月日时分秒 (07-22 17:35:55)</option>
                                    <option value="%m-%d %H:%i">月日时分 (07-22 17:35)</option>
                                    <option value="%m-%d">月日 (07-22)</option>
                                    <option value="%H:%i:%s">时分秒 (17:35:55)</option>
                                    <option value="%H:%i">时分 (17:35)</option>
                                </select>
                                <div class="form-text small">对日期字段进行SQL格式化</div>
                            </div>
                        </div>
                    </div>

                    <!-- 聚合配置 -->
                    <div class="col-md-4">
                        <div class="config-section">
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="aggregation-config">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAggregation">
                                    <label class="form-check-label" for="enableAggregation">
                                        启用聚合查询
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SQL生成测试 -->
            <div class="test-section">
                <h5><i class="bi bi-code-square"></i> SQL生成测试</h5>
                <div class="mb-3">
                    <label class="form-label">测试表名:</label>
                    <input type="text" class="form-control" id="testTableName" value="data_history">
                </div>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-primary me-2" onclick="testSimpleSQL()">
                        <i class="bi bi-play"></i> 测试简单查询
                    </button>
                    <button type="button" class="btn btn-success me-2" onclick="testAggregationSQL()">
                        <i class="bi bi-bar-chart"></i> 测试聚合查询
                    </button>
                    <button type="button" class="btn btn-info" onclick="testSelectAllSQL()">
                        <i class="bi bi-asterisk"></i> 测试SELECT *
                    </button>
                </div>
                
                <div class="code-block" id="sqlOutput">点击上方按钮生成SQL...</div>
                <div id="sqlTestResult" class="test-result" style="display: none;"></div>
            </div>
            
            <!-- 格式示例 -->
            <div class="test-section">
                <h5><i class="bi bi-calendar-check"></i> 格式示例</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>格式选项</th>
                                <th>MySQL格式</th>
                                <th>示例输出</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>年月日时分秒</td>
                                <td>%Y-%m-%d %H:%i:%s</td>
                                <td>2025-07-22 17:35:55</td>
                                <td>完整的日期时间格式</td>
                            </tr>
                            <tr>
                                <td>月日时分秒</td>
                                <td>%m-%d %H:%i:%s</td>
                                <td>07-22 17:35:55</td>
                                <td>不显示年份</td>
                            </tr>
                            <tr>
                                <td>月日时分</td>
                                <td>%m-%d %H:%i</td>
                                <td>07-22 17:35</td>
                                <td>不显示年份和秒</td>
                            </tr>
                            <tr>
                                <td>月日</td>
                                <td>%m-%d</td>
                                <td>07-22</td>
                                <td>仅显示月日</td>
                            </tr>
                            <tr>
                                <td>时分秒</td>
                                <td>%H:%i:%s</td>
                                <td>17:35:55</td>
                                <td>仅显示时间</td>
                            </tr>
                            <tr>
                                <td>时分</td>
                                <td>%H:%i</td>
                                <td>17:35</td>
                                <td>仅显示小时分钟</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟SQL生成器
        class TestDateFormatGenerator {
            constructor() {
                this.fields = [
                    { name: 'id', type: 'INT' },
                    { name: 'name', type: 'VARCHAR' },
                    { name: 'value', type: 'INT' },
                    { name: 'created_at', type: 'DATETIME' },
                    { name: 'updated_at', type: 'TIMESTAMP' },
                    { name: 'timestamp', type: 'DATETIME' }
                ];
            }
            
            formatFieldForSelect(fieldName) {
                const dateField = document.getElementById('dateField')?.value;
                const dateFormat = document.getElementById('dateFormat')?.value;
                
                if (dateField && dateFormat && fieldName === dateField) {
                    return `DATE_FORMAT(${fieldName}, '${dateFormat}') as ${fieldName}`;
                }
                
                return fieldName;
            }
            
            applyDateFormatting(selectClause) {
                const dateField = document.getElementById('dateField')?.value;
                const dateFormat = document.getElementById('dateFormat')?.value;
                
                if (!dateField || !dateFormat || selectClause === '*') {
                    return selectClause;
                }
                
                if (selectClause === '*') {
                    const allFields = this.fields.map(field => {
                        if (field.name === dateField) {
                            return `DATE_FORMAT(${field.name}, '${dateFormat}') as ${field.name}`;
                        }
                        return field.name;
                    });
                    return allFields.join(', ');
                }
                
                return selectClause;
            }
            
            addLimitClause(sql) {
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement) {
                    const outputLimit = parseInt(outputLimitElement.value);
                    if (outputLimit && outputLimit > 0) {
                        if (!sql.toLowerCase().includes('limit')) {
                            sql += `\nLIMIT ${outputLimit}`;
                        }
                    }
                }
                return sql;
            }
            
            generateSimpleSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_history';
                const selectClause = this.applyDateFormatting('name, value');
                let sql = `SELECT ${selectClause}\nFROM ${tableName}`;
                return this.addLimitClause(sql);
            }
            
            generateSelectAllSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_history';
                const selectClause = this.applyDateFormatting('*');
                let sql = `SELECT ${selectClause}\nFROM ${tableName}`;
                return this.addLimitClause(sql);
            }
            
            generateAggregationSQL() {
                const tableName = document.getElementById('testTableName').value || 'data_history';
                const labelField = 'name';
                const valueField = 'value';
                
                const formattedLabelField = this.formatFieldForSelect(labelField);
                const formattedValueField = `MAX(${valueField}) as ${valueField}`;
                
                const dateField = document.getElementById('dateField')?.value;
                const dateFormat = document.getElementById('dateFormat')?.value;
                
                let selectFields = [formattedLabelField, formattedValueField];
                
                if (dateField && dateFormat && dateField !== labelField && dateField !== valueField) {
                    const formattedDateField = `DATE_FORMAT(${dateField}, '${dateFormat}') as ${dateField}`;
                    selectFields.push(formattedDateField);
                }
                
                let sql = `SELECT ${selectFields.join(', ')}\nFROM ${tableName}\nGROUP BY ${labelField}`;
                
                if (dateField && dateField !== labelField && dateField !== valueField) {
                    sql += `, ${dateField}`;
                }
                
                return this.addLimitClause(sql);
            }
        }
        
        const generator = new TestDateFormatGenerator();
        
        // 测试函数
        function testSimpleSQL() {
            const sql = generator.generateSimpleSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('简单查询', sql);
        }
        
        function testSelectAllSQL() {
            const sql = generator.generateSelectAllSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('SELECT *查询', sql);
        }
        
        function testAggregationSQL() {
            const sql = generator.generateAggregationSQL();
            document.getElementById('sqlOutput').textContent = sql;
            validateSQL('聚合查询', sql);
        }
        
        function validateSQL(type, sql) {
            const dateField = document.getElementById('dateField').value;
            const dateFormat = document.getElementById('dateFormat').value;
            const outputLimit = document.getElementById('outputLimit').value;
            
            const hasDateFormat = dateField && dateFormat && sql.includes(`DATE_FORMAT(${dateField}, '${dateFormat}')`);
            const hasLimit = sql.toLowerCase().includes('limit');
            const resultDiv = document.getElementById('sqlTestResult');
            
            let messages = [];
            let isSuccess = true;
            
            // 检查日期格式化
            if (dateField && dateFormat) {
                if (hasDateFormat) {
                    messages.push(`✓ 日期格式化正确应用: ${dateField} -> ${dateFormat}`);
                } else {
                    messages.push(`✗ 日期格式化未正确应用`);
                    isSuccess = false;
                }
            } else {
                messages.push(`- 未配置日期格式化`);
            }
            
            // 检查输出限制
            if (outputLimit) {
                if (hasLimit) {
                    messages.push(`✓ 输出限制正确应用: LIMIT ${outputLimit}`);
                } else {
                    messages.push(`✗ 输出限制未正确应用`);
                    isSuccess = false;
                }
            } else {
                messages.push(`- 未配置输出限制`);
            }
            
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h6><i class="bi bi-${isSuccess ? 'check-circle' : 'x-circle'}"></i> ${type}测试结果</h6>
                <ul class="mb-0">
                    ${messages.map(msg => `<li>${msg}</li>`).join('')}
                </ul>
            `;
            resultDiv.style.display = 'block';
        }
        
        // 事件监听
        document.getElementById('dateField').addEventListener('change', () => {
            const currentSQL = document.getElementById('sqlOutput').textContent;
            if (currentSQL && currentSQL !== '点击上方按钮生成SQL...') {
                testSimpleSQL(); // 重新生成当前类型的SQL
            }
        });
        
        document.getElementById('dateFormat').addEventListener('change', () => {
            const currentSQL = document.getElementById('sqlOutput').textContent;
            if (currentSQL && currentSQL !== '点击上方按钮生成SQL...') {
                testSimpleSQL(); // 重新生成当前类型的SQL
            }
        });
        
        document.getElementById('outputLimit').addEventListener('input', () => {
            const currentSQL = document.getElementById('sqlOutput').textContent;
            if (currentSQL && currentSQL !== '点击上方按钮生成SQL...') {
                testSimpleSQL(); // 重新生成当前类型的SQL
            }
        });
    </script>
</body>
</html>
