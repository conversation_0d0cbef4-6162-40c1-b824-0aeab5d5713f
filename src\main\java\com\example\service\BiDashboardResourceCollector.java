package com.example.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * 大屏资源文件收集服务
 * 用于收集和复制大屏中引用的实际资源文件
 */
@Service
@Slf4j
public class BiDashboardResourceCollector {

    @Value("${upload.image.path}")
    private String imagePath;

    @Value("${upload.material.path}")
    private String materialPath;

    @Value("${upload.video.path}")
    private String videoPath;

    @Value("${server.external-url}")
    private String serverUrl;

    /**
     * 收集所有资源文件到临时目录
     */
    public Map<String, Object> collectResources(List<String> imageUrls, List<String> materialUrls, 
                                              List<String> videoUrls, Path tempDir) {
        log.info("开始收集资源文件到临时目录: {}", tempDir);

        Map<String, Object> result = new HashMap<>();
        List<String> collectedFiles = new ArrayList<>();
        List<String> missingFiles = new ArrayList<>();
        Map<String, String> urlMapping = new HashMap<>();

        try {
            // 创建子目录
            Path imagesDir = tempDir.resolve("resources/images");
            Path materialsDir = tempDir.resolve("resources/materials");
            Path videosDir = tempDir.resolve("resources/videos");

            Files.createDirectories(imagesDir);
            Files.createDirectories(materialsDir);
            Files.createDirectories(videosDir);

            // 收集图片文件
            collectFilesByType(imageUrls, imagePath, imagesDir, "images", 
                             collectedFiles, missingFiles, urlMapping);

            // 收集素材文件
            collectFilesByType(materialUrls, materialPath, materialsDir, "materials", 
                             collectedFiles, missingFiles, urlMapping);

            // 收集视频文件
            collectFilesByType(videoUrls, videoPath, videosDir, "videos", 
                             collectedFiles, missingFiles, urlMapping);

            result.put("collectedFiles", collectedFiles);
            result.put("missingFiles", missingFiles);
            result.put("urlMapping", urlMapping);
            result.put("totalCollected", collectedFiles.size());
            result.put("totalMissing", missingFiles.size());

            log.info("资源收集完成，成功: {}, 缺失: {}", collectedFiles.size(), missingFiles.size());

            return result;

        } catch (Exception e) {
            log.error("收集资源文件失败", e);
            throw new RuntimeException("资源收集失败: " + e.getMessage());
        }
    }

    /**
     * 按类型收集文件
     */
    private void collectFilesByType(List<String> urls, String sourcePath, Path targetDir, 
                                  String urlType, List<String> collectedFiles, 
                                  List<String> missingFiles, Map<String, String> urlMapping) {
        for (String url : urls) {
            try {
                String filename = extractFilenameFromUrl(url);
                if (filename == null) {
                    log.warn("无法从URL提取文件名: {}", url);
                    missingFiles.add(url);
                    continue;
                }

                Path sourceFile = getSourceFilePath(sourcePath, filename);
                if (!Files.exists(sourceFile)) {
                    log.warn("源文件不存在: {}", sourceFile);
                    missingFiles.add(url);
                    continue;
                }

                Path targetFile = targetDir.resolve(filename);
                Files.copy(sourceFile, targetFile, StandardCopyOption.REPLACE_EXISTING);

                collectedFiles.add(targetFile.toString());
                urlMapping.put(url, "/" + urlType + "/" + filename);

                log.debug("成功复制文件: {} -> {}", sourceFile, targetFile);

            } catch (Exception e) {
                log.error("复制文件失败，URL: {}", url, e);
                missingFiles.add(url);
            }
        }
    }

    /**
     * 从URL提取文件名
     */
    private String extractFilenameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            // 移除查询参数
            int queryIndex = url.indexOf('?');
            if (queryIndex > 0) {
                url = url.substring(0, queryIndex);
            }

            // 提取最后一个斜杠后的内容
            int lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex >= 0 && lastSlashIndex < url.length() - 1) {
                return url.substring(lastSlashIndex + 1);
            }

            return null;

        } catch (Exception e) {
            log.warn("提取文件名失败，URL: {}", url, e);
            return null;
        }
    }

    /**
     * 获取源文件路径
     */
    private Path getSourceFilePath(String basePath, String filename) {
        Path baseDir;
        if (Paths.get(basePath).isAbsolute()) {
            baseDir = Paths.get(basePath);
        } else {
            String userDir = System.getProperty("user.dir");
            baseDir = Paths.get(userDir, basePath);
        }
        return baseDir.resolve(filename);
    }

    /**
     * 验证文件是否存在
     */
    public boolean validateFileExists(String url) {
        try {
            String filename = extractFilenameFromUrl(url);
            if (filename == null) {
                return false;
            }

            String sourcePath;
            if (url.contains("/images/")) {
                sourcePath = imagePath;
            } else if (url.contains("/materials/")) {
                sourcePath = materialPath;
            } else if (url.contains("/videos/")) {
                sourcePath = videoPath;
            } else {
                return false;
            }

            Path sourceFile = getSourceFilePath(sourcePath, filename);
            return Files.exists(sourceFile);

        } catch (Exception e) {
            log.warn("验证文件存在性失败，URL: {}", url, e);
            return false;
        }
    }

    /**
     * 获取文件大小
     */
    public long getFileSize(String url) {
        try {
            String filename = extractFilenameFromUrl(url);
            if (filename == null) {
                return 0;
            }

            String sourcePath;
            if (url.contains("/images/")) {
                sourcePath = imagePath;
            } else if (url.contains("/materials/")) {
                sourcePath = materialPath;
            } else if (url.contains("/videos/")) {
                sourcePath = videoPath;
            } else {
                return 0;
            }

            Path sourceFile = getSourceFilePath(sourcePath, filename);
            if (Files.exists(sourceFile)) {
                return Files.size(sourceFile);
            }

            return 0;

        } catch (Exception e) {
            log.warn("获取文件大小失败，URL: {}", url, e);
            return 0;
        }
    }

    /**
     * 创建临时目录
     */
    public Path createTempDirectory() throws IOException {
        return Files.createTempDirectory("bi_dashboard_export_");
    }

    /**
     * 清理临时目录
     */
    public void cleanupTempDirectory(Path tempDir) {
        try {
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                     .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                     .forEach(path -> {
                         try {
                             Files.delete(path);
                         } catch (IOException e) {
                             log.warn("删除临时文件失败: {}", path, e);
                         }
                     });
            }
        } catch (Exception e) {
            log.error("清理临时目录失败: {}", tempDir, e);
        }
    }

    /**
     * 计算总文件大小
     */
    public long calculateTotalSize(List<String> imageUrls, List<String> materialUrls, List<String> videoUrls) {
        long totalSize = 0;

        for (String url : imageUrls) {
            totalSize += getFileSize(url);
        }

        for (String url : materialUrls) {
            totalSize += getFileSize(url);
        }

        for (String url : videoUrls) {
            totalSize += getFileSize(url);
        }

        return totalSize;
    }

    /**
     * 格式化文件大小
     */
    public String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
