<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩色动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: transparent;
        }
        
        .dynamic-border-box {
            position: relative;
            width: 300px;
            height: 200px;
            background: transparent;
            border-radius: 10px;
            padding: 4px; /* 边框宽度 */
            background: linear-gradient(45deg, 
                #ff00cc, #00ccff, #00ff00, #ff0000, 
                #ff00cc, #00ccff, #00ff00, #ff0000);
            background-size: 400%;
            animation: animate-border 8s linear infinite;
        }
        
        @keyframes animate-border {
            0% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
            50% {
                background-position: 300% 0;
                filter: hue-rotate(360deg);
            }
            100% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
        }
        
        .content {
            background: transparent;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            background: linear-gradient(90deg, #ff00cc, #00ccff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 0, 204, 0.5);
        }
        
        p {
            color: #00ccff;
            line-height: 1.6;
            text-shadow: 0 0 8px rgba(0, 204, 255, 0.6);
        }
        
        /* 可选：添加悬停效果 */
        .dynamic-border-box:hover {
            animation-duration: 4s;
        }
        
        /* 可选：响应式设计 */
        @media (max-width: 480px) {
            .dynamic-border-box {
                width: 280px;
                height: 180px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dynamic-border-box">
        <div class="content">
            <h1>彩色动态边框</h1>
            <p>透明背景，只保留彩色边框效果</p>
        </div>
    </div>
</body>
</html>
