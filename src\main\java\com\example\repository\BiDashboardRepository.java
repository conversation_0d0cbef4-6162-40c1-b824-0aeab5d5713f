package com.example.repository;

import com.example.entity.BiDashboard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BiDashboardRepository extends JpaRepository<BiDashboard, Long> {
    
    /**
     * 根据名称查找大屏
     */
    Optional<BiDashboard> findByName(String name);
    
    /**
     * 查找所有大屏，按创建时间倒序
     */
    List<BiDashboard> findAllByOrderByCreatedAtDesc();
    
    /**
     * 根据名称模糊查询大屏
     */
    List<BiDashboard> findByNameContainingOrderByCreatedAtDesc(String name);
    

}
