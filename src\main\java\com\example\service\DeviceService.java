package com.example.service;

import com.example.model.DataItem;
import com.example.model.Device;
import com.example.model.DeviceCondition;
import com.example.repository.DataItemRepository;
import com.example.repository.DeviceConditionRepository;
import com.example.repository.DeviceRepository;
import com.example.repository.OperationLogRepository;
import com.example.repository.DeviceAlertRepository;
import com.example.util.ImageUrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceService {
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private DataItemRepository dataItemRepository;
    
    @Autowired
    private DeviceConditionRepository deviceConditionRepository;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private DeviceAlertRepository deviceAlertRepository;
    
    @Autowired
    private ModbusService modbusService;
    
    @Autowired
    private ImageUrlUtil imageUrlUtil;

    @Transactional
    public Device addDevice(String id, String name, String address, int port) {
        Device device = new Device();
        device.setId(id);
        device.setName(name);
        device.setAddress(address);
        device.setPort(port);
        device.setConnected(false);
        return deviceRepository.save(device);
    }

    @Transactional
    public Device updateDevice(String id, String name, String address, int port) {
        Device device = getDevice(id)
            .orElseThrow(() -> new RuntimeException("设备不存在: " + id));

        // 如果设备已连接且地址或端口发生变化，需要先断开连接
        if (device.getConnected() &&
            (!device.getAddress().equals(address) || !device.getPort().equals(port))) {
            try {
                modbusService.disconnect(id);
                device.setConnected(false);
            } catch (Exception e) {
                log.warn("断开设备连接失败: {}", e.getMessage());
            }
        }

        device.setName(name);
        device.setAddress(address);
        device.setPort(port);

        Device savedDevice = deviceRepository.save(device);
        log.info("设备信息更新成功: {} -> {}:{}", name, address, port);
        return savedDevice;
    }

    @Transactional
    public void deleteDevice(String id) {
        try {
            log.info("开始删除设备及其关联数据: {}", id);
            
            Device device = getDevice(id)
                .orElseThrow(() -> new RuntimeException("设备不存在"));
            
            // 1. 先断开设备连接
            modbusService.disconnect(id);
            
            // 2. 删除设备相关的操作日志（通过条件关联）
            List<DeviceCondition> conditions = deviceConditionRepository.findByDevice(device);
            for (DeviceCondition condition : conditions) {
                operationLogRepository.deleteByConditionId(condition.getId());
            }
            log.info("已删除设备相关的操作日志");
            
            // 3. 删除设备条件
            deviceConditionRepository.deleteAll(conditions);
            log.info("已删除设备相关的条件配置");
            
            // 4. 删除设备预警配置
            deviceAlertRepository.deleteByDeviceId(id);
            log.info("已删除设备相关的预警配置");
            
            // 5. 删除数据项
            List<DataItem> dataItems = dataItemRepository.findByDeviceId(id);
            dataItemRepository.deleteAll(dataItems);
            log.info("已删除设备相关的数据项");
            
            // 6. 最后删除设备
            deviceRepository.delete(device);
            log.info("设备删除完成: {}", id);
            
        } catch (Exception e) {
            log.error("删除设备失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除设备失败: " + e.getMessage(), e);
        }
    }

    @Transactional(readOnly = true)
    public List<Device> getAllDevices() {
        List<Device> devices = deviceRepository.findAll();
        // 处理图片URL
        devices.forEach(this::processDeviceImageUrl);
        return devices;
    }

    @Transactional(readOnly = true)
    public Optional<Device> getDevice(String id) {
        Optional<Device> deviceOpt = deviceRepository.findById(id);
        // 处理图片URL
        deviceOpt.ifPresent(this::processDeviceImageUrl);
        return deviceOpt;
    }
    
    /**
     * 处理设备的图片URL，确保返回完整的外部访问URL
     */
    private void processDeviceImageUrl(Device device) {
        if (device != null && device.getImageUrl() != null && !device.getImageUrl().isEmpty()) {
            device.setImageUrl(imageUrlUtil.getFullImageUrl(device.getImageUrl()));
        }
    }

    @Transactional
    public DataItem addDataItem(String deviceId, String itemId, String name, String address, int refreshInterval) {
        Device device = getDevice(deviceId)
            .orElseThrow(() -> new RuntimeException("设备不存在"));
        
        DataItem dataItem = new DataItem();
        dataItem.setId(itemId);
        dataItem.setName(name);
        dataItem.setAddress(address);
        dataItem.setRefreshInterval(refreshInterval);
        dataItem.setDevice(device);
        
        return dataItemRepository.save(dataItem);
    }

    @Transactional
    public void deleteDataItem(String itemId) {
        dataItemRepository.deleteById(itemId);
    }

    @Transactional(readOnly = true)
    public List<DataItem> getDeviceDataItems(String deviceId) {
        return dataItemRepository.findByDeviceId(deviceId);
    }

    public DataItem updateDataItem(String id, String name, Integer refreshInterval) {
        try {
            DataItem dataItem = dataItemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("监控项不存在: " + id));
            
            // 添加参数验证
            if (name == null || name.trim().isEmpty()) {
                throw new IllegalArgumentException("监控名称不能为空");
            }
            if (refreshInterval == null || refreshInterval < 100) {
                throw new IllegalArgumentException("更新间隔不能小于100毫秒");
            }
            
            dataItem.setName(name.trim());
            dataItem.setRefreshInterval(refreshInterval);
            
            return dataItemRepository.save(dataItem);
        } catch (Exception e) {
            log.error("更新监控项失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新监控项失败: " + e.getMessage(), e);
        }
    }

    public List<Device> findConnectedDevices() {
        return deviceRepository.findByConnectedTrue();
    }

    @Transactional
    public Device updateDeviceImage(String deviceId, String imageUrl) {
        Device device = getDevice(deviceId)
            .orElseThrow(() -> new RuntimeException("设备不存在: " + deviceId));

        device.setImageUrl(imageUrl);
        Device savedDevice = deviceRepository.save(device);

        // 处理图片URL
        processDeviceImageUrl(savedDevice);

        log.info("设备图片更新成功: {} -> {}", deviceId, imageUrl);
        return savedDevice;
    }

    @Transactional(readOnly = true)
    public Optional<DataItem> getDataItem(String id) {
        return dataItemRepository.findById(id);
    }

    @Transactional
    public DataItem saveDataItem(DataItem dataItem) {
        return dataItemRepository.save(dataItem);
    }
} 