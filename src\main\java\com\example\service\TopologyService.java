package com.example.service;

import com.example.entity.Topology;
import com.example.repository.TopologyRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class TopologyService {
    
    private static final String DEFAULT_TOPOLOGY_NAME = "default";
    
    @Autowired
    private TopologyRepository topologyRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 保存组态布局
     * 
     * @param data 组态布局数据（JSON格式）
     * @return 保存后的组态布局实体
     */
    public Topology saveTopology(String data) {
        log.info("保存组态布局数据");
        
        try {
            // 解析JSON数据，获取布局名称
            JsonNode jsonNode = objectMapper.readTree(data);
            String topologyName = DEFAULT_TOPOLOGY_NAME;
            
            // 如果JSON中包含name字段，则使用该字段作为布局名称
            if (jsonNode.has("name") && !jsonNode.get("name").asText().isEmpty()) {
                topologyName = jsonNode.get("name").asText();
            }
            
            log.info("使用布局名称: {}", topologyName);
            
            Optional<Topology> existingTopology = topologyRepository.findByName(topologyName);
            
            Topology topology;
            if (existingTopology.isPresent()) {
                topology = existingTopology.get();
                topology.setData(data);
            } else {
                topology = new Topology();
                topology.setName(topologyName);
                topology.setData(data);
            }
            
            return topologyRepository.save(topology);
        } catch (Exception e) {
            log.error("解析布局数据失败", e);
            throw new RuntimeException("保存布局失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取组态布局
     * 
     * @return 组态布局数据（JSON格式）
     */
    public String getTopology() {
        log.info("获取组态布局数据");
        
        Optional<Topology> topology = topologyRepository.findByName(DEFAULT_TOPOLOGY_NAME);
        
        return topology.map(Topology::getData).orElse("{}");
    }
    
    /**
     * 根据名称获取组态布局
     * 
     * @param name 布局名称
     * @return 组态布局数据（JSON格式）
     */
    public String getTopologyByName(String name) {
        log.info("获取名称为 {} 的组态布局数据", name);
        
        Optional<Topology> topology = topologyRepository.findByName(name);
        
        return topology.map(Topology::getData).orElse("{}");
    }
    
    /**
     * 获取所有组态布局
     * 
     * @return 所有组态布局实体列表
     */
    public List<Topology> getAllTopologies() {
        log.info("获取所有组态布局列表");
        
        return topologyRepository.findAll();
    }

    /**
     * 删除指定名称的组态布局
     * 
     * @param name 布局名称
     */
    public void deleteTopologyByName(String name) {
        log.info("删除名称为 {} 的组态布局", name);
        
        Optional<Topology> topology = topologyRepository.findByName(name);
        if (topology.isPresent()) {
            topologyRepository.delete(topology.get());
            log.info("组态布局已删除");
        } else {
            log.warn("未找到名称为 {} 的组态布局", name);
            throw new RuntimeException("未找到指定的组态布局");
        }
    }
} 