# 多折线图组件数据对应关系完整分析

## 数据流程概览

```
用户选择数据集 → 数据源配置收集 → API调用 → 数据格式化 → 图表渲染
```

## 1. 数据源配置收集

### API: `collectExternalDataConfig(widget)`
**位置**: `bi-data-source-manager.js:1401`

**收集的配置信息**:
```javascript
{
    dataSetId: "dataset_1752559477408",     // 数据集ID
    dataSetName: "测试1",                   // 数据集名称 (来源: 选择器选项文本)
    labelField: "timestamp",                // 标签字段 (X轴)
    valueField: "value",                    // 数值字段 (Y轴)
    alias: "数据集1"                        // 数据集别名
}
```

**关键代码**:
```javascript
// 第1425行: 获取数据集名称
const dataSetName = document.getElementById('dataSetSelect').selectedOptions[0]?.text || '';
```

## 2. 数据集API调用

### API: `GET /api/bi/dataset/{id}/data`
**位置**: `bi-data-source-manager.js:2187`

**请求参数**:
- `labelField`: X轴字段名 (如: "timestamp")
- `valueField`: Y轴字段名 (如: "value")

**API响应格式**:
```javascript
{
    "success": true,
    "data": {
        "labels": ["2024-01-01", "2024-01-02", ...],  // X轴数据
        "values": [100, 200, 150, ...]                // Y轴数据
    }
}
```

## 3. 数据格式化处理

### API: `formatExternalDataForChart(data, chartType, dataSourceConfig)`
**位置**: `bi-data-source-manager.js:2255`

**输入数据**:
```javascript
data = {
    labels: ["2024-01-01", "2024-01-02", ...],
    values: [100, 200, 150, ...]
}
dataSourceConfig = {
    dataSetName: "测试1",
    labelField: "timestamp",
    valueField: "value"
}
```

**多折线图格式化逻辑** (第2362行):
```javascript
if (chartType === 'multi-line-chart') {
    const dataSetName = dataSourceConfig.dataSetName || data.dataSetName || '数据集';
    result.xAxis = finalLabels;
    result.series = [{
        name: dataSetName,        // 折线名称 = 数据集名称
        type: 'line',
        data: finalValues,        // 折线数据 = Y轴数值
        yAxisIndex: 0
    }];
}
```

**输出格式**:
```javascript
{
    success: true,
    xAxis: ["2024-01-01", "2024-01-02", ...],     // X轴标签
    series: [{
        name: "测试1",                             // 系列名称 = 数据集名称
        type: "line",
        data: [100, 200, 150, ...],               // 系列数据 = Y轴数值
        yAxisIndex: 0
    }],
    labels: ["2024-01-01", "2024-01-02", ...],   // 兼容字段
    values: [100, 200, 150, ...]                 // 兼容字段
}
```

## 4. 多折线图组件渲染

### API: `createEChartsMultiLineChart(containerId, data, config)`
**位置**: `bi-echarts-components.js:441`

#### 4.1 数据智能格式化
**API**: `formatMultiLineDataSmart(data, config)`
**位置**: `bi-echarts-components.js:512`

**数据格式检测逻辑**:
```javascript
// 检测多折线格式: {xAxis: [], series: []}
if (data.xAxis && data.series) {
    return {
        xAxisData: data.xAxis,                    // X轴数据
        seriesData: data.series,                  // 系列数据
        legendData: data.series.map(s => s.name) // 图例数据
    };
}

// 检测标准格式: {labels: [], values: []}
if (data.labels && data.values) {
    const seriesName = config.seriesName || '数据';
    return {
        xAxisData: data.labels,
        seriesData: [{
            name: seriesName,
            type: 'line',
            data: data.values,
            yAxisIndex: 0
        }],
        legendData: [seriesName]
    };
}
```

#### 4.2 ECharts配置构建
**位置**: `bi-echarts-components.js:462`

**X轴配置**:
```javascript
xAxis: {
    type: 'category',
    data: formattedData.xAxisData,  // 来源: data.xAxis 或 data.labels
    ...axisConfig
}
```

**Y轴配置**:
```javascript
yAxis: {
    type: 'value',
    ...axisConfig
}
```

**图例配置**:
```javascript
legend: {
    show: config.isShowLegend !== false,
    data: formattedData.legendData,  // 来源: 系列名称数组
    ...
}
```

#### 4.3 系列数据配置
**API**: `setupMultiLineSeriesStandard(option, data, config)`
**位置**: `bi-echarts-components.js:694`

**系列配置逻辑**:
```javascript
option.series = data.seriesData.map((series, index) => {
    return {
        name: series.name,          // 折线名称 = 数据集名称
        type: 'line',
        data: series.data,          // 折线数据点
        lineStyle: {
            color: colors[index],   // 折线颜色
            width: config.lineWidth || 2
        },
        symbol: 'circle',
        showSymbol: config.showSymbol !== false,
        smooth: config.smooth !== false
    };
});
```

## 5. 数据对应关系总结

### 折线名称 (系列名称)
- **来源**: `dataSourceConfig.dataSetName`
- **获取方式**: 数据集选择器的选项文本
- **API路径**: `collectExternalDataConfig` → `formatExternalDataForChart` → `formatMultiLineDataSmart` → `setupMultiLineSeriesStandard`

### X轴标签 (时间轴)
- **来源**: 数据集API的 `data.labels` 字段
- **字段名**: `dataSourceConfig.labelField` (如: "timestamp")
- **API路径**: `/api/bi/dataset/{id}/data?labelField=timestamp` → `data.labels` → `formattedData.xAxis` → `option.xAxis.data`

### Y轴数值 (折线数据)
- **来源**: 数据集API的 `data.values` 字段
- **字段名**: `dataSourceConfig.valueField` (如: "value")
- **API路径**: `/api/bi/dataset/{id}/data?valueField=value` → `data.values` → `series.data` → `option.series[].data`

### 图例显示
- **来源**: 系列名称数组
- **内容**: 数据集名称列表
- **API路径**: `dataSetName` → `series.name` → `legendData` → `option.legend.data`

## 6. 多数据集模式

### 数据合并API: `mergeMultiLineChartData(results, mergeStrategy)`
**位置**: `bi-data-source-manager.js:2738`

**合并逻辑**:
```javascript
results.forEach((result, index) => {
    const seriesName = result.alias || `数据集${index + 1}`;  // 系列名称
    const labels = data.labels;                               // X轴数据
    const values = data.values;                               // Y轴数据
    
    seriesData.push({
        name: seriesName,
        type: 'line',
        data: values,
        yAxisIndex: 0
    });
});
```

## 7. 关键配置项

### 数据源配置
- `dataSetId`: 数据集ID
- `dataSetName`: 数据集名称 (折线名称)
- `labelField`: X轴字段名
- `valueField`: Y轴字段名
- `alias`: 数据集别名 (多数据集模式)

### 图表配置
- `enableDualYAxis`: 是否启用双Y轴
- `showSymbol`: 是否显示标记点
- `smooth`: 是否平滑曲线
- `colorScheme`: 颜色方案
- `isShowLegend`: 是否显示图例

## 8. 调试检查点

### 数据源配置检查
```javascript
console.log('数据源配置:', dataSourceConfig);
// 检查: dataSetName, labelField, valueField
```

### API响应检查
```javascript
console.log('API响应:', result.data);
// 检查: labels数组, values数组, 数据长度
```

### 格式化结果检查
```javascript
console.log('格式化结果:', formattedData);
// 检查: xAxis, series, legendData
```

### ECharts配置检查
```javascript
console.log('ECharts配置:', option);
// 检查: xAxis.data, series[].name, series[].data, legend.data
```

这个完整的数据对应关系分析应该能帮助您定位多折线图不显示的具体问题所在。
