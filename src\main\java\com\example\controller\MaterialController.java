package com.example.controller;

import com.example.entity.MaterialCategory;
import com.example.entity.MaterialFile;
import com.example.service.MaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/materials")
@Slf4j
public class MaterialController {
    
    @Autowired
    private MaterialService materialService;
    
    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public ResponseEntity<List<MaterialCategory>> getAllCategories() {
        try {
            List<MaterialCategory> categories = materialService.getAllCategories();
            return ResponseEntity.ok(categories);
        } catch (Exception e) {
            log.error("获取素材分类失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 根据类型获取分类
     */
    @GetMapping("/categories/type/{type}")
    public ResponseEntity<List<MaterialCategory>> getCategoriesByType(@PathVariable String type) {
        try {
            MaterialCategory.MaterialType materialType = MaterialCategory.MaterialType.valueOf(type);
            List<MaterialCategory> categories = materialService.getCategoriesByType(materialType);
            return ResponseEntity.ok(categories);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            log.error("获取素材分类失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 创建新分类
     */
    @PostMapping("/categories")
    public ResponseEntity<Map<String, Object>> createCategory(
            @RequestParam String name,
            @RequestParam String type,
            @RequestParam(required = false) String description) {
        Map<String, Object> response = new HashMap<>();
        try {
            MaterialCategory.MaterialType materialType = MaterialCategory.MaterialType.valueOf(type);
            MaterialCategory category = materialService.createCategory(name, materialType, description);
            response.put("success", true);
            response.put("data", category);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("error", "无效的分类类型");
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("创建素材分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 更新分类
     */
    @PutMapping("/categories/{id}")
    public ResponseEntity<Map<String, Object>> updateCategory(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam(required = false) String description) {
        Map<String, Object> response = new HashMap<>();
        try {
            MaterialCategory category = materialService.updateCategory(id, name, description);
            response.put("success", true);
            response.put("data", category);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新素材分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    public ResponseEntity<Map<String, Object>> deleteCategory(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            materialService.deleteCategory(id);
            response.put("success", true);
            response.put("message", "分类删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除素材分类失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 根据分类获取素材文件
     */
    @GetMapping("/files/category/{categoryId}")
    public ResponseEntity<List<MaterialFile>> getMaterialsByCategory(@PathVariable Long categoryId) {
        try {
            List<MaterialFile> materials = materialService.getMaterialsByCategory(categoryId);
            return ResponseEntity.ok(materials);
        } catch (Exception e) {
            log.error("获取素材文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 根据类型获取素材文件
     */
    @GetMapping("/files/type/{type}")
    public ResponseEntity<List<MaterialFile>> getMaterialsByType(@PathVariable String type) {
        try {
            MaterialCategory.MaterialType materialType = MaterialCategory.MaterialType.valueOf(type);
            List<MaterialFile> materials = materialService.getMaterialsByType(materialType);
            return ResponseEntity.ok(materials);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            log.error("获取素材文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 获取所有素材文件
     */
    @GetMapping("/files")
    public ResponseEntity<List<MaterialFile>> getAllMaterials() {
        try {
            List<MaterialFile> materials = materialService.getAllMaterials();
            return ResponseEntity.ok(materials);
        } catch (Exception e) {
            log.error("获取所有素材文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 获取默认素材文件
     */
    @GetMapping("/files/default")
    public ResponseEntity<List<MaterialFile>> getDefaultMaterials() {
        try {
            List<MaterialFile> materials = materialService.getDefaultMaterials();
            return ResponseEntity.ok(materials);
        } catch (Exception e) {
            log.error("获取默认素材文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 上传素材文件
     */
    @PostMapping("/files/upload")
    public ResponseEntity<Map<String, Object>> uploadMaterial(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "false") boolean isDefault) {
        Map<String, Object> response = new HashMap<>();
        try {
            MaterialFile materialFile = materialService.uploadMaterial(file, categoryId, isDefault);
            response.put("success", true);
            response.put("data", materialFile);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("上传素材文件失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新素材文件信息
     */
    @PutMapping("/files/{id}")
    public ResponseEntity<Map<String, Object>> updateMaterial(
            @PathVariable Long id,
            @RequestParam String originalName,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "false") boolean isDefault) {
        Map<String, Object> response = new HashMap<>();
        try {
            MaterialFile materialFile = materialService.updateMaterial(id, originalName, categoryId, isDefault);
            response.put("success", true);
            response.put("data", materialFile);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新素材文件失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除素材文件
     */
    @DeleteMapping("/files/{id}")
    public ResponseEntity<Map<String, Object>> deleteMaterial(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            materialService.deleteMaterial(id);
            response.put("success", true);
            response.put("message", "素材文件删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除素材文件失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
