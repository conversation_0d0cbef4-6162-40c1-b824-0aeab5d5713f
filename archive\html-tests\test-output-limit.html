<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输出限制功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 800px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .sql-preview {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-database-check"></i>
                输出限制功能测试
            </h2>
            
            <!-- 测试说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 测试说明</h6>
                <p class="mb-0">此页面用于测试数据集创建过程中步骤2的输出限制功能。该功能允许在SQL查询中自动添加LIMIT子句来控制输出记录数量。</p>
            </div>
            
            <!-- 模拟输出限制输入 -->
            <div class="test-section">
                <h5>输出限制配置测试</h5>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">输出限制</label>
                        <input type="number" class="form-control" id="outputLimit" 
                               placeholder="最大记录数" min="1" max="10000" value="100">
                        <div class="form-text small">限制查询结果的最大记录数，提高查询性能</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">测试表名</label>
                        <input type="text" class="form-control" id="tableName" 
                               placeholder="输入表名" value="data_items">
                        <div class="form-text small">用于生成测试SQL的表名</div>
                    </div>
                </div>
            </div>
            
            <!-- SQL生成测试 -->
            <div class="test-section">
                <h5>SQL生成测试</h5>
                <div class="mb-3">
                    <button type="button" class="btn btn-primary" id="generateSimpleSQL">
                        <i class="bi bi-code-square"></i> 生成简单查询
                    </button>
                    <button type="button" class="btn btn-success" id="generateAggregationSQL">
                        <i class="bi bi-bar-chart"></i> 生成聚合查询
                    </button>
                    <button type="button" class="btn btn-info" id="generateWindowSQL">
                        <i class="bi bi-window"></i> 生成窗口函数查询
                    </button>
                </div>
                
                <div class="sql-preview" id="sqlPreview">
点击上方按钮生成SQL预览...
                </div>
            </div>
            
            <!-- 测试结果 -->
            <div class="test-section">
                <h5>测试结果</h5>
                <div id="testResults">
                    <p class="text-muted">等待测试结果...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟SQL生成功能
        class SQLGenerator {
            addLimitClause(sql) {
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement) {
                    const outputLimit = parseInt(outputLimitElement.value);
                    if (outputLimit && outputLimit > 0) {
                        // 检查SQL是否已经包含LIMIT子句
                        if (!sql.toLowerCase().includes('limit')) {
                            sql += `\nLIMIT ${outputLimit}`;
                        }
                    }
                }
                return sql;
            }
            
            generateSimpleSQL() {
                const tableName = document.getElementById('tableName').value || 'data_items';
                let sql = `SELECT *\nFROM ${tableName}`;
                return this.addLimitClause(sql);
            }
            
            generateAggregationSQL() {
                const tableName = document.getElementById('tableName').value || 'data_items';
                let sql = `SELECT name, MAX(latest_value) as latest_value\nFROM ${tableName}\nGROUP BY name`;
                return this.addLimitClause(sql);
            }
            
            generateWindowSQL() {
                const tableName = document.getElementById('tableName').value || 'data_items';
                let sql = `SELECT name, latest_value\nFROM (\n    SELECT *, ROW_NUMBER() OVER (\n        PARTITION BY name \n        ORDER BY latest_value DESC, id DESC\n    ) as rn\n    FROM ${tableName}\n) t\nWHERE rn = 1`;
                return this.addLimitClause(sql);
            }
        }
        
        const generator = new SQLGenerator();
        
        // 绑定事件
        document.getElementById('generateSimpleSQL').addEventListener('click', () => {
            const sql = generator.generateSimpleSQL();
            document.getElementById('sqlPreview').textContent = sql;
            updateTestResults('简单查询', sql);
        });
        
        document.getElementById('generateAggregationSQL').addEventListener('click', () => {
            const sql = generator.generateAggregationSQL();
            document.getElementById('sqlPreview').textContent = sql;
            updateTestResults('聚合查询', sql);
        });
        
        document.getElementById('generateWindowSQL').addEventListener('click', () => {
            const sql = generator.generateWindowSQL();
            document.getElementById('sqlPreview').textContent = sql;
            updateTestResults('窗口函数查询', sql);
        });
        
        // 输出限制变化时重新生成SQL
        document.getElementById('outputLimit').addEventListener('input', () => {
            const currentSQL = document.getElementById('sqlPreview').textContent;
            if (currentSQL && currentSQL !== '点击上方按钮生成SQL预览...') {
                // 重新生成当前类型的SQL
                document.getElementById('generateSimpleSQL').click();
            }
        });
        
        function updateTestResults(type, sql) {
            const outputLimit = document.getElementById('outputLimit').value;
            const hasLimit = sql.toLowerCase().includes('limit');
            const limitValue = hasLimit ? sql.match(/limit\s+(\d+)/i)?.[1] : null;
            
            let resultHTML = `
                <div class="alert ${hasLimit ? 'alert-success' : 'alert-warning'}">
                    <h6><i class="bi bi-check-circle"></i> ${type}测试结果</h6>
                    <ul class="mb-0">
                        <li>输入限制值: ${outputLimit || '未设置'}</li>
                        <li>SQL包含LIMIT: ${hasLimit ? '是' : '否'}</li>
                        ${hasLimit ? `<li>LIMIT值: ${limitValue}</li>` : ''}
                        <li>功能状态: <span class="${hasLimit ? 'success' : 'error'}">${hasLimit ? '正常' : '异常'}</span></li>
                    </ul>
                </div>
            `;
            
            document.getElementById('testResults').innerHTML = resultHTML;
        }
    </script>
</body>
</html>
