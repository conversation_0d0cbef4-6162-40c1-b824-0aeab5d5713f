/**
 * BI状态指示器组件样式
 * 支持多种形状和动画效果
 */

/* 状态指示器容器 */
.status-indicator-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    user-select: none;
}

/* 基础指示器样式 */
.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* 增强视觉效果的指示器 */
.status-indicator.enhanced-visual {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform-style: preserve-3d;
}

/* 圆形指示器 */
.status-indicator-circle {
    border-radius: 50%;
}

/* 方形指示器 */
.status-indicator-square {
    border-radius: 8px;
}

/* 长方形指示器 */
.status-indicator-rectangle {
    border-radius: 4px;
}



/* 菱形指示器 */
.status-indicator-diamond {
    border-radius: 12px;
    transform: rotate(45deg);
}



/* 状态标签 */
.status-indicator-label {
    margin-top: 8px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 状态动画效果 - 增强版 */
.status-indicator.enhanced-visual.status-condition1 {
    animation: enhanced-pulse-green 2s infinite ease-in-out;
}

.status-indicator.enhanced-visual.status-condition2 {
    animation: enhanced-pulse-yellow 1.8s infinite ease-in-out;
}

.status-indicator.enhanced-visual.status-condition3 {
    animation: enhanced-pulse-red 1.2s infinite ease-in-out;
}

.status-indicator.enhanced-visual.status-offline {
    animation: enhanced-fade-gray 3s infinite ease-in-out;
}

/* 兼容旧版动画 */
.status-indicator.status-condition1:not(.enhanced-visual) {
    animation: pulse-green 2s infinite;
}

.status-indicator.status-condition2:not(.enhanced-visual) {
    animation: pulse-yellow 2s infinite;
}

.status-indicator.status-condition3:not(.enhanced-visual) {
    animation: pulse-red 1.5s infinite;
}

.status-indicator.status-offline:not(.enhanced-visual) {
    animation: fade-gray 3s infinite;
}

/* 增强版脉冲动画 - 绿色 */
@keyframes enhanced-pulse-green {
    0% {
        box-shadow:
            0 0 0 0 rgba(40, 167, 69, 0.8),
            0 0 20px rgba(40, 167, 69, 0.4),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 0 15px rgba(40, 167, 69, 0),
            0 0 30px rgba(40, 167, 69, 0.6),
            0 6px 12px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }
    100% {
        box-shadow:
            0 0 0 0 rgba(40, 167, 69, 0),
            0 0 20px rgba(40, 167, 69, 0.4),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
}

/* 脉冲动画 - 绿色（兼容版） */
@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 增强版脉冲动画 - 黄色 */
@keyframes enhanced-pulse-yellow {
    0% {
        box-shadow:
            0 0 0 0 rgba(255, 193, 7, 0.8),
            0 0 25px rgba(255, 193, 7, 0.5),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 0 18px rgba(255, 193, 7, 0),
            0 0 35px rgba(255, 193, 7, 0.7),
            0 6px 12px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1.08);
    }
    100% {
        box-shadow:
            0 0 0 0 rgba(255, 193, 7, 0),
            0 0 25px rgba(255, 193, 7, 0.5),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
}

/* 脉冲动画 - 黄色（兼容版） */
@keyframes pulse-yellow {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* 增强版脉冲动画 - 红色 */
@keyframes enhanced-pulse-red {
    0% {
        box-shadow:
            0 0 0 0 rgba(220, 53, 69, 0.9),
            0 0 30px rgba(220, 53, 69, 0.6),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 0 20px rgba(220, 53, 69, 0),
            0 0 40px rgba(220, 53, 69, 0.8),
            0 8px 16px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }
    100% {
        box-shadow:
            0 0 0 0 rgba(220, 53, 69, 0),
            0 0 30px rgba(220, 53, 69, 0.6),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transform: scale(1);
    }
}

/* 脉冲动画 - 红色（兼容版） */
@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* 增强版淡入淡出动画 - 灰色 */
@keyframes enhanced-fade-gray {
    0%, 100% {
        opacity: 1;
        box-shadow:
            0 0 15px rgba(108, 117, 125, 0.3),
            0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        box-shadow:
            0 0 8px rgba(108, 117, 125, 0.2),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.02);
        transform: scale(0.98);
    }
}

/* 淡入淡出动画 - 灰色（兼容版） */
@keyframes fade-gray {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}











/* 禁用动画时的样式 */
.status-indicator.no-animation,
.status-indicator.no-animation * {
    animation: none !important;
    transition: background-color 0.3s ease, border-color 0.3s ease !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-indicator-label {
        font-size: 10px;
        margin-top: 4px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .status-indicator {
        border-width: 3px !important;
    }
    
    .status-indicator-label {
        font-weight: bold;
    }
}

/* 性能优化 */
.status-indicator.enhanced-visual {
    will-change: transform, box-shadow, filter;
    transform: translateZ(0); /* 启用硬件加速 */
}

.status-indicator.enhanced-visual svg polygon {
    will-change: transform, filter;
    transform: translateZ(0);
}

/* 移动设备优化 */
@media (max-width: 768px) {
    .status-indicator-container {
        padding: 8px;
    }

    /* 移动设备上减少动画强度 */
    .status-indicator.enhanced-visual {
        animation-duration: 2.5s !important;
    }
}

/* 高对比度模式增强支持 */
@media (prefers-contrast: high) {
    .status-indicator.enhanced-visual {
        border-width: 3px;
        box-shadow: none !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .status-indicator,
    .status-indicator *,
    .status-indicator.enhanced-visual,
    .status-indicator.enhanced-visual * {
        animation: none !important;
        transition: none !important;
    }
}
