<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段选项修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .config-demo {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .log-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .field-options-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-tools"></i>
                字段选项修复验证
            </h2>
            
            <!-- 修复说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 修复说明</h6>
                <p class="mb-0">修复了数据集编辑时日期格式化和聚合配置中的日期字段选择失效问题。现在系统会等待字段选项加载完成，并在需要时主动更新字段选项。</p>
            </div>
            
            <!-- 修复内容 -->
            <div class="test-section">
                <h5><i class="bi bi-wrench"></i> 修复内容</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-demo">
                            <h6><i class="bi bi-clock"></i> 等待机制</h6>
                            <ul class="small mb-0">
                                <li>添加了 <code>waitForFieldOptions</code> 方法</li>
                                <li>在配置恢复前等待字段选项加载完成</li>
                                <li>超时时间设置为10秒，确保充分等待</li>
                                <li>检查日期字段和时间字段选项数量</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-demo">
                            <h6><i class="bi bi-arrow-clockwise"></i> 主动更新</h6>
                            <ul class="small mb-0">
                                <li>添加了 <code>updateFieldOptionsIfNeeded</code> 方法</li>
                                <li>当字段选项不存在时主动触发更新</li>
                                <li>调用字段配置器的更新方法</li>
                                <li>确保字段选项与字段数据同步</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 字段选项状态检查 -->
            <div class="test-section">
                <h5><i class="bi bi-list-check"></i> 字段选项状态检查</h5>
                <div class="mb-3">
                    <button type="button" class="btn btn-primary" onclick="checkFieldOptionsStatus()">
                        <i class="bi bi-search"></i> 检查字段选项状态
                    </button>
                </div>
                
                <div class="field-options-demo">
                    <div class="config-demo">
                        <h6>日期字段选项</h6>
                        <select class="form-select form-select-sm" id="dateField">
                            <option value="">选择日期字段</option>
                        </select>
                        <div class="mt-2">
                            <span id="dateFieldStatus" class="status-indicator status-warning"></span>
                            <span id="dateFieldStatusText">等待检查...</span>
                        </div>
                    </div>
                    
                    <div class="config-demo">
                        <h6>时间字段选项</h6>
                        <select class="form-select form-select-sm" id="timeField">
                            <option value="">选择时间字段（用于排序）</option>
                        </select>
                        <div class="mt-2">
                            <span id="timeFieldStatus" class="status-indicator status-warning"></span>
                            <span id="timeFieldStatusText">等待检查...</span>
                        </div>
                    </div>
                </div>
                
                <div id="fieldOptionsLog" class="log-output mt-3" style="display: none;"></div>
            </div>
            
            <!-- 配置恢复模拟 -->
            <div class="test-section">
                <h5><i class="bi bi-arrow-repeat"></i> 配置恢复模拟</h5>
                <p>模拟数据集编辑时的配置恢复流程，验证字段选项是否正确加载：</p>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-success me-2" onclick="simulateConfigRestore()">
                        <i class="bi bi-play"></i> 模拟配置恢复
                    </button>
                    <button type="button" class="btn btn-warning me-2" onclick="simulateFieldUpdate()">
                        <i class="bi bi-arrow-clockwise"></i> 模拟字段更新
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearLog()">
                        <i class="bi bi-x-circle"></i> 清空日志
                    </button>
                </div>
                
                <div id="restoreLog" class="log-output"></div>
            </div>
            
            <!-- 修复验证 -->
            <div class="test-section">
                <h5><i class="bi bi-check-circle"></i> 修复验证</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="config-demo text-center">
                            <h6>等待机制</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">配置恢复前等待字段选项加载</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-demo text-center">
                            <h6>选项检查</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">设置值前检查选项是否存在</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-demo text-center">
                            <h6>主动更新</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">选项不存在时主动触发更新</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let logContent = '';
        
        // 模拟字段数据
        const mockFields = [
            { name: 'id', type: 'INT' },
            { name: 'name', type: 'VARCHAR' },
            { name: 'value', type: 'INT' },
            { name: 'timestamp', type: 'DATETIME' },
            { name: 'created_at', type: 'DATETIME' },
            { name: 'updated_at', type: 'TIMESTAMP' }
        ];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('restoreLog').textContent = logContent;
        }
        
        function clearLog() {
            logContent = '';
            document.getElementById('restoreLog').textContent = '';
            document.getElementById('fieldOptionsLog').style.display = 'none';
        }
        
        function isDateTimeField(fieldName) {
            const dateTimePatterns = [
                /time/i, /date/i, /created/i, /updated/i, /timestamp/i
            ];
            return dateTimePatterns.some(pattern => pattern.test(fieldName));
        }
        
        function updateFieldOptions() {
            const dateFieldSelect = document.getElementById('dateField');
            const timeFieldSelect = document.getElementById('timeField');
            
            // 清空现有选项
            dateFieldSelect.innerHTML = '<option value="">选择日期字段</option>';
            timeFieldSelect.innerHTML = '<option value="">选择时间字段（用于排序）</option>';
            
            // 添加日期时间字段选项
            mockFields.forEach(field => {
                if (isDateTimeField(field.name)) {
                    const dateOption = document.createElement('option');
                    dateOption.value = field.name;
                    dateOption.textContent = `${field.name} (${field.type})`;
                    dateFieldSelect.appendChild(dateOption);
                    
                    const timeOption = document.createElement('option');
                    timeOption.value = field.name;
                    timeOption.textContent = `${field.name} (${field.type})`;
                    timeFieldSelect.appendChild(timeOption);
                }
            });
            
            log('字段选项更新完成');
        }
        
        function checkFieldOptionsStatus() {
            const dateFieldSelect = document.getElementById('dateField');
            const timeFieldSelect = document.getElementById('timeField');
            
            const dateFieldCount = dateFieldSelect.options.length - 1; // 减去默认选项
            const timeFieldCount = timeFieldSelect.options.length - 1;
            
            // 更新状态显示
            const dateStatus = document.getElementById('dateFieldStatus');
            const dateStatusText = document.getElementById('dateFieldStatusText');
            const timeStatus = document.getElementById('timeFieldStatus');
            const timeStatusText = document.getElementById('timeFieldStatusText');
            
            if (dateFieldCount > 0) {
                dateStatus.className = 'status-indicator status-success';
                dateStatusText.textContent = `已加载 ${dateFieldCount} 个选项`;
            } else {
                dateStatus.className = 'status-indicator status-error';
                dateStatusText.textContent = '无可用选项';
            }
            
            if (timeFieldCount > 0) {
                timeStatus.className = 'status-indicator status-success';
                timeStatusText.textContent = `已加载 ${timeFieldCount} 个选项`;
            } else {
                timeStatus.className = 'status-indicator status-error';
                timeStatusText.textContent = '无可用选项';
            }
            
            // 显示详细日志
            const logDiv = document.getElementById('fieldOptionsLog');
            logDiv.style.display = 'block';
            logDiv.textContent = `字段选项检查结果：
日期字段选项数量: ${dateFieldCount}
时间字段选项数量: ${timeFieldCount}
字段配置器状态: ${window.fieldConfigurator ? '已初始化' : '未初始化'}
模拟字段数据: ${mockFields.length} 个字段
日期时间字段: ${mockFields.filter(f => isDateTimeField(f.name)).map(f => f.name).join(', ')}`;
        }
        
        async function simulateConfigRestore() {
            log('开始模拟配置恢复流程...');
            
            // 1. 模拟等待字段选项
            log('等待字段选项加载...');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 2. 检查字段选项状态
            const dateFieldSelect = document.getElementById('dateField');
            const timeFieldSelect = document.getElementById('timeField');
            
            const dateFieldLoaded = dateFieldSelect.options.length > 1;
            const timeFieldLoaded = timeFieldSelect.options.length > 1;
            
            if (!dateFieldLoaded || !timeFieldLoaded) {
                log('字段选项未加载，尝试主动更新...');
                updateFieldOptions();
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            // 3. 模拟配置恢复
            const mockConfig = {
                dateFormatting: {
                    dateField: 'timestamp',
                    dateFormat: '%Y-%m-%d %H:%i:%s'
                },
                aggregation: {
                    enabled: true,
                    type: 'MAX',
                    timeField: 'created_at'
                }
            };
            
            // 4. 恢复日期格式化配置
            log('恢复日期格式化配置...');
            const dateFieldElement = document.getElementById('dateField');
            if (dateFieldElement) {
                const hasOption = Array.from(dateFieldElement.options).some(option => 
                    option.value === mockConfig.dateFormatting.dateField);
                
                if (hasOption) {
                    dateFieldElement.value = mockConfig.dateFormatting.dateField;
                    log(`✓ 设置日期字段: ${mockConfig.dateFormatting.dateField}`);
                } else {
                    log(`✗ 日期字段选项不存在: ${mockConfig.dateFormatting.dateField}`);
                }
            }
            
            // 5. 恢复聚合配置
            log('恢复聚合配置...');
            const timeFieldElement = document.getElementById('timeField');
            if (timeFieldElement) {
                const hasTimeOption = Array.from(timeFieldElement.options).some(option => 
                    option.value === mockConfig.aggregation.timeField);
                
                if (hasTimeOption) {
                    timeFieldElement.value = mockConfig.aggregation.timeField;
                    log(`✓ 设置时间字段: ${mockConfig.aggregation.timeField}`);
                } else {
                    log(`✗ 时间字段选项不存在: ${mockConfig.aggregation.timeField}`);
                }
            }
            
            log('配置恢复完成！');
        }
        
        function simulateFieldUpdate() {
            log('模拟字段更新...');
            updateFieldOptions();
            checkFieldOptionsStatus();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...');
            checkFieldOptionsStatus();
        });
    </script>
</body>
</html>
