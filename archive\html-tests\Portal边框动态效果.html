<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal边框动态效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #portalCard {
            position: relative;
            width: 100%;
            height: 100%;
            background: rgba(10, 12, 18, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 170, 0.8);
            box-shadow: inset 0 0 20px rgba(0, 255, 170, 0.2);
            overflow: hidden;
            transform-style: preserve-3d;
        }

        #portalCard::before {
            content: "";
            position: absolute;
            inset: 0;
            border-radius: 19px;
            padding: 1px;
            background: linear-gradient(135deg, #00ffaa, #00a3ff);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0.9;
            z-index: 2;
        }

        #portalCard::after {
            content: "";
            position: absolute;
            inset: 1px;
            border-radius: 19px;
            background: linear-gradient(
                135deg,
                rgba(0, 255, 170, 0.1) 0%,
                rgba(0, 179, 255, 0.1) 100%
            );
            opacity: 0.3;
            z-index: 1;
        }

        .gooey-effect {
            position: absolute;
            inset: 0;
            border-radius: 20px;
            overflow: hidden;
            z-index: 0;
            opacity: 0.9;
            filter: blur(2px);
        }

        .gooey-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(8px);
            animation: float-blob 15s infinite ease-in-out;
            opacity: 0.8;
        }

        .gooey-blob:nth-child(1) {
            width: 120px;
            height: 120px;
            left: 20%;
            top: 25%;
            background: radial-gradient(
                circle,
                rgba(0, 255, 170, 0.6) 0%,
                rgba(0, 255, 170, 0) 60%
            );
            animation-duration: 8s;
        }

        .gooey-blob:nth-child(2) {
            width: 100px;
            height: 100px;
            right: 20%;
            top: 30%;
            background: radial-gradient(
                circle,
                rgba(0, 179, 255, 0.6) 0%,
                rgba(0, 179, 255, 0) 60%
            );
            animation-duration: 8s;
            animation-delay: -3s;
        }

        .gooey-blob:nth-child(3) {
            width: 90px;
            height: 90px;
            right: 25%;
            bottom: 30%;
            background: radial-gradient(
                circle,
                rgba(0, 255, 170, 0.6) 0%,
                rgba(0, 255, 170, 0) 60%
            );
            animation-duration: 10s;
            animation-delay: -4s;
        }

        .gooey-blob:nth-child(4) {
            width: 110px;
            height: 110px;
            left: 25%;
            bottom: 25%;
            background: radial-gradient(
                circle,
                rgba(0, 179, 255, 0.6) 0%,
                rgba(0, 179, 255, 0) 60%
            );
            animation-duration: 10s;
            animation-delay: -4s;
        }

        @keyframes float-blob {
            0%, 100% {
                transform: translate(0, 0) scale(1);
            }
            20% {
                transform: translate(8px, 5px) scale(1.02);
            }
            40% {
                transform: translate(5px, 10px) scale(0.98);
            }
            60% {
                transform: translate(-5px, 8px) scale(1.03);
            }
            80% {
                transform: translate(-8px, -5px) scale(0.97);
            }
        }

        .card-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <div id="portalCard">
        <div class="gooey-effect">
            <div class="gooey-blob"></div>
            <div class="gooey-blob"></div>
            <div class="gooey-blob"></div>
            <div class="gooey-blob"></div>
        </div>
        <canvas class="card-bg" id="cardBgEffect"></canvas>
    </div>

    <script>
        (function () {
            const canvas = document.getElementById("cardBgEffect"),
                ctx = canvas.getContext("2d");
            
            function resize() {
                canvas.width = canvas.parentElement.offsetWidth;
                canvas.height = canvas.parentElement.offsetHeight;
            }
            resize();
            window.addEventListener("resize", resize);
            
            const particles = [],
                particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    radius: Math.random() * 2 + 1,
                    vx: Math.random() * 2 - 1,
                    vy: Math.random() * 2 - 1,
                    color: `rgba(0, ${Math.floor(Math.random() * 150 + 150)}, ${Math.floor(
                        Math.random() * 100 + 180
                    )}, 0.7)`
                });
            }
            
            function animate() {
                requestAnimationFrame(animate);
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                for (let i = 0; i < particleCount; i++) {
                    const p = particles[i];
                    p.x += p.vx;
                    p.y += p.vy;
                    
                    if (p.x < 0 || p.x > canvas.width) p.vx *= -1;
                    if (p.y < 0 || p.y > canvas.height) p.vy *= -1;
                    
                    const gradient = ctx.createRadialGradient(
                        p.x, p.y, 0, p.x, p.y, p.radius * 2
                    );
                    gradient.addColorStop(0, "rgba(255,255,255,1)");
                    gradient.addColorStop(1, "rgba(255,255,255,0)");
                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                    ctx.fill();
                    
                    for (let j = i + 1; j < particleCount; j++) {
                        const p2 = particles[j],
                            distance = Math.sqrt(Math.pow(p.x - p2.x, 2) + Math.pow(p.y - p2.y, 2));
                        if (distance < 100) {
                            ctx.beginPath();
                            ctx.strokeStyle = `rgba(0, 220, 180, ${0.1 * (1 - distance / 100)})`;
                            ctx.lineWidth = 0.5;
                            ctx.moveTo(p.x, p.y);
                            ctx.lineTo(p2.x, p2.y);
                            ctx.stroke();
                        }
                    }
                }
            }
            animate();
        })();
    </script>
</body>
</html>