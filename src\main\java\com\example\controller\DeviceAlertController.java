package com.example.controller;

import com.example.model.DataItem;
import com.example.model.DeviceAlert;
import com.example.service.DeviceAlertService;
import com.example.service.DataItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/alerts")
@Slf4j
public class DeviceAlertController {

    @Autowired
    private DeviceAlertService deviceAlertService;
    
    @Autowired
    private DataItemService dataItemService;
    
    /**
     * 获取设备的所有预警配置
     */
    @GetMapping("/device/{deviceId}")
    public ResponseEntity<List<DeviceAlert>> getDeviceAlerts(@PathVariable String deviceId) {
        return ResponseEntity.ok(deviceAlertService.getDeviceAlerts(deviceId));
    }
    
    /**
     * 获取单个预警配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<DeviceAlert> getDeviceAlert(@PathVariable Long id) {
        return deviceAlertService.getDeviceAlert(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建预警配置
     */
    @PostMapping("/device/{deviceId}")
    public ResponseEntity<DeviceAlert> createDeviceAlert(
            @PathVariable String deviceId,
            @RequestBody Map<String, Object> payload) {
        
        String dataItemId = (String) payload.get("dataItemId");
        String name = (String) payload.get("name");
        Integer normalMin = payload.get("normalMin") != null ? Integer.valueOf(payload.get("normalMin").toString()) : null;
        Integer normalMax = payload.get("normalMax") != null ? Integer.valueOf(payload.get("normalMax").toString()) : null;
        Integer warningMin = payload.get("warningMin") != null ? Integer.valueOf(payload.get("warningMin").toString()) : null;
        Integer warningMax = payload.get("warningMax") != null ? Integer.valueOf(payload.get("warningMax").toString()) : null;
        Integer dangerMin = payload.get("dangerMin") != null ? Integer.valueOf(payload.get("dangerMin").toString()) : null;
        Integer dangerMax = payload.get("dangerMax") != null ? Integer.valueOf(payload.get("dangerMax").toString()) : null;
        String remark = (String) payload.get("remark");
        
        DeviceAlert alert = deviceAlertService.createDeviceAlert(
                deviceId, dataItemId, name, 
                normalMin, normalMax, 
                warningMin, warningMax, 
                dangerMin, dangerMax, 
                remark);
        
        return ResponseEntity.ok(alert);
    }
    
    /**
     * 更新预警配置
     */
    @PutMapping("/{id}")
    public ResponseEntity<DeviceAlert> updateDeviceAlert(
            @PathVariable Long id,
            @RequestBody Map<String, Object> payload) {
        
        String name = (String) payload.get("name");
        Integer normalMin = payload.get("normalMin") != null ? Integer.valueOf(payload.get("normalMin").toString()) : null;
        Integer normalMax = payload.get("normalMax") != null ? Integer.valueOf(payload.get("normalMax").toString()) : null;
        Integer warningMin = payload.get("warningMin") != null ? Integer.valueOf(payload.get("warningMin").toString()) : null;
        Integer warningMax = payload.get("warningMax") != null ? Integer.valueOf(payload.get("warningMax").toString()) : null;
        Integer dangerMin = payload.get("dangerMin") != null ? Integer.valueOf(payload.get("dangerMin").toString()) : null;
        Integer dangerMax = payload.get("dangerMax") != null ? Integer.valueOf(payload.get("dangerMax").toString()) : null;
        String remark = (String) payload.get("remark");
        
        DeviceAlert alert = deviceAlertService.updateDeviceAlert(
                id, name, 
                normalMin, normalMax, 
                warningMin, warningMax, 
                dangerMin, dangerMax, 
                remark);
        
        return ResponseEntity.ok(alert);
    }
    
    /**
     * 删除预警配置
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDeviceAlert(@PathVariable Long id) {
        deviceAlertService.deleteDeviceAlert(id);
        return ResponseEntity.noContent().build();
    }
    
    /**
     * 获取数据项的当前状态
     */
    @GetMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> getAlertStatus(@PathVariable Long id) {
        try {
            log.debug("获取预警状态, id: {}", id);
            DeviceAlert alert = deviceAlertService.getDeviceAlert(id)
                    .orElseThrow(() -> new RuntimeException("预警配置不存在"));
            
            // 从数据项中直接获取最新值，不返回整个DataItem对象
            DataItem dataItem = alert.getDataItem();
            Integer currentValue = dataItem.getLatestValue();
            
            int status = deviceAlertService.getAlertStatus(alert, currentValue);
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", alert.getId());
            result.put("currentValue", currentValue);
            result.put("status", status);
            // 可以添加其他必要的字段，但不包含完整的DataItem对象
            
            log.debug("预警状态获取成功, id: {}, status: {}, currentValue: {}", id, status, currentValue);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取预警状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取预警状态失败");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
} 