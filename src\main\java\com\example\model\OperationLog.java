package com.example.model;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "operation_logs")
@Data
public class OperationLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "condition_id", nullable = false)
    private DeviceCondition condition;
    
    @Column(name = "source_value", nullable = false)
    private Integer sourceValue;
    
    @Column(name = "target_value", nullable = false)
    private Integer targetValue;
    
    @Column(nullable = false)
    private Boolean success;
    
    @Column(length = 500)
    private String message;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
} 