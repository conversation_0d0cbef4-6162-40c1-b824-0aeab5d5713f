package com.example.repository;

import com.example.entity.DataSet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据集Repository
 */
@Repository
public interface DataSetRepository extends JpaRepository<DataSet, String> {
    
    /**
     * 根据数据源ID查找数据集
     */
    List<DataSet> findByDataSourceId(String dataSourceId);
    
    /**
     * 根据数据源ID和启用状态查找数据集
     */
    List<DataSet> findByDataSourceIdAndEnabled(String dataSourceId, Boolean enabled);
    
    /**
     * 根据启用状态查找数据集
     */
    List<DataSet> findByEnabled(Boolean enabled);
    
    /**
     * 根据数据类型查找数据集
     */
    List<DataSet> findByDataType(String dataType);
    
    /**
     * 根据名称模糊查询
     */
    List<DataSet> findByNameContainingIgnoreCase(String name);
    
    /**
     * 统计各数据源的数据集数量
     */
    @Query("SELECT d.dataSourceId, d.dataSourceName, COUNT(d) FROM DataSet d GROUP BY d.dataSourceId, d.dataSourceName")
    List<Object[]> countByDataSource();
    
    /**
     * 统计各数据类型的数据集数量
     */
    @Query("SELECT d.dataType, COUNT(d) FROM DataSet d GROUP BY d.dataType")
    List<Object[]> countByDataType();
    
    /**
     * 检查名称是否已存在（排除指定ID）
     */
    @Query("SELECT COUNT(d) > 0 FROM DataSet d WHERE d.name = :name AND d.id != :excludeId")
    boolean existsByNameAndIdNot(@Param("name") String name, @Param("excludeId") String excludeId);
    
    /**
     * 检查名称是否已存在
     */
    boolean existsByName(String name);
    
    /**
     * 根据数据源ID删除所有数据集
     */
    void deleteByDataSourceId(String dataSourceId);
    
    /**
     * 统计指定数据源的数据集数量
     */
    long countByDataSourceId(String dataSourceId);
}
