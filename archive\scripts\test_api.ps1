# 测试大屏设计器API的PowerShell脚本

Write-Host "=== 大屏设计器API测试开始 ===" -ForegroundColor Green

# 创建会话
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

# 测试1: 登录
Write-Host "`n--- 测试1: 用户登录 ---" -ForegroundColor Yellow
try {
    $loginData = @{
        username = 'admin'
        password = 'admin'
    }
    
    $loginResponse = Invoke-RestMethod -Uri 'http://localhost:8080/auth/login' -Method Post -Body $loginData -WebSession $session
    Write-Host "登录成功" -ForegroundColor Green
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "尝试直接访问API..." -ForegroundColor Yellow
}

# 测试2: 获取大屏列表
Write-Host "`n--- 测试2: 获取大屏列表 ---" -ForegroundColor Yellow
try {
    $dashboards = Invoke-RestMethod -Uri 'http://localhost:8080/api/bi/dashboards' -Method Get -WebSession $session
    Write-Host "获取大屏列表成功" -ForegroundColor Green
    Write-Host "大屏数量: $($dashboards.data.Count)" -ForegroundColor Cyan
    
    if ($dashboards.data.Count -gt 0) {
        $firstDashboard = $dashboards.data[0]
        Write-Host "第一个大屏ID: $($firstDashboard.id)" -ForegroundColor Cyan
        Write-Host "第一个大屏名称: $($firstDashboard.name)" -ForegroundColor Cyan
        
        # 测试3: 获取大屏组件
        Write-Host "`n--- 测试3: 获取大屏组件 ---" -ForegroundColor Yellow
        try {
            $widgets = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$($firstDashboard.id)/widgets" -Method Get -WebSession $session
            Write-Host "获取组件列表成功" -ForegroundColor Green
            Write-Host "组件数量: $($widgets.data.Count)" -ForegroundColor Cyan
            
            if ($widgets.data.Count -gt 0) {
                $firstWidget = $widgets.data[0]
                Write-Host "第一个组件ID: $($firstWidget.id)" -ForegroundColor Cyan
                Write-Host "第一个组件类型: $($firstWidget.widgetType)" -ForegroundColor Cyan
                Write-Host "组件配置长度: $($firstWidget.config.Length)" -ForegroundColor Cyan
                
                # 检查是否包含状态信息
                if ($firstWidget.config -and $firstWidget.config.Contains('_stateInfo')) {
                    Write-Host "✅ 组件包含状态信息" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ 组件不包含状态信息" -ForegroundColor Yellow
                }
            }
        } catch {
            Write-Host "获取组件失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试4: 创建测试组件
        Write-Host "`n--- 测试4: 创建测试组件 ---" -ForegroundColor Yellow
        try {
            $testWidgetData = @{
                canvasConfig = @{
                    width = 1920
                    height = 1080
                    backgroundColor = '#000000'
                }
                widgets = @(
                    @{
                        type = 'text-label'
                        value = @{
                            widgetId = 9999
                            setup = @{
                                text = '测试组件'
                                fontSize = 18
                                color = '#ffffff'
                            }
                            data = @{
                                dataSourceType = 'static'
                                dataItemId = $null
                            }
                            position = @{
                                left = 100
                                top = 100
                                width = 200
                                height = 50
                                zIndex = 1001
                            }
                        }
                        isModified = $true
                        lastModified = (Get-Date).ToString('yyyy-MM-ddTHH:mm:ss.fffZ')
                        modifiedPaths = @('setup.text', 'setup.fontSize')
                    }
                )
            }
            
            $jsonData = $testWidgetData | ConvertTo-Json -Depth 10
            $saveResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$($firstDashboard.id)/layout" -Method Put -Body $jsonData -ContentType 'application/json' -WebSession $session
            
            if ($saveResponse.success) {
                Write-Host "✅ 测试组件创建成功" -ForegroundColor Green
                Write-Host "保存的组件数量: $($saveResponse.savedWidgets)" -ForegroundColor Cyan
                
                # 测试5: 验证组件状态保存
                Write-Host "`n--- 测试5: 验证组件状态保存 ---" -ForegroundColor Yellow
                Start-Sleep -Seconds 1
                
                $updatedWidgets = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$($firstDashboard.id)/widgets" -Method Get -WebSession $session
                $testWidget = $updatedWidgets.data | Where-Object { $_.widgetType -eq 'text-label' } | Select-Object -First 1
                
                if ($testWidget) {
                    Write-Host "找到测试组件" -ForegroundColor Green
                    
                    # 检查配置中是否包含状态信息
                    if ($testWidget.config -and $testWidget.config.Contains('_stateInfo')) {
                        Write-Host "✅ 组件状态信息已正确保存" -ForegroundColor Green
                        
                        # 解析配置查看状态信息
                        try {
                            $config = $testWidget.config | ConvertFrom-Json
                            if ($config._stateInfo) {
                                Write-Host "修改状态: $($config._stateInfo.isModified)" -ForegroundColor Cyan
                                Write-Host "最后修改时间: $($config._stateInfo.lastModified)" -ForegroundColor Cyan
                                Write-Host "修改路径数量: $($config._stateInfo.modifiedPaths.Count)" -ForegroundColor Cyan
                            }
                        } catch {
                            Write-Host "解析状态信息失败: $($_.Exception.Message)" -ForegroundColor Red
                        }
                    } else {
                        Write-Host "❌ 组件状态信息未保存" -ForegroundColor Red
                    }
                } else {
                    Write-Host "❌ 未找到测试组件" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ 测试组件创建失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "创建测试组件失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "没有找到大屏，跳过组件测试" -ForegroundColor Yellow
    }
} catch {
    Write-Host "获取大屏列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
