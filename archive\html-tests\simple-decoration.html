<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁装饰效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            overflow: hidden;
        }
        
        .decoration-wrapper {
            position: relative;
            width: 400px;
            height: 300px;
            margin: 50px auto;
            background: transparent;
        }
        
        /* 角落装饰 */
        .corner-decoration {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 3px solid #00ffff;
        }
        
        .corner-tl {
            top: 0;
            left: 0;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }
        
        .corner-tr {
            top: 0;
            right: 0;
            border-left: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate 0.5s;
        }
        
        .corner-bl {
            bottom: 0;
            left: 0;
            border-right: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }
        
        .corner-br {
            bottom: 0;
            right: 0;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1.5s;
        }
        
        @keyframes corner-glow {
            0% {
                box-shadow: 0 0 5px #00ffff;
                border-color: #00ffff;
            }
            100% {
                box-shadow: 0 0 20px #00ffff, 0 0 30px #00ffff;
                border-color: #66ffff;
            }
        }
        
        /* 中心装饰圆环 */
        .center-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .ring {
            position: absolute;
            border: 2px solid rgba(255, 0, 204, 0.6);
            border-radius: 50%;
            animation: ring-pulse 3s ease-in-out infinite;
        }
        
        .ring-1 {
            width: 60px;
            height: 60px;
            top: -30px;
            left: -30px;
            animation-delay: 0s;
        }
        
        .ring-2 {
            width: 100px;
            height: 100px;
            top: -50px;
            left: -50px;
            animation-delay: 1s;
        }
        
        .ring-3 {
            width: 140px;
            height: 140px;
            top: -70px;
            left: -70px;
            animation-delay: 2s;
        }
        
        @keyframes ring-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
        }
        
        /* 浮动装饰点 */
        .floating-dots {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #00ff88, transparent);
            border-radius: 50%;
            animation: dot-float 4s ease-in-out infinite;
        }
        
        .dot:nth-child(1) {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }
        
        .dot:nth-child(2) {
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }
        
        .dot:nth-child(3) {
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }
        
        .dot:nth-child(4) {
            bottom: 35%;
            right: 15%;
            animation-delay: 3s;
        }
        
        @keyframes dot-float {
            0%, 100% {
                transform: translateY(0px);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-15px);
                opacity: 1;
            }
        }
        
        /* 装饰线条 */
        .decorative-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation: line-glow 3s ease-in-out infinite;
        }
        
        .line-top {
            top: 15px;
            left: 60px;
            width: 100px;
            height: 2px;
            animation-delay: 0s;
        }
        
        .line-bottom {
            bottom: 15px;
            right: 60px;
            width: 120px;
            height: 2px;
            animation-delay: 1.5s;
        }
        
        .line-left {
            left: 15px;
            top: 60px;
            width: 2px;
            height: 80px;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 0.75s;
        }
        
        .line-right {
            right: 15px;
            bottom: 60px;
            width: 2px;
            height: 100px;
            background: linear-gradient(to bottom, 
                transparent, 
                rgba(255, 170, 0, 0.8), 
                transparent);
            animation-delay: 2.25s;
        }
        
        @keyframes line-glow {
            0%, 100% {
                opacity: 0.4;
                filter: brightness(1);
            }
            50% {
                opacity: 1;
                filter: brightness(1.5);
            }
        }
        
        /* 内容区域 */
        .content-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #00d4ff;
            z-index: 10;
        }
        
        .content-area h2 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #00d4ff, #ff00cc);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .content-area p {
            font-size: 0.9rem;
            color: #66ddff;
            text-shadow: 0 0 5px rgba(102, 221, 255, 0.5);
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .decoration-wrapper {
                width: 300px;
                height: 200px;
            }
            
            .corner-decoration {
                width: 30px;
                height: 30px;
            }
            
            .ring-1 { width: 40px; height: 40px; top: -20px; left: -20px; }
            .ring-2 { width: 70px; height: 70px; top: -35px; left: -35px; }
            .ring-3 { width: 100px; height: 100px; top: -50px; left: -50px; }
        }
    </style>
</head>
<body>
    <div class="decoration-wrapper">
        <!-- 角落装饰 -->
        <div class="corner-decoration corner-tl"></div>
        <div class="corner-decoration corner-tr"></div>
        <div class="corner-decoration corner-bl"></div>
        <div class="corner-decoration corner-br"></div>
        
        <!-- 中心圆环 -->
        <div class="center-rings">
            <div class="ring ring-1"></div>
            <div class="ring ring-2"></div>
            <div class="ring ring-3"></div>
        </div>
        
        <!-- 浮动装饰点 -->
        <div class="floating-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
        
        <!-- 装饰线条 -->
        <div class="decorative-lines">
            <div class="line line-top"></div>
            <div class="line line-bottom"></div>
            <div class="line line-left"></div>
            <div class="line line-right"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <h2>装饰效果</h2>
            <p>动态装饰组件</p>
        </div>
    </div>
</body>
</html>
