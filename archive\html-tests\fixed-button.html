<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版响应式动态按钮</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100vh;
            padding: 5%;
        }
        
        .dynamic-button {
            position: relative;
            padding: 8% 12%;
            font-size: 1rem;
            font-weight: bold;
            color: #00d4ff;
            background: transparent;
            border: 0.15em solid #00d4ff;
            border-radius: 0.6em;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            width: 100%;
            height: 60%;
            min-width: 80px;
            min-height: 40px;
            max-width: 300px;
            max-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 背景发光效果 */
        .dynamic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(0, 212, 255, 0.2), 
                transparent);
            transition: left 0.5s;
        }
        
        /* 边框发光动画 */
        .dynamic-button::after {
            content: '';
            position: absolute;
            top: -0.15em;
            left: -0.15em;
            right: -0.15em;
            bottom: -0.15em;
            background: linear-gradient(45deg, 
                #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff);
            background-size: 400%;
            border-radius: 0.75em;
            z-index: -1;
            opacity: 0;
            animation: border-flow 3s linear infinite;
            transition: opacity 0.3s;
        }
        
        /* 悬停效果 */
        .dynamic-button:hover::before {
            left: 100%;
        }
        
        .dynamic-button:hover::after {
            opacity: 1;
        }
        
        .dynamic-button:hover {
            color: #ffffff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 
                0 0 15px rgba(0, 212, 255, 0.4),
                0 0 30px rgba(0, 212, 255, 0.2);
            transform: translateY(-2px);
            text-shadow: 
                0 0 10px rgba(0, 212, 255, 0.8),
                0 0 20px rgba(0, 212, 255, 0.6);
        }
        
        /* 点击效果 */
        .dynamic-button:active {
            transform: translateY(0);
            box-shadow: 
                0 0 10px rgba(0, 212, 255, 0.6),
                0 0 20px rgba(0, 212, 255, 0.3);
        }
        
        /* 边框流动动画 */
        @keyframes border-flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 按钮内部装饰 */
        .button-decoration {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .decoration-1 {
            top: 8px;
            left: 8px;
            animation: dot-pulse 2s ease-in-out infinite;
        }
        
        .decoration-2 {
            top: 8px;
            right: 8px;
            animation: dot-pulse 2s ease-in-out infinite 0.5s;
        }
        
        .decoration-3 {
            bottom: 8px;
            left: 8px;
            animation: dot-pulse 2s ease-in-out infinite 1s;
        }
        
        .decoration-4 {
            bottom: 8px;
            right: 8px;
            animation: dot-pulse 2s ease-in-out infinite 1.5s;
        }
        
        .dynamic-button:hover .button-decoration {
            opacity: 1;
        }
        
        @keyframes dot-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* 响应式设计 - 基于媒体查询 */
        @media (max-width: 200px) {
            .dynamic-button {
                font-size: 0.7rem;
                padding: 6% 10%;
                letter-spacing: 0.05em;
                border-width: 0.1em;
            }
            
            .button-decoration {
                width: 3px;
                height: 3px;
            }
            
            .decoration-1, .decoration-2 { top: 5px; }
            .decoration-3, .decoration-4 { bottom: 5px; }
            .decoration-1, .decoration-3 { left: 5px; }
            .decoration-2, .decoration-4 { right: 5px; }
        }
        
        @media (min-width: 400px) {
            .dynamic-button {
                font-size: 1.2rem;
                padding: 10% 15%;
                border-width: 0.2em;
            }
            
            .button-decoration {
                width: 6px;
                height: 6px;
            }
            
            .decoration-1, .decoration-2 { top: 12px; }
            .decoration-3, .decoration-4 { bottom: 12px; }
            .decoration-1, .decoration-3 { left: 12px; }
            .decoration-2, .decoration-4 { right: 12px; }
        }
    </style>
</head>
<body>
    <button class="dynamic-button">
        <span class="button-decoration decoration-1"></span>
        <span class="button-decoration decoration-2"></span>
        <span class="button-decoration decoration-3"></span>
        <span class="button-decoration decoration-4"></span>
        点击我
    </button>
</body>
</html>
