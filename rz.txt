bi-dashboard-designer.js?v=20250127-gauge-fix:807 转换组件 2968 从标准格式: {setupKeys: <PERSON><PERSON><PERSON>(16), configKeys: <PERSON><PERSON><PERSON>(2), styleConfigKeys: <PERSON><PERSON>y(16)}
bi-dashboard-designer.js?v=20250127-gauge-fix:952 开始渲染组件 2968，类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:973 渲染组件 2968 DOM zIndex: 1004
bi-dashboard-designer.js?v=20250127-gauge-fix:1029 === 应用组件 2968 样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:1030 样式配置: {layerName: '状态指示器', background: '', shape: 'circle', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:1059 组件 2968 显示标题栏
bi-dashboard-designer.js?v=20250127-gauge-fix:1063 === 组件 2968 样式配置应用完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:15.344Z
bi-dashboard-designer.js?v=20250127-gauge-fix:4031 获取组件 2968 缓存配置，时间戳: 2025-07-30T02:08:15.344Z
bi-dashboard-designer.js?v=20250127-gauge-fix:4057  组件 2968 配置缓存不一致: {configMatch: true, styleMatch: true, stateMatch: false, cached: {…}, current: {…}}
validateConfigCacheConsistency @ bi-dashboard-designer.js?v=20250127-gauge-fix:4057
updatePropertyPanel @ bi-dashboard-designer.js?v=20250127-gauge-fix:2733
selectWidget @ bi-dashboard-designer.js?v=20250127-gauge-fix:2718
createWidget @ bi-dashboard-designer.js?v=20250127-gauge-fix:842
dropWidget @ bi-dashboard-designer.js?v=20250127-gauge-fix:437
ondrop @ design:560
bi-dashboard-designer.js?v=20250127-gauge-fix:4073 修复组件 2968 配置缓存不一致
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:15.344Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-dashboard-designer.js?v=20250127-gauge-fix:481 修复组件 2968 的修改状态：应为已修改但当前为未修改
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: state_correction
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:15.344Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-dashboard-designer.js?v=20250127-gauge-fix:6106 使用缓存的设备列表
bi-data-source-manager.js?v=20250127:553 恢复数据源配置，组件信息: {id: 2968, type: 'status-indicator', widgetType: undefined}
bi-data-source-manager.js?v=20250127:586 属性面板加载中，跳过onDataSourceTypeChange调用以保护设备选择
bi-dashboard-designer.js?v=20250127-gauge-fix:7576 重置所有配置项的显示状态
bi-dashboard-designer.js?v=20250127-gauge-fix:7608 多折线图配置显示状态已重置
bi-dashboard-designer.js?v=20250127-gauge-fix:7611 配置项显示状态重置完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9354 实时配置监听器已设置，跳过重复设置
bi-dashboard-designer.js?v=20250127-gauge-fix:13718 === 更新图层列表 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:13719 组件zIndex排序: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:13720 === 图层列表更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:15.346Z
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:13718 === 更新图层列表 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:13719 组件zIndex排序: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:13720 === 图层列表更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:859 创建组件 (标准化): {type: 'status-indicator', value: {…}, options: Array(5)}
bi-dashboard-designer.js?v=20250127-gauge-fix:860 创建组件 (兼容格式): {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","dataItemId":null,"data…d":null,"valueField":null,"refreshInterval":5000}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', dataItemId: null, dataSetId: null, labelField: null, valueField: null, …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: undefined, chartType: undefined}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","dataItemId":null,"dataSetId":null,"labelField":null,"valueField":null,"refreshInterval":5000}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 66,
      "timestamp": "2025-07-30T02:08:15.452Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:199 重新渲染状态指示器组件
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: undefined, chartType: undefined}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 66,
      "timestamp": "2025-07-30T02:08:15.452Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-circle enhanced-visual status-condition1" id=​"indicator-1753841295453" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 50%;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-circle enhanced-visual status-condition1" id=​"indicator-1753841295453" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 50%;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: undefined, chartType: undefined}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: circle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #28a745 0%, #007412 50%, #004100 100%)', solid: '#28a745', glow: 'rgba(40, 167, 69, 0.8)', shadow: 'rgba(40, 167, 69, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-condition1
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 66,
      "timestamp": "2025-07-30T02:08:15.452Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: undefined, chartType: undefined}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 66,
      "timestamp": "2025-07-30T02:08:15.452Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-circle enhanced-visual status-condition1" id=​"indicator-1753841295455" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 50%;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-circle enhanced-visual status-condition1" id=​"indicator-1753841295455" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 50%;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: undefined, chartType: undefined}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: circle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #28a745 0%, #007412 50%, #004100 100%)', solid: '#28a745', glow: 'rgba(40, 167, 69, 0.8)', shadow: 'rgba(40, 167, 69, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-condition1
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: undefined, chartType: undefined}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
render @ bi-status-indicator.js?v=20250730-fixed-shapes:246
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:200
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
createWidget @ bi-dashboard-designer.js?v=20250127-gauge-fix:854
dropWidget @ bi-dashboard-designer.js?v=20250127-gauge-fix:437
ondrop @ design:560
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:3786 属性面板加载完成，清除加载标志位
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: properties
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:16.964Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-data-source-manager.js?v=20250127:174 为组件 2968 (status-indicator) 创建配置上下文
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticLabels
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticValues
bi-data-source-manager.js?v=20250127:424 组件 2968 数据源配置收集完成: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11441 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11057 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11064 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250127-gauge-fix:11075 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11109 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:12136 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12137 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:12138 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12154 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4554 === 状态指示器配置更新开始 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4555 更新前config: {}
bi-dashboard-designer.js?v=20250127-gauge-fix:4648 更新后config: {
  "shape": "square",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4649 === 状态指示器配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4652 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4653 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:4654 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:4655 更新前styleConfig: {"layerName":"状态指示器","background":"","shape":"circle","size":60,"borderWidth":2,"borderColor":"#cccccc","condition1":{"min":0,"max":30,"name":"正常","color":"#28a745"},"condition2":{"min":31,"max":70,"name":"警告","color":"#ffc107"},"condition3":{"min":71,"max":100,"name":"危险","color":"#dc3545"},"offlineColor":"#6c757d","showConditionName":true,"fontSize":12,"fontColor":"#333333","fontWeight":"normal","enableAnimation":true,"animationDuration":300}
bi-dashboard-designer.js?v=20250127-gauge-fix:4656 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4665 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4666 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11411 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11412 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:11413 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:11414 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:11419 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:11420 配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:11421 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11434 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250127-gauge-fix:11436 === 追踪结束 ===
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: null
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "square",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-square enhanced-visual status-offline" id=​"indicator-1753841296970" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-square enhanced-visual status-offline" id=​"indicator-1753841296970" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: square
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:4818 实时应用属性: {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","staticLabels":"一月\\n二月\\…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110', enableDataTransform: false, transformOperation: 'add', …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","staticLabels":"一月\n二月\n三月\n四月\n五月\n六月","staticValues":"120\n200\n150\n80\n70\n110","enableDataTransform":false,"transformOperation":"add","transformValue":1,"decimalPlaces":2,"dataSuffix":"","enableWaterTarget":false,"waterTargetSource":"manual","waterTargetValue":100,"waterTargetDevice":"","waterTargetDataItem":"","showTargetValues":true,"enableColumnTarget":false,"columnTargetSource":"manual","columnTargetValue":80,"columnTargetDevice":"","columnTargetDataItem":"","showColumnTargetLabel":true}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 2,
      "timestamp": "2025-07-30T02:08:17.075Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 2,
      "timestamp": "2025-07-30T02:08:17.075Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "square",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 2,
      "timestamp": "2025-07-30T02:08:17.075Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "square",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-square enhanced-visual status-condition1" id=​"indicator-1753841297077" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​正常​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-square enhanced-visual status-condition1" id=​"indicator-1753841297077" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(40, 167, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ background:​ linear-gradient(135deg, rgb(40, 167, 69)​ 0%, rgb(0, 116, 18)​ 50%, rgb(0, 65, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: square
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #28a745 0%, #007412 50%, #004100 100%)', solid: '#28a745', glow: 'rgba(40, 167, 69, 0.8)', shadow: 'rgba(40, 167, 69, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-condition1
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​正常​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '正常', color: '#28a745', condition: 'condition1'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'square', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:203
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250127-gauge-fix:4806
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8558
setTimeout
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8556
handleMouseUp_ @ 未知
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2966 column-percentage-chart
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2966 column-percentage-chart
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2966, type: 'column-percentage-chart', dataSourceConfig: '{"dataSourceType":"dataItem","deviceId":"23732a8f-…TargetDataItem":"","showColumnTargetLabel":false}'}
bi-data-source-manager.js?v=20250127:954 解析字符串格式数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:969 确定的数据源类型: dataItem
bi-data-source-manager.js?v=20250127:970 完整数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:990 处理监控项数据源...
bi-data-source-manager.js?v=20250127:1128 === 处理监控项数据 ===
bi-data-source-manager.js?v=20250127:1129 数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:1130 组件类型: column-percentage-chart
bi-data-source-manager.js?v=20250127:1133 数据项ID: 0dc28b35-6f8b-4111-907c-784385cfa84a
bi-data-source-manager.js?v=20250127:1147 发送监控项数据请求: {widgetType: 'column-percentage-chart', dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', …}
bi-data-source-manager.js?v=20250127:1160 收到监控项数据响应: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-data-source-manager.js?v=20250127:1168 监控项数据格式验证: {hasLabels: false, hasValues: false, hasValue: true, labelsLength: 0, valuesLength: 0, …}
bi-data-source-manager.js?v=20250127:995 数据获取结果: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-data-source-manager.js?v=20250127:996 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2966 column-percentage-chart {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: true, hasData: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9812 更新柱状百分比图，charts[2966]: false
bi-dashboard-designer.js?v=20250127-gauge-fix:9813 柱状百分比图数据: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9825  柱状百分比图实例不存在: echart-2966
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9825
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:12448
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: properties
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:17.956Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-data-source-manager.js?v=20250127:202 已清理组件 2968 的配置上下文
bi-data-source-manager.js?v=20250127:174 为组件 2968 (status-indicator) 创建配置上下文
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticLabels
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticValues
bi-data-source-manager.js?v=20250127:424 组件 2968 数据源配置收集完成: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11441 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11057 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11064 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250127-gauge-fix:11075 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11109 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:12136 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12137 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:12138 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12154 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4554 === 状态指示器配置更新开始 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4555 更新前config: {
  "title": "",
  "shape": "square",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4648 更新后config: {
  "title": "",
  "shape": "rectangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4649 === 状态指示器配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4652 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4653 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:4654 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:4655 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4656 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4665 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4666 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11411 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11412 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:11413 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:11414 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:11419 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:11420 配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:11421 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11434 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250127-gauge-fix:11436 === 追踪结束 ===
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: null
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "rectangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-rectangle enhanced-visual status-offline" id=​"indicator-1753841297961" style=​"height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ width:​ 90px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-rectangle enhanced-visual status-offline" id=​"indicator-1753841297961" style=​"height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ width:​ 90px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: rectangle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:4818 实时应用属性: {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","staticLabels":"一月\\n二月\\…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110', enableDataTransform: false, transformOperation: 'add', …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","staticLabels":"一月\n二月\n三月\n四月\n五月\n六月","staticValues":"120\n200\n150\n80\n70\n110","enableDataTransform":false,"transformOperation":"add","transformValue":1,"decimalPlaces":2,"dataSuffix":"","enableWaterTarget":false,"waterTargetSource":"manual","waterTargetValue":100,"waterTargetDevice":"","waterTargetDataItem":"","showTargetValues":true,"enableColumnTarget":false,"columnTargetSource":"manual","columnTargetValue":80,"columnTargetDevice":"","columnTargetDataItem":"","showColumnTargetLabel":true}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 42,
      "timestamp": "2025-07-30T02:08:18.066Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 42,
      "timestamp": "2025-07-30T02:08:18.066Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "rectangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 42,
      "timestamp": "2025-07-30T02:08:18.066Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "rectangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-rectangle enhanced-visual status-offline" id=​"indicator-1753841298068" style=​"height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ width:​ 90px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​设备离线​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-rectangle enhanced-visual status-offline" id=​"indicator-1753841298068" style=​"height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 8px;​ width:​ 90px;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: rectangle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​设备离线​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'rectangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:203
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250127-gauge-fix:4806
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8558
setTimeout
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8556
handleMouseUp_ @ 未知
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: properties
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:19.014Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-data-source-manager.js?v=20250127:202 已清理组件 2968 的配置上下文
bi-data-source-manager.js?v=20250127:174 为组件 2968 (status-indicator) 创建配置上下文
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticLabels
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticValues
bi-data-source-manager.js?v=20250127:424 组件 2968 数据源配置收集完成: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11441 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11057 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11064 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250127-gauge-fix:11075 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11109 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:12136 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12137 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:12138 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12154 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4554 === 状态指示器配置更新开始 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4555 更新前config: {
  "title": "",
  "shape": "rectangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4648 更新后config: {
  "title": "",
  "shape": "triangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4649 === 状态指示器配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4652 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4653 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:4654 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:4655 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4656 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4665 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4666 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11411 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11412 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:11413 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:11414 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:11419 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:11420 配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:11421 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11434 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250127-gauge-fix:11436 === 追踪结束 ===
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: null
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "triangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-triangle enhanced-visual status-offline" id=​"indicator-1753841299021" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-triangle enhanced-visual status-offline" id=​"indicator-1753841299021" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: triangle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:4818 实时应用属性: {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","staticLabels":"一月\\n二月\\…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110', enableDataTransform: false, transformOperation: 'add', …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","staticLabels":"一月\n二月\n三月\n四月\n五月\n六月","staticValues":"120\n200\n150\n80\n70\n110","enableDataTransform":false,"transformOperation":"add","transformValue":1,"decimalPlaces":2,"dataSuffix":"","enableWaterTarget":false,"waterTargetSource":"manual","waterTargetValue":100,"waterTargetDevice":"","waterTargetDataItem":"","showTargetValues":true,"enableColumnTarget":false,"columnTargetSource":"manual","columnTargetValue":80,"columnTargetDevice":"","columnTargetDataItem":"","showColumnTargetLabel":true}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 85,
      "timestamp": "2025-07-30T02:08:19.130Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 85,
      "timestamp": "2025-07-30T02:08:19.130Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "triangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 85,
      "timestamp": "2025-07-30T02:08:19.130Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "triangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-triangle enhanced-visual status-condition3" id=​"indicator-1753841299134" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(220, 53, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(220, 53, 69)​ 0%, rgb(169, 2, 18)​ 50%, rgb(118, 0, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​危险​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '危险', color: '#dc3545', condition: 'condition3'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-triangle enhanced-visual status-condition3" id=​"indicator-1753841299134" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(220, 53, 69, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(220, 53, 69)​ 0%, rgb(169, 2, 18)​ 50%, rgb(118, 0, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '危险', color: '#dc3545', condition: 'condition3'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: triangle
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #dc3545 0%, #a90212 50%, #760000 100%)', solid: '#dc3545', glow: 'rgba(220, 53, 69, 0.8)', shadow: 'rgba(220, 53, 69, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-condition3
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​危险​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '危险', color: '#dc3545', condition: 'condition3'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '危险', color: '#dc3545', condition: 'condition3'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'triangle', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:203
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250127-gauge-fix:4806
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8558
setTimeout
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8556
handleMouseUp_ @ 未知
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: properties
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:20.069Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-data-source-manager.js?v=20250127:202 已清理组件 2968 的配置上下文
bi-data-source-manager.js?v=20250127:174 为组件 2968 (status-indicator) 创建配置上下文
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticLabels
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticValues
bi-data-source-manager.js?v=20250127:424 组件 2968 数据源配置收集完成: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11441 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11057 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11064 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250127-gauge-fix:11075 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11109 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:12136 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12137 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:12138 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12154 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4554 === 状态指示器配置更新开始 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4555 更新前config: {
  "title": "",
  "shape": "triangle",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4648 更新后config: {
  "title": "",
  "shape": "diamond",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4649 === 状态指示器配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4652 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4653 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:4654 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:4655 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4656 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4665 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4666 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11411 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11412 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:11413 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:11414 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:11419 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:11420 配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:11421 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11434 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250127-gauge-fix:11436 === 追踪结束 ===
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: null
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "diamond",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-diamond enhanced-visual status-offline" id=​"indicator-1753841300076" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 12px;​ transform:​ rotate(45deg)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-diamond enhanced-visual status-offline" id=​"indicator-1753841300076" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 12px;​ transform:​ rotate(45deg)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: diamond
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:4818 实时应用属性: {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","staticLabels":"一月\\n二月\\…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110', enableDataTransform: false, transformOperation: 'add', …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","staticLabels":"一月\n二月\n三月\n四月\n五月\n六月","staticValues":"120\n200\n150\n80\n70\n110","enableDataTransform":false,"transformOperation":"add","transformValue":1,"decimalPlaces":2,"dataSuffix":"","enableWaterTarget":false,"waterTargetSource":"manual","waterTargetValue":100,"waterTargetDevice":"","waterTargetDataItem":"","showTargetValues":true,"enableColumnTarget":false,"columnTargetSource":"manual","columnTargetValue":80,"columnTargetDevice":"","columnTargetDataItem":"","showColumnTargetLabel":true}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 40,
      "timestamp": "2025-07-30T02:08:20.184Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: false, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 40,
      "timestamp": "2025-07-30T02:08:20.184Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "diamond",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 40,
      "timestamp": "2025-07-30T02:08:20.184Z"
    }
  ],
  "deviceOffline": false,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "diamond",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-diamond enhanced-visual status-condition2" id=​"indicator-1753841300186" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(255, 193, 7, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 12px;​ transform:​ rotate(45deg)​;​ background:​ linear-gradient(135deg, rgb(255, 193, 7)​ 0%, rgb(204, 142, 0)​ 50%, rgb(153, 91, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​警告​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '警告', color: '#ffc107', condition: 'condition2'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-diamond enhanced-visual status-condition2" id=​"indicator-1753841300186" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(255, 193, 7, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ border-radius:​ 12px;​ transform:​ rotate(45deg)​;​ background:​ linear-gradient(135deg, rgb(255, 193, 7)​ 0%, rgb(204, 142, 0)​ 50%, rgb(153, 91, 0)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '警告', color: '#ffc107', condition: 'condition2'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: diamond
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #ffc107 0%, #cc8e00 50%, #995b00 100%)', solid: '#ffc107', glow: 'rgba(255, 193, 7, 0.8)', shadow: 'rgba(255, 193, 7, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-condition2
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​警告​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '警告', color: '#ffc107', condition: 'condition2'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '警告', color: '#ffc107', condition: 'condition2'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'diamond', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:203
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250127-gauge-fix:4806
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8558
setTimeout
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8556
handleMouseUp_ @ 未知
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:464 组件 2968 (status-indicator) 已标记为修改: properties
bi-dashboard-designer.js?v=20250127-gauge-fix:4024 缓存组件 2968 配置，时间戳: 2025-07-30T02:08:21.228Z
bi-dashboard-designer.js?v=20250127-gauge-fix:532 同步组件 2968 配置缓存
bi-data-source-manager.js?v=20250127:202 已清理组件 2968 的配置上下文
bi-data-source-manager.js?v=20250127:174 为组件 2968 (status-indicator) 创建配置上下文
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: dataSourceType
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticLabels
bi-data-source-manager.js?v=20250127:244 为组件 2968 缓存DOM元素: staticValues
bi-data-source-manager.js?v=20250127:424 组件 2968 数据源配置收集完成: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11441 === 开始收集样式配置 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11057 🔧 [修复版本 v2.0] 开始收集多折线样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11064 🔧 [修复版本] 折线数量: 1 (来源: 实际数据 )
bi-dashboard-designer.js?v=20250127-gauge-fix:11075 收集折线 1 的样式配置
bi-dashboard-designer.js?v=20250127-gauge-fix:11109 收集到的多折线样式配置: [{…}]
bi-dashboard-designer.js?v=20250127-gauge-fix:12136 === 样式配置收集完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12137 收集到的配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:12138 重要配置项检查: {showDataLabels: false, primaryColor: undefined, theme: 'default', showChartTitle: true, showLegend: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12154 完整样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4554 === 状态指示器配置更新开始 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4555 更新前config: {
  "title": "",
  "shape": "diamond",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4648 更新后config: {
  "title": "",
  "shape": "hexagon",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-dashboard-designer.js?v=20250127-gauge-fix:4649 === 状态指示器配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4652 === 实时应用属性：样式配置更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:4653 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:4654 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:4655 更新前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4656 新的styleConfig: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:4665 更新后styleConfig字符串: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:4666 === 样式配置更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11411 === 样式配置生命周期追踪：实时应用后 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:11412 组件ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:11413 组件类型: status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:11414 当前styleConfig: {"theme":"default","backgroundColor":"#ffffff","borderColor":"#dee2e6","showTitle":true,"transparent":false,"titleFontSize":16,"showLegend":true,"showChartTitle":true,"borderlessMode":false,"showDataLabels":false,"showYAxis":true,"showYAxisLabels":true,"showXAxis":true,"yAxisFontSize":12,"yAxisFontColor":"#8c8c8c","xAxisFontSize":12,"xAxisFontColor":"#8c8c8c","dataLabelFontSize":11,"dataLabelFontColor":"#5470c6","legendFontSize":12,"legendFontColor":"#666666","lineWidth":3,"lineType":"solid","lineColor":"#5470c6","useGradientLine":false,"lineGradientColor":"#91cc75","smoothLine":true,"showArea":false,"useGradientArea":true,"areaStartColor":"#5470c6","areaMidColor":"#91cc75","areaEndColor":"#ffffff","showSymbol":true,"symbolType":"circle","symbolSize":8,"symbolColor":"#5470c6","enableShadow":false,"shadowBlur":10,"shadowOffsetY":3,"shadowColor":"#000000","multiLineSmooth":true,"multiLineShowSymbol":true,"multiLineSymbolSize":6,"multiLineWidth":2,"multiLineShowArea":false,"multiLineEnableDualYAxis":false,"useIndividualStyles":true,"lineCount":1,"individualLineStyles":[{"color":"#5470c6","width":2,"type":"solid","useGradient":false,"gradientColor":"#91cc75","showSymbol":true,"symbolSize":6,"symbolType":"circle","symbolColor":"#5470c6","symbolBorderColor":"#ffffff","symbolBorderWidth":0,"showArea":false,"areaOpacity":30,"showLabel":false,"labelPosition":"top","labelColor":"#333333","labelFontSize":12,"labelFontWeight":"normal"}],"barWidth":60,"borderRadius":0,"borderRadiusMode":"top","barColor":"#5470c6","useGradientBar":false,"barStartColor":"#5470c6","barEndColor":"#91cc75","gaugeMin":0,"gaugeMax":100,"gaugeUnit":"%","gaugeRadius":80,"pieType":"ring","pieRadius":70,"pieInnerRadius":40,"pieOuterRadius":70,"pieBorderRadius":0,"pieBorderRadiusMode":"all","showPieBorder":true,"pieShowDataLabels":true,"useCustomPieColors":false,"waterBorderWidth":5,"waterBorderColor":"#1890ff","useWaterBorderGradient":false,"waterBorderGradientColor":"#52c41a","waterWaveColor":"#1890ff","useWaterWaveGradient":false,"waterWaveGradientColor":"#52c41a","waterAmplitude":20,"showWaterLabel":true,"waterLabelFontSize":20,"waterLabelFontColor":"#333333","showPercentageSymbol":true,"columnBarWidth":40,"columnBorderRadius":8,"columnUseGradient":true,"columnStartColor":"#4facfe","columnEndColor":"#00f2fe","columnEnableShadow":true,"columnShadowColor":"#000000","columnShadowBlur":10,"columnShowDataLabel":true,"columnDataLabelPosition":"vertical-top","columnDataLabelColor":"#333333","columnDataLabelFontSize":14,"columnShowPercentage":true,"columnShowBarBackground":true,"columnBarBackgroundColor":"#f5f5f5","columnAllowOverflow":true,"textFontFamily":"Arial, sans-serif","textFontSize":16,"textFontColor":"#333333","textFontWeight":"normal","textFontStyle":"normal","textDecoration":"none","textAlign":"left","textVerticalAlign":"top","textLineHeight":1.5,"textLetterSpacing":0,"textPadding":10,"textBorderRadius":0,"enableTextGradient":false,"textGradientType":"linear","textGradientStartColor":"#ffffff","textGradientEndColor":"#f0f0f0","textGradientAngle":0,"imageUrl":"","imageBorderRadius":0,"imageRotation":0,"imageOpacity":100,"imageObjectFit":"contain","decorationObjectFit":"contain","decorationOpacity":100,"decorationRotation":0,"decorationBlendMode":"normal","tableStyle":"table","tableSize":"table-normal","showTableHeader":true,"tableHeaderBgColor":"#f8f9fa","tableHeaderTextColor":"#212529","tableBodyBgColor":"#ffffff","tableBodyTextColor":"#212529","tableBorderColor":"#dee2e6","tableHeaderFontSize":14,"tableBodyFontSize":12,"tableFontWeight":"normal","enableTableCarousel":false,"tableRowsPerPage":5,"tableCarouselInterval":3,"showTablePagination":false,"hyperlinkUrl":"","hyperlinkText":"点击跳转","hyperlinkTarget":"_self","hyperlinkFontSize":16,"hyperlinkFontColor":"#007bff","hyperlinkFontWeight":"normal","hyperlinkTextAlign":"left","hyperlinkFontStyle":"normal","hyperlinkTextDecoration":"none","hyperlinkOpacity":100,"htmlOpacity":100,"videoUrl":"","poster":"","autoplay":false,"loop":false,"muted":true,"controls":true,"opacity":100,"borderWidth":0,"shadow":false,"timeFormat":"YYYY-MM-DD HH:mm:ss","timeFontSize":24,"timeFontWeight":"normal","timeFontStyle":"normal","timeColorType":"solid","timeColor":"#333333","timeGradientStart":"#667eea","timeGradientEnd":"#764ba2","timeGradientDirection":"to right","timeOpacity":100,"timeTextShadow":false}
bi-dashboard-designer.js?v=20250127-gauge-fix:11419 解析后的样式配置: {theme: 'default', backgroundColor: '#ffffff', borderColor: '#dee2e6', showTitle: true, transparent: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:11420 配置项数量: 169
bi-dashboard-designer.js?v=20250127-gauge-fix:11421 重要配置检查: {showDataLabels: false, primaryColor: undefined, theme: 'default'}
bi-dashboard-designer.js?v=20250127-gauge-fix:11434 附加信息: {formConfigKeys: Array(169), hasFormConfig: true}
bi-dashboard-designer.js?v=20250127-gauge-fix:11436 === 追踪结束 ===
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: null
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "hexagon",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-hexagon enhanced-visual status-offline" id=​"indicator-1753841301234" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-hexagon enhanced-visual status-offline" id=​"indicator-1753841301234" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '无数据', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: hexagon
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:12404 组件 2968 自动刷新条件检查: {dataSourceType: 'static', hasDataItemId: false, hasDataSetId: false, isMultiDataSet: false, dataSetsCount: 0, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12452 设置组件 2968 静态数据源一次性更新 (数据源类型: static)
bi-dashboard-designer.js?v=20250127-gauge-fix:4818 实时应用属性: {id: 2968, type: 'status-indicator', x: 214, y: 342, width: 300, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2968 status-indicator
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2968 status-indicator
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2968, type: 'status-indicator', dataSourceConfig: '{"dataSourceType":"static","staticLabels":"一月\\n二月\\…nTargetDataItem":"","showColumnTargetLabel":true}'}
bi-data-source-manager.js?v=20250127:932 处理状态指示器组件数据...
bi-data-source-manager.js?v=20250127:3093 === 处理状态指示器数据 ===
bi-data-source-manager.js?v=20250127:3094 数据源配置: {dataSourceType: 'static', staticLabels: '一月\n二月\n三月\n四月\n五月\n六月', staticValues: '120\n200\n150\n80\n70\n110', enableDataTransform: false, transformOperation: 'add', …}
bi-data-source-manager.js?v=20250127:3099 状态指示器未配置数据源，返回示例数据
bi-dashboard-designer.js?v=20250127-gauge-fix:12471 组件 2968 静态数据源更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2968 status-indicator {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: false, hasData: true, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9874 === 状态指示器数据更新 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9875 Widget ID: 2968
bi-dashboard-designer.js?v=20250127-gauge-fix:9876 Widget Config: {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9877 Data Source Config: {"dataSourceType":"static","staticLabels":"一月\n二月\n三月\n四月\n五月\n六月","staticValues":"120\n200\n150\n80\n70\n110","enableDataTransform":false,"transformOperation":"add","transformValue":1,"decimalPlaces":2,"dataSuffix":"","enableWaterTarget":false,"waterTargetSource":"manual","waterTargetValue":100,"waterTargetDevice":"","waterTargetDataItem":"","showTargetValues":true,"enableColumnTarget":false,"columnTargetSource":"manual","columnTargetValue":80,"columnTargetDevice":"","columnTargetDataItem":"","showColumnTargetLabel":true}
bi-dashboard-designer.js?v=20250127-gauge-fix:9878 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 97,
      "timestamp": "2025-07-30T02:08:21.340Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:192 更新状态指示器组件: 2968 {success: true, data: Array(1), deviceOffline: true, message: '使用示例数据'}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 97,
      "timestamp": "2025-07-30T02:08:21.340Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "hexagon",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​无数据​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:277 指示器元素不存在，重新创建...
bi-status-indicator.js?v=20250730-fixed-shapes:41 渲染状态指示器组件: 2968 {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:254 === 状态指示器状态更新 ===
bi-status-indicator.js?v=20250730-fixed-shapes:255 Widget ID: 2968
bi-status-indicator.js?v=20250730-fixed-shapes:256 接收到的数据: {
  "success": true,
  "data": [
    {
      "value": 97,
      "timestamp": "2025-07-30T02:08:21.340Z"
    }
  ],
  "deviceOffline": true,
  "message": "使用示例数据"
}
bi-status-indicator.js?v=20250730-fixed-shapes:257 Widget配置: {
  "title": "",
  "shape": "hexagon",
  "size": 60,
  "borderWidth": 2,
  "borderColor": "#cccccc",
  "condition1": {
    "min": 0,
    "max": 30,
    "name": "正常",
    "color": "#28a745"
  },
  "condition2": {
    "min": 31,
    "max": 70,
    "name": "警告",
    "color": "#ffc107"
  },
  "condition3": {
    "min": 71,
    "max": 100,
    "name": "危险",
    "color": "#dc3545"
  },
  "offlineColor": "#6c757d",
  "showConditionName": true,
  "fontSize": 12,
  "fontColor": "#333333",
  "fontWeight": "normal",
  "enableAnimation": true,
  "animationDuration": 300,
  "showChartTitle": true,
  "titleFontSize": 16
}
bi-status-indicator.js?v=20250730-fixed-shapes:260 查找容器: #widget-2968 .status-indicator-container
bi-status-indicator.js?v=20250730-fixed-shapes:261 找到的容器: <div class=​"status-indicator-container" style=​"width:​ 100%;​ height:​ 100%;​ display:​ flex;​ flex-direction:​ column;​ align-items:​ center;​ justify-content:​ center;​ position:​ relative;​">​…​</div>​flex
bi-status-indicator.js?v=20250730-fixed-shapes:270 找到的指示器元素: <div class=​"-hexagon enhanced-visual status-offline" id=​"indicator-1753841301342" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:271 找到的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​设备离线​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: <div class=​"-hexagon enhanced-visual status-offline" id=​"indicator-1753841301342" style=​"width:​ 60px;​ height:​ 60px;​ border:​ 2px solid rgb(204, 204, 204)​;​ position:​ relative;​ transition:​ 300ms cubic-bezier(0.4, 0, 0.2, 1)​;​ box-sizing:​ border-box;​ box-shadow:​ rgba(108, 117, 125, 0.8)​ 0px 0px 20px, rgba(0, 0, 0, 0.1)​ 0px 4px 8px, rgba(0, 0, 0, 0.06)​ 0px 2px 4px, rgba(255, 255, 255, 0.1)​ 0px 1px 0px inset;​ backdrop-filter:​ blur(10px)​;​ background:​ linear-gradient(135deg, rgb(108, 117, 125)​ 0%, rgb(57, 66, 74)​ 50%, rgb(6, 15, 23)​ 100%)​;​">​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:374 形状: hexagon
bi-status-indicator.js?v=20250730-fixed-shapes:378 颜色配置: {gradient: 'linear-gradient(135deg, #6c757d 0%, #39424a 50%, #060f17 100%)', solid: '#6c757d', glow: 'rgba(108, 117, 125, 0.8)', shadow: 'rgba(108, 117, 125, 0.3)'}
bi-status-indicator.js?v=20250730-fixed-shapes:386 添加状态类名: status-offline
bi-status-indicator.js?v=20250730-fixed-shapes:387 === 指示器颜色更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-status-indicator.js?v=20250730-fixed-shapes:284 重新创建后的指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:285 重新创建后的标签元素: <div class=​"status-indicator-label" style=​"margin-top:​ 8px;​ font-size:​ 12px;​ color:​ rgb(51, 51, 51)​;​ font-weight:​ normal;​ text-align:​ center;​ white-space:​ nowrap;​">​设备离线​</div>​
bi-status-indicator.js?v=20250730-fixed-shapes:290 判断的状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:363 === 更新指示器颜色（增强版）===
bi-status-indicator.js?v=20250730-fixed-shapes:364 指示器元素: null
bi-status-indicator.js?v=20250730-fixed-shapes:365 状态: {name: '设备离线', color: '#6c757d', condition: 'offline'}
bi-status-indicator.js?v=20250730-fixed-shapes:366 配置: {title: '', chartType: undefined, shape: 'hexagon', size: 60, borderWidth: 2, …}
bi-status-indicator.js?v=20250730-fixed-shapes:369  指示器元素不存在，无法更新颜色
updateIndicatorColor @ bi-status-indicator.js?v=20250730-fixed-shapes:369
updateIndicatorStatus @ bi-status-indicator.js?v=20250730-fixed-shapes:293
updateWidget @ bi-status-indicator.js?v=20250730-fixed-shapes:203
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9885
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
waitForChartInitStatic @ bi-dashboard-designer.js?v=20250127-gauge-fix:12470
setTimeout
setupWidgetAutoRefresh @ bi-dashboard-designer.js?v=20250127-gauge-fix:12475
applyPropertiesRealTime @ bi-dashboard-designer.js?v=20250127-gauge-fix:4806
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8558
setTimeout
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:8556
handleMouseUp_ @ 未知
bi-status-indicator.js?v=20250730-fixed-shapes:300 === 状态指示器状态更新完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9886 状态指示器更新完成
bi-dashboard-designer.js?v=20250127-gauge-fix:12512 安全更新组件数据: 2966 column-percentage-chart
bi-dashboard-designer.js?v=20250127-gauge-fix:9682 更新组件数据: 2966 column-percentage-chart
bi-data-source-manager.js?v=20250127:908 === BiDataSourceManager - 开始获取组件数据 ===
bi-data-source-manager.js?v=20250127:909 组件信息: {id: 2966, type: 'column-percentage-chart', dataSourceConfig: '{"dataSourceType":"dataItem","deviceId":"23732a8f-…TargetDataItem":"","showColumnTargetLabel":false}'}
bi-data-source-manager.js?v=20250127:954 解析字符串格式数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:969 确定的数据源类型: dataItem
bi-data-source-manager.js?v=20250127:970 完整数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:990 处理监控项数据源...
bi-data-source-manager.js?v=20250127:1128 === 处理监控项数据 ===
bi-data-source-manager.js?v=20250127:1129 数据源配置: {dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', historyCount: 50, …}
bi-data-source-manager.js?v=20250127:1130 组件类型: column-percentage-chart
bi-data-source-manager.js?v=20250127:1133 数据项ID: 0dc28b35-6f8b-4111-907c-784385cfa84a
bi-data-source-manager.js?v=20250127:1147 发送监控项数据请求: {widgetType: 'column-percentage-chart', dataSourceType: 'dataItem', deviceId: '23732a8f-ed36-4382-9444-6195fb621a26', dataItemId: '0dc28b35-6f8b-4111-907c-784385cfa84a', dataMode: 'realtime', …}
bi-data-source-manager.js?v=20250127:1160 收到监控项数据响应: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-data-source-manager.js?v=20250127:1168 监控项数据格式验证: {hasLabels: false, hasValues: false, hasValue: true, labelsLength: 0, valuesLength: 0, …}
bi-data-source-manager.js?v=20250127:995 数据获取结果: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-data-source-manager.js?v=20250127:996 === BiDataSourceManager - 数据获取完成 ===
bi-dashboard-designer.js?v=20250127-gauge-fix:9693 转换后的数据: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9705 更新组件显示: 2966 column-percentage-chart {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9708 数据格式验证: {success: true, hasLabels: false, hasValues: false, hasValue: true, hasData: false, …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9812 更新柱状百分比图，charts[2966]: false
bi-dashboard-designer.js?v=20250127-gauge-fix:9813 柱状百分比图数据: {address: '40001', success: true, name: '拔楦数量', value: 36, deviceName: '拔楦机', …}
bi-dashboard-designer.js?v=20250127-gauge-fix:9825  柱状百分比图实例不存在: echart-2966
updateWidgetDisplay @ bi-dashboard-designer.js?v=20250127-gauge-fix:9825
updateWidgetData @ bi-dashboard-designer.js?v=20250127-gauge-fix:9694
await in updateWidgetData
updateWidgetDataSafe @ bi-dashboard-designer.js?v=20250127-gauge-fix:12523
(匿名) @ bi-dashboard-designer.js?v=20250127-gauge-fix:12448
