package com.example.repository;

import com.example.model.DataHistory;
import com.example.model.DataItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface DataHistoryRepository extends JpaRepository<DataHistory, Long> {
    
    List<DataHistory> findByDataItemOrderByTimestampDesc(DataItem dataItem);
    
    @Query("SELECT dh FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp BETWEEN :startTime AND :endTime ORDER BY dh.timestamp DESC")
    List<DataHistory> findByDataItemAndTimestampBetween(
        @Param("dataItem") DataItem dataItem,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );
    
    @Query("DELETE FROM DataHistory dh WHERE dh.timestamp < :beforeTime")
    void deleteDataBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);
    
    @Modifying
    @Query("DELETE FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp < :beforeTime")
    void deleteByDataItemAndTimestampBefore(
        @Param("dataItem") DataItem dataItem,
        @Param("beforeTime") LocalDateTime beforeTime
    );
    
    @Modifying
    @Transactional
    @Query("DELETE FROM DataHistory dh WHERE dh.dataItem.id NOT IN :activeItemIds")
    int cleanInvalidHistoryData(@Param("activeItemIds") List<String> activeItemIds);

    @Modifying
    @Transactional
    @Query("DELETE FROM DataHistory dh")
    int cleanAllHistoryData();

    // 统计查询方法
    @Query("SELECT MAX(dh.value) FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp BETWEEN :startTime AND :endTime")
    Optional<Integer> findMaxValueByDataItemAndTimestampBetween(
        @Param("dataItem") DataItem dataItem,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query("SELECT MIN(dh.value) FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp BETWEEN :startTime AND :endTime")
    Optional<Integer> findMinValueByDataItemAndTimestampBetween(
        @Param("dataItem") DataItem dataItem,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query("SELECT AVG(CAST(dh.value AS double)) FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp BETWEEN :startTime AND :endTime")
    Optional<Double> findAvgValueByDataItemAndTimestampBetween(
        @Param("dataItem") DataItem dataItem,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query("SELECT COUNT(dh) FROM DataHistory dh WHERE dh.dataItem = :dataItem AND dh.timestamp BETWEEN :startTime AND :endTime")
    Long countByDataItemAndTimestampBetween(
        @Param("dataItem") DataItem dataItem,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );
}