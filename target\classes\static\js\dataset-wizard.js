/**
 * 数据集创建向导
 */
class DataSetWizard {
    constructor() {
        this.currentStep = 1;
        this.maxSteps = 4;
        this.selectedDataSource = null;
        this.selectedTable = null;
        this.selectedFields = [];
        this.queryConfig = null;
        this.transformConfig = null;
        this.fieldConfiguration = null;
        this.outputLimit = null;
        this.advancedConfig = null;

        // 编辑模式相关
        this.isEditMode = false;
        this.editingDataSetId = null;
        this.originalDataSet = null;
    }

    /**
     * 初始化向导
     */
    init() {
        this.bindEvents();
        this.loadDataSources();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 向导导航按钮
        document.getElementById('wizardNextBtn').addEventListener('click', () => {
            this.nextStep();
        });

        document.getElementById('wizardPrevBtn').addEventListener('click', () => {
            this.prevStep();
        });

        document.getElementById('wizardSaveBtn').addEventListener('click', () => {
            this.saveDataSet();
        });

        // 数据源选择变化
        document.getElementById('wizardDataSource').addEventListener('change', (e) => {
            this.onDataSourceChange(e.target.value);
        });

        // 预览数据按钮
        document.getElementById('previewDataBtn').addEventListener('click', () => {
            this.previewData();
        });

        // 自定义SQL变化
        document.getElementById('customSql').addEventListener('input', () => {
            this.onSqlChange();
        });

        // 刷新数据源按钮
        document.getElementById('refreshDataSourcesBtn').addEventListener('click', () => {
            this.refreshDataSources();
        });
    }

    /**
     * 显示向导（创建模式）
     */
    show() {
        this.resetToCreateMode();
        this.currentStep = 1;
        this.updateStepDisplay();

        // 每次显示时重新加载数据源列表，确保数据最新
        this.loadDataSources();

        const modal = new bootstrap.Modal(document.getElementById('dataSetWizardModal'));
        modal.show();
    }

    /**
     * 显示向导（编辑模式）
     */
    async showForEdit(dataSetId) {
        this.isEditMode = true;
        this.editingDataSetId = dataSetId;

        try {
            console.log('开始编辑数据集:', dataSetId);

            // 1. 先显示模态框
            this.currentStep = 1;
            this.updateStepDisplay();

            const modal = new bootstrap.Modal(document.getElementById('dataSetWizardModal'));
            modal.show();

            // 2. 监听模态框显示完成事件，然后加载数据
            const modalElement = document.getElementById('dataSetWizardModal');
            modalElement.addEventListener('shown.bs.modal', async () => {
                try {
                    console.log('模态框显示完成，开始加载数据集信息');
                    await this.loadDataSetForEdit(dataSetId);
                } catch (error) {
                    console.error('加载数据集信息失败:', error);
                    alert('加载数据集信息失败: ' + error.message);
                }
            }, { once: true }); // 只执行一次

        } catch (error) {
            console.error('打开数据集编辑失败:', error);
            alert('打开数据集编辑失败: ' + error.message);
        }
    }

    /**
     * 重置为创建模式
     */
    resetToCreateMode() {
        this.isEditMode = false;
        this.editingDataSetId = null;
        this.originalDataSet = null;
        this.selectedDataSource = null;
        this.selectedTable = null;
        this.selectedFields = [];
        this.queryConfig = null;
        this.transformConfig = null;
        this.fieldConfiguration = null;
        this.outputLimit = null;
        this.advancedConfig = null;

        // 清空表单
        this.clearAllForms();
    }

    /**
     * 加载数据集用于编辑
     */
    async loadDataSetForEdit(dataSetId) {
        try {
            const response = await fetch(`/api/dataset/${dataSetId}`);
            const result = await response.json();

            if (result.success && result.data) {
                this.originalDataSet = result.data;
                await this.populateEditForm(result.data);
                console.log('数据集加载成功:', result.data.name);
            } else {
                throw new Error(result.message || '加载数据集失败');
            }
        } catch (error) {
            console.error('加载数据集失败:', error);
            throw error;
        }
    }

    /**
     * 填充编辑表单
     */
    async populateEditForm(dataSet) {
        console.log('开始填充编辑表单:', dataSet);

        try {
            // 1. 设置数据源
            this.selectedDataSource = dataSet.dataSourceId;
            console.log('设置数据源ID:', dataSet.dataSourceId);

            // 2. 加载数据源列表并设置选中项
            await this.loadDataSources();
            await this.waitForElement('wizardDataSource');

            const dataSourceSelect = document.getElementById('wizardDataSource');
            if (dataSourceSelect) {
                dataSourceSelect.value = dataSet.dataSourceId;
                console.log('数据源选择框设置完成:', dataSourceSelect.value);
            }

            // 3. 解析查询配置（先解析，后面需要用到）
            if (dataSet.queryConfig) {
                try {
                    this.queryConfig = JSON.parse(dataSet.queryConfig);
                    console.log('解析查询配置:', this.queryConfig);
                } catch (e) {
                    console.warn('解析查询配置失败:', e);
                }
            }

            // 4. 解析转换配置（先解析，后面需要用到）
            if (dataSet.transformConfig) {
                try {
                    this.transformConfig = JSON.parse(dataSet.transformConfig);
                    console.log('解析转换配置:', this.transformConfig);
                } catch (e) {
                    console.warn('解析转换配置失败:', e);
                }
            }

            // 5. 加载表元数据
            await this.loadTableMetadata();
            console.log('表元数据加载完成');

            // 6. 恢复表选择（基于SQL或转换配置）
            await this.restoreTableSelection();

            // 7. 设置SQL查询
            if (this.queryConfig && this.queryConfig.sql) {
                await this.waitForElement('customSql');
                const sqlTextarea = document.getElementById('customSql');
                if (sqlTextarea) {
                    sqlTextarea.value = this.queryConfig.sql;
                    console.log('SQL设置完成:', this.queryConfig.sql);
                }
            }

            // 8. 恢复字段角色配置
            if (this.transformConfig) {
                await this.restoreFieldConfiguration(this.transformConfig);
            }

            // 9. 填充基本信息
            await this.fillBasicInfo(dataSet);

            console.log('编辑表单填充完成');
        } catch (error) {
            console.error('填充编辑表单失败:', error);
            throw error;
        }
    }

    /**
     * 等待DOM元素准备完成
     */
    async waitForElement(elementId, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                const element = document.getElementById(elementId);
                if (element) {
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error(`等待元素 ${elementId} 超时`));
                    return;
                }

                setTimeout(checkElement, 100);
            };

            checkElement();
        });
    }

    /**
     * 等待字段选项加载完成
     */
    async waitForFieldOptions(timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkFieldOptions = () => {
                try {
                    const dateFieldSelect = document.getElementById('dateField');
                    const timeFieldSelect = document.getElementById('timeField');

                    // 检查元素是否存在
                    if (!dateFieldSelect || !timeFieldSelect) {
                        if (Date.now() - startTime > timeout) {
                            console.warn('字段选项元素等待超时');
                            resolve(false);
                        } else {
                            setTimeout(checkFieldOptions, 100);
                        }
                        return;
                    }

                    // 检查选项是否已加载（有超过默认选项的内容）
                    const dateFieldLoaded = dateFieldSelect.options.length > 1;
                    const timeFieldLoaded = timeFieldSelect.options.length > 1;

                    // 检查字段配置器是否有字段数据
                    const hasFieldData = window.fieldConfigurator &&
                                       window.fieldConfigurator.fields &&
                                       window.fieldConfigurator.fields.length > 0;

                    if (dateFieldLoaded && timeFieldLoaded && hasFieldData) {
                        console.log('字段选项加载完成');
                        resolve(true);
                    } else if (Date.now() - startTime > timeout) {
                        console.warn('字段选项加载超时，尝试主动更新');
                        // 超时时尝试主动更新字段选项
                        this.updateFieldOptionsIfNeeded();
                        resolve(false);
                    } else {
                        setTimeout(checkFieldOptions, 100);
                    }
                } catch (error) {
                    console.error('检查字段选项时出错:', error);
                    if (Date.now() - startTime > timeout) {
                        resolve(false);
                    } else {
                        setTimeout(checkFieldOptions, 100);
                    }
                }
            };

            checkFieldOptions();
        });
    }

    /**
     * 如果需要则更新字段选项
     */
    updateFieldOptionsIfNeeded() {
        try {
            if (window.fieldConfigurator && window.fieldConfigurator.fields && window.fieldConfigurator.fields.length > 0) {
                console.log('主动更新字段选项');
                window.fieldConfigurator.updateDateFieldOptions();
                window.fieldConfigurator.updateTimeFieldOptions();
                return true;
            }
        } catch (error) {
            console.error('更新字段选项失败:', error);
        }
        return false;
    }

    /**
     * 填充基本信息
     */
    async fillBasicInfo(dataSet) {
        try {
            await this.waitForElement('dataSetName');
            await this.waitForElement('dataSetDescription');

            const nameInput = document.getElementById('dataSetName');
            const descInput = document.getElementById('dataSetDescription');

            if (nameInput) {
                nameInput.value = dataSet.name || '';
                console.log('数据集名称设置完成:', dataSet.name);
            }

            if (descInput) {
                descInput.value = dataSet.description || '';
                console.log('数据集描述设置完成:', dataSet.description);
            }
        } catch (error) {
            console.warn('填充基本信息失败:', error);
        }
    }

    /**
     * 恢复表选择
     */
    async restoreTableSelection() {
        try {
            console.log('开始恢复表选择');

            // 等待表列表加载完成
            await this.waitForTableList();

            // 尝试从SQL中选择对应的表
            if (this.queryConfig && this.queryConfig.sql) {
                await this.trySelectTableFromSQL(this.queryConfig.sql);
            }

            console.log('表选择恢复完成');
        } catch (error) {
            console.error('恢复表选择失败:', error);
        }
    }

    /**
     * 等待表列表加载完成
     */
    async waitForTableList(timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkTableList = () => {
                const tableList = document.getElementById('tableList');
                const tableItems = tableList ? tableList.querySelectorAll('.table-item') : [];

                if (tableItems.length > 0) {
                    console.log('表列表已准备，共', tableItems.length, '个表');
                    resolve();
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    console.warn('等待表列表超时，继续执行');
                    resolve(); // 不要reject，继续执行
                    return;
                }

                setTimeout(checkTableList, 200);
            };

            checkTableList();
        });
    }

    /**
     * 恢复字段配置
     */
    async restoreFieldConfiguration(transformConfig) {
        if (!window.fieldConfigurator || !transformConfig) {
            console.warn('字段配置器未准备或转换配置为空');
            return;
        }

        try {
            // 设置字段角色
            const fieldRoles = {};
            if (transformConfig.labelField) {
                fieldRoles[transformConfig.labelField] = 'label';
            }
            if (transformConfig.valueField) {
                fieldRoles[transformConfig.valueField] = 'value';
            }

            console.log('准备恢复字段配置:', fieldRoles);
            console.log('准备恢复筛选条件:', transformConfig.filterConditions);

            // 等待字段配置器准备完成
            await this.waitForFieldConfigurator();

            // 恢复字段配置器的状态（保留现有表和字段信息）
            if (window.fieldConfigurator.fields && window.fieldConfigurator.fields.length > 0) {
                window.fieldConfigurator.fieldRoles = fieldRoles;

                // 更新UI显示
                Object.keys(fieldRoles).forEach(fieldName => {
                    window.fieldConfigurator.updateFieldItemAppearance(fieldName, fieldRoles[fieldName]);
                });

                // 恢复筛选条件
                if (transformConfig.filterConditions) {
                    console.log('开始恢复筛选条件:', transformConfig.filterConditions);
                    window.fieldConfigurator.setFilterConditions(transformConfig.filterConditions);
                    console.log('筛选条件恢复完成');
                } else {
                    console.log('没有筛选条件需要恢复');
                }

                console.log('字段配置恢复完成:', fieldRoles);
            } else {
                console.warn('字段配置器没有字段数据，跳过字段配置恢复');
            }
        } catch (error) {
            console.error('恢复字段配置失败:', error);
        }
    }

    /**
     * 等待字段配置器准备完成
     */
    async waitForFieldConfigurator(timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkConfigurator = () => {
                if (window.fieldConfigurator &&
                    window.fieldConfigurator.fields &&
                    window.fieldConfigurator.fields.length > 0) {
                    resolve();
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    console.warn('等待字段配置器超时，继续执行');
                    resolve(); // 不要reject，继续执行
                    return;
                }

                setTimeout(checkConfigurator, 200);
            };

            checkConfigurator();
        });
    }

    /**
     * 尝试从SQL中选择对应的表
     */
    async trySelectTableFromSQL(sql) {
        try {
            // 简单的表名提取（可以改进）
            const fromMatch = sql.match(/FROM\s+(\w+)/i);
            if (fromMatch && fromMatch[1]) {
                const tableName = fromMatch[1];
                console.log('从SQL中提取表名:', tableName);

                // 查找并选择对应的表
                const tableItems = document.querySelectorAll('.table-item');
                for (const tableItem of tableItems) {
                    const tableNameSpan = tableItem.querySelector('span');
                    if (tableNameSpan && tableNameSpan.textContent === tableName) {
                        console.log('找到匹配的表，自动选择:', tableName);

                        // 手动触发表选择逻辑
                        await this.selectTableByElement(tableItem, tableName);
                        return true; // 表示成功选择了表
                    }
                }

                console.warn('未找到匹配的表:', tableName);
            } else {
                console.warn('无法从SQL中提取表名:', sql);
            }
            return false;
        } catch (error) {
            console.warn('从SQL选择表失败:', error);
            return false;
        }
    }

    /**
     * 通过元素选择表
     */
    async selectTableByElement(tableElement, tableName) {
        try {
            // 查找表数据（从渲染的表列表中获取）
            const tableData = this.findTableDataByName(tableName);
            if (tableData) {
                console.log('找到表数据:', tableData);
                this.selectTable(tableData, tableElement);
                return true;
            } else {
                console.warn('未找到表数据:', tableName);
                return false;
            }
        } catch (error) {
            console.error('选择表失败:', error);
            return false;
        }
    }

    /**
     * 根据表名查找表数据
     */
    findTableDataByName(tableName) {
        // 从最近加载的表元数据中查找
        if (this.lastLoadedTables) {
            return this.lastLoadedTables.find(table => table.name === tableName);
        }
        return null;
    }

    /**
     * 清空所有表单
     */
    clearAllForms() {
        // 清空数据源选择
        const dataSourceSelect = document.getElementById('wizardDataSource');
        if (dataSourceSelect) {
            dataSourceSelect.value = '';
        }

        // 清空SQL
        const sqlTextarea = document.getElementById('customSql');
        if (sqlTextarea) {
            sqlTextarea.value = '';
        }

        // 清空基本信息
        const nameInput = document.getElementById('dataSetName');
        const descInput = document.getElementById('dataSetDescription');
        if (nameInput) nameInput.value = '';
        if (descInput) descInput.value = '';

        // 清空字段配置
        if (window.fieldConfigurator) {
            window.fieldConfigurator.resetFieldSelections();
        }
    }

    /**
     * 下一步
     */
    async nextStep() {
        if (!await this.validateCurrentStep()) {
            return;
        }

        if (this.currentStep < this.maxSteps) {
            this.currentStep++;
            this.updateStepDisplay();

            // 执行步骤特定的逻辑
            switch (this.currentStep) {
                case 2:
                    await this.ensureTableMetadataLoaded();
                    // 在编辑模式下，进入步骤2时恢复高级配置
                    if (this.isEditMode && this.originalDataSet) {
                        await this.restoreStep2Configurations();
                    }
                    break;
                case 3:
                    this.setupFieldMapping();
                    break;
                case 4:
                    this.setupPreview();
                    break;
            }
        }
    }

    /**
     * 上一步
     */
    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }

    /**
     * 更新步骤显示
     */
    updateStepDisplay() {
        // 更新模态框标题
        const modalTitle = document.querySelector('#dataSetWizardModal .modal-title');
        if (modalTitle) {
            modalTitle.textContent = this.isEditMode ? '编辑数据集' : '数据集创建向导';
        }

        // 更新步骤指示器
        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');

            if (stepNumber === this.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            }
        });

        // 显示/隐藏步骤内容
        document.querySelectorAll('.wizard-step').forEach((step, index) => {
            step.style.display = (index + 1 === this.currentStep) ? 'block' : 'none';
        });

        // 更新按钮状态
        const prevBtn = document.getElementById('wizardPrevBtn');
        const nextBtn = document.getElementById('wizardNextBtn');
        const saveBtn = document.getElementById('wizardSaveBtn');

        prevBtn.style.display = this.currentStep > 1 ? 'inline-block' : 'none';
        nextBtn.style.display = this.currentStep < this.maxSteps ? 'inline-block' : 'none';
        saveBtn.style.display = this.currentStep === this.maxSteps ? 'inline-block' : 'none';

        // 更新按钮文本
        if (this.currentStep < this.maxSteps) {
            nextBtn.textContent = '下一步';
        }

        // 更新保存按钮文本
        if (saveBtn) {
            saveBtn.textContent = this.isEditMode ? '保存修改' : '保存数据集';
        }
    }

    /**
     * 验证当前步骤
     */
    async validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                const dataSourceId = document.getElementById('wizardDataSource').value;
                if (!dataSourceId) {
                    alert('请选择数据源');
                    return false;
                }

                // 确保数据源已设置并且表元数据已加载
                if (this.selectedDataSource !== dataSourceId) {
                    this.selectedDataSource = dataSourceId;
                    // 如果数据源发生变化，需要重新加载表元数据
                    await this.loadTableMetadata();
                }

                return true;

            case 2:
                const sql = document.getElementById('customSql').value.trim();
                if (!sql) {
                    alert('请配置字段或输入SQL查询语句');
                    return false;
                }

                // 获取字段配置
                if (window.fieldConfigurator) {
                    const config = window.fieldConfigurator.getConfiguration();
                    this.fieldConfiguration = config;
                    this.queryConfig = { sql: sql };

                    // 检查是否至少配置了标签字段和数值字段
                    const hasLabel = Object.values(config.fieldRoles).includes('label');
                    const hasValue = Object.values(config.fieldRoles).includes('value');

                    if (!hasLabel || !hasValue) {
                        alert('请至少配置一个标签字段和一个数值字段');
                        return false;
                    }

                    // 保存筛选条件到transformConfig（临时保存，最终在步骤3确认）
                    this.tempFilterConditions = config.filterConditions;
                    console.log('临时保存筛选条件:', this.tempFilterConditions);
                } else {
                    this.queryConfig = { sql: sql };
                }

                // 保存输出限制设置
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement && outputLimitElement.value) {
                    const outputLimit = parseInt(outputLimitElement.value);
                    if (outputLimit > 0) {
                        this.outputLimit = outputLimit;
                        console.log('保存输出限制:', this.outputLimit);
                    }
                }

                // 保存日期格式化和聚合配置到临时变量
                this.saveAdvancedConfigurations();

                return true;

            case 3:
                const labelField = document.getElementById('labelField').value;
                const valueField = document.getElementById('valueField').value;
                const dataSetName = document.getElementById('dataSetName').value.trim();

                if (!labelField || !valueField) {
                    alert('请选择标签字段和数值字段');
                    return false;
                }

                if (!dataSetName) {
                    alert('请输入数据集名称');
                    return false;
                }

                this.transformConfig = {
                    labelField: labelField,
                    valueField: valueField,
                    filterConditions: this.tempFilterConditions || []
                };

                console.log('保存transformConfig:', this.transformConfig);
                return true;

            case 4:
                return true;

            default:
                return true;
        }
    }

    /**
     * 加载数据源列表
     */
    async loadDataSources() {
        try {
            const response = await fetch('/datasource/api/list');
            const result = await response.json();

            if (result.success) {
                this.populateDataSourceSelect(result.data);
            }
        } catch (error) {
            console.error('加载数据源失败:', error);
            alert('加载数据源失败: ' + error.message);
        }
    }

    /**
     * 刷新数据源列表
     */
    async refreshDataSources() {
        const refreshBtn = document.getElementById('refreshDataSourcesBtn');
        const originalText = refreshBtn.innerHTML;

        try {
            // 显示加载状态
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
            refreshBtn.disabled = true;

            const response = await fetch('/datasource/api/list');
            const result = await response.json();

            if (result.success) {
                this.populateDataSourceSelect(result.data);

                // 显示成功提示
                this.showDataSourceStatus('数据源列表已更新', 'success');
            } else {
                throw new Error(result.message || '刷新失败');
            }
        } catch (error) {
            console.error('刷新数据源失败:', error);
            this.showDataSourceStatus('刷新失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }
    }

    /**
     * 填充数据源选择框
     */
    populateDataSourceSelect(dataSources) {
        const select = document.getElementById('wizardDataSource');
        const currentValue = select.value; // 保存当前选择

        select.innerHTML = '<option value="">请选择数据源</option>';

        let hasCurrentValue = false;
        dataSources.forEach(dataSource => {
            if (dataSource.enabled && dataSource.type === 'database') {
                const option = document.createElement('option');
                option.value = dataSource.id;
                option.textContent = `${dataSource.name} (${dataSource.type})`;
                select.appendChild(option);

                if (dataSource.id === currentValue) {
                    hasCurrentValue = true;
                }
            }
        });

        // 如果之前的选择仍然存在，恢复选择
        if (hasCurrentValue) {
            select.value = currentValue;
        }

        console.log('数据源列表已更新，共', dataSources.filter(ds => ds.enabled && ds.type === 'database').length, '个可用数据源');
    }

    /**
     * 显示数据源状态信息
     */
    showDataSourceStatus(message, type) {
        // 创建或获取状态元素
        let statusElement = document.querySelector('.datasource-status');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.className = 'datasource-status mt-2';
            document.getElementById('wizardDataSource').parentNode.appendChild(statusElement);
        }

        statusElement.textContent = message;
        statusElement.className = `datasource-status mt-2 text-${type === 'success' ? 'success' : 'danger'}`;

        // 3秒后清除状态
        setTimeout(() => {
            statusElement.textContent = '';
            statusElement.className = 'datasource-status mt-2';
        }, 3000);
    }

    /**
     * 数据源选择变化
     */
    async onDataSourceChange(dataSourceId) {
        this.selectedDataSource = dataSourceId;
        if (dataSourceId) {
            console.log('选择数据源:', dataSourceId);
            // 立即加载表元数据，为步骤2准备数据
            await this.loadTableMetadata();
        } else {
            // 清除表数据
            this.clearTableData();
        }
    }

    /**
     * 加载表元数据
     */
    async loadTableMetadata() {
        if (!this.selectedDataSource) {
            this.showNoDataSourceMessage();
            return;
        }

        try {
            // 显示加载状态
            this.showTableLoadingState();

            const response = await fetch(`/api/dataset/metadata/${this.selectedDataSource}`);
            const result = await response.json();

            if (result.success && result.metadata) {
                this.renderTables(result.metadata.tables || []);
                this.hideTableLoadingState();
                console.log('表元数据加载成功，共', result.metadata.tables?.length || 0, '个表');
            } else {
                this.hideTableLoadingState();
                this.showTableErrorMessage('获取表信息失败: ' + (result.message || '未知错误'));
            }
        } catch (error) {
            console.error('加载表元数据失败:', error);
            this.hideTableLoadingState();
            this.showTableErrorMessage('加载表信息失败: ' + error.message);
        }
    }

    /**
     * 显示表加载状态
     */
    showTableLoadingState() {
        const tableList = document.getElementById('tableList');
        tableList.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载表信息...</div>
            </div>
        `;
    }

    /**
     * 隐藏表加载状态
     */
    hideTableLoadingState() {
        // 加载状态会被renderTables或错误信息替换，这里不需要特殊处理
    }

    /**
     * 显示未选择数据源消息
     */
    showNoDataSourceMessage() {
        const tableList = document.getElementById('tableList');
        tableList.innerHTML = `
            <div class="text-center p-3 text-muted">
                <i class="bi bi-info-circle mb-2" style="font-size: 2rem;"></i>
                <div>请先在步骤1中选择数据源</div>
            </div>
        `;

        // 清除字段配置
        this.clearFieldConfiguration();
    }

    /**
     * 显示表错误消息
     */
    showTableErrorMessage(message) {
        const tableList = document.getElementById('tableList');
        tableList.innerHTML = `
            <div class="text-center p-3">
                <i class="bi bi-exclamation-triangle text-warning mb-2" style="font-size: 2rem;"></i>
                <div class="text-danger">${message}</div>
                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="dataSetWizard.retryLoadTableMetadata()">
                    <i class="bi bi-arrow-clockwise"></i> 重试
                </button>
            </div>
        `;

        // 清除字段配置
        this.clearFieldConfiguration();
    }

    /**
     * 清除表数据
     */
    clearTableData() {
        const tableList = document.getElementById('tableList');
        tableList.innerHTML = '<div class="text-muted">请选择数据源</div>';

        // 清除字段配置
        this.clearFieldConfiguration();
    }

    /**
     * 清除字段配置
     */
    clearFieldConfiguration() {
        if (window.fieldConfigurator) {
            window.fieldConfigurator.setTableAndFields(null, []);
        }
    }

    /**
     * 重试加载表元数据
     */
    async retryLoadTableMetadata() {
        await this.loadTableMetadata();
    }

    /**
     * 确保表元数据已加载
     */
    async ensureTableMetadataLoaded() {
        if (!this.selectedDataSource) {
            this.showNoDataSourceMessage();
            return;
        }

        // 检查是否已有表数据
        const tableList = document.getElementById('tableList');
        const hasTableData = tableList.querySelector('.table-item');

        if (!hasTableData) {
            // 如果没有表数据，重新加载
            await this.loadTableMetadata();
        }
    }

    /**
     * 渲染表列表
     */
    renderTables(tables) {
        const tableList = document.getElementById('tableList');
        tableList.innerHTML = '';

        // 保存表数据以供后续使用
        this.lastLoadedTables = tables;

        if (tables.length === 0) {
            tableList.innerHTML = '<div class="text-muted">没有找到数据表</div>';
            return;
        }

        tables.forEach(table => {
            const tableItem = document.createElement('div');
            tableItem.className = 'table-item';
            tableItem.innerHTML = `
                <i class="bi bi-table"></i>
                <span>${table.name}</span>
                ${table.remarks ? `<small class="text-muted d-block">${table.remarks}</small>` : ''}
            `;

            tableItem.addEventListener('click', () => {
                this.selectTable(table, tableItem);
            });

            tableList.appendChild(tableItem);
        });

        console.log('表列表渲染完成，共', tables.length, '个表');
    }

    /**
     * 选择表
     */
    selectTable(table, element) {
        // 清除之前的选择
        document.querySelectorAll('.table-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 选择当前表
        element.classList.add('selected');
        this.selectedTable = table;

        // 使用新的字段配置器
        if (window.fieldConfigurator) {
            window.fieldConfigurator.setTableAndFields(table, table.columns || []);
        }

        console.log('选择表:', table.name, '字段数量:', table.columns ? table.columns.length : 0);
    }

    /**
     * 渲染字段列表
     */
    renderFields(columns) {
        const fieldList = document.getElementById('fieldList');
        fieldList.innerHTML = '';

        if (columns.length === 0) {
            fieldList.innerHTML = '<div class="text-muted">没有找到字段信息</div>';
            return;
        }

        columns.forEach(column => {
            const fieldItem = document.createElement('div');
            fieldItem.className = 'field-item d-flex justify-content-between align-items-center';
            fieldItem.innerHTML = `
                <div>
                    <i class="bi bi-diagram-3"></i>
                    <span class="field-name">${column.name}</span>
                    ${column.remarks ? `<small class="text-muted d-block ms-3">${column.remarks}</small>` : ''}
                </div>
                <div class="field-type">${column.type}${column.size ? `(${column.size})` : ''}</div>
            `;

            fieldItem.addEventListener('click', () => {
                this.selectField(column, fieldItem);
            });

            fieldList.appendChild(fieldItem);
        });
    }

    /**
     * 选择字段
     */
    selectField(column, element) {
        // 清除之前的选择
        document.querySelectorAll('.field-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 选择当前字段
        element.classList.add('selected');

        // 将字段添加到SQL中（简单实现）
        const sqlTextarea = document.getElementById('customSql');
        const currentSql = sqlTextarea.value;

        // 如果是SELECT *，替换为具体字段
        if (currentSql.includes('SELECT *')) {
            const tableName = this.selectedTable ? this.selectedTable.name : 'table_name';
            sqlTextarea.value = `SELECT ${column.name} FROM ${tableName}`;
        } else {
            // 简单地在光标位置插入字段名
            const cursorPos = sqlTextarea.selectionStart;
            const textBefore = currentSql.substring(0, cursorPos);
            const textAfter = currentSql.substring(cursorPos);
            sqlTextarea.value = textBefore + column.name + textAfter;

            // 设置光标位置
            const newPos = cursorPos + column.name.length;
            sqlTextarea.setSelectionRange(newPos, newPos);
        }

        sqlTextarea.focus();
        console.log('选择字段:', column.name);
    }

    /**
     * SQL变化处理
     */
    onSqlChange() {
        // 可以在这里添加SQL语法检查等逻辑
        const sql = document.getElementById('customSql').value.trim();
        console.log('SQL变化:', sql);
    }

    /**
     * 设置字段映射
     */
    async setupFieldMapping() {
        // 如果有字段配置器的结果，直接使用
        if (this.fieldConfiguration && this.fieldConfiguration.fieldRoles) {
            this.populateFieldMappingFromConfig();
        } else {
            // 通过预览查询获取字段信息
            await this.loadFieldsFromPreview();
        }
    }

    /**
     * 从字段配置器结果填充字段映射
     */
    populateFieldMappingFromConfig() {
        const fieldRoles = this.fieldConfiguration.fieldRoles;

        // 找到标签字段和数值字段
        const labelField = Object.keys(fieldRoles).find(field => fieldRoles[field] === 'label');
        const valueField = Object.keys(fieldRoles).find(field => fieldRoles[field] === 'value');

        // 填充选择框
        const labelSelect = document.getElementById('labelField');
        const valueSelect = document.getElementById('valueField');

        // 清空并重新填充选项
        labelSelect.innerHTML = '<option value="">请选择标签字段</option>';
        valueSelect.innerHTML = '<option value="">请选择数值字段</option>';

        // 只处理标签和数值字段
        Object.keys(fieldRoles).forEach(field => {
            const role = fieldRoles[field];
            if (role === 'label' || role === 'value') {
                const labelOption = document.createElement('option');
                labelOption.value = field;
                labelOption.textContent = field;
                labelSelect.appendChild(labelOption);

                const valueOption = document.createElement('option');
                valueOption.value = field;
                valueOption.textContent = field;
                valueSelect.appendChild(valueOption);
            }
        });

        // 设置选中值
        if (labelField) {
            labelSelect.value = labelField;
        }
        if (valueField) {
            valueSelect.value = valueField;
        }

        console.log('从配置填充字段映射:', { labelField, valueField });
    }

    /**
     * 从预览中加载字段
     */
    async loadFieldsFromPreview() {
        try {
            const sql = document.getElementById('customSql').value.trim();
            const queryConfig = JSON.stringify({ sql: sql });
            
            const response = await fetch(`/api/dataset/preview/${this.selectedDataSource}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ queryConfig: queryConfig, limit: 1 })
            });
            
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                const fields = Object.keys(result.data[0]);
                this.populateFieldSelects(fields);
            } else {
                alert('无法获取字段信息，请检查SQL语句');
            }
        } catch (error) {
            console.error('加载字段失败:', error);
            alert('加载字段失败: ' + error.message);
        }
    }

    /**
     * 填充字段选择框
     */
    populateFieldSelects(fields) {
        const labelSelect = document.getElementById('labelField');
        const valueSelect = document.getElementById('valueField');
        
        // 清空选项
        labelSelect.innerHTML = '<option value="">请选择标签字段</option>';
        valueSelect.innerHTML = '<option value="">请选择数值字段</option>';
        
        // 添加字段选项
        fields.forEach(field => {
            const labelOption = document.createElement('option');
            labelOption.value = field;
            labelOption.textContent = field;
            labelSelect.appendChild(labelOption);
            
            const valueOption = document.createElement('option');
            valueOption.value = field;
            valueOption.textContent = field;
            valueSelect.appendChild(valueOption);
        });
    }

    /**
     * 设置预览
     */
    setupPreview() {
        // 显示配置摘要
        console.log('设置预览');
    }

    /**
     * 预览数据
     */
    async previewData() {
        try {
            const sql = document.getElementById('customSql').value.trim();
            const queryConfig = JSON.stringify({ sql: sql });
            
            const response = await fetch(`/api/dataset/preview/${this.selectedDataSource}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ queryConfig: queryConfig, limit: 10 })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.renderPreviewTable(result.data);
            } else {
                alert('预览失败: ' + result.message);
            }
        } catch (error) {
            console.error('预览数据失败:', error);
            alert('预览数据失败: ' + error.message);
        }
    }

    /**
     * 渲染预览表格
     */
    renderPreviewTable(data) {
        const thead = document.getElementById('previewTableHead');
        const tbody = document.getElementById('previewTableBody');
        
        if (!data || data.length === 0) {
            thead.innerHTML = '';
            tbody.innerHTML = '<tr><td colspan="100%" class="text-center text-muted">没有数据</td></tr>';
            return;
        }
        
        // 生成表头
        const fields = Object.keys(data[0]);
        thead.innerHTML = '<tr>' + fields.map(field => `<th>${field}</th>`).join('') + '</tr>';
        
        // 生成数据行
        tbody.innerHTML = data.map(row => 
            '<tr>' + fields.map(field => `<td>${row[field] || ''}</td>`).join('') + '</tr>'
        ).join('');
    }

    /**
     * 保存数据集
     */
    async saveDataSet() {
        try {
            if (this.isEditMode) {
                await this.updateDataSet();
            } else {
                await this.createDataSet();
            }
        } catch (error) {
            console.error('保存数据集失败:', error);
            alert('保存数据集失败: ' + error.message);
        }
    }

    /**
     * 恢复步骤2的配置（在进入步骤2时调用）
     */
    async restoreStep2Configurations() {
        try {
            console.log('开始恢复步骤2配置');

            // 等待步骤2的DOM元素加载完成
            await this.waitForStep2Elements();

            // 恢复高级配置
            await this.restoreAdvancedConfigurations(this.originalDataSet);

            console.log('步骤2配置恢复完成');
        } catch (error) {
            console.error('恢复步骤2配置失败:', error);
        }
    }

    /**
     * 等待步骤2的DOM元素加载完成
     */
    async waitForStep2Elements() {
        try {
            console.log('等待步骤2元素加载...');

            // 等待主要的步骤2元素
            await this.waitForElement('outputLimit');
            await this.waitForElement('dateField');
            await this.waitForElement('dateFormat');
            await this.waitForElement('enableAggregation');

            console.log('步骤2元素加载完成');
        } catch (error) {
            console.warn('等待步骤2元素超时，但继续执行:', error);
        }
    }

    /**
     * 恢复高级配置（输出限制、日期格式化、聚合配置）
     */
    async restoreAdvancedConfigurations(dataSet) {
        try {
            console.log('开始恢复高级配置');

            // 1. 恢复输出限制
            if (dataSet.defaultOutputLimit) {
                await this.waitForElement('outputLimit');
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement) {
                    outputLimitElement.value = dataSet.defaultOutputLimit;
                    this.outputLimit = dataSet.defaultOutputLimit;
                    console.log('恢复输出限制:', dataSet.defaultOutputLimit);
                }
            }

            // 2. 等待字段选项加载完成
            console.log('等待字段选项加载...');
            const fieldOptionsReady = await this.waitForFieldOptions();

            if (!fieldOptionsReady) {
                console.warn('字段选项未完全加载，但继续恢复配置');
            }

            // 3. 从transformConfig恢复高级配置
            if (this.transformConfig && this.transformConfig.advancedConfig) {
                await this.restoreFromAdvancedConfig(this.transformConfig.advancedConfig);
            } else {
                // 4. 备选方案：从SQL解析配置
                if (this.queryConfig && this.queryConfig.sql) {
                    await this.restoreFromSQLParsing(this.queryConfig.sql);
                }
            }

            // 5. 配置恢复完成后，重新生成SQL确保一致性
            await this.regenerateSQLAfterConfigRestore();

            console.log('高级配置恢复完成');
        } catch (error) {
            console.error('恢复高级配置失败:', error);
        }
    }

    /**
     * 配置恢复后重新生成SQL
     */
    async regenerateSQLAfterConfigRestore() {
        try {
            console.log('开始重新生成SQL以确保与配置一致');

            // 等待一小段时间确保所有配置都已设置完成
            await new Promise(resolve => setTimeout(resolve, 300));

            // 检查字段配置器是否可用
            if (window.fieldConfigurator && typeof window.fieldConfigurator.generateSQL === 'function') {
                // 检查是否有选中的表和字段配置
                if (window.fieldConfigurator.selectedTable &&
                    window.fieldConfigurator.fields &&
                    window.fieldConfigurator.fields.length > 0) {

                    console.log('字段配置器可用，重新生成SQL');
                    window.fieldConfigurator.generateSQL();
                    console.log('SQL重新生成完成');
                } else {
                    console.log('字段配置器未完全初始化，跳过SQL重新生成');
                }
            } else {
                console.warn('字段配置器不可用，无法重新生成SQL');
            }
        } catch (error) {
            console.error('重新生成SQL失败:', error);
        }
    }

    /**
     * 从高级配置恢复UI
     */
    async restoreFromAdvancedConfig(advancedConfig) {
        try {
            // 恢复日期格式化配置
            if (advancedConfig.dateFormatting) {
                await this.waitForElement('dateField');
                await this.waitForElement('dateFormat');

                const dateFieldElement = document.getElementById('dateField');
                const dateFormatElement = document.getElementById('dateFormat');

                // 检查并设置日期字段
                if (dateFieldElement && advancedConfig.dateFormatting.dateField) {
                    // 检查选项是否存在，如果不存在则尝试更新
                    const hasOption = Array.from(dateFieldElement.options).some(option =>
                        option.value === advancedConfig.dateFormatting.dateField);

                    if (!hasOption) {
                        console.log('日期字段选项不存在，尝试更新字段选项');
                        this.updateFieldOptionsIfNeeded();
                        // 等待一小段时间让选项更新
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }

                    dateFieldElement.value = advancedConfig.dateFormatting.dateField;
                    console.log('设置日期字段:', advancedConfig.dateFormatting.dateField);
                }

                if (dateFormatElement && advancedConfig.dateFormatting.dateFormat) {
                    dateFormatElement.value = advancedConfig.dateFormatting.dateFormat;
                    console.log('设置日期格式:', advancedConfig.dateFormatting.dateFormat);
                }

                console.log('恢复日期格式化配置:', advancedConfig.dateFormatting);
            }

            // 恢复聚合配置
            if (advancedConfig.aggregation) {
                await this.waitForElement('enableAggregation');

                const enableAggregationElement = document.getElementById('enableAggregation');
                if (enableAggregationElement) {
                    enableAggregationElement.checked = advancedConfig.aggregation.enabled || false;

                    // 触发聚合选项显示
                    if (advancedConfig.aggregation.enabled) {
                        const aggregationOptions = document.getElementById('aggregationOptions');
                        if (aggregationOptions) {
                            aggregationOptions.style.display = 'block';
                        }

                        // 恢复聚合类型和时间字段
                        await this.waitForElement('aggregationType');
                        await this.waitForElement('timeField');

                        const aggregationTypeElement = document.getElementById('aggregationType');
                        const timeFieldElement = document.getElementById('timeField');

                        if (aggregationTypeElement && advancedConfig.aggregation.type) {
                            aggregationTypeElement.value = advancedConfig.aggregation.type;
                            console.log('设置聚合类型:', advancedConfig.aggregation.type);
                        }

                        if (timeFieldElement && advancedConfig.aggregation.timeField) {
                            // 检查时间字段选项是否存在
                            const hasTimeOption = Array.from(timeFieldElement.options).some(option =>
                                option.value === advancedConfig.aggregation.timeField);

                            if (!hasTimeOption) {
                                console.log('时间字段选项不存在，尝试更新字段选项');
                                this.updateFieldOptionsIfNeeded();
                                // 等待一小段时间让选项更新
                                await new Promise(resolve => setTimeout(resolve, 200));
                            }

                            timeFieldElement.value = advancedConfig.aggregation.timeField;
                            console.log('设置时间字段:', advancedConfig.aggregation.timeField);
                        }
                    }
                }

                console.log('恢复聚合配置:', advancedConfig.aggregation);
            }
        } catch (error) {
            console.error('从高级配置恢复失败:', error);
        }
    }

    /**
     * 从SQL解析恢复配置（备选方案）
     */
    async restoreFromSQLParsing(sql) {
        try {
            console.log('尝试从SQL解析配置:', sql);

            // 解析LIMIT子句
            const limitMatch = sql.match(/LIMIT\s+(\d+)/i);
            if (limitMatch && limitMatch[1]) {
                await this.waitForElement('outputLimit');
                const outputLimitElement = document.getElementById('outputLimit');
                if (outputLimitElement && !outputLimitElement.value) {
                    outputLimitElement.value = limitMatch[1];
                    this.outputLimit = parseInt(limitMatch[1]);
                    console.log('从SQL解析输出限制:', limitMatch[1]);
                }
            }

            // 解析DATE_FORMAT函数
            const dateFormatMatch = sql.match(/DATE_FORMAT\s*\(\s*(\w+)\s*,\s*'([^']+)'\s*\)/i);
            if (dateFormatMatch && dateFormatMatch[1] && dateFormatMatch[2]) {
                await this.waitForElement('dateField');
                await this.waitForElement('dateFormat');

                const dateFieldElement = document.getElementById('dateField');
                const dateFormatElement = document.getElementById('dateFormat');

                if (dateFieldElement && !dateFieldElement.value) {
                    // 检查日期字段选项是否存在
                    const hasDateOption = Array.from(dateFieldElement.options).some(option =>
                        option.value === dateFormatMatch[1]);

                    if (!hasDateOption) {
                        console.log('从SQL解析的日期字段选项不存在，尝试更新字段选项');
                        this.updateFieldOptionsIfNeeded();
                        // 等待一小段时间让选项更新
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }

                    dateFieldElement.value = dateFormatMatch[1];
                    console.log('从SQL设置日期字段:', dateFormatMatch[1]);
                }

                if (dateFormatElement && !dateFormatElement.value) {
                    dateFormatElement.value = dateFormatMatch[2];
                    console.log('从SQL设置日期格式:', dateFormatMatch[2]);
                }

                console.log('从SQL解析日期格式化:', dateFormatMatch[1], dateFormatMatch[2]);
            }

            // 解析聚合函数
            const aggregationMatch = sql.match(/(MAX|MIN|AVG|SUM|COUNT)\s*\(/i);
            const groupByMatch = sql.match(/GROUP\s+BY/i);

            if (aggregationMatch && groupByMatch) {
                await this.waitForElement('enableAggregation');

                const enableAggregationElement = document.getElementById('enableAggregation');
                if (enableAggregationElement && !enableAggregationElement.checked) {
                    enableAggregationElement.checked = true;

                    // 显示聚合选项
                    const aggregationOptions = document.getElementById('aggregationOptions');
                    if (aggregationOptions) {
                        aggregationOptions.style.display = 'block';
                    }

                    // 设置聚合类型
                    await this.waitForElement('aggregationType');
                    const aggregationTypeElement = document.getElementById('aggregationType');
                    if (aggregationTypeElement) {
                        aggregationTypeElement.value = aggregationMatch[1].toUpperCase();
                    }

                    console.log('从SQL解析聚合配置:', aggregationMatch[1]);
                }
            }
        } catch (error) {
            console.error('从SQL解析配置失败:', error);
        }
    }

    /**
     * 保存高级配置（日期格式化、聚合配置）
     */
    saveAdvancedConfigurations() {
        try {
            // 获取日期格式化配置
            const dateField = document.getElementById('dateField')?.value;
            const dateFormat = document.getElementById('dateFormat')?.value;

            // 获取聚合配置
            const enableAggregation = document.getElementById('enableAggregation')?.checked;
            const aggregationType = document.getElementById('aggregationType')?.value;
            const timeField = document.getElementById('timeField')?.value;

            // 保存到临时配置
            this.advancedConfig = {
                dateFormatting: {
                    dateField: dateField || null,
                    dateFormat: dateFormat || null
                },
                aggregation: {
                    enabled: enableAggregation || false,
                    type: aggregationType || null,
                    timeField: timeField || null
                }
            };

            console.log('保存高级配置:', this.advancedConfig);
        } catch (error) {
            console.error('保存高级配置失败:', error);
        }
    }

    /**
     * 创建数据集
     */
    async createDataSet() {
        // 合并高级配置到transformConfig
        const finalTransformConfig = {
            ...this.transformConfig,
            advancedConfig: this.advancedConfig
        };

        const dataSet = {
            name: document.getElementById('dataSetName').value.trim(),
            description: document.getElementById('dataSetDescription').value.trim(),
            dataSourceId: this.selectedDataSource,
            queryConfig: JSON.stringify(this.queryConfig),
            transformConfig: JSON.stringify(finalTransformConfig),
            defaultOutputLimit: this.outputLimit,
            dataType: 'realtime',
            enabled: true
        };

        const response = await fetch('/api/dataset/create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dataSet)
        });

        const result = await response.json();

        if (result.success) {
            alert('数据集创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('dataSetWizardModal')).hide();

            // 刷新数据集列表
            if (window.dataSourceManager && window.dataSourceManager.loadDataSets) {
                window.dataSourceManager.loadDataSets();
            }
        } else {
            throw new Error(result.message || '创建失败');
        }
    }

    /**
     * 更新数据集
     */
    async updateDataSet() {
        // 合并高级配置到transformConfig
        const finalTransformConfig = {
            ...this.transformConfig,
            advancedConfig: this.advancedConfig
        };

        const dataSet = {
            name: document.getElementById('dataSetName').value.trim(),
            description: document.getElementById('dataSetDescription').value.trim(),
            dataSourceId: this.selectedDataSource,
            queryConfig: JSON.stringify(this.queryConfig),
            transformConfig: JSON.stringify(finalTransformConfig),
            defaultOutputLimit: this.outputLimit,
            dataType: this.originalDataSet?.dataType || 'realtime',
            enabled: this.originalDataSet?.enabled !== false // 保持原有启用状态
        };

        const response = await fetch(`/api/dataset/${this.editingDataSetId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dataSet)
        });

        const result = await response.json();

        if (result.success) {
            alert('数据集更新成功！');
            bootstrap.Modal.getInstance(document.getElementById('dataSetWizardModal')).hide();

            // 刷新数据集列表
            if (window.dataSourceManager && window.dataSourceManager.loadDataSets) {
                window.dataSourceManager.loadDataSets();
            }
        } else {
            throw new Error(result.message || '更新失败');
        }
    }
}

// 全局实例
window.dataSetWizard = new DataSetWizard();
