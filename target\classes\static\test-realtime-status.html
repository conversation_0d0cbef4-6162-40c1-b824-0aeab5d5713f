<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>状态指示器实时测试</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bi-status-indicator.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .indicator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .indicator-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .indicator-info {
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .status-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .connection-status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background: #d4edda;
            color: #155724;
        }
        .disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>状态指示器实时测试</h1>
        
        <div id="connection-status" class="connection-status disconnected">
            WebSocket连接状态: 未连接
        </div>

        <div class="test-container">
            <h3>实时状态指示器</h3>
            <p>这些指示器会根据模拟的实时数据自动更新状态</p>
            
            <div class="indicator-grid" id="indicator-grid">
                <!-- 动态生成的指示器将在这里显示 -->
            </div>
        </div>

        <div class="test-container">
            <h3>状态变化日志</h3>
            <div id="status-log" class="status-log">
                等待状态更新...
            </div>
            <button class="btn btn-secondary btn-sm mt-2" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script src="/js/sockjs.min.js"></script>
    <script src="/js/stomp.min.js"></script>
    <script src="/js/bi-status-indicator.js"></script>
    <script>
        // 状态指示器实例
        const indicators = {};
        let stompClient = null;
        let simulationInterval = null;

        // 测试配置
        const baseConfig = {
            size: 80,
            condition1: { min: 0, max: 30, name: '正常', color: '#28a745' },
            condition2: { min: 31, max: 70, name: '警告', color: '#ffc107' },
            condition3: { min: 71, max: 100, name: '危险', color: '#dc3545' },
            offlineColor: '#6c757d',
            showConditionName: true
        };

        // 创建测试指示器
        const testIndicators = [
            { id: 'temp-sensor', name: '温度传感器', shape: 'circle' },
            { id: 'pressure-gauge', name: '压力表', shape: 'square' },
            { id: 'flow-meter', name: '流量计', shape: 'rectangle' },
            { id: 'level-sensor', name: '液位传感器', shape: 'triangle' },
            { id: 'vibration-monitor', name: '振动监测', shape: 'diamond' },
            { id: 'power-status', name: '电源状态', shape: 'hexagon' }
        ];

        function initializeIndicators() {
            const grid = document.getElementById('indicator-grid');
            
            testIndicators.forEach(item => {
                // 创建容器
                const container = document.createElement('div');
                container.className = 'indicator-item';
                container.innerHTML = `
                    <div id="${item.id}"></div>
                    <div class="indicator-info">
                        <div><strong>${item.name}</strong></div>
                        <div id="${item.id}-status">等待数据...</div>
                        <div id="${item.id}-value">--</div>
                    </div>
                `;
                grid.appendChild(container);

                // 创建指示器实例
                const config = { ...baseConfig, shape: item.shape };
                indicators[item.id] = new BiStatusIndicator(item.id, config);
                
                // 初始渲染
                indicators[item.id].render({ value: 0 });
            });
        }

        function updateIndicatorStatus(id, data) {
            const indicator = indicators[id];
            if (!indicator) return;

            // 更新指示器
            indicator.render(data);

            // 更新信息显示
            const statusElement = document.getElementById(`${id}-status`);
            const valueElement = document.getElementById(`${id}-value`);

            if (data.deviceOffline) {
                statusElement.textContent = '设备离线';
                valueElement.textContent = '--';
            } else {
                const status = indicator.determineStatus(data, baseConfig);
                statusElement.textContent = status.name;
                valueElement.textContent = `${data.value}%`;
            }

            // 记录日志
            logStatusChange(id, data);
        }

        function logStatusChange(id, data) {
            const log = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            const indicatorName = testIndicators.find(item => item.id === id)?.name || id;
            
            let message;
            if (data.deviceOffline) {
                message = `[${timestamp}] ${indicatorName}: 设备离线`;
            } else {
                const status = indicators[id].determineStatus(data, baseConfig);
                message = `[${timestamp}] ${indicatorName}: ${status.name} (${data.value}%)`;
            }
            
            log.innerHTML += message + '\n';
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('status-log').innerHTML = '';
        }

        // 模拟实时数据更新
        function startSimulation() {
            simulationInterval = setInterval(() => {
                testIndicators.forEach(item => {
                    // 随机生成数据
                    const isOffline = Math.random() < 0.05; // 5%概率离线
                    
                    if (isOffline) {
                        updateIndicatorStatus(item.id, { deviceOffline: true });
                    } else {
                        const value = Math.floor(Math.random() * 100);
                        updateIndicatorStatus(item.id, { value: value });
                    }
                });
            }, 2000); // 每2秒更新一次
        }

        function stopSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
                simulationInterval = null;
            }
        }

        // WebSocket连接（如果可用）
        function connectWebSocket() {
            try {
                const socket = new SockJS('/ws');
                stompClient = Stomp.over(socket);
                
                stompClient.connect({}, function(frame) {
                    console.log('WebSocket连接成功: ' + frame);
                    updateConnectionStatus(true);
                    
                    // 订阅设备状态更新
                    stompClient.subscribe('/topic/devices', function(message) {
                        const devices = JSON.parse(message.body);
                        console.log('收到设备状态更新:', devices);
                        // 这里可以根据实际设备数据更新指示器
                    });
                }, function(error) {
                    console.log('WebSocket连接失败: ' + error);
                    updateConnectionStatus(false);
                    // 连接失败时使用模拟数据
                    startSimulation();
                });
            } catch (error) {
                console.log('WebSocket不可用，使用模拟数据');
                updateConnectionStatus(false);
                startSimulation();
            }
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            if (connected) {
                statusElement.className = 'connection-status connected';
                statusElement.textContent = 'WebSocket连接状态: 已连接';
            } else {
                statusElement.className = 'connection-status disconnected';
                statusElement.textContent = 'WebSocket连接状态: 未连接 (使用模拟数据)';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化状态指示器实时测试页面');
            initializeIndicators();
            connectWebSocket();
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            stopSimulation();
            if (stompClient) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>
