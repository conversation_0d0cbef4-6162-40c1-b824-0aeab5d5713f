package com.example.service.connector;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.*;

/**
 * 数据库连接器实现
 * 支持MySQL、PostgreSQL、SQL Server等数据库
 */
@Slf4j
@Component
public class DatabaseConnector implements DataSourceConnector {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String getType() {
        return "database";
    }
    
    @Override
    public ConnectionTestResult testConnection(String connectionConfig) {
        long startTime = System.currentTimeMillis();
        
        try {
            JsonNode config = objectMapper.readTree(connectionConfig);
            String url = buildJdbcUrl(config);
            String username = config.get("username").asText();
            String password = config.get("password").asText();
            
            try (Connection connection = DriverManager.getConnection(url, username, password)) {
                if (connection.isValid(5)) {
                    long responseTime = System.currentTimeMillis() - startTime;
                    
                    // 获取数据库信息
                    DatabaseMetaData metaData = connection.getMetaData();
                    Map<String, Object> details = new HashMap<>();
                    details.put("databaseProductName", metaData.getDatabaseProductName());
                    details.put("databaseProductVersion", metaData.getDatabaseProductVersion());
                    details.put("driverName", metaData.getDriverName());
                    details.put("driverVersion", metaData.getDriverVersion());
                    
                    ConnectionTestResult result = new ConnectionTestResult(true, "连接成功", responseTime);
                    result.setDetails(details);
                    return result;
                } else {
                    return new ConnectionTestResult(false, "连接无效");
                }
            }
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            return new ConnectionTestResult(false, "连接失败: " + e.getMessage());
        }
    }
    
    @Override
    public QueryResult executeQuery(String queryConfig, String connectionConfig) {
        long startTime = System.currentTimeMillis();
        
        try {
            JsonNode config = objectMapper.readTree(connectionConfig);
            JsonNode query = objectMapper.readTree(queryConfig);
            
            String url = buildJdbcUrl(config);
            String username = config.get("username").asText();
            String password = config.get("password").asText();
            String sql = query.get("sql").asText();
            
            try (Connection connection = DriverManager.getConnection(url, username, password);
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                // 设置查询参数
                if (query.has("parameters")) {
                    JsonNode parameters = query.get("parameters");
                    int paramIndex = 1;
                    for (JsonNode param : parameters) {
                        statement.setObject(paramIndex++, param.asText());
                    }
                }
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    List<Map<String, Object>> data = new ArrayList<>();
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    
                    while (resultSet.next()) {
                        Map<String, Object> row = new LinkedHashMap<>();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnLabel(i);
                            Object value = resultSet.getObject(i);
                            row.put(columnName, value);
                        }
                        data.add(row);
                    }
                    
                    long executeTime = System.currentTimeMillis() - startTime;
                    QueryResult result = new QueryResult(true, data);
                    result.setExecuteTime(executeTime);
                    
                    // 设置元数据
                    Map<String, Object> metadata = new HashMap<>();
                    List<Map<String, Object>> columns = new ArrayList<>();
                    for (int i = 1; i <= columnCount; i++) {
                        Map<String, Object> column = new HashMap<>();
                        column.put("name", metaData.getColumnLabel(i));
                        column.put("type", metaData.getColumnTypeName(i));
                        column.put("nullable", metaData.isNullable(i) == ResultSetMetaData.columnNullable);
                        columns.add(column);
                    }
                    metadata.put("columns", columns);
                    result.setMetadata(metadata);
                    
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("数据库查询执行失败", e);
            return new QueryResult(false, "查询失败: " + e.getMessage());
        }
    }
    
    @Override
    public MetadataResult getMetadata(String connectionConfig) {
        try {
            JsonNode config = objectMapper.readTree(connectionConfig);
            String url = buildJdbcUrl(config);
            String username = config.get("username").asText();
            String password = config.get("password").asText();
            
            try (Connection connection = DriverManager.getConnection(url, username, password)) {
                DatabaseMetaData metaData = connection.getMetaData();
                Map<String, Object> metadata = new HashMap<>();
                
                // 获取表列表
                List<Map<String, Object>> tables = new ArrayList<>();
                String databaseType = getDatabaseType(config);
                String currentSchema = getCurrentSchema(metaData, config, databaseType);

                log.info("获取表列表 - 数据库类型: {}, 当前Schema: {}", databaseType, currentSchema);

                int totalTables = 0;
                int filteredTables = 0;
                int accessibleTables = 0;

                try (ResultSet rs = metaData.getTables(null, currentSchema, "%", new String[]{"TABLE"})) {
                    while (rs.next()) {
                        totalTables++;
                        String tableName = rs.getString("TABLE_NAME");
                        String schemaName = rs.getString("TABLE_SCHEM");
                        String tableType = rs.getString("TABLE_TYPE");

                        log.debug("检查表: {} (schema: {}, type: {})", tableName, schemaName, tableType);

                        // 过滤系统表和特殊表
                        if (isSystemTable(tableName, schemaName, databaseType)) {
                            log.debug("跳过系统表: {}.{}", schemaName, tableName);
                            continue;
                        }
                        filteredTables++;

                        // 验证表是否可访问
                        if (!isTableAccessible(connection, tableName, schemaName)) {
                            log.debug("跳过不可访问的表: {}.{}", schemaName, tableName);
                            continue;
                        }
                        accessibleTables++;

                        Map<String, Object> table = new HashMap<>();
                        table.put("name", tableName);
                        table.put("schema", schemaName);
                        table.put("type", tableType);
                        table.put("remarks", rs.getString("REMARKS"));

                        // 获取表的列信息
                        List<Map<String, Object>> columns = getTableColumns(metaData, tableName);
                        if (columns.isEmpty()) {
                            log.debug("跳过无列信息的表: {}.{}", schemaName, tableName);
                            continue;
                        }
                        table.put("columns", columns);

                        tables.add(table);
                        log.debug("添加表: {}.{} (列数: {})", schemaName, tableName, columns.size());
                    }
                }

                log.info("表过滤统计 - 总表数: {}, 过滤后: {}, 可访问: {}, 最终: {}",
                        totalTables, filteredTables, accessibleTables, tables.size());

                metadata.put("tables", tables);
                
                // 获取数据库信息
                Map<String, Object> dbInfo = new HashMap<>();
                dbInfo.put("productName", metaData.getDatabaseProductName());
                dbInfo.put("productVersion", metaData.getDatabaseProductVersion());
                dbInfo.put("driverName", metaData.getDriverName());
                dbInfo.put("driverVersion", metaData.getDriverVersion());
                metadata.put("database", dbInfo);
                
                return new MetadataResult(true, metadata);
            }
        } catch (Exception e) {
            log.error("获取数据库元数据失败", e);
            return new MetadataResult(false, "获取元数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public ValidationResult validateQuery(String queryConfig, String connectionConfig) {
        try {
            JsonNode query = objectMapper.readTree(queryConfig);
            
            if (!query.has("sql") || query.get("sql").asText().trim().isEmpty()) {
                return new ValidationResult(false, "SQL查询不能为空");
            }
            
            String sql = query.get("sql").asText().trim().toLowerCase();
            
            // 基本的SQL安全检查
            if (sql.contains("drop ") || sql.contains("delete ") || 
                sql.contains("update ") || sql.contains("insert ") ||
                sql.contains("alter ") || sql.contains("create ")) {
                return new ValidationResult(false, "不允许执行DDL或DML语句，仅支持SELECT查询");
            }
            
            return new ValidationResult(true, "查询配置有效");
        } catch (Exception e) {
            return new ValidationResult(false, "查询配置格式错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据库类型
     */
    private String getDatabaseType(JsonNode config) {
        return config.has("driverType") ? config.get("driverType").asText() : "mysql";
    }

    /**
     * 获取当前schema
     */
    private String getCurrentSchema(DatabaseMetaData metaData, JsonNode config, String databaseType) {
        try {
            switch (databaseType.toLowerCase()) {
                case "mysql":
                    // MySQL使用数据库名作为schema
                    return config.get("database").asText();
                case "postgresql":
                    // PostgreSQL默认使用public schema
                    return "public";
                case "sqlserver":
                    // SQL Server默认使用dbo schema
                    return "dbo";
                default:
                    return null; // 使用默认schema
            }
        } catch (Exception e) {
            log.warn("获取当前schema失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断是否为系统表
     */
    private boolean isSystemTable(String tableName, String schemaName, String databaseType) {
        if (tableName == null) {
            return true;
        }

        String lowerTableName = tableName.toLowerCase();
        String lowerSchemaName = schemaName != null ? schemaName.toLowerCase() : "";

        switch (databaseType.toLowerCase()) {
            case "mysql":
                // MySQL系统表和schema过滤
                return lowerSchemaName.equals("information_schema") ||
                       lowerSchemaName.equals("performance_schema") ||
                       lowerSchemaName.equals("mysql") ||
                       lowerSchemaName.equals("sys") ||
                       lowerTableName.startsWith("tmp_") ||
                       lowerTableName.startsWith("temp_");

            case "postgresql":
                // PostgreSQL系统表过滤
                return lowerSchemaName.equals("information_schema") ||
                       lowerSchemaName.equals("pg_catalog") ||
                       lowerSchemaName.equals("pg_toast") ||
                       lowerSchemaName.startsWith("pg_") ||
                       lowerTableName.startsWith("pg_") ||
                       lowerTableName.startsWith("sql_");

            case "sqlserver":
                // SQL Server系统表过滤
                return lowerSchemaName.equals("information_schema") ||
                       lowerSchemaName.equals("sys") ||
                       lowerTableName.startsWith("sys") ||
                       lowerTableName.startsWith("msp") ||
                       lowerTableName.startsWith("dt") ||
                       lowerTableName.startsWith("#");

            default:
                // 通用系统表过滤
                return lowerSchemaName.equals("information_schema") ||
                       lowerTableName.startsWith("sys") ||
                       lowerTableName.startsWith("tmp_") ||
                       lowerTableName.startsWith("temp_");
        }
    }

    /**
     * 验证表是否可访问
     */
    private boolean isTableAccessible(Connection connection, String tableName, String schemaName) {
        try {
            String fullTableName = schemaName != null && !schemaName.isEmpty() ?
                schemaName + "." + tableName : tableName;

            // 尝试查询表结构，不获取数据
            String sql = "SELECT * FROM " + fullTableName + " WHERE 1=0";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.executeQuery();
                return true;
            }
        } catch (SQLException e) {
            log.debug("表 {} 不可访问: {}", tableName, e.getMessage());
            return false;
        }
    }

    /**
     * 获取表的列信息
     */
    private List<Map<String, Object>> getTableColumns(DatabaseMetaData metaData, String tableName) {
        List<Map<String, Object>> columns = new ArrayList<>();
        try (ResultSet rs = metaData.getColumns(null, null, tableName, "%")) {
            while (rs.next()) {
                Map<String, Object> column = new HashMap<>();
                column.put("name", rs.getString("COLUMN_NAME"));
                column.put("type", rs.getString("TYPE_NAME"));
                column.put("size", rs.getInt("COLUMN_SIZE"));
                column.put("nullable", rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                column.put("defaultValue", rs.getString("COLUMN_DEF"));
                column.put("remarks", rs.getString("REMARKS"));
                column.put("position", rs.getInt("ORDINAL_POSITION"));
                columns.add(column);
            }
        } catch (SQLException e) {
            log.warn("获取表 {} 的列信息失败: {}", tableName, e.getMessage());
        }
        return columns;
    }

    /**
     * 构建JDBC URL
     */
    private String buildJdbcUrl(JsonNode config) {
        String host = config.get("host").asText();
        int port = config.get("port").asInt();
        String database = config.get("database").asText();
        String driverType = config.has("driverType") ? config.get("driverType").asText() : "mysql";

        switch (driverType.toLowerCase()) {
            case "mysql":
                return String.format("********************************************************************************************",
                                    host, port, database);
            case "postgresql":
                return String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
            case "sqlserver":
                return String.format("**************************************", host, port, database);
            case "oracle":
                return String.format("**************************", host, port, database);
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + driverType);
        }
    }
}
