/* BI大屏设计器样式 */

/* 基础布局 */
.bi-designer {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.bi-designer-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

.bi-designer-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 组件面板样式 */
.bi-component-panel {
    width: 280px;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    flex-shrink: 0;
}

.bi-component-category {
    margin-bottom: 1rem;
}

.bi-component-category-header {
    background: #e9ecef;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

.bi-component-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f3f4;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
}

.bi-component-item:hover {
    background: #e3f2fd;
    transform: translateX(4px);
}

.bi-component-item:active {
    cursor: grabbing;
}

.bi-component-item .icon {
    width: 24px;
    height: 24px;
    margin-right: 0.75rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 1rem;
}

.bi-component-item .icon.chart { background: #e3f2fd; color: #1976d2; }
.bi-component-item .icon.table { background: #f3e5f5; color: #7b1fa2; }
.bi-component-item .icon.text { background: #e8f5e8; color: #388e3c; }
.bi-component-item .icon.image { background: #fff3e0; color: #f57c00; }

/* 画布容器样式 */
.bi-canvas-container {
    flex: 1;
    position: relative;
    overflow: auto;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bi-canvas {
    position: relative;
    background: white;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    margin: 20px;
    min-width: 800px;
    min-height: 600px;
}

/* 网格显示 - 使用伪元素确保在背景之上且不冲突 */
.canvas {
    position: relative;
}

.canvas.show-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(to right, #e0e0e0 1px, transparent 1px),
        linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
    opacity: 1;
}

/* 确保组件在网格之上 */
.bi-widget {
    position: relative;
    z-index: 10;
}

/* 组件样式 */
.bi-widget {
    position: absolute;
    border: 2px solid transparent;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: move;
    min-width: 20px;
    min-height: 20px;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.bi-widget:hover {
    border-color: #6c757d;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.bi-widget.selected {
    border-color: #0d6efd;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.bi-widget.dragging {
    opacity: 0.8;
    transform: rotate(2deg);
    z-index: 1000;
}

.bi-widget-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.bi-widget-content {
    padding: 0.75rem;
    height: calc(100% - 40px);
    overflow: hidden;
}

.bi-widget-actions {
    display: none;
    gap: 0.25rem;
}

.bi-widget.selected .bi-widget-actions {
    display: flex;
}

.bi-widget-action-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.bi-widget-action-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* 调整手柄 */
.bi-resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #0d6efd;
    border: 1px solid white;
    border-radius: 50%;
    cursor: se-resize;
    right: -4px;
    bottom: -4px;
    display: none;
}

.bi-widget.selected .bi-resize-handle {
    display: block;
}

/* 属性面板样式 */
.bi-property-panel {
    width: 320px;
    background: #f8f9fa;
    border-left: 1px solid #dee2e6;
    overflow-y: auto;
    flex-shrink: 0;
}

.bi-property-section {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.bi-property-section h6 {
    margin-bottom: 0.75rem;
    color: #495057;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bi-property-section .form-label {
    font-size: 0.8rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.bi-property-section .form-control,
.bi-property-section .form-select {
    font-size: 0.875rem;
    border-radius: 4px;
}

/* 拖拽相关样式 */
.bi-drag-over {
    background: rgba(13, 110, 253, 0.05);
    border: 2px dashed #0d6efd;
}

.bi-drop-indicator {
    position: absolute;
    border: 2px dashed #28a745;
    background: rgba(40, 167, 69, 0.1);
    pointer-events: none;
    z-index: 999;
}

/* 对齐线样式 */
.bi-alignment-line {
    position: absolute;
    background: #dc3545;
    pointer-events: none;
    z-index: 998;
}

.bi-alignment-line.horizontal {
    height: 1px;
    width: 100%;
}

.bi-alignment-line.vertical {
    width: 1px;
    height: 100%;
}

/* 工具栏样式 */
.bi-toolbar {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.bi-toolbar-group {
    display: flex;
    gap: 0.25rem;
    padding: 0 0.5rem;
    border-right: 1px solid rgba(255,255,255,0.2);
}

.bi-toolbar-group:last-child {
    border-right: none;
}

/* 加载状态 */
.bi-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.875rem;
}

.bi-loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: bi-spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes bi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.bi-empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.bi-empty-state .icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.bi-empty-state h4 {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .bi-component-panel {
        width: 240px;
    }
    
    .bi-property-panel {
        width: 280px;
    }
}

@media (max-width: 992px) {
    .bi-designer-body {
        flex-direction: column;
    }
    
    .bi-component-panel,
    .bi-property-panel {
        width: 100%;
        height: 200px;
        border: none;
        border-top: 1px solid #dee2e6;
    }
    
    .bi-canvas-container {
        flex: 1;
        min-height: 400px;
    }
}

/* 图表容器样式 */
.bi-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.bi-chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 数据表格样式 */
.bi-data-table {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.bi-data-table table {
    margin-bottom: 0;
    font-size: 0.875rem;
}

/* 文本组件样式 */
.bi-text-widget {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    word-wrap: break-word;
}

/* 图片组件样式 */
.bi-image-widget {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.bi-image-widget img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 仪表盘样式 */
.bi-gauge-widget {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.bi-gauge-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #0d6efd;
    line-height: 1;
}

.bi-gauge-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.bi-gauge-unit {
    font-size: 1rem;
    color: #495057;
    margin-left: 0.25rem;
}
