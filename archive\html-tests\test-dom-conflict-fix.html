<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM冲突修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .config-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
        }
        .test-result {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-visible { background-color: #28a745; }
        .status-hidden { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>DOM冲突修复测试</h2>
        <p class="text-muted">测试数据源模态框与数据集创建向导之间的DOM元素冲突问题</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>模拟数据源模态框</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="simulateDataSourceModal()">
                            打开数据源模态框
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="closeDataSourceModal()">
                            关闭数据源模态框
                        </button>
                        
                        <!-- 模拟数据源模态框 -->
                        <div id="dataSourceModal" style="display: none; margin-top: 1rem;">
                            <div class="config-section" id="databaseConfig">
                                <h6>数据库配置</h6>
                                <p>这是数据源模态框内的配置区域</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>模拟数据集创建向导</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" onclick="checkDatasetWizardVisibility()">
                            检查数据集向导显示状态
                        </button>
                        
                        <!-- 模拟数据集创建向导的配置区域 -->
                        <div class="config-section mt-3" id="tableConfig">
                            <h6>表选择配置</h6>
                            <p>这是数据集向导中的表配置区域</p>
                        </div>
                        
                        <div class="config-section" id="fieldConfig">
                            <h6>字段配置</h6>
                            <p>这是数据集向导中的字段配置区域</p>
                        </div>
                        
                        <div class="config-section" id="filterConfig">
                            <h6>筛选条件配置</h6>
                            <p>这是数据集向导中的筛选条件配置区域</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">点击上方按钮开始测试</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟修复后的数据源管理器方法
        function hideAllConfigSections() {
            // 修复后：只影响数据源模态框内的配置区域
            document.querySelectorAll('#dataSourceModal .config-section').forEach(section => {
                section.style.display = 'none';
            });
        }
        
        // 模拟修复前的方法（用于对比）
        function hideAllConfigSectionsOld() {
            // 修复前：影响页面上所有配置区域
            document.querySelectorAll('.config-section').forEach(section => {
                section.style.display = 'none';
            });
        }
        
        function simulateDataSourceModal() {
            // 显示模态框
            document.getElementById('dataSourceModal').style.display = 'block';
            
            // 调用修复后的隐藏方法
            hideAllConfigSections();
            
            // 显示数据库配置
            const databaseConfig = document.getElementById('databaseConfig');
            if (databaseConfig) {
                databaseConfig.style.display = 'block';
            }
            
            updateTestResults('数据源模态框已打开，配置区域已处理');
        }
        
        function closeDataSourceModal() {
            document.getElementById('dataSourceModal').style.display = 'none';
            updateTestResults('数据源模态框已关闭');
        }
        
        function checkDatasetWizardVisibility() {
            const configSections = [
                { id: 'tableConfig', name: '表选择配置' },
                { id: 'fieldConfig', name: '字段配置' },
                { id: 'filterConfig', name: '筛选条件配置' }
            ];
            
            let results = '<h6>数据集向导配置区域显示状态：</h6>';
            let allVisible = true;
            
            configSections.forEach(section => {
                const element = document.getElementById(section.id);
                const isVisible = element && window.getComputedStyle(element).display !== 'none';
                const statusClass = isVisible ? 'status-visible' : 'status-hidden';
                const statusText = isVisible ? '可见' : '隐藏';
                
                results += `<div><span class="status-indicator ${statusClass}"></span>${section.name}: ${statusText}</div>`;
                
                if (!isVisible) {
                    allVisible = false;
                }
            });
            
            results += '<br>';
            if (allVisible) {
                results += '<div class="alert alert-success">✅ 修复成功！所有配置区域都正常显示</div>';
            } else {
                results += '<div class="alert alert-danger">❌ 仍有问题！部分配置区域被隐藏</div>';
            }
            
            updateTestResults(results);
        }
        
        function updateTestResults(content) {
            document.getElementById('testResults').innerHTML = content;
        }
        
        // 测试修复前后的对比
        function testComparison() {
            updateTestResults(`
                <h6>修复前后对比测试：</h6>
                <button class="btn btn-danger btn-sm" onclick="testOldMethod()">测试修复前的方法</button>
                <button class="btn btn-success btn-sm ms-2" onclick="testNewMethod()">测试修复后的方法</button>
                <div id="comparisonResult" class="mt-2"></div>
            `);
        }
        
        function testOldMethod() {
            // 重置所有元素显示
            document.querySelectorAll('.config-section').forEach(section => {
                section.style.display = 'block';
            });
            
            // 使用修复前的方法
            hideAllConfigSectionsOld();
            
            setTimeout(() => {
                checkVisibilityAndReport('修复前的方法');
            }, 100);
        }
        
        function testNewMethod() {
            // 重置所有元素显示
            document.querySelectorAll('.config-section').forEach(section => {
                section.style.display = 'block';
            });
            
            // 使用修复后的方法
            hideAllConfigSections();
            
            setTimeout(() => {
                checkVisibilityAndReport('修复后的方法');
            }, 100);
        }
        
        function checkVisibilityAndReport(methodName) {
            const datasetSections = ['tableConfig', 'fieldConfig', 'filterConfig'];
            const hiddenCount = datasetSections.filter(id => {
                const element = document.getElementById(id);
                return element && window.getComputedStyle(element).display === 'none';
            }).length;
            
            const result = hiddenCount === 0 ? 
                `✅ ${methodName}: 数据集配置区域正常显示` : 
                `❌ ${methodName}: ${hiddenCount}个数据集配置区域被误隐藏`;
                
            document.getElementById('comparisonResult').innerHTML += `<div>${result}</div>`;
        }
        
        // 页面加载完成后显示对比测试按钮
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateTestResults(`
                    <p>DOM冲突修复测试说明：</p>
                    <ol>
                        <li>点击"打开数据源模态框"模拟用户操作</li>
                        <li>点击"检查数据集向导显示状态"验证修复效果</li>
                        <li>如果所有配置区域都显示为"可见"，说明修复成功</li>
                    </ol>
                    <button class="btn btn-info" onclick="testComparison()">运行修复前后对比测试</button>
                `);
            }, 500);
        });
    </script>
</body>
</html>
