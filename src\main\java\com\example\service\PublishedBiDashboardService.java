package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.PublishedBiDashboard;
import com.example.repository.PublishedBiDashboardRepository;
import com.example.repository.BiDashboardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class PublishedBiDashboardService {
    
    @Autowired
    private PublishedBiDashboardRepository publishedBiDashboardRepository;
    
    @Autowired
    private BiDashboardRepository biDashboardRepository;
    
    /**
     * 发布大屏
     */
    public PublishedBiDashboard publishDashboard(Long dashboardId, String name, String description, int validityDays) {
        BiDashboard dashboard = biDashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new RuntimeException("大屏不存在"));
        
        PublishedBiDashboard published = new PublishedBiDashboard();
        published.setName(name);
        published.setDescription(description);
        published.setDashboard(dashboard);
        published.setAccessToken(generateAccessToken());
        
        // 设置过期时间
        if (validityDays > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, validityDays);
            published.setExpiryDate(calendar.getTime());
        }
        // validityDays <= 0 表示永久有效，不设置过期时间
        
        return publishedBiDashboardRepository.save(published);
    }
    
    /**
     * 撤销发布
     */
    public void revokeDashboard(Long publishedId) {
        PublishedBiDashboard published = publishedBiDashboardRepository.findById(publishedId)
                .orElseThrow(() -> new RuntimeException("发布记录不存在"));

        published.setStatus("REVOKED");
        publishedBiDashboardRepository.save(published);
    }

    /**
     * 删除发布记录
     */
    public void deleteDashboard(Long publishedId) {
        PublishedBiDashboard published = publishedBiDashboardRepository.findById(publishedId)
                .orElseThrow(() -> new RuntimeException("发布记录不存在"));

        publishedBiDashboardRepository.delete(published);
    }

    /**
     * 恢复发布记录
     */
    public void restoreDashboard(Long publishedId) {
        PublishedBiDashboard published = publishedBiDashboardRepository.findById(publishedId)
                .orElseThrow(() -> new RuntimeException("发布记录不存在"));

        if (!"REVOKED".equals(published.getStatus())) {
            throw new RuntimeException("只能恢复已撤销的发布记录");
        }

        // 检查是否已过期
        if (published.getExpiryDate() != null && published.getExpiryDate().before(new Date())) {
            throw new RuntimeException("发布记录已过期，无法恢复");
        }

        published.setStatus("ACTIVE");
        publishedBiDashboardRepository.save(published);
    }
    
    /**
     * 根据访问令牌获取发布的大屏
     */
    public Optional<PublishedBiDashboard> getByAccessToken(String accessToken) {
        return publishedBiDashboardRepository.findByAccessToken(accessToken);
    }
    
    /**
     * 获取所有发布记录
     */
    @Transactional(readOnly = true)
    public List<PublishedBiDashboard> getAllPublished() {
        return publishedBiDashboardRepository.findAllByOrderByPublishedAtDesc();
    }
    
    /**
     * 获取有效的发布记录
     */
    public List<PublishedBiDashboard> getActivePublished() {
        return publishedBiDashboardRepository.findAllActive(new Date());
    }
    
    /**
     * 更新过期状态
     */
    public void updateExpiredStatus() {
        List<PublishedBiDashboard> expired = publishedBiDashboardRepository.findAllExpired(new Date());
        for (PublishedBiDashboard published : expired) {
            published.setStatus("EXPIRED");
        }
        publishedBiDashboardRepository.saveAll(expired);
    }
    
    /**
     * 检查大屏是否已发布
     */
    public boolean isDashboardPublished(Long dashboardId) {
        return publishedBiDashboardRepository.existsActiveByDashboardId(dashboardId, new Date());
    }
    
    /**
     * 生成访问令牌
     */
    private String generateAccessToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取有效期选项
     */
    public Map<String, Integer> getValidityOptions() {
        Map<String, Integer> options = new LinkedHashMap<>();
        options.put("1天", 1);
        options.put("3天", 3);
        options.put("7天", 7);
        options.put("30天", 30);
        options.put("半年", 180);
        options.put("永久", 0);
        return options;
    }
}
