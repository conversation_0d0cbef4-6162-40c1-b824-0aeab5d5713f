package com.example.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * HTML代码片段实体类
 */
@Entity
@Table(name = "html_code_snippets")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HtmlCodeSnippet {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "html_content", nullable = false, columnDefinition = "TEXT")
    private String htmlContent;
    
    @Column(name = "tags", length = 500)
    private String tags;
    
    @Column(name = "is_favorite")
    private Boolean isFavorite = false;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 多对一关系：多个HTML代码片段属于一个分类
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id")
    private HtmlCodeCategory category;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 获取标签数组
     */
    public String[] getTagArray() {
        if (tags == null || tags.trim().isEmpty()) {
            return new String[0];
        }
        return tags.split(",");
    }
    
    /**
     * 设置标签数组
     */
    public void setTagArray(String[] tagArray) {
        if (tagArray == null || tagArray.length == 0) {
            this.tags = null;
        } else {
            this.tags = String.join(",", tagArray);
        }
    }
}
