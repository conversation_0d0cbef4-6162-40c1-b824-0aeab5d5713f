package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "material_files")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialFile {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "filename", nullable = false)
    private String filename;
    
    @Column(name = "original_name", nullable = false)
    private String originalName;
    
    @Column(name = "file_url", nullable = false, length = 500)
    private String fileUrl;
    
    @Column(name = "file_size", nullable = false)
    private Long fileSize;
    
    @Column(name = "file_type", nullable = false, length = 50)
    private String fileType;
    
    @Column(name = "is_default")
    private Boolean isDefault = false;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 多对一关系：多个素材文件属于一个分类
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id")
    @JsonIgnoreProperties("materialFiles")
    private MaterialCategory category;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 获取文件大小的友好显示格式
     */
    public String getFileSizeDisplay() {
        if (fileSize == null) return "0 B";
        
        double size = fileSize.doubleValue();
        if (size < 1024) {
            return String.format("%.0f B", size);
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024);
        } else {
            return String.format("%.2f MB", size / (1024 * 1024));
        }
    }
    
    /**
     * 检查是否为动画文件
     */
    public boolean isAnimated() {
        return fileType != null && (
            fileType.equals("image/gif") || 
            fileType.equals("image/apng") ||
            fileType.equals("image/webp")
        );
    }
}
