package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.repository.BiDashboardRepository;
import com.example.repository.BiWidgetRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class BiDashboardImportService {

    @Autowired
    private BiDashboardRepository dashboardRepository;

    @Autowired
    private BiWidgetRepository widgetRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 导入大屏模板
     */
    @Transactional
    public Map<String, Object> importDashboardTemplate(MultipartFile file, String dashboardName) {
        log.info("开始导入大屏模板，文件名: {}, 大屏名称: {}", file.getOriginalFilename(), dashboardName);

        try {
            // 1. 解析上传文件
            Map<String, Object> templateData = parseTemplateFile(file);

            // 2. 验证模板格式
            validateTemplate(templateData);

            // 3. 创建新大屏
            BiDashboard dashboard = createDashboard(templateData, dashboardName);

            // 4. 创建组件
            List<BiWidget> widgets = createWidgets(dashboard.getId(), templateData);

            // 5. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("dashboardId", dashboard.getId());
            result.put("dashboardName", dashboard.getName());
            result.put("widgetCount", widgets.size());
            result.put("message", "模板导入成功");

            log.info("大屏模板导入成功，新大屏ID: {}, 组件数: {}", dashboard.getId(), widgets.size());
            return result;

        } catch (Exception e) {
            log.error("导入大屏模板失败，文件: {}", file.getOriginalFilename(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 解析模板文件
     */
    private Map<String, Object> parseTemplateFile(MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件为空");
        }

        if (!file.getOriginalFilename().toLowerCase().endsWith(".json")) {
            throw new RuntimeException("文件格式错误，请上传JSON文件");
        }

        // 文件大小限制（10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new RuntimeException("文件大小超过限制（10MB）");
        }

        try {
            String content = new String(file.getBytes(), "UTF-8");
            JsonNode rootNode = objectMapper.readTree(content);
            return objectMapper.convertValue(rootNode, Map.class);
        } catch (Exception e) {
            throw new RuntimeException("文件解析失败，请检查JSON格式: " + e.getMessage());
        }
    }

    /**
     * 验证模板格式
     */
    private void validateTemplate(Map<String, Object> templateData) {
        // 检查必要字段
        if (!templateData.containsKey("metadata")) {
            throw new RuntimeException("模板格式错误：缺少metadata字段");
        }

        if (!templateData.containsKey("canvasConfig")) {
            throw new RuntimeException("模板格式错误：缺少canvasConfig字段");
        }

        if (!templateData.containsKey("widgets")) {
            throw new RuntimeException("模板格式错误：缺少widgets字段");
        }

        // 检查版本兼容性
        Map<String, Object> metadata = (Map<String, Object>) templateData.get("metadata");
        String version = (String) metadata.get("version");
        if (version == null || !version.equals("1.0")) {
            log.warn("模板版本不匹配，当前支持版本: 1.0, 模板版本: {}", version);
        }

        // 检查组件数据
        List<Map<String, Object>> widgets = (List<Map<String, Object>>) templateData.get("widgets");
        if (widgets == null) {
            throw new RuntimeException("组件数据格式错误");
        }

        // 验证每个组件的基本结构
        for (int i = 0; i < widgets.size(); i++) {
            Map<String, Object> widget = widgets.get(i);
            if (!widget.containsKey("type") || !widget.containsKey("position") || !widget.containsKey("setup")) {
                throw new RuntimeException("组件 " + (i + 1) + " 格式错误：缺少必要字段");
            }
        }

        log.info("模板验证通过，组件数: {}", widgets.size());
    }

    /**
     * 创建大屏
     */
    private BiDashboard createDashboard(Map<String, Object> templateData, String dashboardName) throws Exception {
        Map<String, Object> metadata = (Map<String, Object>) templateData.get("metadata");
        Map<String, Object> canvasConfig = (Map<String, Object>) templateData.get("canvasConfig");

        BiDashboard dashboard = new BiDashboard();
        
        // 设置基本信息
        if (dashboardName != null && !dashboardName.trim().isEmpty()) {
            dashboard.setName(dashboardName.trim());
        } else {
            String templateName = (String) metadata.get("templateName");
            dashboard.setName(templateName != null ? templateName + "_导入" : "导入的大屏");
        }

        dashboard.setDescription((String) metadata.get("description"));
        dashboard.setCreatedAt(LocalDateTime.now());
        dashboard.setUpdatedAt(LocalDateTime.now());

        // 设置画布配置
        String canvasConfigJson = objectMapper.writeValueAsString(canvasConfig);
        dashboard.setCanvasConfig(canvasConfigJson);

        // 保存大屏
        dashboard = dashboardRepository.save(dashboard);
        log.info("创建大屏成功，ID: {}, 名称: {}", dashboard.getId(), dashboard.getName());

        return dashboard;
    }

    /**
     * 创建组件
     */
    private List<BiWidget> createWidgets(Long dashboardId, Map<String, Object> templateData) throws Exception {
        List<Map<String, Object>> widgetDataList = (List<Map<String, Object>>) templateData.get("widgets");
        List<BiWidget> widgets = new ArrayList<>();

        for (int i = 0; i < widgetDataList.size(); i++) {
            Map<String, Object> widgetData = widgetDataList.get(i);
            
            try {
                BiWidget widget = createWidget(dashboardId, widgetData, i);
                widgets.add(widget);
            } catch (Exception e) {
                log.error("创建组件失败，索引: {}, 类型: {}", i, widgetData.get("type"), e);
                // 继续创建其他组件
            }
        }

        log.info("创建组件完成，成功: {}, 总数: {}", widgets.size(), widgetDataList.size());
        return widgets;
    }

    /**
     * 创建单个组件
     */
    private BiWidget createWidget(Long dashboardId, Map<String, Object> widgetData, int index) throws Exception {
        BiWidget widget = new BiWidget();

        // 基本信息
        widget.setDashboardId(dashboardId);
        widget.setWidgetType((String) widgetData.get("type"));
        widget.setCreatedAt(LocalDateTime.now());
        widget.setUpdatedAt(LocalDateTime.now());

        // 位置配置
        Map<String, Object> position = (Map<String, Object>) widgetData.get("position");
        String positionJson = objectMapper.writeValueAsString(position);
        widget.setPosition(positionJson);

        // 兼容性字段
        widget.setPositionX(((Number) position.getOrDefault("left", 0)).intValue());
        widget.setPositionY(((Number) position.getOrDefault("top", 0)).intValue());
        widget.setWidth(((Number) position.getOrDefault("width", 300)).intValue());
        widget.setHeight(((Number) position.getOrDefault("height", 200)).intValue());
        widget.setZIndex(((Number) position.getOrDefault("zIndex", 1000 + index)).intValue());

        // 样式配置
        Map<String, Object> setup = (Map<String, Object>) widgetData.get("setup");
        String setupJson = objectMapper.writeValueAsString(setup);
        widget.setSetup(setupJson);

        // 数据配置（使用默认空配置）
        Map<String, Object> dataConfig = (Map<String, Object>) widgetData.getOrDefault("data", new HashMap<>());
        String dataJson = objectMapper.writeValueAsString(dataConfig);
        widget.setData(dataJson);

        // 保存组件
        widget = widgetRepository.save(widget);
        log.debug("创建组件成功，ID: {}, 类型: {}", widget.getId(), widget.getWidgetType());

        return widget;
    }

    /**
     * 预览模板信息（不实际导入）
     */
    public Map<String, Object> previewTemplate(MultipartFile file) {
        try {
            Map<String, Object> templateData = parseTemplateFile(file);
            validateTemplate(templateData);

            Map<String, Object> metadata = (Map<String, Object>) templateData.get("metadata");
            Map<String, Object> canvasConfig = (Map<String, Object>) templateData.get("canvasConfig");
            List<Map<String, Object>> widgets = (List<Map<String, Object>>) templateData.get("widgets");

            Map<String, Object> preview = new HashMap<>();
            preview.put("success", true);
            preview.put("templateName", metadata.get("templateName"));
            preview.put("description", metadata.get("description"));
            preview.put("exportTime", metadata.get("exportTime"));
            preview.put("canvasSize", canvasConfig.get("width") + "×" + canvasConfig.get("height"));
            preview.put("widgetCount", widgets.size());

            // 统计组件类型
            Map<String, Integer> widgetTypes = new HashMap<>();
            for (Map<String, Object> widget : widgets) {
                String type = (String) widget.get("type");
                widgetTypes.put(type, widgetTypes.getOrDefault(type, 0) + 1);
            }
            preview.put("widgetTypes", widgetTypes);

            return preview;

        } catch (Exception e) {
            Map<String, Object> preview = new HashMap<>();
            preview.put("success", false);
            preview.put("message", "预览失败: " + e.getMessage());
            return preview;
        }
    }
}
