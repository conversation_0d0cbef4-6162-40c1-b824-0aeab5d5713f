package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.entity.HtmlCodeSnippet;
import com.example.repository.BiDashboardRepository;
import com.example.repository.BiWidgetRepository;
import com.example.repository.HtmlCodeSnippetRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 大屏资源文件分析服务
 * 用于分析大屏配置中引用的所有资源文件
 */
@Service
@Slf4j
public class BiDashboardResourceAnalyzer {

    @Autowired
    private BiDashboardRepository dashboardRepository;

    @Autowired
    private BiWidgetRepository widgetRepository;

    @Autowired
    private HtmlCodeSnippetRepository htmlCodeSnippetRepository;

    @Autowired
    private ObjectMapper objectMapper;

    // URL匹配模式
    private static final Pattern IMAGE_URL_PATTERN = Pattern.compile("http://[^/]+/images/([^\"'\\s]+)");
    private static final Pattern MATERIAL_URL_PATTERN = Pattern.compile("http://[^/]+/materials/([^\"'\\s]+)");
    private static final Pattern VIDEO_URL_PATTERN = Pattern.compile("http://[^/]+/videos/([^\"'\\s]+)");

    /**
     * 分析大屏中的所有资源文件
     */
    public Map<String, Object> analyzeResources(Long dashboardId) {
        log.info("开始分析大屏资源文件，ID: {}", dashboardId);

        // 获取大屏信息
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
        if (!dashboardOpt.isPresent()) {
            throw new RuntimeException("大屏不存在: " + dashboardId);
        }

        BiDashboard dashboard = dashboardOpt.get();
        List<BiWidget> widgets = widgetRepository.findByDashboardIdOrderByCreatedAt(dashboardId);

        Map<String, Object> result = new HashMap<>();
        Set<String> imageUrls = new HashSet<>();
        Set<String> materialUrls = new HashSet<>();
        Set<String> videoUrls = new HashSet<>();
        Set<Long> htmlSnippetIds = new HashSet<>();

        try {
            // 分析画布配置
            if (dashboard.getCanvasConfig() != null) {
                analyzeCanvasConfig(dashboard.getCanvasConfig(), imageUrls, materialUrls, videoUrls);
            }

            // 分析组件配置
            for (BiWidget widget : widgets) {
                analyzeWidgetConfig(widget, imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            }

            // 获取HTML代码片段详情
            List<HtmlCodeSnippet> htmlSnippets = new ArrayList<>();
            if (!htmlSnippetIds.isEmpty()) {
                htmlSnippets = htmlCodeSnippetRepository.findAllById(htmlSnippetIds);
            }

            // 构建结果
            result.put("imageUrls", new ArrayList<>(imageUrls));
            result.put("materialUrls", new ArrayList<>(materialUrls));
            result.put("videoUrls", new ArrayList<>(videoUrls));
            result.put("htmlSnippets", htmlSnippets);
            result.put("totalFiles", imageUrls.size() + materialUrls.size() + videoUrls.size());
            result.put("totalHtmlSnippets", htmlSnippets.size());

            log.info("资源分析完成，图片: {}, 素材: {}, 视频: {}, HTML片段: {}", 
                    imageUrls.size(), materialUrls.size(), videoUrls.size(), htmlSnippets.size());

            return result;

        } catch (Exception e) {
            log.error("分析大屏资源失败，ID: {}", dashboardId, e);
            throw new RuntimeException("资源分析失败: " + e.getMessage());
        }
    }

    /**
     * 分析画布配置中的资源
     */
    private void analyzeCanvasConfig(String canvasConfig, Set<String> imageUrls, 
                                   Set<String> materialUrls, Set<String> videoUrls) {
        try {
            JsonNode canvasNode = objectMapper.readTree(canvasConfig);
            
            // 分析背景图片
            if (canvasNode.has("backgroundImage")) {
                String bgImage = canvasNode.get("backgroundImage").asText();
                if (bgImage != null && !bgImage.isEmpty()) {
                    categorizeUrl(bgImage, imageUrls, materialUrls, videoUrls);
                }
            }

            // 分析其他可能的资源引用
            analyzeJsonNode(canvasNode, imageUrls, materialUrls, videoUrls);

        } catch (Exception e) {
            log.warn("解析画布配置失败: {}", e.getMessage());
        }
    }

    /**
     * 分析组件配置中的资源
     */
    private void analyzeWidgetConfig(BiWidget widget, Set<String> imageUrls,
                                   Set<String> materialUrls, Set<String> videoUrls,
                                   Set<Long> htmlSnippetIds) {
        try {
            log.debug("分析组件配置，组件ID: {}, 类型: {}", widget.getId(), widget.getWidgetType());
            log.debug("组件字段内容 - setup: {}", widget.getSetup());
            log.debug("组件字段内容 - data: {}", widget.getData());
            log.debug("组件字段内容 - config: {}", widget.getConfig());
            log.debug("组件字段内容 - options: {}", widget.getOptions());

            // 分析各个配置字段
            analyzeConfigField("setup", widget.getSetup(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            analyzeConfigField("data", widget.getData(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            analyzeConfigField("position", widget.getPosition(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            analyzeConfigField("options", widget.getOptions(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            analyzeConfigField("config", widget.getConfig(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);
            analyzeConfigField("dataSourceConfig", widget.getDataSourceConfig(), imageUrls, materialUrls, videoUrls, htmlSnippetIds);

        } catch (Exception e) {
            log.warn("分析组件配置失败，组件ID: {}, 错误: {}", widget.getId(), e.getMessage());
        }
    }

    /**
     * 分析配置字段中的资源
     */
    private void analyzeConfigField(String fieldName, String configJson, Set<String> imageUrls,
                                  Set<String> materialUrls, Set<String> videoUrls,
                                  Set<Long> htmlSnippetIds) {
        if (configJson == null || configJson.trim().isEmpty()) {
            return;
        }

        try {
            // 尝试解析为JSON
            JsonNode configNode = objectMapper.readTree(configJson);
            log.debug("分析配置字段: {}, JSON内容: {}", fieldName, configJson);
            analyzeJsonNode(configNode, imageUrls, materialUrls, videoUrls);
            extractHtmlSnippetIds(configNode, htmlSnippetIds);

        } catch (Exception e) {
            // 如果不是JSON，直接作为文本分析
            log.debug("配置字段 {} 不是JSON格式，作为文本分析: {}", fieldName, configJson);
            analyzeTextContent(configJson, imageUrls, materialUrls, videoUrls);
        }
    }

    /**
     * 递归分析JSON节点中的资源
     */
    private void analyzeJsonNode(JsonNode node, Set<String> imageUrls, 
                               Set<String> materialUrls, Set<String> videoUrls) {
        if (node == null) {
            return;
        }

        if (node.isTextual()) {
            String text = node.asText();
            categorizeUrl(text, imageUrls, materialUrls, videoUrls);
        } else if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                // 检查常见的资源字段名
                if (isResourceField(key) && value.isTextual()) {
                    String url = value.asText();
                    log.debug("发现资源字段: {} = {}", key, url);
                    categorizeUrl(url, imageUrls, materialUrls, videoUrls);
                }
                
                // 递归分析子节点
                analyzeJsonNode(value, imageUrls, materialUrls, videoUrls);
            });
        } else if (node.isArray()) {
            for (JsonNode arrayItem : node) {
                analyzeJsonNode(arrayItem, imageUrls, materialUrls, videoUrls);
            }
        }
    }

    /**
     * 提取HTML代码片段ID
     */
    private void extractHtmlSnippetIds(JsonNode node, Set<Long> htmlSnippetIds) {
        if (node == null) {
            return;
        }

        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                // 检查HTML代码片段相关字段
                if (isHtmlSnippetField(key)) {
                    try {
                        Long snippetId = null;
                        if (value.isNumber()) {
                            snippetId = value.asLong();
                        } else if (value.isTextual()) {
                            String textValue = value.asText();
                            if (textValue != null && !textValue.trim().isEmpty()) {
                                snippetId = Long.parseLong(textValue.trim());
                            }
                        }

                        if (snippetId != null) {
                            log.debug("发现HTML片段字段: {} = {}", key, snippetId);
                            htmlSnippetIds.add(snippetId);
                        }
                    } catch (NumberFormatException e) {
                        log.debug("HTML片段ID解析失败: {} = {}", key, value.asText());
                    }
                }
                
                // 递归分析子节点
                extractHtmlSnippetIds(value, htmlSnippetIds);
            });
        } else if (node.isArray()) {
            for (JsonNode arrayItem : node) {
                extractHtmlSnippetIds(arrayItem, htmlSnippetIds);
            }
        }
    }

    /**
     * 分析文本内容中的资源URL
     */
    private void analyzeTextContent(String text, Set<String> imageUrls, 
                                  Set<String> materialUrls, Set<String> videoUrls) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        // 使用正则表达式匹配URL
        Matcher imageMatcher = IMAGE_URL_PATTERN.matcher(text);
        while (imageMatcher.find()) {
            imageUrls.add(imageMatcher.group(0));
        }

        Matcher materialMatcher = MATERIAL_URL_PATTERN.matcher(text);
        while (materialMatcher.find()) {
            materialUrls.add(materialMatcher.group(0));
        }

        Matcher videoMatcher = VIDEO_URL_PATTERN.matcher(text);
        while (videoMatcher.find()) {
            videoUrls.add(videoMatcher.group(0));
        }
    }

    /**
     * 根据URL类型分类
     */
    private void categorizeUrl(String url, Set<String> imageUrls,
                             Set<String> materialUrls, Set<String> videoUrls) {
        if (url == null || url.trim().isEmpty()) {
            return;
        }

        if (url.contains("/images/")) {
            log.debug("分类为图片URL: {}", url);
            imageUrls.add(url);
        } else if (url.contains("/materials/")) {
            log.debug("分类为素材URL: {}", url);
            materialUrls.add(url);
        } else if (url.contains("/videos/")) {
            log.debug("分类为视频URL: {}", url);
            videoUrls.add(url);
        } else {
            log.debug("未分类的URL: {}", url);
        }
    }

    /**
     * 判断是否为资源字段
     */
    private boolean isResourceField(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("url") ||
               lowerFieldName.contains("src") ||
               lowerFieldName.contains("image") ||
               lowerFieldName.contains("video") ||
               lowerFieldName.contains("background") ||
               lowerFieldName.contains("icon") ||
               lowerFieldName.contains("avatar") ||
               lowerFieldName.contains("logo") ||
               lowerFieldName.contains("material") ||
               lowerFieldName.contains("decoration") ||
               lowerFieldName.contains("poster");
    }

    /**
     * 判断是否为HTML代码片段字段
     */
    private boolean isHtmlSnippetField(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("htmlsnippetid") ||
               lowerFieldName.contains("html_snippet_id") ||
               lowerFieldName.contains("snippetid") ||
               lowerFieldName.contains("htmlcodeid") ||
               lowerFieldName.contains("html_code_id") ||
               lowerFieldName.equals("htmlsnippetid") ||
               lowerFieldName.equals("snippet_id") ||
               lowerFieldName.equals("code_id");
    }
}
