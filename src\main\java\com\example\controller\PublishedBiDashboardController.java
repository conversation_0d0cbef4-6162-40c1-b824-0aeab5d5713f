package com.example.controller;

import com.example.entity.BiDashboard;
import com.example.entity.PublishedBiDashboard;
import com.example.service.BiDashboardService;
import com.example.service.PublishedBiDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/bi/published")
public class PublishedBiDashboardController {
    
    @Autowired
    private PublishedBiDashboardService publishedBiDashboardService;
    
    @Autowired
    private BiDashboardService biDashboardService;
    
    /**
     * 发布管理页面
     */
    @GetMapping("/management")
    public String publishManagement(Model model) {
        try {
            System.out.println("=== 开始加载BI大屏发布管理页面 ===");

            List<PublishedBiDashboard> allPublished = publishedBiDashboardService.getAllPublished();
            System.out.println("成功获取发布记录数量: " + (allPublished != null ? allPublished.size() : "null"));

            if (allPublished == null || allPublished.isEmpty()) {
                System.out.println("没有发布记录，直接返回空列表");
                model.addAttribute("publishedList", new ArrayList<>());
                model.addAttribute("dashboards", biDashboardService.getAllDashboards());
                model.addAttribute("validityOptions", publishedBiDashboardService.getValidityOptions());
                return "bi/publish-management";
            }

            // 将实体转换为Map对象，避免懒加载问题
            List<Map<String, Object>> publishedList = new ArrayList<>();

            for (int i = 0; i < allPublished.size(); i++) {
                PublishedBiDashboard published = allPublished.get(i);
                System.out.println("处理第" + (i+1) + "个发布记录，ID: " + published.getId());

                try {
                    Map<String, Object> item = new HashMap<>();

                    // 基本字段
                    System.out.println("  - 设置基本字段");
                    item.put("id", published.getId());
                    item.put("name", published.getName());
                    item.put("description", published.getDescription());
                    item.put("accessToken", published.getAccessToken());
                    item.put("publishedAt", published.getPublishedAt());
                    item.put("expiryDate", published.getExpiryDate());
                    item.put("createdAt", published.getCreatedAt());

                    // 状态字段
                    System.out.println("  - 设置状态字段");
                    String actualStatus = published.getStatus();
                    System.out.println("  - 原始状态: " + actualStatus);

                    // 检查是否过期（避免调用isValid方法）
                    if ("ACTIVE".equals(actualStatus) && published.getExpiryDate() != null) {
                        if (published.getExpiryDate().before(new Date())) {
                            actualStatus = "EXPIRED";
                            System.out.println("  - 状态已更新为: EXPIRED");
                        }
                    }
                    item.put("status", actualStatus);

                    // 安全地获取关联的大屏名称
                    System.out.println("  - 获取关联大屏名称");
                    String dashboardName = "未知大屏";
                    try {
                        BiDashboard dashboard = published.getDashboard();
                        System.out.println("  - Dashboard对象: " + (dashboard != null ? "存在" : "null"));
                        if (dashboard != null) {
                            dashboardName = dashboard.getName();
                            System.out.println("  - Dashboard名称: " + dashboardName);
                        }
                    } catch (Exception e) {
                        System.err.println("  - 获取大屏名称失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                    item.put("dashboardName", dashboardName);

                    publishedList.add(item);
                    System.out.println("  - 第" + (i+1) + "个记录处理完成");

                } catch (Exception e) {
                    System.err.println("处理第" + (i+1) + "个发布记录时出错: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                    e.printStackTrace();
                    throw e; // 重新抛出异常以便调试
                }
            }

            System.out.println("所有发布记录处理完成，获取其他数据");
            List<BiDashboard> dashboards = biDashboardService.getAllDashboards();
            Map<String, Integer> validityOptions = publishedBiDashboardService.getValidityOptions();

            model.addAttribute("publishedList", publishedList);
            model.addAttribute("dashboards", dashboards);
            model.addAttribute("validityOptions", validityOptions);

            System.out.println("=== BI大屏发布管理页面加载完成 ===");
            return "bi/publish-management";

        } catch (Exception e) {
            System.err.println("=== BI大屏发布管理页面加载失败 ===");
            System.err.println("错误类型: " + e.getClass().getSimpleName());
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
            throw e; // 重新抛出异常以便Spring处理
        }
    }
    
    /**
     * 发布大屏
     */
    @PostMapping("/publish")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> publishDashboard(
            @RequestParam Long dashboardId,
            @RequestParam String name,
            @RequestParam(required = false) String description,
            @RequestParam int validityDays) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            PublishedBiDashboard published = publishedBiDashboardService.publishDashboard(
                    dashboardId, name, description, validityDays);
            
            response.put("success", true);
            response.put("message", "大屏发布成功");
            response.put("accessToken", published.getAccessToken());
            response.put("publishedId", published.getId());
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "发布失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 撤销发布
     */
    @PostMapping("/revoke/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> revokeDashboard(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            publishedBiDashboardService.revokeDashboard(id);
            response.put("success", true);
            response.put("message", "发布已撤销");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "撤销失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 删除发布记录
     */
    @DeleteMapping("/delete/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteDashboard(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            publishedBiDashboardService.deleteDashboard(id);
            response.put("success", true);
            response.put("message", "发布记录已删除");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 恢复发布记录
     */
    @PostMapping("/restore/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> restoreDashboard(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            publishedBiDashboardService.restoreDashboard(id);
            response.put("success", true);
            response.put("message", "发布已恢复");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "恢复失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }
    
    /**
     * 公开访问页面
     */
    @GetMapping("/{accessToken}")
    public String viewPublishedDashboard(@PathVariable String accessToken, Model model) {
        System.out.println("=== 访问发布的大屏页面 ===");
        System.out.println("访问令牌: " + accessToken);

        Optional<PublishedBiDashboard> publishedOpt = publishedBiDashboardService.getByAccessToken(accessToken);

        if (!publishedOpt.isPresent()) {
            System.out.println("访问令牌无效: " + accessToken);
            model.addAttribute("error", "访问令牌无效");
            return "error/expired";
        }

        PublishedBiDashboard published = publishedOpt.get();
        System.out.println("找到发布记录: " + published.getName());

        if (!published.isValid()) {
            System.out.println("发布已过期或被撤销");
            model.addAttribute("error", "发布已过期或被撤销");
            return "error/expired";
        }

        BiDashboard dashboard = published.getDashboard();
        System.out.println("关联的大屏: " + dashboard.getName() + " (ID: " + dashboard.getId() + ")");

        // 重要：加载大屏的组件数据
        try {
            Optional<BiDashboard> dashboardWithWidgets = biDashboardService.getDashboardWithWidgets(dashboard.getId());
            if (dashboardWithWidgets.isPresent()) {
                dashboard = dashboardWithWidgets.get();
                System.out.println("成功加载组件数据，组件数量: " + (dashboard.getWidgets() != null ? dashboard.getWidgets().size() : 0));

                // 确保画布配置不为空
                if (dashboard.getCanvasConfig() == null || dashboard.getCanvasConfig().trim().isEmpty()) {
                    dashboard.setCanvasConfig("{\"width\":1920,\"height\":1080,\"backgroundColor\":\"#ffffff\",\"backgroundType\":\"color\"}");
                    System.out.println("使用默认画布配置");
                } else {
                    System.out.println("画布配置: " + dashboard.getCanvasConfig());
                }
            } else {
                System.out.println("警告：无法加载大屏组件数据");
            }
        } catch (Exception e) {
            System.err.println("加载组件数据失败: " + e.getMessage());
            e.printStackTrace();
        }

        model.addAttribute("dashboard", dashboard);
        model.addAttribute("published", published);
        model.addAttribute("isPublicView", true);

        System.out.println("=== 发布大屏页面数据准备完成 ===");
        return "bi/published-dashboard";
    }
    
    /**
     * 获取发布状态
     */
    @GetMapping("/status/{dashboardId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getPublishStatus(@PathVariable Long dashboardId) {
        Map<String, Object> response = new HashMap<>();
        
        boolean isPublished = publishedBiDashboardService.isDashboardPublished(dashboardId);
        response.put("isPublished", isPublished);
        
        return ResponseEntity.ok(response);
    }
}
