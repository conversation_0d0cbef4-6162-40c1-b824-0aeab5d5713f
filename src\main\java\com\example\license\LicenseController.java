package com.example.license;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

/**
 * 许可证控制器
 * 处理序列号激活相关的Web请求
 */
@Controller
@RequestMapping("/license")
public class LicenseController {
    
    @Autowired
    private LicenseService licenseService;
    
    /**
     * 显示激活页面
     */
    @GetMapping("/activation")
    public String showActivationPage(Model model) {
        System.out.println("=== 显示激活页面 ===");

        // 检查是否已经激活
        if (licenseService.isLicenseValid()) {
            System.out.println("许可证已有效，重定向到登录页面");
            return "redirect:/auth/login";
        }

        // 获取当前硬件ID用于显示
        String hardwareId = LicenseUtils.generateHardwareId();
        model.addAttribute("hardwareId", hardwareId);
        System.out.println("硬件ID: " + hardwareId);

        return "license/activation";
    }




    /**
     * 处理激活请求
     */
    @PostMapping("/activate")
    public String activateLicense(@RequestParam(required = false) String userId,
                                 @RequestParam(required = false) String serialNumber,
                                 Model model) {



        // 验证输入参数
        if (userId == null || userId.trim().isEmpty()) {
            System.out.println("错误: 用户ID为空");
            model.addAttribute("error", "请输入用户ID");
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            return "license/activation";
        }

        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            System.out.println("错误: 序列号为空");
            model.addAttribute("error", "请输入序列号");
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            model.addAttribute("userId", userId);
            return "license/activation";
        }

        System.out.println("开始执行激活...");
        // 执行激活
        String result = licenseService.activateLicense(userId.trim(), serialNumber.trim());
        System.out.println("激活结果: " + result);

        if ("激活成功".equals(result)) {
            System.out.println("激活成功，准备跳转");
            model.addAttribute("success", "激活成功！正在跳转到登录页面...");
            model.addAttribute("redirectUrl", "/auth/login");
            return "license/activation";
        } else {
            System.out.println("激活失败，返回错误信息");
            model.addAttribute("error", result);
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            model.addAttribute("userId", userId);
            model.addAttribute("serialNumber", serialNumber);
            return "license/activation";
        }
    }
    

    


}
