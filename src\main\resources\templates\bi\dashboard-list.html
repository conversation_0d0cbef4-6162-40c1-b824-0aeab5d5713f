<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - BI大屏管理</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bootstrap-icons.min.css" rel="stylesheet">
    <link href="/css/button-styles.css" rel="stylesheet">
    <style>
        .dashboard-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .dashboard-preview {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        .btn-group-actions {
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - BI大屏管理', 'biDashboard', true, true, true, null)"></div>

    <div class="container-fluid">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="bi bi-bar-chart"></i> BI大屏管理</h2>
                <p class="text-muted">创建和管理数据可视化大屏</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-success me-2" onclick="showImportModal()">
                    <i class="bi bi-upload"></i> 导入模板
                </button>
                <button class="btn btn-primary" onclick="showCreateModal()">
                    <i class="bi bi-plus-circle"></i> 新建大屏
                </button>
            </div>
        </div>

        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <span th:text="${error}"></span>
        </div>

        <!-- 大屏列表 -->
        <div class="row" id="dashboardList">
            <div th:each="dashboard : ${dashboards}" class="col-md-4 col-lg-3 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="dashboard-preview">
                        <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title" th:text="${dashboard.name}">大屏名称</h5>
                        <p class="card-text text-muted" th:text="${dashboard.description ?: '暂无描述'}">大屏描述</p>
                        <small class="text-muted">
                            创建时间: <span th:text="${dashboard.createdAt != null ? dashboard.createdAt : '未知'}"></span>
                        </small>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex btn-group-actions flex-wrap">
                            <button class="btn btn-outline-primary btn-sm flex-fill"
                                    th:onclick="|designDashboard(${dashboard.id})|">
                                <i class="bi bi-pencil"></i> 设计
                            </button>
                            <button class="btn btn-outline-success btn-sm flex-fill"
                                    th:onclick="|previewDashboard(${dashboard.id})|">
                                <i class="bi bi-eye"></i> 预览
                            </button>
                            <button class="btn btn-outline-info btn-sm flex-fill"
                                    th:onclick="|publishDashboard(${dashboard.id})|"
                                    th:data-name="${dashboard.name}">
                                <i class="bi bi-share"></i> 发布
                            </button>
                            <!-- 导出按钮 -->
                            <button class="btn btn-outline-warning btn-sm"
                                    th:onclick="|showExportModal(${dashboard.id})|"
                                    th:data-name="${dashboard.name}"
                                    th:data-id="${dashboard.id}">
                                <i class="bi bi-download"></i> 导出
                            </button>
                            <button class="btn btn-outline-danger btn-sm"
                                    th:onclick="|deleteDashboard(${dashboard.id})|"
                                    th:data-name="${dashboard.name}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div th:if="${#lists.isEmpty(dashboards)}" class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-bar-chart" style="font-size: 4rem; color: #dee2e6;"></i>
                    <h4 class="mt-3 text-muted">暂无大屏</h4>
                    <p class="text-muted">点击"新建大屏"开始创建您的第一个数据可视化大屏</p>
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="bi bi-plus-circle"></i> 新建大屏
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建大屏模态框 -->
    <div class="modal fade" id="createDashboardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建大屏</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createDashboardForm">
                        <div class="mb-3">
                            <label for="dashboardName" class="form-label">大屏名称 *</label>
                            <input type="text" class="form-control" id="dashboardName" required>
                        </div>
                        <div class="mb-3">
                            <label for="dashboardDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="dashboardDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createDashboard()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入模板模态框 -->
    <div class="modal fade" id="importTemplateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入大屏模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 文件上传区域 -->
                    <div id="uploadArea" class="border border-dashed border-2 p-4 text-center mb-3"
                         style="border-color: #dee2e6; cursor: pointer;"
                         onclick="document.getElementById('templateFile').click()">
                        <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #6c757d;"></i>
                        <h5 class="mt-2">点击选择文件或拖拽文件到此处</h5>
                        <p class="text-muted">支持 .json 格式的模板文件，最大 10MB</p>
                        <input type="file" id="templateFile" accept=".json" style="display: none;" onchange="handleFileSelect(this)">
                    </div>

                    <!-- 文件信息显示 -->
                    <div id="fileInfo" style="display: none;" class="alert alert-info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-file-earmark-text"></i>
                                <span id="fileName"></span>
                                <small class="text-muted">(<span id="fileSize"></span>)</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFile()">
                                <i class="bi bi-x"></i> 清除
                            </button>
                        </div>
                    </div>

                    <!-- 模板预览信息 -->
                    <div id="templatePreview" style="display: none;">
                        <h6>模板信息预览</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>模板名称:</strong> <span id="previewTemplateName"></span></p>
                                <p><strong>画布尺寸:</strong> <span id="previewCanvasSize"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>组件数量:</strong> <span id="previewWidgetCount"></span></p>
                                <p><strong>导出时间:</strong> <span id="previewExportTime"></span></p>
                            </div>
                        </div>
                        <div class="mb-3">
                            <p><strong>描述:</strong> <span id="previewDescription"></span></p>
                        </div>
                        <div class="mb-3">
                            <label for="importDashboardName" class="form-label">新大屏名称</label>
                            <input type="text" class="form-control" id="importDashboardName"
                                   placeholder="留空则使用模板名称">
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="importError" style="display: none;" class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <span id="importErrorMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="importBtn" onclick="importTemplate()" disabled>
                        <i class="bi bi-upload"></i> 导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出选择模态框 -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-download"></i> 导出大屏
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 大屏信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p><strong>大屏名称:</strong> <span id="exportDashboardName">-</span></p>
                            <p><strong>预计文件名:</strong> <span id="exportFileName">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>预计大小:</strong> <span id="exportTotalSize">计算中...</span></p>
                            <p><strong>预览时间:</strong> <span id="exportTime">-</span></p>
                        </div>
                    </div>

                    <!-- 资源统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-image text-primary" style="font-size: 2rem;"></i>
                                    <h5 class="card-title mt-2">图片文件</h5>
                                    <p class="card-text"><span id="exportImageCount">0</span> 个</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-palette text-success" style="font-size: 2rem;"></i>
                                    <h5 class="card-title mt-2">素材文件</h5>
                                    <p class="card-text"><span id="exportMaterialCount">0</span> 个</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-play-circle text-warning" style="font-size: 2rem;"></i>
                                    <h5 class="card-title mt-2">视频文件</h5>
                                    <p class="card-text"><span id="exportVideoCount">0</span> 个</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-code-slash text-info" style="font-size: 2rem;"></i>
                                    <h5 class="card-title mt-2">HTML代码</h5>
                                    <p class="card-text"><span id="exportHtmlCount">0</span> 个</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导出选项 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-file-earmark-code text-primary" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">模板配置</h5>
                                    <p class="card-text">仅导出大屏的配置信息，不包含资源文件</p>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="bi bi-check-circle text-success"></i> 画布配置</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 组件布局</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 样式设置</li>
                                        <li><i class="bi bi-x-circle text-muted"></i> 图片/视频文件</li>
                                        <li><i class="bi bi-x-circle text-muted"></i> 素材文件</li>
                                        <li><i class="bi bi-x-circle text-muted"></i> HTML代码</li>
                                    </ul>
                                    <button type="button" class="btn btn-outline-primary w-100" id="exportTemplateBtn">
                                        <i class="bi bi-file-earmark-code"></i> 导出模板配置
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-archive text-warning" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">完整资源包</h5>
                                    <p class="card-text">导出大屏的所有内容，包含所有资源文件</p>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="bi bi-check-circle text-success"></i> 模板配置文件</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 图片/视频文件</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 素材文件</li>
                                        <li><i class="bi bi-check-circle text-success"></i> HTML代码片段</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 资源清单</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 使用说明</li>
                                    </ul>
                                    <button type="button" class="btn btn-warning w-100" id="exportResourcesBtn">
                                        <i class="bi bi-archive"></i> 导出完整资源包
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示创建模态框
        function showCreateModal() {
            const modal = new bootstrap.Modal(document.getElementById('createDashboardModal'));
            modal.show();
        }

        // 创建大屏
        function createDashboard() {
            const name = document.getElementById('dashboardName').value.trim();
            const description = document.getElementById('dashboardDescription').value.trim();
            
            if (!name) {
                alert('请输入大屏名称');
                return;
            }
            
            const formData = new FormData();
            formData.append('name', name);
            if (description) {
                formData.append('description', description);
            }
            
            fetch('/api/bi/dashboard', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('创建成功');
                    location.reload();
                } else {
                    alert('创建失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建失败');
            });
        }

        // 设计大屏
        function designDashboard(id) {
            window.location.href = `/bi/dashboard/${id}/design`;
        }

        // 预览大屏
        function previewDashboard(id) {
            window.open(`/bi/dashboard/${id}/preview`, '_blank');
        }

        // 发布大屏
        function publishDashboard(id) {
            // 从事件源获取名称
            const button = event.target.closest('button');
            const name = button.getAttribute('data-name') || '未知大屏';

            const publishName = prompt(`请输入发布名称:`, `${name} - 发布版`);
            if (!publishName) return;

            const validityDays = prompt(`请选择有效期:\n1 - 1天\n3 - 3天\n7 - 7天\n30 - 30天\n180 - 半年\n0 - 永久`, '7');
            if (validityDays === null) return;

            const formData = new FormData();
            formData.append('dashboardId', id);
            formData.append('name', publishName);
            formData.append('validityDays', validityDays);

            fetch('/bi/published/publish', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`发布成功！\n访问令牌：${data.accessToken}\n\n您可以通过以下链接访问：\n${window.location.origin}/bi/published/${data.accessToken}`);
                } else {
                    alert('发布失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发布失败：网络错误');
            });
        }

        // 删除大屏
        function deleteDashboard(id) {
            // 从事件源获取名称
            const button = event.target.closest('button');
            const name = button.getAttribute('data-name') || '未知大屏';

            // 首先检查是否有发布记录
            fetch(`/api/bi/dashboard/${id}/check-published`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.hasPublished) {
                            // 有发布记录，显示详细确认对话框
                            showDeleteConfirmWithPublished(id, name, data.publishedRecords);
                        } else {
                            // 没有发布记录，直接确认删除
                            if (confirm(`确定要删除大屏"${name}"吗？此操作不可恢复。`)) {
                                performDelete(id);
                            }
                        }
                    } else {
                        alert('检查发布记录失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('检查发布记录失败');
                });
        }

        // 显示有发布记录的删除确认对话框
        function showDeleteConfirmWithPublished(id, name, publishedRecords) {
            let recordsInfo = '';
            publishedRecords.forEach(record => {
                const status = record.status === 'ACTIVE' ? '有效' :
                              record.status === 'EXPIRED' ? '已过期' : '已撤销';
                const publishedDate = new Date(record.publishedAt).toLocaleString();
                recordsInfo += `\n- ${record.name} (${status}, 发布于: ${publishedDate})`;
            });

            const message = `大屏"${name}"存在 ${publishedRecords.length} 条发布记录：${recordsInfo}\n\n删除大屏将同时删除所有发布记录，此操作不可恢复。\n\n确定要继续删除吗？`;

            if (confirm(message)) {
                performForceDelete(id);
            }
        }

        // 执行普通删除
        function performDelete(id) {
            fetch(`/api/bi/dashboard/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败');
            });
        }

        // 显示导入模态框
        function showImportModal() {
            const modal = new bootstrap.Modal(document.getElementById('importTemplateModal'));
            modal.show();
        }

        // 导出大屏模板
        function exportDashboard(id) {
            // 从模态框中获取大屏名称，如果模态框不存在则尝试从按钮获取
            let dashboardName;
            const exportNameElement = document.getElementById('exportDashboardName');
            if (exportNameElement && exportNameElement.textContent !== '-') {
                dashboardName = exportNameElement.textContent;
            } else if (event && event.target) {
                const targetButton = event.target.closest('button');
                dashboardName = targetButton ? targetButton.getAttribute('data-name') : '未知大屏';
            } else {
                dashboardName = '未知大屏';
            }

            if (confirm(`确定要导出大屏"${dashboardName}"的模板吗？`)) {
                // 创建下载链接
                const downloadUrl = `/api/bi/dashboard/${id}/export`;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功提示
                alert('模板导出已开始，请稍候下载...');
            }
        }

        // 处理文件选择
        function handleFileSelect(input) {
            const file = input.files[0];
            if (!file) return;

            // 验证文件类型
            if (!file.name.toLowerCase().endsWith('.json')) {
                showImportError('请选择 JSON 格式的文件');
                return;
            }

            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) {
                showImportError('文件大小不能超过 10MB');
                return;
            }

            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('uploadArea').style.display = 'none';

            // 预览模板
            previewTemplate(file);
        }

        // 预览模板
        function previewTemplate(file) {
            const formData = new FormData();
            formData.append('file', file);

            fetch('/api/bi/dashboard/import/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示预览信息
                    document.getElementById('previewTemplateName').textContent = data.templateName || '未知';
                    document.getElementById('previewCanvasSize').textContent = data.canvasSize || '未知';
                    document.getElementById('previewWidgetCount').textContent = data.widgetCount || 0;
                    document.getElementById('previewExportTime').textContent =
                        data.exportTime ? new Date(data.exportTime).toLocaleString() : '未知';
                    document.getElementById('previewDescription').textContent = data.description || '无描述';

                    // 设置默认名称
                    document.getElementById('importDashboardName').value =
                        (data.templateName || '导入的大屏') + '_' + new Date().getTime();

                    document.getElementById('templatePreview').style.display = 'block';
                    document.getElementById('importError').style.display = 'none';
                    document.getElementById('importBtn').disabled = false;
                } else {
                    showImportError(data.message || '模板预览失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showImportError('模板预览失败');
            });
        }

        // 导入模板
        function importTemplate() {
            const fileInput = document.getElementById('templateFile');
            const file = fileInput.files[0];
            const dashboardName = document.getElementById('importDashboardName').value.trim();

            if (!file) {
                showImportError('请选择要导入的文件');
                return;
            }

            // 显示加载状态
            const importBtn = document.getElementById('importBtn');
            const originalText = importBtn.innerHTML;
            importBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导入中...';
            importBtn.disabled = true;

            const formData = new FormData();
            formData.append('file', file);
            if (dashboardName) {
                formData.append('name', dashboardName);
            }

            fetch('/api/bi/dashboard/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`导入成功！\n大屏名称: ${data.dashboardName}\n组件数量: ${data.widgetCount}`);
                    location.reload();
                } else {
                    showImportError(data.message || '导入失败');
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showImportError('导入失败');
                importBtn.innerHTML = originalText;
                importBtn.disabled = false;
            });
        }

        // 清除文件
        function clearFile() {
            document.getElementById('templateFile').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('templatePreview').style.display = 'none';
            document.getElementById('importError').style.display = 'none';
            document.getElementById('uploadArea').style.display = 'block';
            document.getElementById('importBtn').disabled = true;
        }

        // 显示导入错误
        function showImportError(message) {
            document.getElementById('importErrorMessage').textContent = message;
            document.getElementById('importError').style.display = 'block';
            document.getElementById('templatePreview').style.display = 'none';
            document.getElementById('importBtn').disabled = true;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 执行强制删除（包含发布记录）
        function performForceDelete(id) {
            fetch(`/api/bi/dashboard/${id}/force-delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功（包含发布记录）');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败');
            });
        }

        // 导出完整资源包
        function exportDashboardResources(id) {
            // 从模态框中获取大屏名称
            const dashboardName = document.getElementById('exportDashboardName').textContent;

            if (confirm(`确定要导出大屏"${dashboardName}"的完整资源包吗？\n\n资源包将包含：\n- 大屏模板配置\n- 所有引用的图片、视频、素材文件\n- HTML代码片段\n- 资源清单和使用说明`)) {
                // 创建下载链接
                const downloadUrl = `/api/bi/dashboard/${id}/export-resources`;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功提示
                alert('资源包导出已开始，请稍候下载...');
            }
        }

        // 显示导出模态框
        function showExportModal(id) {
            const targetElement = event.target.closest('button');
            const dashboardName = targetElement.getAttribute('data-name');

            // 显示加载状态
            const originalText = targetElement.innerHTML;
            targetElement.innerHTML = '<i class="bi bi-hourglass-split"></i> 加载中...';
            targetElement.style.pointerEvents = 'none';

            fetch(`/api/bi/dashboard/${id}/resource-preview`)
                .then(response => response.json())
                .then(data => {
                    if (data.success !== false) {
                        // 填充预览信息
                        document.getElementById('exportDashboardName').textContent = data.dashboardName || dashboardName;
                        document.getElementById('exportFileName').textContent = data.estimatedFileName || '未知';
                        document.getElementById('exportTotalSize').textContent = data.formattedSize || '计算中...';
                        document.getElementById('exportTime').textContent = new Date().toLocaleString();

                        // 填充资源统计
                        const analysis = data.resourceAnalysis || {};
                        document.getElementById('exportImageCount').textContent = (analysis.imageUrls || []).length;
                        document.getElementById('exportMaterialCount').textContent = (analysis.materialUrls || []).length;
                        document.getElementById('exportVideoCount').textContent = (analysis.videoUrls || []).length;
                        document.getElementById('exportHtmlCount').textContent = (analysis.htmlSnippets || []).length;

                        // 设置导出按钮事件
                        document.getElementById('exportTemplateBtn').onclick = function() {
                            bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
                            exportDashboard(id);
                        };

                        document.getElementById('exportResourcesBtn').onclick = function() {
                            bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
                            exportDashboardResources(id);
                        };

                        // 显示模态框
                        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
                        modal.show();
                    } else {
                        alert('获取预览信息失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取预览信息失败');
                })
                .finally(() => {
                    // 恢复按钮状态
                    targetElement.innerHTML = originalText;
                    targetElement.style.pointerEvents = 'auto';
                });
        }
    </script>
</body>
</html>
