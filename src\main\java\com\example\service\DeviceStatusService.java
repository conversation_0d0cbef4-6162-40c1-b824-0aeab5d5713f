package com.example.service;

import com.example.model.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class DeviceStatusService {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private DeviceService deviceService;
    
    @Scheduled(fixedRate = 3000)
    public void broadcastDeviceStatus() {
        try {
            List<Device> devices = deviceService.getAllDevices();
            messagingTemplate.convertAndSend("/topic/devices", devices);
        } catch (Exception e) {
            log.error("广播设备状态失败", e);
        }
    }
} 