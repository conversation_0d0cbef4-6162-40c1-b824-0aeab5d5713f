package com.example.controller;

import com.example.model.User;
import com.example.repository.UserRepository;
import com.example.service.PasswordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpSession;
import java.util.Optional;

@Controller
@RequestMapping("/auth")
public class AuthController {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordService passwordService;
    
    @GetMapping("/login")
    public String loginPage() {
        return "login";
    }
    
    @PostMapping("/login")
    public String login(@RequestParam String username, 
                       @RequestParam String password,
                       HttpSession session,
                       Model model) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        
        if (userOpt.isPresent() && passwordService.verifyPassword(password, userOpt.get().getPassword())) {
            User user = userOpt.get();
            
            // 如果密码是明文存储的，则在登录时进行加密
            if (passwordService.needsEncryption(user.getPassword())) {
                user.setPassword(passwordService.encryptPassword(password));
                userRepository.save(user);
            }
            
            session.setAttribute("user", user);
            return "redirect:/";
        }
        
        model.addAttribute("error", "用户名或密码错误");
        return "login";
    }
    
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/auth/login";
    }
    
    // 显示修改密码表单
    @GetMapping("/change-password")
    public String showChangePasswordForm(HttpSession session, Model model) {
        // 检查用户是否已登录
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return "redirect:/auth/login";
        }
        
        return "change-password";
    }
    
    // 处理修改密码请求
    @PostMapping("/change-password")
    public String changePassword(
            @RequestParam String currentPassword,
            @RequestParam String newPassword,
            @RequestParam String confirmPassword,
            HttpSession session,
            Model model) {
        
        // 检查用户是否已登录
        User user = (User) session.getAttribute("user");
        if (user == null) {
            return "redirect:/auth/login";
        }
        
        // 验证当前密码
        if (!passwordService.verifyPassword(currentPassword, user.getPassword())) {
            model.addAttribute("error", "当前密码不正确");
            return "change-password";
        }
        
        // 验证新密码和确认密码是否一致
        if (!newPassword.equals(confirmPassword)) {
            model.addAttribute("error", "两次输入的新密码不一致");
            return "change-password";
        }
        
        // 加密并更新密码
        user.setPassword(passwordService.encryptPassword(newPassword));
        userRepository.save(user);
        
        // 更新会话中的用户信息
        session.setAttribute("user", user);
        
        model.addAttribute("success", "密码修改成功");
        return "change-password";
    }
} 