<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - 登录</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.min.css}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --border-color: rgba(255, 255, 255, 0.1);
            --box-shadow-color: rgba(0, 0, 0, 0.2);
        }

        body {
            min-height: 100vh;
            background: url([[@{/system-images/tech-bg.jpg}]]) no-repeat center center fixed;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            padding: 2rem;
            background: rgba(17, 25, 40, 0.75);
            backdrop-filter: blur(12px);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px 0 var(--box-shadow-color);
        }

        .system-title {
            color: #ffffff;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            text-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
            position: relative;
        }

        .system-title::after {
            content: '';
            display: block;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            margin: 0.5rem auto;
        }

        .form-label {
            color: #ffffff;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .input-group {
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .input-group:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .input-group-text {
            background-color: rgba(255, 255, 255, 0.05);
            border: none;
            color: #ffffff;
        }

        .form-control {
            border: none;
            background-color: rgba(255, 255, 255, 0.05);
            color: #ffffff;
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            box-shadow: none;
            color: #ffffff;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .btn-login {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(45deg, #0d6efd, #0099ff);
            border: none;
            border-radius: 8px;
            color: #ffffff;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .btn-login:hover {
            background: linear-gradient(45deg, #0099ff, #0d6efd);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(4px);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-container {
            animation: fadeIn 0.5s ease-out;
        }

        /* 响应式调整 */
        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="system-title">胜大科技智联管理系统</h2>
        <div class="text-center mb-3">
            <small class="text-muted">版本 8.2</small>
        </div>
        <div th:if="${error}" class="error-message" th:text="${error}"></div>
        <form method="post" th:action="@{/auth/login}">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="bi bi-person"></i>
                </span>
                <input type="text" class="form-control" id="username" name="username" required 
                       placeholder="请输入用户名" autocomplete="off">
            </div>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="bi bi-lock"></i>
                </span>
                <input type="password" class="form-control" id="password" name="password" required
                       placeholder="请输入密码">
            </div>
            <button type="submit" class="btn btn-login">
                <i class="bi bi-box-arrow-in-right me-2"></i>登录系统
            </button>
        </form>
    </div>
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
</body>
</html> 