package com.example.entity;

import lombok.Data;
import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "published_topology")
public class PublishedTopology {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "data", columnDefinition = "TEXT")
    private String data;
    
    @Column(name = "access_token", nullable = false, unique = true)
    private String accessToken;
    
    @Column(name = "expiry_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiryDate;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "published_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date publishedAt;
    
    @Column(name = "status", nullable = false)
    private String status; // ACTIVE, EXPIRED, REVOKED
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "topology_id", nullable = false)
    private Topology topology;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        publishedAt = new Date();
        if (status == null) {
            status = "ACTIVE";
        }
    }
    
    // 检查发布是否有效
    @Transient
    public boolean isValid() {
        if (!"ACTIVE".equals(status)) {
            return false;
        }
        
        if (expiryDate == null) {
            return true; // 永久有效
        }
        
        return expiryDate.after(new Date());
    }
} 