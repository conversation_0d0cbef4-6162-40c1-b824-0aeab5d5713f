import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Debug tool for license system
 */
public class DebugLicense {
    
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String DATE_FORMAT = "yyyyMMdd";
    

    
    public static void main(String[] args) {
        String testUserId = "USER001";
        String testStartDate = "20250726";
        String testEndDate = "20250727";
        String testSerial = "SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP";
        
        System.out.println("=== License Debug Tool ===");
        System.out.println();
        
        // Test 1: Generate a new serial number
        System.out.println("1. Generating new serial number:");
        try {
            String newSerial = generateSerialNumber(testUserId, testStartDate, testEndDate);
            System.out.println("Generated: " + newSerial);
            System.out.println();
        } catch (Exception e) {
            System.out.println("Generation failed: " + e.getMessage());
            e.printStackTrace();
            System.out.println();
        }
        
        // Test 2: Parse the test serial number
        System.out.println("2. Parsing test serial number:");
        System.out.println("Serial: " + testSerial);
        try {
            Map<String, String> data = parseSerialNumber(testSerial);
            System.out.println("Parsed data:");
            for (Map.Entry<String, String> entry : data.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
            System.out.println();
        } catch (Exception e) {
            System.out.println("Parsing failed: " + e.getMessage());
            e.printStackTrace();
            System.out.println();
        }
        
        // Test 3: Validate the serial number
        System.out.println("3. Validating serial number:");
        System.out.println("Input User ID: " + testUserId);
        System.out.println("Serial Number: " + testSerial);
        try {
            boolean isValid = validateSerialNumber(testUserId, testSerial);
            System.out.println("Validation result: " + (isValid ? "VALID" : "INVALID"));
            
            // Detailed validation
            Map<String, String> data = parseSerialNumber(testSerial);
            String serialUserId = data.get("userId");
            String startDate = data.get("startDate");
            String endDate = data.get("endDate");
            String checksum = data.get("checksum");
            
            System.out.println("Detailed validation:");
            System.out.println("  Serial User ID: " + serialUserId);
            System.out.println("  Input User ID: " + testUserId);
            System.out.println("  User ID Match: " + testUserId.equals(serialUserId));
            
            LocalDate today = LocalDate.now();
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            
            System.out.println("  Today: " + today);
            System.out.println("  Start Date: " + start);
            System.out.println("  End Date: " + end);
            System.out.println("  Date Valid: " + (!today.isBefore(start) && !today.isAfter(end)));
            
            String expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
            System.out.println("  Expected Checksum: " + expectedChecksum);
            System.out.println("  Actual Checksum: " + checksum);
            System.out.println("  Checksum Match: " + checksum.equals(expectedChecksum));
            
        } catch (Exception e) {
            System.out.println("Validation failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String generateSerialNumber(String userId, String startDate, String endDate) {
        try {
            String checksum = generateChecksum(userId + startDate + endDate);
            
            Map<String, String> data = new HashMap<>();
            data.put("userId", userId);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("checksum", checksum);
            
            String json = String.format(
                "{\"userId\":\"%s\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"checksum\":\"%s\"}",
                userId, startDate, endDate, checksum
            );
            byte[] encrypted = aesEncrypt(json);
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate serial number", e);
        }
    }
    
    private static Map<String, String> parseSerialNumber(String serialNumber) {
        try {
            if (!serialNumber.startsWith(SERIAL_PREFIX)) {
                throw new IllegalArgumentException("Invalid serial number format");
            }

            String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
            byte[] encrypted = Base64.getDecoder().decode(base64Data);
            String json = aesDecrypt(encrypted);

            // Parse JSON manually
            Map<String, String> result = new HashMap<>();
            result.put("userId", extractJsonValue(json, "userId"));
            result.put("startDate", extractJsonValue(json, "startDate"));
            result.put("endDate", extractJsonValue(json, "endDate"));
            result.put("checksum", extractJsonValue(json, "checksum"));
            return result;

        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid serial number", e);
        }
    }

    private static String extractJsonValue(String json, String key) {
        String pattern = "\"" + key + "\":\"";
        int start = json.indexOf(pattern);
        if (start == -1) return null;
        start += pattern.length();
        int end = json.indexOf("\"", start);
        return json.substring(start, end);
    }
    
    private static boolean validateSerialNumber(String inputUserId, String serialNumber) {
        try {
            Map<String, String> data = parseSerialNumber(serialNumber);
            
            String serialUserId = data.get("userId");
            String startDate = data.get("startDate");
            String endDate = data.get("endDate");
            String checksum = data.get("checksum");
            
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }
            
            LocalDate today = LocalDate.now();
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            
            if (today.isBefore(start) || today.isAfter(end)) {
                return false;
            }
            
            String expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
            return checksum.equals(expectedChecksum);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    private static String generateChecksum(String input) {
        return md5(input).substring(0, 6).toUpperCase();
    }
    
    private static byte[] aesEncrypt(String plainText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
    }
    
    private static String aesDecrypt(byte[] encrypted) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 calculation failed", e);
        }
    }
}
