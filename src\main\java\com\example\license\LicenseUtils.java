package com.example.license;

import com.fasterxml.jackson.databind.ObjectMapper;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 许可证工具类
 * 提供加密解密、硬件ID生成、序列号处理等功能
 */
public class LicenseUtils {
    
    // 常量定义
    public static final String SERIAL_PREFIX = "SDPLC-";
    public static final String AES_KEY = "SDPLC2024LICENSE";
    public static final String LICENSE_DIR = "C:\\ProgramData\\SHENGDA-PLC";
    public static final String LICENSE_FILE = LICENSE_DIR + "\\license.json";
    public static final String DATE_FORMAT = "yyyyMMdd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 确保许可证目录存在
     * @return 目录是否创建成功
     */
    public static boolean ensureLicenseDirectoryExists() {
        try {
            Path licensePath = Paths.get(LICENSE_DIR);
            if (!Files.exists(licensePath)) {
                Files.createDirectories(licensePath);
                System.out.println("创建许可证目录: " + LICENSE_DIR);
            }
            return true;
        } catch (Exception e) {
            System.err.println("创建许可证目录失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 生成序列号
     * @param userId 用户ID
     * @param startDate 激活开始日期 (yyyyMMdd)
     * @param endDate 激活结束日期 (yyyyMMdd)
     * @return 加密的序列号
     */
    public static String generateSerialNumber(String userId, String startDate, String endDate) {
        try {
            // 生成校验码
            String checksum = generateChecksum(userId + startDate + endDate);

            // 构造原始数据
            Map<String, String> data = new HashMap<>();
            data.put("userId", userId);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("checksum", checksum);

            // 转换为JSON
            String json = objectMapper.writeValueAsString(data);

            // AES加密
            byte[] encrypted = aesEncrypt(json);

            // Base64编码并添加前缀
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败", e);
        }
    }

    /**
     * 生成序列号（扩展版本，包含许可使用期限）
     * @param userId 用户ID
     * @param startDate 激活开始日期 (yyyyMMdd)
     * @param endDate 激活结束日期 (yyyyMMdd)
     * @param licenseStartDate 许可使用开始日期 (yyyyMMdd)
     * @param licenseEndDate 许可使用结束日期 (yyyyMMdd)
     * @return 加密的序列号
     */
    public static String generateSerialNumber(String userId, String startDate, String endDate,
                                            String licenseStartDate, String licenseEndDate) {
        try {
            // 生成校验码（包含所有日期信息）
            String checksum = generateChecksum(userId + startDate + endDate + licenseStartDate + licenseEndDate);

            // 构造原始数据
            Map<String, String> data = new HashMap<>();
            data.put("userId", userId);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("licenseStartDate", licenseStartDate);
            data.put("licenseEndDate", licenseEndDate);
            data.put("checksum", checksum);

            // 转换为JSON
            String json = objectMapper.writeValueAsString(data);

            // AES加密
            byte[] encrypted = aesEncrypt(json);

            // Base64编码并添加前缀
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败", e);
        }
    }
    
    /**
     * 解析序列号
     * @param serialNumber 序列号
     * @return 解析后的数据Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, String> parseSerialNumber(String serialNumber) {
        try {
            // 检查前缀
            if (!serialNumber.startsWith(SERIAL_PREFIX)) {
                throw new IllegalArgumentException("序列号格式不正确");
            }
            
            // 去除前缀并解码
            String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
            byte[] encrypted = Base64.getDecoder().decode(base64Data);
            
            // AES解密
            String json = aesDecrypt(encrypted);
            
            // 解析JSON
            return objectMapper.readValue(json, Map.class);
            
        } catch (Exception e) {
            throw new IllegalArgumentException("序列号无效", e);
        }
    }
    
    /**
     * 验证序列号（用于激活时验证）
     * @param inputUserId 输入的用户ID
     * @param serialNumber 序列号
     * @return 验证结果
     */
    public static boolean validateSerialNumberForActivation(String inputUserId, String serialNumber) {
        try {
            // 解析序列号
            Map<String, String> data = parseSerialNumber(serialNumber);

            String serialUserId = data.get("userId");
            String startDate = data.get("startDate");
            String endDate = data.get("endDate");
            String checksum = data.get("checksum");

            // 验证用户ID
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }

            // 验证激活时间窗口
            LocalDate today = LocalDate.now();
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));

            if (today.isBefore(start) || today.isAfter(end)) {
                return false;
            }

            // 验证校验码
            String expectedChecksum;
            if (data.containsKey("licenseStartDate") && data.containsKey("licenseEndDate")) {
                // 新格式序列号
                String licenseStartDate = data.get("licenseStartDate");
                String licenseEndDate = data.get("licenseEndDate");
                expectedChecksum = generateChecksum(serialUserId + startDate + endDate + licenseStartDate + licenseEndDate);
            } else {
                // 旧格式序列号
                expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
            }
            return checksum.equals(expectedChecksum);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证序列号（兼容旧版本，用于许可使用验证）
     * @param inputUserId 输入的用户ID
     * @param serialNumber 序列号
     * @return 验证结果
     */
    public static boolean validateSerialNumber(String inputUserId, String serialNumber) {
        try {
            // 解析序列号
            Map<String, String> data = parseSerialNumber(serialNumber);

            String serialUserId = data.get("userId");
            String checksum = data.get("checksum");

            // 验证用户ID
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }

            // 检查是否为新格式序列号（包含许可使用期限）
            if (data.containsKey("licenseStartDate") && data.containsKey("licenseEndDate")) {
                // 新格式：验证许可使用期限
                String licenseStartDate = data.get("licenseStartDate");
                String licenseEndDate = data.get("licenseEndDate");

                LocalDate today = LocalDate.now();
                LocalDate licenseStart = LocalDate.parse(licenseStartDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
                LocalDate licenseEnd = LocalDate.parse(licenseEndDate, DateTimeFormatter.ofPattern(DATE_FORMAT));

                if (today.isBefore(licenseStart) || today.isAfter(licenseEnd)) {
                    return false;
                }

                // 验证校验码
                String startDate = data.get("startDate");
                String endDate = data.get("endDate");
                String expectedChecksum = generateChecksum(serialUserId + startDate + endDate + licenseStartDate + licenseEndDate);
                return checksum.equals(expectedChecksum);
            } else {
                // 旧格式：验证激活时间（保持向后兼容）
                String startDate = data.get("startDate");
                String endDate = data.get("endDate");

                LocalDate today = LocalDate.now();
                LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
                LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));

                if (today.isBefore(start) || today.isAfter(end)) {
                    return false;
                }

                // 验证校验码
                String expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
                return checksum.equals(expectedChecksum);
            }

        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 生成硬件ID
     * @return 硬件ID
     */
    public static String generateHardwareId() {
        try {
            String computerName = System.getProperty("user.name");
            String userName = System.getProperty("user.name");
            String input = computerName + userName;
            return md5(input).substring(0, 16).toUpperCase();
        } catch (Exception e) {
            // 备用方案
            return md5("DEFAULT_HARDWARE_ID").substring(0, 16).toUpperCase();
        }
    }
    
    /**
     * 生成校验码
     * @param input 输入字符串
     * @return 校验码
     */
    public static String generateChecksum(String input) {
        return md5(input).substring(0, 6).toUpperCase();
    }

    /**
     * 加密许可证文件内容
     * @param licenseJson 许可证JSON字符串
     * @return 加密后的Base64字符串
     */
    public static String encryptLicenseFile(String licenseJson) {
        try {
            byte[] encrypted = aesEncrypt(licenseJson);
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("许可证文件加密失败", e);
        }
    }

    /**
     * 解密许可证文件内容
     * @param encryptedData 加密的Base64字符串
     * @return 解密后的JSON字符串
     */
    public static String decryptLicenseFile(String encryptedData) {
        try {
            byte[] encrypted = Base64.getDecoder().decode(encryptedData);
            return aesDecrypt(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("许可证文件解密失败", e);
        }
    }

    /**
     * AES加密
     * @param plainText 明文
     * @return 加密后的字节数组
     */
    private static byte[] aesEncrypt(String plainText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * AES解密
     * @param encrypted 加密的字节数组
     * @return 解密后的明文
     */
    private static String aesDecrypt(byte[] encrypted) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    /**
     * MD5哈希
     * @param input 输入字符串
     * @return MD5哈希值
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5计算失败", e);
        }
    }
}
