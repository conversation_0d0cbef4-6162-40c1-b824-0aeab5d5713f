<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期格式化选项排序验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .format-option {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .format-option.correct {
            border-left: 4px solid #28a745;
            background: #f8fff9;
        }
        
        .format-option.incorrect {
            border-left: 4px solid #dc3545;
            background: #fff8f8;
        }
        
        .sql-demo {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        
        .data-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .status-correct {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-fixed {
            background-color: #cce5ff;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-calendar-date"></i>
                日期格式化选项排序验证
            </h2>
            
            <!-- 问题说明 -->
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> 问题描述</h6>
                <p class="mb-0">检查日期格式化中的所有选项，发现有些选项不影响输出记录是最新的，但有些选项在选中后，会导致输出的记录是旧的。</p>
            </div>
            
            <!-- 各选项分析 -->
            <div class="test-section">
                <h5><i class="bi bi-list-ul"></i> 各选项排序分析</h5>
                
                <!-- 选项1：年月日时分秒 -->
                <div class="format-option correct">
                    <h6><i class="bi bi-check-circle text-success"></i> %Y-%m-%d %H:%i:%s (年月日时分秒)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">2025-07-22 17:35:55
2024-12-25 14:30:20
2024-07-23 16:45:10</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">2025-07-22 17:35:55  ← 最新
2024-12-25 14:30:20
2024-07-23 16:45:10  ← 最旧</div>
                            <span class="status-badge status-correct">✅ 排序正确</span>
                        </div>
                    </div>
                    <p class="small text-success mb-0">年份在前，字符串排序与时间排序一致</p>
                </div>
                
                <!-- 选项2：月日时分秒 -->
                <div class="format-option incorrect">
                    <h6><i class="bi bi-x-circle text-danger"></i> %m-%d %H:%i:%s (月日时分秒)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">07-22 17:35:55
12-25 14:30:20
07-23 16:45:10</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">12-25 14:30:20  ← 字符串最大
07-23 16:45:10
07-22 17:35:55  ← 实际最新</div>
                            <span class="status-badge status-incorrect">❌ 排序错误</span>
                        </div>
                    </div>
                    <p class="small text-danger mb-0">月份在前，跨年数据排序错误（12月比07月大）</p>
                </div>
                
                <!-- 选项3：月日时分 -->
                <div class="format-option incorrect">
                    <h6><i class="bi bi-x-circle text-danger"></i> %m-%d %H:%i (月日时分)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">07-22 17:35
12-25 14:30
07-23 16:45</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">12-25 14:30  ← 字符串最大
07-23 16:45
07-22 17:35  ← 实际最新</div>
                            <span class="status-badge status-incorrect">❌ 排序错误</span>
                        </div>
                    </div>
                    <p class="small text-danger mb-0">同样的月份优先问题</p>
                </div>
                
                <!-- 选项4：月日 -->
                <div class="format-option incorrect">
                    <h6><i class="bi bi-x-circle text-danger"></i> %m-%d (月日)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">07-22
12-25
07-23</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">12-25  ← 字符串最大
07-23
07-22  ← 实际最新</div>
                            <span class="status-badge status-incorrect">❌ 排序错误</span>
                        </div>
                    </div>
                    <p class="small text-danger mb-0">缺少年份信息，月份排序错误</p>
                </div>
                
                <!-- 选项5：时分秒 -->
                <div class="format-option incorrect">
                    <h6><i class="bi bi-x-circle text-danger"></i> %H:%i:%s (时分秒)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">17:35:55
14:30:20
16:45:10</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">17:35:55  ← 时间最晚
16:45:10
14:30:20  ← 时间最早</div>
                            <span class="status-badge status-incorrect">❌ 排序错误</span>
                        </div>
                    </div>
                    <p class="small text-danger mb-0">只有时间信息，无法区分不同日期</p>
                </div>
                
                <!-- 选项6：时分 -->
                <div class="format-option incorrect">
                    <h6><i class="bi bi-x-circle text-danger"></i> %H:%i (时分)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>格式化结果：</strong>
                            <div class="data-example">17:35
14:30
16:45</div>
                        </div>
                        <div class="col-md-6">
                            <strong>字符串排序结果：</strong>
                            <div class="data-example">17:35  ← 时间最晚
16:45
14:30  ← 时间最早</div>
                            <span class="status-badge status-incorrect">❌ 排序错误</span>
                        </div>
                    </div>
                    <p class="small text-danger mb-0">同样只有时间信息，排序无意义</p>
                </div>
            </div>
            
            <!-- 修复方案 -->
            <div class="test-section">
                <h5><i class="bi bi-tools"></i> 修复方案</h5>
                
                <div class="format-option">
                    <h6>强制原始字段排序</h6>
                    <p>无论选择哪种日期格式化选项，ORDER BY都强制使用原始时间字段进行排序：</p>
                    
                    <div class="sql-demo">-- 修复前（错误）：
SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
ORDER BY timestamp DESC  -- 引用格式化后的别名，按字符串排序
LIMIT 5

-- 修复后（正确）：
SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
ORDER BY timestamp DESC  -- 强制使用原始timestamp字段排序
LIMIT 5</div>
                </div>
                
                <div class="format-option">
                    <h6>新增方法：getOrderByFieldForTimeSort</h6>
                    <div class="sql-demo">getOrderByFieldForTimeSort(timeField) {
    // 检查是否配置了日期格式化
    const dateField = document.getElementById('dateField')?.value;
    const dateFormat = document.getElementById('dateFormat')?.value;
    
    // 如果配置了日期格式化，强制使用原始字段排序
    if (dateField && dateFormat && timeField === dateField) {
        console.log(`检测到日期格式化配置，强制使用原始字段 ${timeField} 进行排序`);
        return timeField; // 强制返回原始字段名
    }
    
    return timeField;
}</div>
                </div>
            </div>
            
            <!-- 修复效果 -->
            <div class="test-section">
                <h5><i class="bi bi-check-circle"></i> 修复效果</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="format-option">
                            <h6>修复前</h6>
                            <ul class="small">
                                <li>✅ %Y-%m-%d %H:%i:%s - 排序正确</li>
                                <li>❌ %m-%d %H:%i:%s - 排序错误</li>
                                <li>❌ %m-%d %H:%i - 排序错误</li>
                                <li>❌ %m-%d - 排序错误</li>
                                <li>❌ %H:%i:%s - 排序错误</li>
                                <li>❌ %H:%i - 排序错误</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="format-option">
                            <h6>修复后</h6>
                            <ul class="small">
                                <li>✅ %Y-%m-%d %H:%i:%s - 排序正确</li>
                                <li>✅ %m-%d %H:%i:%s - 排序正确 <span class="status-badge status-fixed">已修复</span></li>
                                <li>✅ %m-%d %H:%i - 排序正确 <span class="status-badge status-fixed">已修复</span></li>
                                <li>✅ %m-%d - 排序正确 <span class="status-badge status-fixed">已修复</span></li>
                                <li>✅ %H:%i:%s - 排序正确 <span class="status-badge status-fixed">已修复</span></li>
                                <li>✅ %H:%i - 排序正确 <span class="status-badge status-fixed">已修复</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="bi bi-check-circle"></i> 修复总结</h6>
                    <p class="mb-0">现在所有日期格式化选项都能正确排序，用户可以选择任何喜欢的显示格式，同时确保获取最新的数据记录。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
