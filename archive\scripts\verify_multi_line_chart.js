/**
 * 多折线图组件验证脚本
 * 用于验证多折线图组件的各项功能是否正常工作
 */

// 验证函数是否存在
function verifyFunctionExists() {
    console.log('=== 验证函数存在性 ===');
    
    const functions = [
        'createEChartsMultiLineChart',
        'formatMultiLineData', 
        'setupMultiLineAxes',
        'setupMultiLineSeries'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✓ ${funcName} 函数存在`);
        } else {
            console.error(`✗ ${funcName} 函数不存在`);
        }
    });
}

// 验证配置对象
function verifyConfigs() {
    console.log('=== 验证配置对象 ===');
    
    // 验证组件配置
    if (typeof widgetConfigs !== 'undefined' && widgetConfigs['multi-line-chart']) {
        console.log('✓ multi-line-chart 配置存在');
        console.log('配置详情:', widgetConfigs['multi-line-chart']);
    } else {
        console.error('✗ multi-line-chart 配置不存在');
    }
    
    // 验证数据源管理器配置
    if (typeof window.biDataSourceManager !== 'undefined') {
        const supportedTypes = window.biDataSourceManager.getSupportedDataSources('multi-line-chart');
        if (supportedTypes && supportedTypes.length > 0) {
            console.log('✓ 数据源管理器支持 multi-line-chart');
            console.log('支持的数据源类型:', supportedTypes);
        } else {
            console.error('✗ 数据源管理器不支持 multi-line-chart');
        }
    } else {
        console.error('✗ 数据源管理器未初始化');
    }
}

// 验证数据格式化
function verifyDataFormatting() {
    console.log('=== 验证数据格式化 ===');
    
    const testData = {
        xAxis: ['07-25', '07-26', '07-27'],
        series: [
            {
                name: '测试系列1',
                type: 'line',
                data: [10, 20, 15],
                yAxisIndex: 0
            },
            {
                name: '测试系列2', 
                type: 'line',
                data: [5, 15, 25],
                yAxisIndex: 1
            }
        ]
    };
    
    try {
        if (typeof formatMultiLineData === 'function') {
            const formatted = formatMultiLineData(testData, {});
            console.log('✓ 数据格式化成功');
            console.log('格式化结果:', formatted);
        } else {
            console.error('✗ formatMultiLineData 函数不存在');
        }
    } catch (error) {
        console.error('✗ 数据格式化失败:', error);
    }
}

// 验证图表创建
function verifyChartCreation() {
    console.log('=== 验证图表创建 ===');
    
    // 创建测试容器
    const testContainer = document.createElement('div');
    testContainer.id = 'test-multi-line-chart';
    testContainer.style.width = '400px';
    testContainer.style.height = '300px';
    document.body.appendChild(testContainer);
    
    const testData = {
        xAxis: ['测试1', '测试2', '测试3'],
        series: [
            {
                name: '系列A',
                type: 'line',
                data: [10, 20, 15],
                yAxisIndex: 0
            }
        ]
    };
    
    const testConfig = {
        enableDualYAxis: false,
        smoothCurve: true,
        showSymbol: true,
        customColor: [{color: '#36c5e7'}]
    };
    
    try {
        if (typeof createEChartsMultiLineChart === 'function') {
            const chart = createEChartsMultiLineChart('test-multi-line-chart', testData, testConfig);
            if (chart) {
                console.log('✓ 图表创建成功');
                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                }, 1000);
            } else {
                console.error('✗ 图表创建返回null');
            }
        } else {
            console.error('✗ createEChartsMultiLineChart 函数不存在');
        }
    } catch (error) {
        console.error('✗ 图表创建失败:', error);
        // 清理测试容器
        if (document.body.contains(testContainer)) {
            document.body.removeChild(testContainer);
        }
    }
}

// 验证默认数据
function verifyDefaultData() {
    console.log('=== 验证默认数据 ===');
    
    try {
        if (typeof getDefaultChartData === 'function') {
            const defaultData = getDefaultChartData('multi-line-chart');
            console.log('✓ 默认数据获取成功');
            console.log('默认数据:', defaultData);
        } else {
            console.error('✗ getDefaultChartData 函数不存在');
        }
        
        if (typeof getEmptyChartData === 'function') {
            const emptyData = getEmptyChartData('multi-line-chart');
            console.log('✓ 空数据获取成功');
            console.log('空数据:', emptyData);
        } else {
            console.error('✗ getEmptyChartData 函数不存在');
        }
    } catch (error) {
        console.error('✗ 数据获取失败:', error);
    }
}

// 运行所有验证
function runAllVerifications() {
    console.log('开始验证多折线图组件...');
    
    verifyFunctionExists();
    verifyConfigs();
    verifyDataFormatting();
    verifyDefaultData();
    verifyChartCreation();
    
    console.log('验证完成！');
}

// 导出验证函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        verifyFunctionExists,
        verifyConfigs,
        verifyDataFormatting,
        verifyChartCreation,
        verifyDefaultData,
        runAllVerifications
    };
}

// 如果在浏览器环境中，自动运行验证
if (typeof window !== 'undefined') {
    // 等待页面加载完成后运行验证
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllVerifications);
    } else {
        runAllVerifications();
    }
}
