package com.example.service;

import com.example.model.DataHourlyStats;
import com.example.model.DataItem;
import com.example.repository.DataHourlyStatsRepository;
import com.example.repository.DataHistoryRepository;
import com.example.repository.DataItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataAnalysisService {
    private final DataItemRepository dataItemRepository;
    private final DataHistoryRepository dataHistoryRepository;
    private final DataHourlyStatsRepository dataHourlyStatsRepository;
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");
    
    @Scheduled(cron = "0 0 * * * *") // 每小时的0分0秒执行
    @Transactional
    public void calculateHourlyStats() {
        // 计算上一小时的统计数据
        LocalDateTime now = LocalDateTime.now(ZONE_ID);
        LocalDateTime currentHour = now.truncatedTo(ChronoUnit.HOURS);
        LocalDateTime previousHour = currentHour.minusHours(1);
        LocalDateTime previousHourEnd = currentHour.minusSeconds(1);
        
        log.info("开始计算小时统计数据，时间范围: {} 到 {}", previousHour, previousHourEnd);
        
        List<DataItem> enabledItems = dataItemRepository.findByStatsEnabledTrue();
        log.info("找到 {} 个启用统计功能的监控项", enabledItems.size());
        
        for (DataItem item : enabledItems) {
            try {
                calculateItemHourlyStats(item, previousHour, previousHourEnd);
            } catch (Exception e) {
                log.error("计算监控项 {} 的小时统计数据失败: {}", item.getName(), e.getMessage());
            }
        }
        
        log.info("小时统计数据计算完成");
    }
    
    private void calculateItemHourlyStats(DataItem item, LocalDateTime startTime, LocalDateTime endTime) {
        // 检查是否已存在该小时的统计数据
        Optional<DataHourlyStats> existingStats = dataHourlyStatsRepository
            .findByDataItemAndHourTimestamp(item, startTime);
        
        if (existingStats.isPresent()) {
            log.debug("监控项 {} 在 {} 的统计数据已存在，跳过", item.getName(), startTime);
            return;
        }
        
        // 查询该小时的历史数据统计
        Optional<Integer> maxValue = dataHistoryRepository
            .findMaxValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Optional<Integer> minValue = dataHistoryRepository
            .findMinValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Optional<Double> avgValue = dataHistoryRepository
            .findAvgValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Long sampleCount = dataHistoryRepository
            .countByDataItemAndTimestampBetween(item, startTime, endTime);
        
        // 如果没有数据，跳过
        if (!maxValue.isPresent() || sampleCount == 0) {
            log.debug("监控项 {} 在 {} 没有历史数据，跳过统计", item.getName(), startTime);
            return;
        }
        
        // 计算与前一小时的差值
        Integer diffValue = null;
        LocalDateTime previousHourTime = startTime.minusHours(1);
        Optional<DataHourlyStats> previousStats = dataHourlyStatsRepository
            .findPreviousHourStats(item, previousHourTime);
        
        if (previousStats.isPresent()) {
            diffValue = maxValue.get() - previousStats.get().getMaxValue();
        }
        
        // 创建统计记录
        DataHourlyStats stats = new DataHourlyStats();
        stats.setDataItem(item);
        stats.setDataItemName(item.getName());
        stats.setDataItemAddress(item.getAddress());
        stats.setDeviceId(item.getDevice().getId());
        stats.setDeviceName(item.getDevice().getName());
        stats.setHourTimestamp(startTime);
        stats.setMaxValue(maxValue.get());
        stats.setMinValue(minValue.get());
        stats.setAvgValue(BigDecimal.valueOf(avgValue.get()).setScale(2, RoundingMode.HALF_UP));
        stats.setDiffValue(diffValue);
        stats.setSampleCount(sampleCount.intValue());
        stats.setCreatedAt(LocalDateTime.now(ZONE_ID));
        
        dataHourlyStatsRepository.save(stats);
        
        log.debug("已保存监控项 {} 在 {} 的统计数据: 最大值={}, 最小值={}, 平均值={}, 差值={}, 采样数={}", 
            item.getName(), startTime, maxValue.get(), minValue.get(), avgValue.get(), diffValue, sampleCount);
    }
    
    public List<DataHourlyStats> getHourlyStats(String dataItemId, int hours) {
        Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
        if (!dataItemOpt.isPresent()) {
            throw new IllegalArgumentException("监控项不存在");
        }
        
        DataItem dataItem = dataItemOpt.get();
        if (!dataItem.getStatsEnabled()) {
            throw new IllegalArgumentException("该监控项未启用统计功能");
        }
        
        LocalDateTime endTime = LocalDateTime.now(ZONE_ID).truncatedTo(ChronoUnit.HOURS);
        LocalDateTime startTime = endTime.minusHours(hours);
        
        return dataHourlyStatsRepository.findByDataItemAndHourTimestampBetweenOrderByHourTimestampDesc(
            dataItem, startTime, endTime);
    }

    /**
     * 按时间范围重新计算统计数据
     * @param dataItemId 监控项ID
     * @param hours 时间范围（小时数）
     */
    @Transactional
    public void recalculateStatsByTimeRange(String dataItemId, int hours) {
        Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
        if (!dataItemOpt.isPresent()) {
            throw new IllegalArgumentException("监控项不存在");
        }

        DataItem dataItem = dataItemOpt.get();
        if (!dataItem.getStatsEnabled()) {
            throw new IllegalArgumentException("该监控项未启用统计功能");
        }

        LocalDateTime now = LocalDateTime.now(ZONE_ID);
        LocalDateTime currentHour = now.truncatedTo(ChronoUnit.HOURS);
        LocalDateTime startTime = currentHour.minusHours(hours);

        log.info("开始重新计算监控项 {} 的统计数据，时间范围: {} 到 {}",
                dataItem.getName(), startTime, currentHour);

        // 按小时遍历时间范围
        LocalDateTime hourTime = startTime;
        int calculatedCount = 0;
        int skippedCount = 0;

        while (hourTime.isBefore(currentHour)) {
            LocalDateTime hourEnd = hourTime.plusHours(1).minusSeconds(1);

            try {
                // 检查该小时是否有历史数据
                Long sampleCount = dataHistoryRepository
                    .countByDataItemAndTimestampBetween(dataItem, hourTime, hourEnd);

                if (sampleCount > 0) {
                    // 重新计算该小时的统计数据（覆盖现有数据）
                    recalculateItemHourlyStats(dataItem, hourTime, hourEnd);
                    calculatedCount++;
                } else {
                    log.debug("监控项 {} 在 {} 没有历史数据，跳过", dataItem.getName(), hourTime);
                    skippedCount++;
                }
            } catch (Exception e) {
                log.error("重新计算监控项 {} 在 {} 的统计数据失败: {}",
                        dataItem.getName(), hourTime, e.getMessage());
            }

            hourTime = hourTime.plusHours(1);
        }

        log.info("监控项 {} 统计数据重新计算完成，计算了 {} 个小时，跳过 {} 个小时",
                dataItem.getName(), calculatedCount, skippedCount);
    }

    /**
     * 重新计算单个小时的统计数据（支持覆盖）
     */
    private void recalculateItemHourlyStats(DataItem item, LocalDateTime startTime, LocalDateTime endTime) {
        // 查询该小时的历史数据统计
        Optional<Integer> maxValue = dataHistoryRepository
            .findMaxValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Optional<Integer> minValue = dataHistoryRepository
            .findMinValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Optional<Double> avgValue = dataHistoryRepository
            .findAvgValueByDataItemAndTimestampBetween(item, startTime, endTime);
        Long sampleCount = dataHistoryRepository
            .countByDataItemAndTimestampBetween(item, startTime, endTime);

        // 如果没有数据，跳过
        if (!maxValue.isPresent() || sampleCount == 0) {
            log.debug("监控项 {} 在 {} 没有历史数据，跳过统计", item.getName(), startTime);
            return;
        }

        // 计算与前一小时的差值
        Integer diffValue = null;
        LocalDateTime previousHourTime = startTime.minusHours(1);
        Optional<DataHourlyStats> previousStats = dataHourlyStatsRepository
            .findPreviousHourStats(item, previousHourTime);

        if (previousStats.isPresent()) {
            diffValue = maxValue.get() - previousStats.get().getMaxValue();
        }

        // 检查是否已存在该小时的统计数据
        Optional<DataHourlyStats> existingStats = dataHourlyStatsRepository
            .findByDataItemAndHourTimestamp(item, startTime);

        DataHourlyStats stats;
        if (existingStats.isPresent()) {
            // 更新现有记录
            stats = existingStats.get();
            log.debug("更新监控项 {} 在 {} 的统计数据", item.getName(), startTime);
        } else {
            // 创建新记录
            stats = new DataHourlyStats();
            stats.setDataItem(item);
            stats.setDataItemName(item.getName());
            stats.setDataItemAddress(item.getAddress());
            stats.setDeviceId(item.getDevice().getId());
            stats.setDeviceName(item.getDevice().getName());
            stats.setHourTimestamp(startTime);
            log.debug("创建监控项 {} 在 {} 的统计数据", item.getName(), startTime);
        }

        // 更新统计数据
        stats.setMaxValue(maxValue.get());
        stats.setMinValue(minValue.get());
        stats.setAvgValue(BigDecimal.valueOf(avgValue.get()).setScale(2, RoundingMode.HALF_UP));
        stats.setDiffValue(diffValue);
        stats.setSampleCount(sampleCount.intValue());
        stats.setCreatedAt(LocalDateTime.now(ZONE_ID));

        dataHourlyStatsRepository.save(stats);
    }
}
