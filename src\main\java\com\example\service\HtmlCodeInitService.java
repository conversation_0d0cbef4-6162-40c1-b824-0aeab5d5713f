package com.example.service;

import com.example.entity.HtmlCodeCategory;
import com.example.entity.HtmlCodeSnippet;
import com.example.repository.HtmlCodeCategoryRepository;
import com.example.repository.HtmlCodeSnippetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * HTML代码管理初始化服务
 * 在应用启动时创建默认的分类和代码片段
 */
@Service
@Slf4j
public class HtmlCodeInitService implements CommandLineRunner {
    
    @Autowired
    private HtmlCodeCategoryRepository categoryRepository;
    
    @Autowired
    private HtmlCodeSnippetRepository snippetRepository;
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        log.info("开始初始化HTML代码管理数据...");
        
        // 检查是否已经有数据
        if (categoryRepository.count() > 0) {
            log.info("HTML代码分类已存在，跳过初始化");
            return;
        }
        
        try {
            // 创建默认分类
            createDefaultCategories();
            
            // 创建默认代码片段
            createDefaultSnippets();
            
            log.info("HTML代码管理数据初始化完成");
        } catch (Exception e) {
            log.error("HTML代码管理数据初始化失败", e);
        }
    }
    
    private void createDefaultCategories() {
        log.info("创建默认HTML代码分类...");
        
        String[][] categories = {
            {"装饰", "装饰效果组件"},
            {"边框", "边框效果组件"},
            {"按钮", "按钮效果组件"},
            {"动画", "动画效果组件"},
            {"图表", "图表装饰组件"}
        };
        
        for (int i = 0; i < categories.length; i++) {
            HtmlCodeCategory category = new HtmlCodeCategory();
            category.setName(categories[i][0]);
            category.setDescription(categories[i][1]);
            category.setSortOrder(i + 1);
            categoryRepository.save(category);
            log.info("创建分类: {}", categories[i][0]);
        }
    }
    
    private void createDefaultSnippets() {
        log.info("创建默认HTML代码片段...");
        
        // 获取分类
        HtmlCodeCategory decorationCategory = categoryRepository.findByName("装饰");
        HtmlCodeCategory borderCategory = categoryRepository.findByName("边框");
        HtmlCodeCategory buttonCategory = categoryRepository.findByName("按钮");
        HtmlCodeCategory animationCategory = categoryRepository.findByName("动画");
        
        // 创建装饰类代码片段
        createDecorationSnippet(decorationCategory);
        
        // 创建边框类代码片段
        createBorderSnippet(borderCategory);
        
        // 创建按钮类代码片段
        createButtonSnippet(buttonCategory);
        
        // 创建动画类代码片段
        createAnimationSnippet(animationCategory);
    }
    
    private void createDecorationSnippet(HtmlCodeCategory category) {
        String htmlContent = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <style>\n" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }\n" +
            "        body { background: transparent; width: 100%; height: 100%; overflow: hidden; }\n" +
            "        .decoration-container { position: relative; width: 100%; height: 100%; }\n" +
            "        .corner { position: absolute; width: 8%; height: 8%; max-width: 40px; max-height: 40px; min-width: 20px; min-height: 20px; border: clamp(2px, 0.3vw, 4px) solid #00ffff; }\n" +
            "        .corner-tl { top: 2%; left: 2%; border-right: none; border-bottom: none; animation: glow 2s ease-in-out infinite alternate; }\n" +
            "        .corner-tr { top: 2%; right: 2%; border-left: none; border-bottom: none; animation: glow 2s ease-in-out infinite alternate 0.5s; }\n" +
            "        .corner-bl { bottom: 2%; left: 2%; border-right: none; border-top: none; animation: glow 2s ease-in-out infinite alternate 1s; }\n" +
            "        .corner-br { bottom: 2%; right: 2%; border-left: none; border-top: none; animation: glow 2s ease-in-out infinite alternate 1.5s; }\n" +
            "        @keyframes glow { 0% { box-shadow: 0 0 0.5vw #00ffff; } 100% { box-shadow: 0 0 2vw #00ffff; } }\n" +
            "        .center-ring { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 20vw; height: 20vw; max-width: 100px; max-height: 100px; min-width: 50px; min-height: 50px; border: clamp(1px, 0.2vw, 3px) solid rgba(255, 0, 204, 0.6); border-radius: 50%; animation: pulse 3s ease-in-out infinite; }\n" +
            "        @keyframes pulse { 0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); } 50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); } }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <div class=\"decoration-container\">\n" +
            "        <div class=\"corner corner-tl\"></div>\n" +
            "        <div class=\"corner corner-tr\"></div>\n" +
            "        <div class=\"corner corner-bl\"></div>\n" +
            "        <div class=\"corner corner-br\"></div>\n" +
            "        <div class=\"center-ring\"></div>\n" +
            "    </div>\n" +
            "</body>\n" +
            "</html>";
        
        HtmlCodeSnippet snippet = new HtmlCodeSnippet();
        snippet.setTitle("响应式装饰效果");
        snippet.setDescription("自适应容器的装饰效果，包含角落装饰、圆环、浮动点等");
        snippet.setHtmlContent(htmlContent);
        snippet.setCategory(category);
        snippet.setTags("responsive,decoration,corner,ring");
        snippet.setIsFavorite(true);
        snippet.setSortOrder(1);
        snippetRepository.save(snippet);
        log.info("创建代码片段: 响应式装饰效果");
    }
    
    private void createBorderSnippet(HtmlCodeCategory category) {
        String htmlContent = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <style>\n" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }\n" +
            "        body { background: transparent; width: 100%; height: 100%; }\n" +
            "        .border-container { width: 100%; height: 100%; padding: 2%; display: flex; justify-content: center; align-items: center; }\n" +
            "        .dynamic-border { position: relative; width: 100%; height: 100%; border-radius: 2%; background: linear-gradient(45deg, #ff00cc, #00ccff, #00ff00, #ff0000, #ff00cc, #00ccff, #00ff00, #ff0000); background-size: 400%; animation: border-flow 8s linear infinite; }\n" +
            "        @keyframes border-flow { 0% { background-position: 0 0; filter: hue-rotate(0deg); } 50% { background-position: 300% 0; filter: hue-rotate(360deg); } 100% { background-position: 0 0; filter: hue-rotate(0deg); } }\n" +
            "        .content { background: transparent; border-radius: 1.5%; padding: 5%; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; }\n" +
            "        .title { font-size: clamp(1.2rem, 5vw, 3rem); margin-bottom: 0.5em; background: linear-gradient(90deg, #ff00cc, #00ccff); -webkit-background-clip: text; background-clip: text; color: transparent; font-weight: bold; }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <div class=\"border-container\">\n" +
            "        <div class=\"dynamic-border\">\n" +
            "            <div class=\"content\">\n" +
            "                <h1 class=\"title\">动态边框</h1>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n" +
            "</body>\n" +
            "</html>";
        
        HtmlCodeSnippet snippet = new HtmlCodeSnippet();
        snippet.setTitle("响应式动态边框");
        snippet.setDescription("彩色渐变边框动画效果，自适应容器大小");
        snippet.setHtmlContent(htmlContent);
        snippet.setCategory(category);
        snippet.setTags("responsive,border,gradient,animation");
        snippet.setIsFavorite(true);
        snippet.setSortOrder(1);
        snippetRepository.save(snippet);
        log.info("创建代码片段: 响应式动态边框");
    }
    
    private void createButtonSnippet(HtmlCodeCategory category) {
        String htmlContent = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <style>\n" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }\n" +
            "        body { background: transparent; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; padding: 2%; }\n" +
            "        .dynamic-button { position: relative; padding: 4% 8%; font-size: clamp(1rem, 4vw, 2rem); font-weight: bold; color: #00d4ff; background: transparent; border: 0.2em solid #00d4ff; border-radius: 0.6em; cursor: pointer; overflow: hidden; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 0.1em; width: 100%; max-width: 300px; min-width: 120px; height: auto; min-height: 3em; display: flex; align-items: center; justify-content: center; }\n" +
            "        .dynamic-button::before { content: \"\"; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent); transition: left 0.5s; }\n" +
            "        .dynamic-button::after { content: \"\"; position: absolute; top: -0.2em; left: -0.2em; right: -0.2em; bottom: -0.2em; background: linear-gradient(45deg, #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff); background-size: 400%; border-radius: 0.8em; z-index: -1; opacity: 0; animation: border-flow 3s linear infinite; transition: opacity 0.3s; }\n" +
            "        .dynamic-button:hover::before { left: 100%; }\n" +
            "        .dynamic-button:hover::after { opacity: 1; }\n" +
            "        .dynamic-button:hover { color: #ffffff; background: rgba(0, 212, 255, 0.1); box-shadow: 0 0 1em rgba(0, 212, 255, 0.4), 0 0 2em rgba(0, 212, 255, 0.2); transform: translateY(-0.1em); text-shadow: 0 0 0.5em rgba(0, 212, 255, 0.8); }\n" +
            "        @keyframes border-flow { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <button class=\"dynamic-button\">点击我</button>\n" +
            "</body>\n" +
            "</html>";
        
        HtmlCodeSnippet snippet = new HtmlCodeSnippet();
        snippet.setTitle("响应式动态按钮");
        snippet.setDescription("多层发光效果和边框流动动画的响应式按钮");
        snippet.setHtmlContent(htmlContent);
        snippet.setCategory(category);
        snippet.setTags("responsive,button,glow,animation");
        snippet.setIsFavorite(true);
        snippet.setSortOrder(1);
        snippetRepository.save(snippet);
        log.info("创建代码片段: 响应式动态按钮");
    }
    
    private void createAnimationSnippet(HtmlCodeCategory category) {
        String htmlContent = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <style>\n" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }\n" +
            "        body { background: transparent; width: 100%; height: 100%; overflow: hidden; }\n" +
            "        .particles-container { position: relative; width: 100%; height: 100%; }\n" +
            "        .particle { position: absolute; width: clamp(4px, 1vw, 8px); height: clamp(4px, 1vw, 8px); background: radial-gradient(circle, #00ff88, transparent); border-radius: 50%; animation: float 6s ease-in-out infinite; }\n" +
            "        .particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }\n" +
            "        .particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 1s; }\n" +
            "        .particle:nth-child(3) { top: 30%; right: 15%; animation-delay: 2s; }\n" +
            "        .particle:nth-child(4) { bottom: 40%; left: 30%; animation-delay: 3s; }\n" +
            "        .particle:nth-child(5) { bottom: 20%; right: 25%; animation-delay: 4s; }\n" +
            "        .particle:nth-child(6) { top: 70%; right: 40%; animation-delay: 5s; }\n" +
            "        @keyframes float { 0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; } 50% { transform: translateY(-20px) scale(1.2); opacity: 1; } }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <div class=\"particles-container\">\n" +
            "        <div class=\"particle\"></div>\n" +
            "        <div class=\"particle\"></div>\n" +
            "        <div class=\"particle\"></div>\n" +
            "        <div class=\"particle\"></div>\n" +
            "        <div class=\"particle\"></div>\n" +
            "        <div class=\"particle\"></div>\n" +
            "    </div>\n" +
            "</body>\n" +
            "</html>";
        
        HtmlCodeSnippet snippet = new HtmlCodeSnippet();
        snippet.setTitle("浮动粒子动画");
        snippet.setDescription("科技感浮动粒子背景动画效果");
        snippet.setHtmlContent(htmlContent);
        snippet.setCategory(category);
        snippet.setTags("responsive,particles,float,animation");
        snippet.setIsFavorite(false);
        snippet.setSortOrder(1);
        snippetRepository.save(snippet);
        log.info("创建代码片段: 浮动粒子动画");
    }
}
