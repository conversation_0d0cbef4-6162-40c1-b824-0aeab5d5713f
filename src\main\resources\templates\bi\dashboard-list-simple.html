<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BI大屏管理 - 胜大PLC管理系统</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bootstrap-icons.min.css" rel="stylesheet">
    <link href="/css/button-styles.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <div th:replace="fragments/navbar :: navbar('BI大屏管理', 'biDashboard', true, true, true, null)"></div>

    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="bi bi-bar-chart"></i> BI大屏管理（简化版）</h2>
                <p class="text-muted">创建和管理数据可视化大屏</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="createTestDashboard()">
                    <i class="bi bi-plus-circle"></i> 新建测试大屏
                </button>
            </div>
        </div>

        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <span th:text="${error}"></span>
        </div>

        <!-- 成功提示 -->
        <div th:if="${success}" class="alert alert-success" role="alert">
            <i class="bi bi-check-circle"></i> <span th:text="${success}"></span>
        </div>

        <!-- 大屏列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>大屏列表</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(dashboards)}" class="text-center py-4">
                            <p class="text-muted">暂无大屏数据</p>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(dashboards)}">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>描述</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="dashboard : ${dashboards}">
                                        <td th:text="${dashboard.id}">1</td>
                                        <td th:text="${dashboard.name}">大屏名称</td>
                                        <td th:text="${dashboard.description ?: '无描述'}">描述</td>
                                        <td th:text="${dashboard.createdAt != null ? dashboard.createdAt : '未知'}">时间</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    th:onclick="|deleteDashboard(${dashboard.id})|">
                                                删除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script>
        // 创建测试大屏
        function createTestDashboard() {
            const name = '测试大屏_' + new Date().getTime();
            const description = '这是一个测试大屏';
            
            const formData = new FormData();
            formData.append('name', name);
            formData.append('description', description);
            
            fetch('/api/bi/dashboard', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('创建成功');
                    location.reload();
                } else {
                    alert('创建失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建失败: ' + error.message);
            });
        }

        // 删除大屏
        function deleteDashboard(id) {
            // 首先检查是否有发布记录
            fetch(`/api/bi/dashboard/${id}/check-published`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.hasPublished) {
                            // 有发布记录，显示详细确认对话框
                            let recordsInfo = '';
                            data.publishedRecords.forEach(record => {
                                const status = record.status === 'ACTIVE' ? '有效' :
                                              record.status === 'EXPIRED' ? '已过期' : '已撤销';
                                const publishedDate = new Date(record.publishedAt).toLocaleString();
                                recordsInfo += `\n- ${record.name} (${status}, 发布于: ${publishedDate})`;
                            });

                            const message = `该大屏存在 ${data.publishedRecords.length} 条发布记录：${recordsInfo}\n\n删除大屏将同时删除所有发布记录，此操作不可恢复。\n\n确定要继续删除吗？`;

                            if (confirm(message)) {
                                performForceDelete(id);
                            }
                        } else {
                            // 没有发布记录，直接确认删除
                            if (confirm('确定要删除这个大屏吗？')) {
                                performDelete(id);
                            }
                        }
                    } else {
                        alert('检查发布记录失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('检查发布记录失败');
                });
        }

        // 执行普通删除
        function performDelete(id) {
            fetch(`/api/bi/dashboard/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败: ' + error.message);
            });
        }

        // 执行强制删除（包含发布记录）
        function performForceDelete(id) {
            fetch(`/api/bi/dashboard/${id}/force-delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功（包含发布记录）');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
