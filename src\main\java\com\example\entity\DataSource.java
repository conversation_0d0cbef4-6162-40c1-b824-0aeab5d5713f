package com.example.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 数据源实体
 * 用于管理外部数据源连接配置
 */
@Entity
@Table(name = "data_sources")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSource {
    
    @Id
    @Column(length = 50)
    private String id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    /**
     * 数据源类型：database, api, file, mqtt
     */
    @Column(nullable = false, length = 20)
    private String type;
    
    /**
     * 连接配置（JSON格式）
     * 数据库：{host, port, database, username, password, driverClass}
     * API：{baseUrl, headers, authentication}
     * 文件：{filePath, fileType, encoding}
     */
    @Column(columnDefinition = "TEXT")
    private String connectionConfig;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 连接状态：connected, disconnected, error
     */
    @Column(length = 20)
    private String status = "disconnected";
    
    /**
     * 最后连接测试时间
     */
    private LocalDateTime lastTestTime;
    
    /**
     * 连接测试结果消息
     */
    @Column(length = 500)
    private String testMessage;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(length = 50)
    private String updatedBy;
}
