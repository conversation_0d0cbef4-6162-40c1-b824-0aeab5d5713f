# 大屏预览页面处理规划文档

## 项目概述

本项目是一个基于Spring Boot的PLC管理系统，包含BI大屏功能。当前大屏预览页面处于初代状态，需要进行全面的处理和优化，使其能够正确显示设计页面中的所有内容。

## 当前状态分析

### 1. 现有文件结构
- **预览页面**: `src/main/resources/templates/bi/dashboard-preview.html`
- **设计器页面**: `src/main/resources/templates/bi/dashboard-designer.html`
- **核心JS文件**: `src/main/resources/static/js/bi-dashboard-designer.js`
- **样式文件**: `src/main/resources/static/css/bi-dashboard.css`
- **后端控制器**: `src/main/java/com/example/controller/BiDashboardController.java`

### 2. 现有组件类型
根据设计器页面分析，系统支持以下组件类型：

#### 图表组件
- `line-chart` - 折线图
- `bar-chart` - 柱状图  
- `horizontal-bar-chart` - 水平柱状图
- `gauge-chart` - 仪表盘
- `pie-chart` - 饼图
- `water-chart` - 水波图
- `data-table` - 数据表格

#### 基础组件
- `text-label` - 文本标签
- `image-widget` - 图片组件
- `decoration-widget` - 装饰组件

### 3. 数据源支持
- 静态数据源
- 监控项数据源 (DataItem)
- 数据集数据源 (DataSet)
- 实时数据更新机制

### 4. 当前预览页面问题
- 组件渲染不完整，缺少部分组件类型的实现
- 数据源连接和数据获取逻辑需要完善
- 样式和布局需要优化
- 实时数据更新机制需要改进
- 缺少错误处理和异常情况处理

## 处理规划

### 阶段一：组件渲染完善

#### 1.1 图表组件处理
**目标**: 完善所有图表组件的渲染逻辑

**任务清单**:
- [ ] 完善折线图组件 (`line-chart`)
  - 数据获取和格式化
  - 样式配置应用
  - 实时数据更新
- [ ] 完善柱状图组件 (`bar-chart`)
  - 支持渐变色彩
  - 数据标签显示
  - 动画效果
- [ ] 实现水平柱状图组件 (`horizontal-bar-chart`)
- [ ] 完善仪表盘组件 (`gauge-chart`)
  - 多色段显示
  - 数值范围配置
- [ ] 实现饼图组件 (`pie-chart`)
  - 3D效果支持
  - 占比数据展示
- [ ] 实现水波图组件 (`water-chart`)
  - 动态水波效果
  - 进度展示
- [ ] 完善数据表格组件 (`data-table`)
  - 实时数据更新
  - 表格样式配置

#### 1.2 基础组件处理
**目标**: 实现所有基础组件的完整功能

**任务清单**:
- [ ] 完善文本标签组件 (`text-label`)
  - 动态文本内容
  - 字体样式配置
- [ ] 完善图片组件 (`image-widget`)
  - 图片加载和显示
  - 缩放和定位
- [ ] 实现装饰组件 (`decoration-widget`)
  - 动态装饰效果
  - 多种装饰样式

### 阶段二：数据源集成

#### 2.1 数据源连接器完善
**目标**: 确保所有数据源类型都能正确连接和获取数据

**任务清单**:
- [ ] 监控项数据源 (DataItem)
  - 实时数据获取
  - 历史数据查询
  - 数据格式转换
- [ ] 数据集数据源 (DataSet)
  - SQL查询执行
  - 数据字段映射
  - 结果集处理
- [ ] 静态数据源
  - 配置数据解析
  - 模拟数据生成

#### 2.2 数据更新机制
**目标**: 实现高效的实时数据更新

**任务清单**:
- [ ] WebSocket连接实现
- [ ] 数据推送机制
- [ ] 客户端数据缓存
- [ ] 更新频率控制

### 阶段三：样式和主题系统

#### 3.1 主题预设应用
**目标**: 将设计器中的主题预设应用到预览页面

**任务清单**:
- [ ] 主题配置解析
- [ ] 组件样式应用
- [ ] 画布背景处理
- [ ] 颜色方案实现

#### 3.2 响应式布局
**目标**: 支持不同屏幕尺寸的自适应显示

**任务清单**:
- [ ] 画布缩放算法
- [ ] 组件位置调整
- [ ] 字体大小适配
- [ ] 布局优化

### 阶段四：交互功能增强

#### 4.1 控制面板功能
**目标**: 完善预览页面的控制功能

**任务清单**:
- [ ] 全屏切换功能
- [ ] 数据刷新控制
- [ ] 返回设计器功能
- [ ] 状态指示器

#### 4.2 错误处理和用户体验
**目标**: 提供良好的错误处理和用户反馈

**任务清单**:
- [ ] 组件加载失败处理
- [ ] 数据获取异常处理
- [ ] 网络连接状态监控
- [ ] 用户友好的错误提示

## 技术实现要点

### 1. 前端技术栈
- **图表库**: Chart.js / ECharts
- **UI框架**: Bootstrap 5
- **图标库**: Bootstrap Icons
- **JavaScript**: ES6+ 原生JavaScript

### 2. 后端技术栈
- **框架**: Spring Boot
- **数据库**: MySQL/H2
- **ORM**: JPA/Hibernate
- **API**: RESTful API

### 3. 关键技术点
- 组件配置的JSON序列化/反序列化
- 数据源的动态连接和查询
- 实时数据的WebSocket推送
- 图表组件的动态渲染
- 响应式布局的实现

## 预期成果

完成本规划后，大屏预览页面将具备以下能力：

1. **完整的组件支持**: 所有设计器中的组件类型都能正确渲染
2. **实时数据展示**: 支持多种数据源的实时数据获取和显示
3. **美观的视觉效果**: 应用主题配置，提供专业的大屏展示效果
4. **良好的用户体验**: 流畅的交互，友好的错误处理
5. **高性能表现**: 优化的数据更新机制，减少不必要的资源消耗

## 开发优先级

1. **高优先级**: 图表组件渲染、数据源连接
2. **中优先级**: 基础组件实现、样式主题应用
3. **低优先级**: 装饰效果、高级交互功能

## 风险评估

1. **技术风险**: ECharts库的版本兼容性问题
2. **性能风险**: 大量实时数据更新可能影响页面性能
3. **兼容性风险**: 不同浏览器的兼容性问题
4. **数据风险**: 数据源连接失败或数据格式不匹配

## 测试策略

1. **单元测试**: 各组件的独立功能测试
2. **集成测试**: 数据源连接和数据流测试
3. **性能测试**: 大量数据和长时间运行测试
4. **兼容性测试**: 多浏览器和多设备测试
