package com.example.controller;

import com.example.entity.Topology;
import com.example.service.DeviceService;
import com.example.service.TopologyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/topology")
@Slf4j
public class TopologyController {
    
    @Autowired
    private DeviceService deviceService;
    
    @Autowired
    private TopologyService topologyService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @GetMapping
    public String showTopologyPage(Model model) {
        log.info("显示组态页面");
        model.addAttribute("devices", deviceService.getAllDevices());
        return "topology/index";
    }
    
    @PostMapping("/save")
    @ResponseBody
    public ResponseEntity<?> saveTopology(@RequestBody Map<String, Object> topologyData) {
        log.info("保存组态布局: {}", topologyData);
        try {
            // 将Map转换为JSON字符串
            String jsonData = objectMapper.writeValueAsString(topologyData);
            topologyService.saveTopology(jsonData);
            Map<String, String> response = new HashMap<>();
            response.put("message", "组态布局保存成功");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            log.error("保存组态布局失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/load")
    @ResponseBody
    public ResponseEntity<?> loadTopology() {
        log.info("加载组态布局");
        try {
            String topologyData = topologyService.getTopology();
            // 将JSON字符串转换为Map
            Map<String, Object> data = objectMapper.readValue(topologyData, Map.class);
            return ResponseEntity.ok().body(data);
        } catch (Exception e) {
            log.error("加载组态布局失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("message", "加载失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/load/{name}")
    @ResponseBody
    public ResponseEntity<?> loadTopologyByName(@PathVariable String name) {
        log.info("加载名称为 {} 的组态布局", name);
        try {
            String topologyData = topologyService.getTopologyByName(name);
            // 将JSON字符串转换为Map
            Map<String, Object> data = objectMapper.readValue(topologyData, Map.class);
            return ResponseEntity.ok().body(data);
        } catch (Exception e) {
            log.error("加载组态布局失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("message", "加载失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @DeleteMapping("/delete/{name}")
    @ResponseBody
    public ResponseEntity<?> deleteTopology(@PathVariable String name) {
        log.info("删除名称为 {} 的组态布局", name);
        try {
            topologyService.deleteTopologyByName(name);
            Map<String, String> response = new HashMap<>();
            response.put("message", "删除成功");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            log.error("删除组态布局失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/preview/{name}")
    public String previewTopology(@PathVariable String name, Model model) {
        log.info("预览名称为 {} 的组态布局", name);
        try {
            String topologyData = topologyService.getTopologyByName(name);
            model.addAttribute("topologyName", name);
            model.addAttribute("topologyData", topologyData);
            model.addAttribute("devices", deviceService.getAllDevices());
            return "topology/preview";
        } catch (Exception e) {
            log.error("预览组态布局失败", e);
            model.addAttribute("error", "预览失败: " + e.getMessage());
            return "error";
        }
    }
}

@RestController
@RequestMapping("/api")
@Slf4j
class TopologyApiController {
    
    @Autowired
    private TopologyService topologyService;
    
    @GetMapping("/topologies")
    public ResponseEntity<List<Topology>> getAllTopologies() {
        log.info("获取所有组态布局列表");
        try {
            List<Topology> topologies = topologyService.getAllTopologies();
            return ResponseEntity.ok().body(topologies);
        } catch (Exception e) {
            log.error("获取组态布局列表失败", e);
            return ResponseEntity.badRequest().body(Collections.emptyList());
        }
    }
} 