<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤2布局修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .layout-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .config-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .before {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .after {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .column-indicator {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .layout-comparison {
            position: relative;
            margin: 2rem 0;
        }
        
        .visual-guide {
            border-top: 2px dashed #007bff;
            margin: 1rem 0;
            position: relative;
        }
        
        .visual-guide::after {
            content: "对齐线";
            position: absolute;
            right: 0;
            top: -12px;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-layout-three-columns"></i>
                步骤2布局修复验证
            </h2>
            
            <!-- 修复说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 修复说明</h6>
                <p class="mb-0">修复了第二行三栏"挤在右边"的问题，通过调整列宽度与第一行保持一致，实现视觉对齐和更好的布局效果。</p>
            </div>
            
            <!-- 修复前后对比 -->
            <div class="layout-comparison">
                <h5><i class="bi bi-arrow-left-right"></i> 修复前后对比</h5>
                
                <!-- 修复前 -->
                <div class="layout-demo before">
                    <h6><i class="bi bi-x-circle text-warning"></i> 修复前（不对齐）</h6>
                    
                    <!-- 第一行 -->
                    <div class="row mb-3">
                        <div class="col-md-3" style="position: relative;">
                            <div class="column-indicator">col-md-3</div>
                            <div class="config-section">
                                <strong>表选择</strong>
                                <small>25% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>字段配置</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-5" style="position: relative;">
                            <div class="column-indicator">col-md-5</div>
                            <div class="config-section">
                                <strong>筛选条件</strong>
                                <small>41.67% 宽度</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二行（修复前） -->
                    <div class="row">
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>输出限制</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>日期格式化</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>聚合配置</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-2">
                        <small class="text-warning">❌ 第二行与第一行列宽度不一致，视觉上不对齐</small>
                    </div>
                </div>
                
                <div class="visual-guide"></div>
                
                <!-- 修复后 -->
                <div class="layout-demo after">
                    <h6><i class="bi bi-check-circle text-success"></i> 修复后（完美对齐）</h6>
                    
                    <!-- 第一行 -->
                    <div class="row mb-3">
                        <div class="col-md-3" style="position: relative;">
                            <div class="column-indicator">col-md-3</div>
                            <div class="config-section">
                                <strong>表选择</strong>
                                <small>25% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>字段配置</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-5" style="position: relative;">
                            <div class="column-indicator">col-md-5</div>
                            <div class="config-section">
                                <strong>筛选条件</strong>
                                <small>41.67% 宽度</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二行（修复后） -->
                    <div class="row">
                        <div class="col-md-3" style="position: relative;">
                            <div class="column-indicator">col-md-3</div>
                            <div class="config-section">
                                <strong>输出限制</strong>
                                <small>25% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-4" style="position: relative;">
                            <div class="column-indicator">col-md-4</div>
                            <div class="config-section">
                                <strong>日期格式化</strong>
                                <small>33.33% 宽度</small>
                            </div>
                        </div>
                        <div class="col-md-5" style="position: relative;">
                            <div class="column-indicator">col-md-5</div>
                            <div class="config-section">
                                <strong>聚合配置</strong>
                                <small>41.67% 宽度</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-2">
                        <small class="text-success">✅ 第二行与第一行列宽度完全一致，视觉完美对齐</small>
                    </div>
                </div>
            </div>
            
            <!-- 修复详情 -->
            <div class="layout-demo">
                <h5><i class="bi bi-code-slash"></i> 修复详情</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>修改内容</h6>
                        <ul class="small">
                            <li><strong>输出限制</strong>：col-md-4 → col-md-3</li>
                            <li><strong>日期格式化</strong>：col-md-4 → col-md-4 (保持不变)</li>
                            <li><strong>聚合配置</strong>：col-md-4 → col-md-5</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>修复效果</h6>
                        <ul class="small">
                            <li>✅ 第二行与第一行列宽度完全一致</li>
                            <li>✅ 视觉上完美对齐，不再"挤在右边"</li>
                            <li>✅ 保持响应式布局的正确性</li>
                            <li>✅ 内容分布更加合理和美观</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 布局原理 -->
            <div class="layout-demo">
                <h5><i class="bi bi-grid-3x3"></i> Bootstrap网格系统原理</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>列宽度计算</h6>
                        <div class="small">
                            <p><strong>Bootstrap 12列网格系统：</strong></p>
                            <ul>
                                <li>col-md-3 = 3/12 = 25%</li>
                                <li>col-md-4 = 4/12 = 33.33%</li>
                                <li>col-md-5 = 5/12 = 41.67%</li>
                            </ul>
                            <p><strong>总和：</strong>3 + 4 + 5 = 12 ✅</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>对齐原理</h6>
                        <div class="small">
                            <p><strong>视觉对齐的关键：</strong></p>
                            <ul>
                                <li>相同的列宽度在垂直方向上对齐</li>
                                <li>创建清晰的视觉网格结构</li>
                                <li>避免内容看起来"挤在一边"</li>
                                <li>提供一致的用户体验</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 响应式测试 -->
            <div class="layout-demo">
                <h5><i class="bi bi-phone"></i> 响应式效果</h5>
                <p class="small">在不同屏幕尺寸下的表现：</p>
                <div class="row">
                    <div class="col-md-4">
                        <div class="config-section">
                            <strong>大屏幕 (≥768px)</strong>
                            <small>使用 col-md-* 布局</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-section">
                            <strong>中等屏幕</strong>
                            <small>保持网格布局</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="config-section">
                            <strong>小屏幕 (<768px)</strong>
                            <small>自动堆叠显示</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
