<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <!-- 这个头部不会被渲染，只是为了IDE支持 -->
</head>
<body>
    <!-- 
        导航条片段
        参数:
        - pageTitle: 页面标题
        - activeMenu: 当前激活的菜单项 (home, topology, fileManager)
        - showHomeButton: 是否显示主页按钮
        - showTopologyButton: 是否显示组态按钮
        - showFileManagerButton: 是否显示文件管理按钮
        - customButtons: 自定义按钮HTML (可选)
    -->
    <nav th:fragment="navbar(pageTitle, activeMenu, showHomeButton, showTopologyButton, showFileManagerButton, customButtons)"
         class="navbar navbar-expand-lg"
         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; padding: 0.5rem 1rem; margin-bottom: 1rem; height: 56px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
         th:with="pageTitle=${pageTitle ?: '胜大科技智联管理系统'},
                 activeMenu=${activeMenu ?: 'home'},
                 showHomeButton=${showHomeButton != null ? showHomeButton : true},
                 showTopologyButton=${showTopologyButton != null ? showTopologyButton : true},
                 showFileManagerButton=${showFileManagerButton != null ? showFileManagerButton : true}">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <!-- 系统标题 -->
                <span class="navbar-brand"
                      style="color: white; font-size: 1.5rem; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                      th:text="${pageTitle}">胜大科技智联管理系统</span>
                <small class="text-white-50 ms-2" style="font-size: 0.75rem;">v8.2</small>
                
                <!-- 主页按钮 -->
                <button th:if="${showHomeButton}" 
                        class="btn btn-outline-light btn-sm ms-3" 
                        th:classappend="${activeMenu == 'home' ? 'active' : ''}"
                        onclick="window.location.href='/'">
                    <i class="bi bi-house-door"></i> 主页
                </button>
                
                <!-- 组态按钮 -->
                <a th:if="${showTopologyButton}" 
                   href="/topology" 
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'topology' ? 'active' : ''}">
                    <i class="bi bi-diagram-3"></i> 组态
                </a>
                
                <!-- 文件管理按钮 -->
                <a th:if="${showFileManagerButton}"
                   href="/file-manager"
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'fileManager' ? 'active' : ''}">
                    <i class="bi bi-images"></i> 文件管理
                </a>

                <!-- 设备管理按钮 -->
                <a href="/device/management"
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'deviceManagement' ? 'active' : ''}">
                    <i class="bi bi-cpu"></i> 设备管理
                </a>

                <!-- BI大屏按钮 -->
                <a href="/bi/dashboard"
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'biDashboard' ? 'active' : ''}">
                    <i class="bi bi-bar-chart"></i> BI大屏
                </a>

                <!-- 发布管理按钮 -->
                <a href="/bi/published/management"
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'biPublish' ? 'active' : ''}">
                    <i class="bi bi-share"></i> 发布管理
                </a>

                <!-- 数据源管理按钮 -->
                <a href="/datasource"
                   class="btn btn-outline-light btn-sm ms-3"
                   th:classappend="${activeMenu == 'dataSource' ? 'active' : ''}">
                    <i class="bi bi-database"></i> 数据源管理
                </a>

                <!-- 自定义按钮 -->
                <th:block th:if="${customButtons != null}" th:utext="${customButtons}"></th:block>
            </div>
            
            <!-- 用户信息 - 使用简单的按钮组 -->
            <div class="user-info">
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="bi bi-person-circle"></i>
                        <span th:text="${session.user != null ? session.user.username : '游客'}">用户名</span>
                    </span>
                    <a href="/auth/change-password" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-key"></i> 修改密码
                    </a>
                    <a href="/auth/logout" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-box-arrow-right"></i> 退出
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 添加JavaScript初始化下拉菜单 -->
    <script th:fragment="navbar-script">
        // 这个脚本现在不再需要，但保留片段以避免引用错误
        console.log('导航条已加载');
    </script>
</body>
</html> 