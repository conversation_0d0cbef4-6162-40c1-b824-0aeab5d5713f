package com.example.repository;

import com.example.entity.HtmlCodeCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * HTML代码分类数据访问接口
 */
@Repository
public interface HtmlCodeCategoryRepository extends JpaRepository<HtmlCodeCategory, Long> {
    
    /**
     * 按排序顺序查询所有分类
     */
    List<HtmlCodeCategory> findAllByOrderBySortOrderAsc();
    
    /**
     * 检查分类名称是否已存在
     */
    boolean existsByName(String name);
    
    /**
     * 根据名称查找分类
     */
    HtmlCodeCategory findByName(String name);
    
    /**
     * 获取最大排序顺序
     */
    @Query("SELECT MAX(c.sortOrder) FROM HtmlCodeCategory c")
    Integer findMaxSortOrder();
}
