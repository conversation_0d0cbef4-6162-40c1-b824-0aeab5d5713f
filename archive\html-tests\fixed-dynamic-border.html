<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版彩色动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;           /* ✅ 透明背景 */
            width: 100%;                      /* ✅ 宽度100% */
            height: 100vh;                    /* ✅ 高度100vh */
            overflow: hidden;                 /* ✅ 防止滚动条 */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            width: 100%;                      /* ✅ 容器100%宽度 */
            height: 100%;                     /* ✅ 容器100%高度 */
            padding: 3%;                      /* ✅ 适度内边距 */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .dynamic-border-box {
            position: relative;
            width: 100%;                      /* ✅ 修复：100%宽度而不是固定300px */
            height: 100%;                     /* ✅ 修复：100%高度而不是固定200px */
            background: transparent;
            border-radius: 1vh;               /* ✅ 修复：使用vh单位 */
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 80px;                  /* ✅ 最小宽度保证可见性 */
            min-height: 40px;                 /* ✅ 最小高度保证可见性 */
        }
        
        /* 动态边框效果 - 核心代码 */
        .dynamic-border-box::before {
            content: '';
            position: absolute;
            top: -0.3vh;                      /* ✅ 修复：使用vh单位 */
            left: -0.3vh;
            right: -0.3vh;
            bottom: -0.3vh;
            background: linear-gradient(45deg, 
                #ff00cc, #00ccff, #00ff00, #ff0000, 
                #ff00cc, #00ccff, #00ff00, #ff0000);
            background-size: 400%;
            border-radius: 1.2vh;             /* ✅ 修复：使用vh单位 */
            z-index: -1;
            animation: animate-border 8s linear infinite;
        }
        
        @keyframes animate-border {
            0% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
            50% {
                background-position: 300% 0;
                filter: hue-rotate(360deg);
            }
            100% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
        }
        
        .content {
            background: transparent;          /* ✅ 修复：透明背景 */
            border-radius: 0.8vh;             /* ✅ 修复：使用vh单位 */
            padding: 2vh;                     /* ✅ 修复：使用vh单位 */
            text-align: center;
            color: #00d4ff;                   /* ✅ 修复：更明显的颜色 */
            width: 100%;                      /* ✅ 修复：100%宽度 */
            height: 100%;                     /* ✅ 修复：100%高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            /* 移除box-shadow以保持透明效果 */
        }
        
        h1 {
            font-size: 4vh;                   /* ✅ 修复：使用vh单位而不是rem */
            margin-bottom: 1vh;               /* ✅ 修复：使用vh单位 */
            background: linear-gradient(90deg, #ff00cc, #00ccff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 1vh rgba(255, 0, 204, 0.5); /* ✅ 增强可见性 */
        }
        
        p {
            color: #66ddff;                   /* ✅ 修复：更明显的颜色 */
            line-height: 1.4;
            font-size: 2vh;                   /* ✅ 修复：使用vh单位 */
            text-shadow: 0 0 0.5vh rgba(102, 221, 255, 0.6); /* ✅ 增强可见性 */
        }
        
        /* 悬停效果 */
        .dynamic-border-box:hover::before {
            animation-duration: 4s;
        }
        
        .dynamic-border-box:hover h1 {
            text-shadow: 0 0 2vh rgba(255, 0, 204, 0.8);
        }
        
        .dynamic-border-box:hover p {
            text-shadow: 0 0 1vh rgba(102, 221, 255, 0.8);
        }
        
        /* ✅ 修复：基于容器高度的响应式设计 */
        @media (max-height: 50px) {
            .dynamic-border-box {
                border-radius: 0.5vh;
            }
            
            .dynamic-border-box::before {
                top: -0.2vh;
                left: -0.2vh;
                right: -0.2vh;
                bottom: -0.2vh;
                border-radius: 0.6vh;
            }
            
            .content {
                padding: 1vh;
                border-radius: 0.4vh;
            }
            
            h1 {
                font-size: 2vh;
                margin-bottom: 0.5vh;
            }
            
            p {
                font-size: 1.2vh;
            }
        }
        
        @media (min-height: 50px) and (max-height: 100px) {
            h1 {
                font-size: 3vh;
                margin-bottom: 0.8vh;
            }
            
            p {
                font-size: 1.5vh;
            }
        }
        
        @media (min-height: 100px) and (max-height: 200px) {
            h1 {
                font-size: 4vh;
                margin-bottom: 1vh;
            }
            
            p {
                font-size: 2vh;
            }
        }
        
        @media (min-height: 200px) {
            h1 {
                font-size: 5vh;
                margin-bottom: 1.2vh;
            }
            
            p {
                font-size: 2.5vh;
            }
        }
        
        /* 极小容器的特殊处理 */
        @media (max-width: 80px) or (max-height: 40px) {
            .dynamic-border-box {
                border-radius: 4px;
            }
            
            .dynamic-border-box::before {
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                border-radius: 6px;
            }
            
            .content {
                padding: 5px;
                border-radius: 3px;
            }
            
            h1 {
                font-size: 12px;
                margin-bottom: 3px;
            }
            
            p {
                font-size: 8px;
                line-height: 1.2;
            }
        }
        
        /* 宽度响应式补充 */
        @media (max-width: 150px) {
            h1 {
                font-size: 3vh;
            }
            
            p {
                font-size: 1.5vh;
                line-height: 1.2;
            }
        }
        
        @media (min-width: 300px) {
            .content {
                padding: 3vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dynamic-border-box">
            <div class="content">
                <h1>动态边框</h1>
                <p>彩色渐变边框效果<br>自适应容器大小</p>
            </div>
        </div>
    </div>
</body>
</html>
