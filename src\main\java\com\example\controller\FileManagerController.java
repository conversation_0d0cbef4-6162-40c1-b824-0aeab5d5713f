package com.example.controller;

import com.example.service.ImageReferenceService;
import com.example.service.VideoReferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;

@Controller
public class FileManagerController {
    private static final Logger logger = LoggerFactory.getLogger(FileManagerController.class);

    @Value("${upload.image.path}")
    private String uploadPath;
    
    @Value("${upload.image.url-prefix}")
    private String urlPrefix;

    @Value("${upload.video.path}")
    private String videoUploadPath;

    @Value("${upload.video.url-prefix}")
    private String videoUrlPrefix;

    @Value("${server.external-url}")
    private String serverExternalUrl;
    
    private final ImageReferenceService imageReferenceService;
    private final VideoReferenceService videoReferenceService;

    @Autowired
    public FileManagerController(ImageReferenceService imageReferenceService, VideoReferenceService videoReferenceService) {
        this.imageReferenceService = imageReferenceService;
        this.videoReferenceService = videoReferenceService;
    }

    @GetMapping("/file-manager")
    public String fileManagerPage(Model model) {
        logger.info("访问文件管理页面");
        return "file-manager";
    }

    @GetMapping("/api/files/images")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getImageFiles() {
        List<Map<String, Object>> files = new ArrayList<>();
        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(uploadPath).isAbsolute()) {
                uploadDir = Paths.get(uploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, uploadPath).toAbsolutePath().normalize();
            }

            if (Files.exists(uploadDir)) {
                File[] fileList = uploadDir.toFile().listFiles();
                if (fileList != null) {
                    DecimalFormat df = new DecimalFormat("#.##");
                    Arrays.sort(fileList, Comparator.comparing(File::lastModified).reversed());
                    
                    for (File file : fileList) {
                        if (file.isFile() && isImageFile(file.getName())) {
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("url", "http://" + serverExternalUrl + urlPrefix + "/" + file.getName());
                            
                            // 文件大小显示
                            double size = file.length();
                            String sizeDisplay;
                            if (size < 1024) {
                                sizeDisplay = size + " B";
                            } else if (size < 1024 * 1024) {
                                sizeDisplay = df.format(size / 1024) + " KB";
                            } else {
                                sizeDisplay = df.format(size / (1024 * 1024)) + " MB";
                            }
                            fileInfo.put("size", sizeDisplay);
                            fileInfo.put("lastModified", new Date(file.lastModified()));
                            
                            // 添加使用状态信息
                            try {
                                Map<String, Object> usageInfo = imageReferenceService.checkImageUsage(file.getName());
                                fileInfo.put("isUsed", usageInfo.get("isUsed"));
                                fileInfo.put("references", usageInfo.get("references"));
                            } catch (Exception e) {
                                logger.error("检查图片 {} 的使用状态时出错: {}", file.getName(), e.getMessage());
                                fileInfo.put("isUsed", false);
                                fileInfo.put("references", new ArrayList<>());
                            }
                            
                            files.add(fileInfo);
                        }
                    }
                }
            }
            return ResponseEntity.ok(files);
        } catch (Exception e) {
            logger.error("获取图片文件列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @DeleteMapping("/api/files/images/{filename}")
    @ResponseBody
    public ResponseEntity<Map<String, String>> deleteImageFile(@PathVariable String filename) {
        Map<String, String> response = new HashMap<>();
        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(uploadPath).isAbsolute()) {
                uploadDir = Paths.get(uploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, uploadPath).toAbsolutePath().normalize();
            }

            Path filePath = uploadDir.resolve(filename).normalize();
            
            // 安全检查：确保文件路径在uploadDir下
            if (!filePath.startsWith(uploadDir)) {
                response.put("error", "非法的文件路径");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                response.put("message", "文件已成功删除");
                return ResponseEntity.ok(response);
            } else {
                response.put("error", "文件不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (IOException e) {
            logger.error("删除图片文件失败", e);
            response.put("error", "删除文件失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @DeleteMapping("/api/files/images/unused")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteUnusedImageFiles() {
        Map<String, Object> response = new HashMap<>();
        int deletedCount = 0;
        List<String> deletedFiles = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();
        
        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(uploadPath).isAbsolute()) {
                uploadDir = Paths.get(uploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, uploadPath).toAbsolutePath().normalize();
            }

            if (Files.exists(uploadDir)) {
                File[] fileList = uploadDir.toFile().listFiles();
                if (fileList != null) {
                    for (File file : fileList) {
                        if (file.isFile() && isImageFile(file.getName())) {
                            // 检查图片是否被使用
                            Map<String, Object> usageInfo = imageReferenceService.checkImageUsage(file.getName());
                            Boolean isUsed = (Boolean) usageInfo.get("isUsed");
                            
                            // 如果图片未被使用，则删除
                            if (!isUsed) {
                                Path filePath = uploadDir.resolve(file.getName()).normalize();
                                
                                // 安全检查：确保文件路径在uploadDir下
                                if (!filePath.startsWith(uploadDir)) {
                                    failedFiles.add(file.getName() + " (非法的文件路径)");
                                    continue;
                                }
                                
                                try {
                                    Files.delete(filePath);
                                    deletedCount++;
                                    deletedFiles.add(file.getName());
                                    logger.info("已删除未使用的图片文件: {}", file.getName());
                                } catch (IOException e) {
                                    logger.error("删除图片文件失败: {}", file.getName(), e);
                                    failedFiles.add(file.getName() + " (" + e.getMessage() + ")");
                                }
                            }
                        }
                    }
                }
            }
            
            response.put("success", true);
            response.put("deletedCount", deletedCount);
            response.put("deletedFiles", deletedFiles);
            response.put("failedFiles", failedFiles);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("批量删除未使用图片文件失败", e);
            response.put("success", false);
            response.put("error", "批量删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    private boolean isImageFile(String filename) {
        filename = filename.toLowerCase();
        return filename.endsWith(".jpg") || filename.endsWith(".jpeg") ||
               filename.endsWith(".png") || filename.endsWith(".gif") ||
               filename.endsWith(".bmp") || filename.endsWith(".webp");
    }

    @GetMapping("/api/files/videos")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getVideoFiles() {
        List<Map<String, Object>> files = new ArrayList<>();
        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(videoUploadPath).isAbsolute()) {
                uploadDir = Paths.get(videoUploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, videoUploadPath).toAbsolutePath().normalize();
            }

            if (Files.exists(uploadDir)) {
                File[] fileList = uploadDir.toFile().listFiles();
                if (fileList != null) {
                    DecimalFormat df = new DecimalFormat("#.##");
                    Arrays.sort(fileList, Comparator.comparing(File::lastModified).reversed());

                    for (File file : fileList) {
                        if (file.isFile() && isVideoFile(file.getName())) {
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("url", "http://" + serverExternalUrl + videoUrlPrefix + "/" + file.getName());

                            // 文件大小
                            long sizeInBytes = file.length();
                            String size;
                            if (sizeInBytes < 1024) {
                                size = sizeInBytes + " B";
                            } else if (sizeInBytes < 1024 * 1024) {
                                size = df.format(sizeInBytes / 1024.0) + " KB";
                            } else if (sizeInBytes < 1024 * 1024 * 1024) {
                                size = df.format(sizeInBytes / (1024.0 * 1024.0)) + " MB";
                            } else {
                                size = df.format(sizeInBytes / (1024.0 * 1024.0 * 1024.0)) + " GB";
                            }
                            fileInfo.put("size", size);

                            // 修改时间
                            fileInfo.put("lastModified", new Date(file.lastModified()));

                            // 添加使用状态信息
                            try {
                                Map<String, Object> usageInfo = videoReferenceService.checkVideoUsage(file.getName());
                                fileInfo.put("isUsed", usageInfo.get("isUsed"));
                                fileInfo.put("references", usageInfo.get("references"));
                            } catch (Exception e) {
                                logger.error("检查视频 {} 的使用状态时出错: {}", file.getName(), e.getMessage());
                                fileInfo.put("isUsed", false);
                                fileInfo.put("references", new ArrayList<>());
                            }

                            files.add(fileInfo);
                        }
                    }
                }
            }

            return ResponseEntity.ok(files);
        } catch (Exception e) {
            logger.error("获取视频文件列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(files);
        }
    }

    @DeleteMapping("/api/files/videos/{filename}")
    @ResponseBody
    public ResponseEntity<Map<String, String>> deleteVideoFile(@PathVariable String filename) {
        Map<String, String> response = new HashMap<>();
        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(videoUploadPath).isAbsolute()) {
                uploadDir = Paths.get(videoUploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, videoUploadPath).toAbsolutePath().normalize();
            }

            Path filePath = uploadDir.resolve(filename).normalize();

            // 安全检查：确保文件路径在uploadDir下
            if (!filePath.startsWith(uploadDir)) {
                response.put("error", "非法的文件路径");
                return ResponseEntity.badRequest().body(response);
            }

            if (Files.exists(filePath)) {
                Files.delete(filePath);
                response.put("success", "视频文件删除成功");
                logger.info("视频文件删除成功: {}", filename);
            } else {
                response.put("error", "视频文件不存在");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("删除视频文件失败: {}", filename, e);
            response.put("error", "删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    private boolean isVideoFile(String filename) {
        filename = filename.toLowerCase();
        return filename.endsWith(".mp4") || filename.endsWith(".webm") ||
               filename.endsWith(".ogg") || filename.endsWith(".avi") ||
               filename.endsWith(".mov") || filename.endsWith(".wmv") ||
               filename.endsWith(".flv") || filename.endsWith(".mkv");
    }
}