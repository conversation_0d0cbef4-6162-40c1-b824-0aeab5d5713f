package com.example.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 设备管理页面控制器
 * 提供设备管理相关的页面路由
 */
@Controller
@RequestMapping("/device")
public class DeviceManagementController {
    
    /**
     * 显示设备管理页面
     * 用户可以在此页面查看设备状态、监控项数值，并进行数据修改操作
     */
    @GetMapping("/management")
    public String showManagementPage() {
        return "device/management";
    }
}
