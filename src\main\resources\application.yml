server:
  port: 8080
  # 服务器外部访问地址，用于生成图片URL等资源链接
  # 可以是IP地址、域名或localhost，根据实际部署环境配置
  # 例如：*************:8080 或 example.com:8080
  external-url: 127.0.0.1:8080

spring:
  application:
    name: shengda-smart-management-system
  thymeleaf:
    cache: false
  web:
    resources:
      static-locations: classpath:/static/, file:${upload.image.path}
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: ${upload.image.max-size}
      max-request-size: ${upload.image.max-size}
      enabled: true
  # MySQL数据库配置
  datasource:
    url: ********************************************************************************************************
    username: root
    password: nothing
    driver-class-name: com.mysql.cj.jdbc.Driver
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: true

# 日志配置
logging:
  level:
    com.example: DEBUG
    org.springframework.web: DEBUG
    org.thymeleaf: DEBUG
    root: INFO

# Modbus TCP配置
modbus:
  host: localhost
  port: 502
  timeout: 3000

# MQTT配置
mqtt:
  client-id: modbus-mqtt-web
  connection-timeout: 30
  keep-alive-interval: 60
  clean-session: true

# 文件上传配置
upload:
  image:
    # 图片存储路径，支持绝对路径（如：D:/images）或相对路径（如：upload/images）
    path: D:/images
    # 图片访问路径前缀，用于构建图片URL
    url-prefix: /images
    max-size: 10MB
    allowed-types: image/jpeg,image/png,image/gif
  material:
    # 素材存储路径，支持绝对路径（如：D:/materials）或相对路径（如：upload/materials）
    path: D:/materials
    # 素材访问路径前缀，用于构建素材URL
    url-prefix: /materials
    max-size: 50MB
    allowed-types: image/jpeg,image/png,image/gif,image/webp,image/apng
  video:
    # 视频存储路径，支持绝对路径（如：D:/videos）或相对路径（如：upload/videos）
    path: D:/videos
    # 视频访问路径前缀，用于构建视频URL
    url-prefix: /videos
    max-size: 100MB
    allowed-types: video/mp4,video/webm,video/ogg,video/avi,video/mov,video/wmv,video/flv