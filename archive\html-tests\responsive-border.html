<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            width: 100%;
            height: 100%;
        }
        
        .responsive-border-container {
            width: 100%;
            height: 100%;
            padding: 2%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .dynamic-border-box {
            position: relative;
            width: 100%;
            height: 100%;
            max-width: 600px;
            max-height: 400px;
            min-width: 200px;
            min-height: 150px;
            background: transparent;
            border-radius: 2%;
            padding: 1%;
            background: linear-gradient(45deg, 
                #ff00cc, #00ccff, #00ff00, #ff0000, 
                #ff00cc, #00ccff, #00ff00, #ff0000);
            background-size: 400%;
            animation: animate-border 8s linear infinite;
        }
        
        @keyframes animate-border {
            0% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
            50% {
                background-position: 300% 0;
                filter: hue-rotate(360deg);
            }
            100% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
        }
        
        .content {
            background: transparent;
            border-radius: 1.5%;
            padding: 5%;
            text-align: center;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        h1 {
            font-size: clamp(1.2rem, 5vw, 3rem);
            margin-bottom: 0.5em;
            background: linear-gradient(90deg, #ff00cc, #00ccff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 0.5em rgba(255, 0, 204, 0.5);
        }
        
        p {
            color: #00ccff;
            line-height: 1.6;
            text-shadow: 0 0 0.4em rgba(0, 204, 255, 0.6);
            font-size: clamp(0.8rem, 3vw, 1.2rem);
            max-width: 80%;
        }
        
        /* 悬停效果 */
        .dynamic-border-box:hover {
            animation-duration: 4s;
        }
        
        /* 容器适配样式 */
        .fill-container {
            width: 100% !important;
            height: 100% !important;
            max-width: none !important;
            max-height: none !important;
        }
        
        /* 响应式断点 */
        @media (max-width: 480px) {
            .responsive-border-container {
                padding: 1%;
            }
            
            .dynamic-border-box {
                border-radius: 3%;
                padding: 0.5%;
            }
            
            .content {
                padding: 3%;
                border-radius: 2.5%;
            }
        }
        
        @media (min-width: 1200px) {
            .content {
                padding: 6%;
            }
        }
        
        /* 纵横比保持 */
        .aspect-ratio-16-9 {
            aspect-ratio: 16/9;
            height: auto;
        }
        
        .aspect-ratio-4-3 {
            aspect-ratio: 4/3;
            height: auto;
        }
        
        .aspect-ratio-1-1 {
            aspect-ratio: 1/1;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="responsive-border-container">
        <div class="dynamic-border-box">
            <div class="content">
                <h1>响应式边框</h1>
                <p>这个边框会根据容器大小自动缩放，保持完美的比例和视觉效果。</p>
            </div>
        </div>
    </div>
</body>
</html>
