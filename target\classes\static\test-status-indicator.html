<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>状态指示器组件测试</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bi-status-indicator.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .indicator-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: center;
        }
        .indicator-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        .indicator-label {
            font-size: 14px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>状态指示器组件测试</h1>
        
        <div class="test-container">
            <h3>基本形状测试</h3>
            <div class="indicator-row">
                <div class="indicator-wrapper">
                    <div id="circle-test"></div>
                    <div class="indicator-label">圆形</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="square-test"></div>
                    <div class="indicator-label">正方形</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="rectangle-test"></div>
                    <div class="indicator-label">矩形</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="triangle-test"></div>
                    <div class="indicator-label">三角形</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="diamond-test"></div>
                    <div class="indicator-label">菱形</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="hexagon-test"></div>
                    <div class="indicator-label">六边形</div>
                </div>
            </div>
        </div>

        <div class="test-container">
            <h3>状态测试</h3>
            <div class="indicator-row">
                <div class="indicator-wrapper">
                    <div id="status-normal"></div>
                    <div class="indicator-label">正常状态</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="status-warning"></div>
                    <div class="indicator-label">警告状态</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="status-danger"></div>
                    <div class="indicator-label">危险状态</div>
                </div>
                <div class="indicator-wrapper">
                    <div id="status-offline"></div>
                    <div class="indicator-label">离线状态</div>
                </div>
            </div>
        </div>

        <div class="test-container">
            <h3>动态测试</h3>
            <div class="indicator-row">
                <div class="indicator-wrapper">
                    <div id="dynamic-test"></div>
                    <div class="indicator-label">动态状态切换</div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="switchStatus()">切换状态</button>
        </div>
    </div>

    <script src="/js/bi-status-indicator.js"></script>
    <script>
        // 测试配置
        const testConfig = {
            shape: 'circle',
            size: 60,
            condition1: { min: 0, max: 30, name: '正常', color: '#28a745' },
            condition2: { min: 31, max: 70, name: '警告', color: '#ffc107' },
            condition3: { min: 71, max: 100, name: '危险', color: '#dc3545' },
            offlineColor: '#6c757d',
            showConditionName: true
        };

        // 创建测试实例
        const indicators = {};

        // 基本形状测试
        const shapes = ['circle', 'square', 'rectangle', 'triangle', 'diamond', 'hexagon'];
        shapes.forEach(shape => {
            const config = { ...testConfig, shape: shape };
            const data = { value: 25 }; // 正常状态
            indicators[shape] = new BiStatusIndicator(`${shape}-test`, config);
            indicators[shape].render(data);
        });

        // 状态测试
        const statusTests = [
            { id: 'status-normal', value: 25 },    // 正常
            { id: 'status-warning', value: 50 },   // 警告
            { id: 'status-danger', value: 85 },    // 危险
            { id: 'status-offline', deviceOffline: true } // 离线
        ];

        statusTests.forEach(test => {
            const config = { ...testConfig };
            indicators[test.id] = new BiStatusIndicator(test.id, config);
            indicators[test.id].render(test);
        });

        // 动态测试
        let currentStatus = 0;
        const statusValues = [25, 50, 85, null]; // null表示离线
        const statusNames = ['正常', '警告', '危险', '离线'];

        indicators['dynamic'] = new BiStatusIndicator('dynamic-test', testConfig);
        indicators['dynamic'].render({ value: statusValues[0] });

        function switchStatus() {
            currentStatus = (currentStatus + 1) % statusValues.length;
            const value = statusValues[currentStatus];
            const data = value === null ? { deviceOffline: true } : { value: value };
            
            indicators['dynamic'].render(data);
            
            // 更新标签
            const label = document.querySelector('#dynamic-test').parentElement.querySelector('.indicator-label');
            label.textContent = `动态状态切换 - ${statusNames[currentStatus]}`;
        }

        console.log('状态指示器测试页面加载完成');
        console.log('创建的指示器实例:', Object.keys(indicators));
    </script>
</body>
</html>
