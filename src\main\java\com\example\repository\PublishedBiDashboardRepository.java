package com.example.repository;

import com.example.entity.PublishedBiDashboard;
import com.example.entity.BiDashboard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface PublishedBiDashboardRepository extends JpaRepository<PublishedBiDashboard, Long> {
    
    // 根据访问令牌查找发布的大屏
    Optional<PublishedBiDashboard> findByAccessToken(String accessToken);
    
    // 根据大屏ID查找所有发布记录
    List<PublishedBiDashboard> findByDashboardId(Long dashboardId);
    
    // 查找所有有效的发布记录
    @Query("SELECT p FROM PublishedBiDashboard p WHERE p.status = 'ACTIVE' AND (p.expiryDate IS NULL OR p.expiryDate > :currentDate)")
    List<PublishedBiDashboard> findAllActive(@Param("currentDate") Date currentDate);
    
    // 查找所有过期的发布记录
    @Query("SELECT p FROM PublishedBiDashboard p WHERE p.status = 'ACTIVE' AND p.expiryDate IS NOT NULL AND p.expiryDate <= :currentDate")
    List<PublishedBiDashboard> findAllExpired(@Param("currentDate") Date currentDate);
    
    // 根据大屏查找有效的发布记录
    @Query("SELECT p FROM PublishedBiDashboard p WHERE p.dashboard = :dashboard AND p.status = 'ACTIVE' AND (p.expiryDate IS NULL OR p.expiryDate > :currentDate)")
    List<PublishedBiDashboard> findActiveByDashboard(@Param("dashboard") BiDashboard dashboard, @Param("currentDate") Date currentDate);
    
    // 检查大屏是否已发布
    @Query("SELECT COUNT(p) > 0 FROM PublishedBiDashboard p WHERE p.dashboard.id = :dashboardId AND p.status = 'ACTIVE' AND (p.expiryDate IS NULL OR p.expiryDate > :currentDate)")
    boolean existsActiveByDashboardId(@Param("dashboardId") Long dashboardId, @Param("currentDate") Date currentDate);
    
    // 按发布时间倒序查找所有发布记录
    List<PublishedBiDashboard> findAllByOrderByPublishedAtDesc();
    
    // 根据状态查找发布记录
    List<PublishedBiDashboard> findByStatusOrderByPublishedAtDesc(String status);
}
