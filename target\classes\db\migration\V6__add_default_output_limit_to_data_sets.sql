-- 为数据集表添加默认输出限制字段
-- 执行时间：2025-07-23

-- 检查列是否已存在的存储过程
DELIMITER //

CREATE PROCEDURE add_default_output_limit_if_not_exist()
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    -- 检查列是否存在
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'data_sets'
    AND COLUMN_NAME = 'default_output_limit';
    
    -- 如果列不存在，则添加
    IF column_exists = 0 THEN
        ALTER TABLE data_sets ADD COLUMN default_output_limit INT COMMENT '默认输出限制（记录数量），null表示不限制';
    END IF;
END //

DELIMITER ;

-- 执行存储过程
CALL add_default_output_limit_if_not_exist();

-- 删除存储过程
DROP PROCEDURE IF EXISTS add_default_output_limit_if_not_exist;

-- 添加注释说明
ALTER TABLE data_sets MODIFY COLUMN default_output_limit INT COMMENT '默认输出限制（记录数量），null表示不限制，正整数表示最大输出行数';
