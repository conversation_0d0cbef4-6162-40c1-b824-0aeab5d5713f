<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态按钮</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .dynamic-button {
            position: relative;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: bold;
            color: #00d4ff;
            background: transparent;
            border: 2px solid #00d4ff;
            border-radius: 10px;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        /* 背景发光效果 */
        .dynamic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(0, 212, 255, 0.2), 
                transparent);
            transition: left 0.5s;
        }
        
        /* 边框发光动画 */
        .dynamic-button::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, 
                #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff);
            background-size: 400%;
            border-radius: 12px;
            z-index: -1;
            opacity: 0;
            animation: border-flow 3s linear infinite;
            transition: opacity 0.3s;
        }
        
        /* 悬停效果 */
        .dynamic-button:hover::before {
            left: 100%;
        }
        
        .dynamic-button:hover::after {
            opacity: 1;
        }
        
        .dynamic-button:hover {
            color: #ffffff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 
                0 0 20px rgba(0, 212, 255, 0.4),
                0 0 40px rgba(0, 212, 255, 0.2);
            transform: translateY(-2px);
        }
        
        /* 点击效果 */
        .dynamic-button:active {
            transform: translateY(0);
            box-shadow: 
                0 0 15px rgba(0, 212, 255, 0.6),
                0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        /* 边框流动动画 */
        @keyframes border-flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 按钮内部装饰 */
        .button-decoration {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #00ffff;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .decoration-1 {
            top: 8px;
            left: 8px;
            animation: dot-pulse 2s ease-in-out infinite;
        }
        
        .decoration-2 {
            top: 8px;
            right: 8px;
            animation: dot-pulse 2s ease-in-out infinite 0.5s;
        }
        
        .decoration-3 {
            bottom: 8px;
            left: 8px;
            animation: dot-pulse 2s ease-in-out infinite 1s;
        }
        
        .decoration-4 {
            bottom: 8px;
            right: 8px;
            animation: dot-pulse 2s ease-in-out infinite 1.5s;
        }
        
        .dynamic-button:hover .button-decoration {
            opacity: 1;
        }
        
        @keyframes dot-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* 文字发光效果 */
        .dynamic-button:hover {
            text-shadow: 
                0 0 10px rgba(0, 212, 255, 0.8),
                0 0 20px rgba(0, 212, 255, 0.6),
                0 0 30px rgba(0, 212, 255, 0.4);
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .dynamic-button {
                padding: 12px 30px;
                font-size: 16px;
                letter-spacing: 1px;
            }
            
            .button-decoration {
                width: 4px;
                height: 4px;
            }
            
            .decoration-1, .decoration-2 { top: 6px; }
            .decoration-3, .decoration-4 { bottom: 6px; }
            .decoration-1, .decoration-3 { left: 6px; }
            .decoration-2, .decoration-4 { right: 6px; }
        }
    </style>
</head>
<body>
    <button class="dynamic-button">
        <span class="button-decoration decoration-1"></span>
        <span class="button-decoration decoration-2"></span>
        <span class="button-decoration decoration-3"></span>
        <span class="button-decoration decoration-4"></span>
        点击我
    </button>
</body>
</html>
