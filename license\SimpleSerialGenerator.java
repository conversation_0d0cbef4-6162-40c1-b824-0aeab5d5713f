import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Scanner;

/**
 * Simple Serial Number Generator - Console Version
 * Used to generate encrypted serial numbers for SDPLC system
 */
public class SimpleSerialGenerator {
    
    // Constants definition (consistent with LicenseUtils)
    private static final String SERIAL_PREFIX = "SDPLC-";
    private static final String AES_KEY = "SDPLC2024LICENSE";
    private static final String DATE_FORMAT = "yyyyMMdd";
    

    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== SDPLC Serial Number Generator ===");
        System.out.println();
        
        try {
            // Get user input
            System.out.print("Enter User ID (e.g., USER001): ");
            String userId = scanner.nextLine().trim();
            
            System.out.print("Enter Start Date (YYYYMMDD, default today): ");
            String startDateInput = scanner.nextLine().trim();
            String startDate = startDateInput.isEmpty() ? 
                LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT)) : startDateInput;
            
            System.out.print("Enter End Date (YYYYMMDD, default tomorrow): ");
            String endDateInput = scanner.nextLine().trim();
            String endDate = endDateInput.isEmpty() ? 
                LocalDate.now().plusDays(1).format(DateTimeFormatter.ofPattern(DATE_FORMAT)) : endDateInput;
            
            // Generate serial number
            String serialNumber = generateSerialNumber(userId, startDate, endDate);
            
            System.out.println();
            System.out.println("=== Generated Serial Number ===");
            System.out.println("User ID: " + userId);
            System.out.println("Valid From: " + startDate);
            System.out.println("Valid To: " + endDate);
            System.out.println("Serial Number: " + serialNumber);
            System.out.println();
            
            // Verify the generated serial number
            System.out.println("=== Verification ===");
            boolean isValid = validateSerialNumber(userId, serialNumber);
            System.out.println("Validation Result: " + (isValid ? "VALID" : "INVALID"));
            
            if (isValid) {
                String[] data = parseSerialNumber(serialNumber);
                System.out.println("Parsed Data - UserId: " + data[0] + ", StartDate: " + data[1] +
                                 ", EndDate: " + data[2] + ", Checksum: " + data[3]);
            }
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * Generate serial number
     */
    private static String generateSerialNumber(String userId, String startDate, String endDate) {
        try {
            // Generate checksum
            String checksum = generateChecksum(userId + startDate + endDate);

            // Construct JSON manually
            String json = String.format(
                "{\"userId\":\"%s\",\"startDate\":\"%s\",\"endDate\":\"%s\",\"checksum\":\"%s\"}",
                userId, startDate, endDate, checksum
            );
            
            // AES encrypt
            byte[] encrypted = aesEncrypt(json);
            
            // Base64 encode and add prefix
            return SERIAL_PREFIX + Base64.getEncoder().encodeToString(encrypted);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate serial number", e);
        }
    }
    
    /**
     * Parse serial number
     */
    private static String[] parseSerialNumber(String serialNumber) {
        try {
            // Check prefix
            if (!serialNumber.startsWith(SERIAL_PREFIX)) {
                throw new IllegalArgumentException("Invalid serial number format");
            }

            // Remove prefix and decode
            String base64Data = serialNumber.substring(SERIAL_PREFIX.length());
            byte[] encrypted = Base64.getDecoder().decode(base64Data);

            // AES decrypt
            String json = aesDecrypt(encrypted);

            // Parse JSON manually (simple parsing)
            String userId = extractJsonValue(json, "userId");
            String startDate = extractJsonValue(json, "startDate");
            String endDate = extractJsonValue(json, "endDate");
            String checksum = extractJsonValue(json, "checksum");

            return new String[]{userId, startDate, endDate, checksum};

        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid serial number", e);
        }
    }

    /**
     * Simple JSON value extraction
     */
    private static String extractJsonValue(String json, String key) {
        String pattern = "\"" + key + "\":\"";
        int start = json.indexOf(pattern);
        if (start == -1) return null;
        start += pattern.length();
        int end = json.indexOf("\"", start);
        return json.substring(start, end);
    }
    
    /**
     * Validate serial number
     */
    private static boolean validateSerialNumber(String inputUserId, String serialNumber) {
        try {
            // Parse serial number
            String[] data = parseSerialNumber(serialNumber);

            String serialUserId = data[0];
            String startDate = data[1];
            String endDate = data[2];
            String checksum = data[3];
            
            // Validate user ID
            if (!inputUserId.equals(serialUserId)) {
                return false;
            }
            
            // Validate date range
            LocalDate today = LocalDate.now();
            LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(DATE_FORMAT));
            
            if (today.isBefore(start) || today.isAfter(end)) {
                return false;
            }
            
            // Validate checksum
            String expectedChecksum = generateChecksum(serialUserId + startDate + endDate);
            return checksum.equals(expectedChecksum);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Generate checksum
     */
    private static String generateChecksum(String input) {
        return md5(input).substring(0, 6).toUpperCase();
    }
    
    /**
     * AES encrypt
     */
    private static byte[] aesEncrypt(String plainText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * AES decrypt
     */
    private static String aesDecrypt(byte[] encrypted) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    /**
     * MD5 hash
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 calculation failed", e);
        }
    }
}
