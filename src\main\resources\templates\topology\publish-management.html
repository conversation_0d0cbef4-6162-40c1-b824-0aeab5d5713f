<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组态布局发布管理</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --transition: all 0.3s ease;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
        }

        /* 导航条美化样式 */
        .navbar.navbar-expand-lg {
            background: var(--primary-gradient) !important;
            background-color: transparent !important;
            padding: 0.5rem 1rem !important;
            margin-bottom: 1rem !important;
            height: 56px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            border: none !important;
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
        }

        .navbar .text-white {
            color: white !important;
            margin-right: 15px;
            font-weight: 500;
        }

        .navbar .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: var(--transition);
            font-weight: 500;
        }

        .navbar .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
            color: white;
        }

        .navbar .btn-outline-light.active {
            background-color: rgba(255, 255, 255, 0.3);
            border-color: white;
            color: white;
        }

        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
            border-radius: 16px 16px 0 0;
            padding: 1.5rem;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-body {
            padding: 2rem;
        }

        .table th, .table td {
            vertical-align: middle;
            padding: 1rem 0.75rem;
            border: none;
            border-bottom: 1px solid #f1f3f4;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .status-expired {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        .status-revoked {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .access-url {
            max-width: 250px;
            word-break: break-all;
        }

        .expiry-date {
            width: 150px;
        }

        .action-buttons {
            width: 120px;
        }

        .btn {
            border-radius: 0.375rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-primary {
            color: #667eea;
            border: 2px solid #667eea;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }

        .btn-outline-secondary {
            color: #6c757d;
            border: 2px solid #6c757d;
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border-color: transparent;
        }

        .btn-outline-danger {
            color: #dc3545;
            border: 2px solid #dc3545;
            background: transparent;
        }

        .btn-outline-danger:hover {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            border-color: transparent;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .main-container {
            padding: 2rem;
        }

        h4 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .table-responsive {
            border-radius: 0.375rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 模态框美化 */
        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
            border-radius: 16px 16px 0 0;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .card-body {
                padding: 1.5rem;
            }

            .table th,
            .table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- 引入导航条片段 -->
    <nav th:replace="fragments/navbar :: navbar('组态布局发布管理', 'topology', true, true, true, null)"></nav>

    <div class="container-fluid main-container">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>组态布局发布管理</h4>
                    <a href="/topology" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-arrow-left"></i> 返回组态编辑
                    </a>
                </div>
                
                <!-- 组态布局选择和发布表单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">发布新组态布局</h5>
                    </div>
                    <div class="card-body">
                        <form id="publishForm" class="row g-3">
                            <div class="col-md-6">
                                <label for="topologyId" class="form-label">选择组态布局</label>
                                <select class="form-select" id="topologyId" required>
                                    <option value="">-- 请选择组态布局 --</option>
                                    <option th:each="topology : ${topologies}" 
                                            th:value="${topology.id}" 
                                            th:text="${topology.name}"></option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="expiryDays" class="form-label">有效期</label>
                                <select class="form-select" id="expiryDays">
                                    <option value="1">1天</option>
                                    <option value="7">7天</option>
                                    <option value="30" selected>30天</option>
                                    <option value="">永久有效</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-share"></i> 发布
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 已发布组态布局列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">已发布组态布局列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>发布时间</th>
                                        <th>有效期至</th>
                                        <th>状态</th>
                                        <th>访问地址</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="publishedListBody">
                                    <tr th:each="published : ${publishedList}">
                                        <td th:text="${published.name}">默认布局</td>
                                        <td th:text="${#dates.format(published.publishedAt, 'yyyy-MM-dd HH:mm')}">2023-01-01</td>
                                        <td class="expiry-date" th:text="${published.expiryDate != null ? #dates.format(published.expiryDate, 'yyyy-MM-dd HH:mm') : '永久有效'}">2023-01-08</td>
                                        <td>
                                            <span class="status-badge" 
                                                  th:classappend="${'status-' + published.status.toLowerCase()}"
                                                  th:text="${published.status == 'ACTIVE' ? '有效' : '已过期'}">
                                                有效
                                            </span>
                                        </td>
                                        <td class="access-url">
                                            <a th:href="${published.accessUrl}" target="_blank" th:text="${published.accessUrl}">
                                                http://example.com/published/token
                                            </a>
                                        </td>
                                        <td class="action-buttons">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a th:href="${published.accessUrl}" target="_blank" class="btn btn-outline-primary" title="访问">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button class="btn btn-outline-secondary copy-btn" 
                                                        th:data-url="${published.accessUrl}" 
                                                        title="复制链接">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                                <button class="btn btn-outline-danger revoke-btn"
                                                        th:data-id="${published.id}"
                                                        title="删除">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr th:if="${publishedList.empty}">
                                        <td colspan="6" class="text-center">暂无已发布的组态布局</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 发布结果模态框 -->
    <div class="modal fade" id="publishResultModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发布成功</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">访问地址</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="resultAccessUrl" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="copyAccessUrlBtn">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">有效期至</label>
                        <input type="text" class="form-control" id="resultExpiryDate" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a href="#" class="btn btn-primary" id="viewPublishedLink" target="_blank">查看</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div class="modal fade" id="revokeConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除此发布记录吗？删除后，该记录将彻底移除且无法恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmRevokeBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 发布表单提交
            const publishForm = document.getElementById('publishForm');
            publishForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const topologyId = document.getElementById('topologyId').value;
                const expiryDays = document.getElementById('expiryDays').value;
                
                if (!topologyId) {
                    alert('请选择组态布局');
                    return;
                }
                
                try {
                    const response = await fetch('/api/topology/publish', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            topologyId: topologyId,
                            expiryDays: expiryDays ? parseInt(expiryDays) : null
                        })
                    });
                    
                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || '发布失败');
                    }
                    
                    const result = await response.json();
                    
                    // 显示发布结果
                    document.getElementById('resultAccessUrl').value = result.accessUrl;
                    document.getElementById('resultExpiryDate').value = result.expiryDate 
                        ? new Date(result.expiryDate).toLocaleString() 
                        : '永久有效';
                    document.getElementById('viewPublishedLink').href = result.accessUrl;
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('publishResultModal'));
                    modal.show();
                    
                    // 刷新页面以显示新发布的布局
                    setTimeout(() => window.location.reload(), 1000);
                } catch (error) {
                    alert(error.message || '发布失败，请重试');
                }
            });
            
            // 复制访问链接
            document.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const url = this.getAttribute('data-url');
                    navigator.clipboard.writeText(url)
                        .then(() => {
                            // 修改按钮样式显示复制成功
                            const icon = this.querySelector('i');
                            icon.classList.remove('bi-clipboard');
                            icon.classList.add('bi-check');
                            setTimeout(() => {
                                icon.classList.remove('bi-check');
                                icon.classList.add('bi-clipboard');
                            }, 2000);
                        })
                        .catch(err => {
                            console.error('复制失败:', err);
                            alert('复制失败，请手动复制');
                        });
                });
            });
            
            // 复制结果对话框中的访问链接
            document.getElementById('copyAccessUrlBtn').addEventListener('click', function() {
                const input = document.getElementById('resultAccessUrl');
                navigator.clipboard.writeText(input.value)
                    .then(() => {
                        const icon = this.querySelector('i');
                        icon.classList.remove('bi-clipboard');
                        icon.classList.add('bi-check');
                        setTimeout(() => {
                            icon.classList.remove('bi-check');
                            icon.classList.add('bi-clipboard');
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('复制失败:', err);
                        alert('复制失败，请手动复制');
                    });
            });
            
            // 删除发布记录
            let currentRevokeId = null;

            document.querySelectorAll('.revoke-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    currentRevokeId = this.getAttribute('data-id');
                    const modal = new bootstrap.Modal(document.getElementById('revokeConfirmModal'));
                    modal.show();
                });
            });

            document.getElementById('confirmRevokeBtn').addEventListener('click', async function() {
                if (!currentRevokeId) return;

                try {
                    const response = await fetch(`/api/topology/revoke/${currentRevokeId}`, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || '删除失败');
                    }

                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('revokeConfirmModal')).hide();

                    // 刷新页面
                    window.location.reload();
                } catch (error) {
                    alert(error.message || '删除失败，请重试');
                }
            });
        });
    </script>
</body>
</html> 