<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版响应式动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }
        
        .border-container {
            width: 100%;
            height: 100%;
            padding: 3%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .dynamic-border {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 8px;
            background: linear-gradient(45deg, 
                #ff00cc, #00ccff, #00ff00, #ff0000, 
                #ff00cc, #00ccff, #00ff00, #ff0000);
            background-size: 400%;
            animation: border-flow 8s linear infinite;
            padding: 4px;
        }
        
        @keyframes border-flow {
            0% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
            50% {
                background-position: 300% 0;
                filter: hue-rotate(360deg);
            }
            100% {
                background-position: 0 0;
                filter: hue-rotate(0deg);
            }
        }
        
        .content {
            background: transparent;
            border-radius: 4px;
            padding: 8%;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .title {
            font-size: 1.5rem;
            margin-bottom: 0.5em;
            background: linear-gradient(90deg, #ff00cc, #00ccff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 0, 204, 0.5);
        }
        
        .subtitle {
            font-size: 0.9rem;
            color: #00ccff;
            text-shadow: 0 0 5px rgba(0, 204, 255, 0.6);
            line-height: 1.4;
        }
        
        /* 悬停效果 */
        .dynamic-border:hover {
            animation-duration: 4s;
        }
        
        /* 响应式设计 - 基于媒体查询 */
        @media (max-width: 300px) {
            .border-container {
                padding: 2%;
            }
            
            .dynamic-border {
                border-radius: 4px;
                padding: 2px;
            }
            
            .content {
                padding: 5%;
                border-radius: 2px;
            }
            
            .title {
                font-size: 1rem;
                margin-bottom: 0.3em;
            }
            
            .subtitle {
                font-size: 0.7rem;
            }
        }
        
        @media (min-width: 500px) {
            .border-container {
                padding: 4%;
            }
            
            .dynamic-border {
                border-radius: 12px;
                padding: 6px;
            }
            
            .content {
                padding: 10%;
                border-radius: 8px;
            }
            
            .title {
                font-size: 2rem;
                margin-bottom: 0.7em;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
        }
        
        @media (min-width: 800px) {
            .title {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.3rem;
            }
        }
        
        /* 额外的装饰效果 */
        .dynamic-border::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: center-pulse 3s ease-in-out infinite;
        }
        
        @keyframes center-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(0.5);
            }
            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1.5);
            }
        }
        
        /* 角落装饰 */
        .corner-accent {
            position: absolute;
            width: 15px;
            height: 15px;
            border: 2px solid rgba(255, 255, 255, 0.6);
        }
        
        .corner-tl {
            top: 10px;
            left: 10px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner-tr {
            top: 10px;
            right: 10px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner-bl {
            bottom: 10px;
            left: 10px;
            border-right: none;
            border-top: none;
        }
        
        .corner-br {
            bottom: 10px;
            right: 10px;
            border-left: none;
            border-top: none;
        }
    </style>
</head>
<body>
    <div class="border-container">
        <div class="dynamic-border">
            <div class="corner-accent corner-tl"></div>
            <div class="corner-accent corner-tr"></div>
            <div class="corner-accent corner-bl"></div>
            <div class="corner-accent corner-br"></div>
            
            <div class="content">
                <h1 class="title">动态边框</h1>
                <p class="subtitle">自适应容器的彩色边框效果</p>
            </div>
        </div>
    </div>
</body>
</html>
