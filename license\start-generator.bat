@echo off
title SDPLC Serial Number Generator

echo.
echo ========================================
echo    SDPLC Serial Number Generator
echo ========================================
echo.

echo Checking Java environment...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found!
    echo Please install Java 8 or higher and configure PATH environment variable.
    echo.
    pause
    exit /b 1
)

echo Java environment check passed
echo.

echo Compiling serial number generator...
javac -encoding UTF-8 "d:\sdplc\license\SDPLCSerialGenerator.java"
if %errorlevel% neq 0 (
    echo Compilation failed! Please check Java environment and source code.
    echo.
    pause
    exit /b 1
)

echo Compilation successful
echo.

echo Starting serial number generator...
echo.
echo Usage Instructions:
echo   - User ID: Only supports uppercase letters and numbers
echo   - Date format: YYYYMMDD (example: 20250728)
echo   - Installation validity: Time window for serial number activation
echo.

echo Starting GUI application...
cd /d "d:\sdplc\license"
start "SDPLC Serial Number Generator" java SDPLCSerialGenerator

echo.
echo Serial number generator started successfully!
echo.
echo If the program window does not appear, please check:
echo 1. Java version is 8 or higher
echo 2. System supports Swing GUI
echo 3. Firewall is not blocking the program
echo 4. Display settings allow new windows
echo.

echo Press any key to exit this console...
pause >nul
