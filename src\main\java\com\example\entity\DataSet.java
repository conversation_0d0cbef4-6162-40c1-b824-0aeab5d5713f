package com.example.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 数据集实体
 * 基于数据源创建的数据集，定义具体的查询和数据转换逻辑
 */
@Entity
@Table(name = "data_sets")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSet {
    
    @Id
    @Column(length = 50)
    private String id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    /**
     * 关联的数据源ID
     */
    @Column(nullable = false, length = 50)
    private String dataSourceId;
    
    /**
     * 数据源名称（冗余字段，便于显示）
     */
    @Column(length = 100)
    private String dataSourceName;
    
    /**
     * 查询配置（JSON格式）
     * 数据库：{sql, parameters}
     * API：{endpoint, method, parameters, headers}
     * 文件：{sheetName, range, filters}
     */
    @Column(columnDefinition = "TEXT")
    private String queryConfig;
    
    /**
     * 数据转换配置（JSON格式）
     * {fieldMappings, calculations, filters, sorting}
     */
    @Column(columnDefinition = "TEXT")
    private String transformConfig;
    
    /**
     * 缓存配置（JSON格式）
     * {enabled, ttl, refreshInterval}
     */
    @Column(columnDefinition = "TEXT")
    private String cacheConfig;
    
    /**
     * 数据类型：realtime, historical, static
     */
    @Column(length = 20)
    private String dataType = "realtime";
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuteTime;
    
    /**
     * 最后执行结果
     */
    @Column(length = 500)
    private String lastExecuteResult;
    
    /**
     * 数据行数（最后一次查询结果）
     */
    private Integer dataRowCount;

    /**
     * 默认输出限制（记录数量），null表示不限制
     */
    private Integer defaultOutputLimit;

    /**
     * 创建时间
     */
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(length = 50)
    private String updatedBy;
}
