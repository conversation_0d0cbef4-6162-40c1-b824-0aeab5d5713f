package com.example.repository;

import com.example.entity.BiWidget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BiWidgetRepository extends JpaRepository<BiWidget, Long> {
    
    /**
     * 根据大屏ID查找所有组件
     */
    List<BiWidget> findByDashboardIdOrderByCreatedAt(Long dashboardId);

    /**
     * 根据大屏ID和组件类型查找组件
     */
    List<BiWidget> findByDashboardIdAndWidgetType(Long dashboardId, String widgetType);

    /**
     * 删除指定大屏的所有组件
     */
    void deleteByDashboardId(Long dashboardId);

    /**
     * 统计指定大屏的组件数量
     */
    @Query("SELECT COUNT(w) FROM BiWidget w WHERE w.dashboardId = :dashboardId")
    long countByDashboardId(@Param("dashboardId") Long dashboardId);
}
