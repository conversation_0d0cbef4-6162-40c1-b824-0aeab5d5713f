<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤2布局调整验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .config-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            min-height: 200px;
        }
        
        .row-label {
            background: #e9ecef;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            font-weight: bold;
            color: #495057;
        }
        
        .demo-content {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .height-indicator {
            position: absolute;
            right: 10px;
            top: 10px;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.75rem;
        }
        
        .comparison-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            background: white;
        }
        
        .before-after {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .before, .after {
            flex: 1;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .before {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .after {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-layout-three-columns"></i>
                步骤2布局调整验证
            </h2>
            
            <!-- 布局说明 -->
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 布局调整说明</h6>
                <p class="mb-0">将输出限制、日期格式化、聚合配置三个功能从筛选条件下方移出，创建独立的第二行，避免数据表选择和字段配置下方出现大的空白区域。</p>
            </div>
            
            <!-- 调整前后对比 -->
            <div class="comparison-section">
                <h5><i class="bi bi-arrow-left-right"></i> 布局对比</h5>
                <div class="before-after">
                    <div class="before">
                        <h6><i class="bi bi-x-circle text-warning"></i> 调整前</h6>
                        <ul class="mb-0">
                            <li>数据表选择 (col-md-3) - 内容少，下方大片空白</li>
                            <li>字段配置 (col-md-4) - 内容少，下方大片空白</li>
                            <li>筛选条件 (col-md-5) - 内容多，挤着三个子配置</li>
                            <li>视觉不平衡，空间利用率低</li>
                        </ul>
                    </div>
                    <div class="after">
                        <h6><i class="bi bi-check-circle text-success"></i> 调整后</h6>
                        <ul class="mb-0">
                            <li>第一行：数据表选择、字段配置、筛选条件</li>
                            <li>第二行：输出限制、日期格式化、聚合配置</li>
                            <li>每个功能独立占用 col-md-4 宽度</li>
                            <li>视觉平衡，空间利用率高</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 新布局演示 -->
            <div class="comparison-section">
                <h5><i class="bi bi-grid-3x2"></i> 新布局演示</h5>
                
                <!-- 第一行：主配置区域 -->
                <div class="row-label">第一行：主配置区域</div>
                <div class="row mb-4">
                    <!-- 数据表选择 -->
                    <div class="col-md-3">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-3</div>
                            <label class="form-label fw-bold">数据表选择</label>
                            <div class="demo-content">
                                <p>• 显示可用数据表列表</p>
                                <p>• 支持表名搜索和筛选</p>
                                <p>• 显示表的基本信息</p>
                                <p>• 内容相对较少</p>
                            </div>
                        </div>
                    </div>

                    <!-- 字段配置 -->
                    <div class="col-md-4">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-4</div>
                            <label class="form-label fw-bold">字段配置</label>
                            <div class="demo-content">
                                <p>• 显示表字段列表</p>
                                <p>• 配置字段角色（标签/数值）</p>
                                <p>• 字段类型和描述</p>
                                <p>• 重置字段选择功能</p>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="col-md-5">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-5</div>
                            <label class="form-label fw-bold">筛选条件</label>
                            <div class="demo-content">
                                <p>• 动态添加筛选条件</p>
                                <p>• 支持多种操作符</p>
                                <p>• 条件组合和逻辑关系</p>
                                <p>• 现在只包含筛选功能，不再拥挤</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行：功能配置区域 -->
                <div class="row-label">第二行：功能配置区域</div>
                <div class="row mb-4">
                    <!-- 输出限制配置 -->
                    <div class="col-md-4">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-4</div>
                            <label class="form-label fw-bold">输出限制</label>
                            <div class="demo-content">
                                <p>• 设置最大记录数</p>
                                <p>• 数值输入框 (1-10000)</p>
                                <p>• 提高查询性能</p>
                                <p>• 在SQL中添加LIMIT子句</p>
                                <div class="mt-2">
                                    <input type="number" class="form-control form-control-sm" placeholder="最大记录数" min="1" max="10000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日期格式化配置 -->
                    <div class="col-md-4">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-4</div>
                            <label class="form-label fw-bold">日期格式化</label>
                            <div class="demo-content">
                                <p>• 选择日期字段</p>
                                <p>• 选择格式化样式</p>
                                <p>• 6种常用日期格式</p>
                                <p>• 使用MySQL DATE_FORMAT函数</p>
                                <div class="mt-2">
                                    <select class="form-select form-select-sm mb-2">
                                        <option>选择日期字段</option>
                                        <option>timestamp</option>
                                        <option>created_at</option>
                                    </select>
                                    <select class="form-select form-select-sm">
                                        <option>选择格式</option>
                                        <option>年月日时分秒</option>
                                        <option>月日时分</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 聚合配置 -->
                    <div class="col-md-4">
                        <div class="config-section position-relative">
                            <div class="height-indicator">col-md-4</div>
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="demo-content">
                                <p>• 启用/禁用聚合查询</p>
                                <p>• 选择聚合类型</p>
                                <p>• 配置时间字段排序</p>
                                <p>• 支持MAX、MIN、AVG等</p>
                                <div class="mt-2">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="demoAggregation">
                                        <label class="form-check-label" for="demoAggregation">
                                            启用聚合查询
                                        </label>
                                    </div>
                                    <select class="form-select form-select-sm">
                                        <option>MAX - 最大值</option>
                                        <option>MIN - 最小值</option>
                                        <option>AVG - 平均值</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 优势总结 -->
            <div class="comparison-section">
                <h5><i class="bi bi-trophy"></i> 布局优势</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-check2-square text-success"></i> 空间利用</h6>
                        <ul>
                            <li>消除了数据表选择和字段配置下方的空白区域</li>
                            <li>每个功能都有充足的显示空间</li>
                            <li>整体布局更加紧凑和高效</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-eye text-primary"></i> 视觉效果</h6>
                        <ul>
                            <li>两行布局视觉平衡，层次清晰</li>
                            <li>功能分组合理，逻辑清晰</li>
                            <li>避免了单列过度拥挤的问题</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
