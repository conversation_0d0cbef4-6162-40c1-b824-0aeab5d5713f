<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CINEMA 4D文字发光特效</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #0a0a0a;
            width: 100%;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .text-container {
            position: relative;
            display: inline-block;
        }

        .cinema4d-text {
            font-size: 8rem;
            font-weight: 900;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            position: relative;
            z-index: 1;
            
            /* 基础发光效果 */
            text-shadow: 
                0 0 10px rgba(0, 212, 255, 0.3),
                0 0 20px rgba(0, 212, 255, 0.2),
                0 0 40px rgba(0, 212, 255, 0.1);
        }

        /* 上半部分发光遮罩层 */
        .glow-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(0, 255, 255, 0.8) 20%,
                rgba(255, 0, 255, 0.8) 50%,
                rgba(0, 212, 255, 0.8) 80%,
                transparent 100%
            );
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
            z-index: 2;
            mix-blend-mode: screen;
            animation: glow-sweep 3s ease-in-out infinite;
        }

        /* 动态扫描光束 */
        .scan-beam {
            position: absolute;
            top: 0;
            left: -100%;
            width: 30%;
            height: 50%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.9) 50%,
                transparent
            );
            z-index: 3;
            animation: scan-move 4s ease-in-out infinite;
            mix-blend-mode: overlay;
        }

        /* 脉冲发光效果 */
        .pulse-glow {
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(
                ellipse at center,
                rgba(0, 255, 255, 0.3) 0%,
                rgba(255, 0, 255, 0.2) 30%,
                transparent 70%
            );
            z-index: 0;
            animation: pulse-effect 2s ease-in-out infinite alternate;
            filter: blur(10px);
        }

        /* 粒子效果 */
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ffff;
            animation: particle-float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) {
            top: 10%;
            left: 20%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            top: 20%;
            right: 25%;
            animation-delay: 1s;
        }

        .particle:nth-child(3) {
            top: 30%;
            left: 70%;
            animation-delay: 2s;
        }

        .particle:nth-child(4) {
            top: 15%;
            right: 40%;
            animation-delay: 3s;
        }

        /* 动画定义 */
        @keyframes glow-sweep {
            0% {
                background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(0, 255, 255, 0.8) 20%,
                    rgba(255, 0, 255, 0.8) 50%,
                    rgba(0, 212, 255, 0.8) 80%,
                    transparent 100%
                );
                opacity: 0.7;
            }
            50% {
                background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(255, 0, 255, 1) 20%,
                    rgba(0, 255, 255, 1) 50%,
                    rgba(255, 255, 0, 1) 80%,
                    transparent 100%
                );
                opacity: 1;
            }
            100% {
                background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(0, 212, 255, 0.8) 20%,
                    rgba(0, 255, 255, 0.8) 50%,
                    rgba(255, 0, 255, 0.8) 80%,
                    transparent 100%
                );
                opacity: 0.7;
            }
        }

        @keyframes scan-move {
            0% {
                left: -100%;
                opacity: 0;
            }
            20% {
                opacity: 1;
            }
            80% {
                opacity: 1;
            }
            100% {
                left: 100%;
                opacity: 0;
            }
        }

        @keyframes pulse-effect {
            0% {
                transform: scale(1);
                opacity: 0.3;
            }
            100% {
                transform: scale(1.1);
                opacity: 0.6;
            }
        }

        @keyframes particle-float {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-20px) scale(1.2);
                opacity: 1;
            }
        }

        /* 3D立体效果 */
        .cinema4d-text::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            color: rgba(0, 212, 255, 0.5);
            z-index: -1;
            transform: translate(2px, 2px);
            text-shadow:
                0 0 5px rgba(0, 212, 255, 0.8),
                0 0 10px rgba(0, 212, 255, 0.6),
                0 0 15px rgba(0, 212, 255, 0.4);
        }

        /* 镜面反射效果 */
        .reflection {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                to bottom,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%
            );
            transform: scaleY(-1);
            opacity: 0.3;
            z-index: -2;
            mask: linear-gradient(
                to bottom,
                rgba(0, 0, 0, 0.3) 0%,
                transparent 70%
            );
        }

        /* 能量波纹效果 */
        .energy-wave {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200%;
            height: 200%;
            transform: translate(-50%, -50%);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            z-index: -1;
            animation: wave-expand 4s ease-out infinite;
        }

        .energy-wave:nth-child(2) {
            animation-delay: 1s;
            border-color: rgba(255, 0, 255, 0.3);
        }

        .energy-wave:nth-child(3) {
            animation-delay: 2s;
            border-color: rgba(255, 255, 0, 0.3);
        }

        @keyframes wave-expand {
            0% {
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cinema4d-text {
                font-size: 4rem;
            }
        }

        @media (max-width: 480px) {
            .cinema4d-text {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="text-container">
        <!-- 能量波纹效果 -->
        <div class="energy-wave"></div>
        <div class="energy-wave"></div>
        <div class="energy-wave"></div>

        <!-- 脉冲发光背景 -->
        <div class="pulse-glow"></div>

        <!-- 主文字 -->
        <div class="cinema4d-text" data-text="CINEMA 4D">CINEMA 4D</div>

        <!-- 镜面反射效果 -->
        <div class="reflection"></div>

        <!-- 上半部分发光遮罩 -->
        <div class="glow-mask"></div>

        <!-- 动态扫描光束 -->
        <div class="scan-beam"></div>

        <!-- 粒子效果 -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
</body>
</html>
