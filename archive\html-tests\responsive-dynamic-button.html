<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式动态按钮</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 2%;
        }
        
        .dynamic-button {
            position: relative;
            padding: 4% 8%;
            font-size: clamp(1rem, 4vw, 2rem);
            font-weight: bold;
            color: #00d4ff;
            background: transparent;
            border: 0.2em solid #00d4ff;
            border-radius: 0.6em;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            width: 100%;
            max-width: 300px;
            min-width: 120px;
            height: auto;
            min-height: 3em;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 背景发光效果 */
        .dynamic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(0, 212, 255, 0.2), 
                transparent);
            transition: left 0.5s;
        }
        
        /* 边框发光动画 */
        .dynamic-button::after {
            content: '';
            position: absolute;
            top: -0.2em;
            left: -0.2em;
            right: -0.2em;
            bottom: -0.2em;
            background: linear-gradient(45deg, 
                #00d4ff, #ff00cc, #00ff88, #ffaa00, #00d4ff);
            background-size: 400%;
            border-radius: 0.8em;
            z-index: -1;
            opacity: 0;
            animation: border-flow 3s linear infinite;
            transition: opacity 0.3s;
        }
        
        /* 悬停效果 */
        .dynamic-button:hover::before {
            left: 100%;
        }
        
        .dynamic-button:hover::after {
            opacity: 1;
        }
        
        .dynamic-button:hover {
            color: #ffffff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 
                0 0 1em rgba(0, 212, 255, 0.4),
                0 0 2em rgba(0, 212, 255, 0.2);
            transform: translateY(-0.1em);
        }
        
        /* 点击效果 */
        .dynamic-button:active {
            transform: translateY(0);
            box-shadow: 
                0 0 0.8em rgba(0, 212, 255, 0.6),
                0 0 1.5em rgba(0, 212, 255, 0.3);
        }
        
        /* 边框流动动画 */
        @keyframes border-flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 按钮内部装饰 */
        .button-decoration {
            position: absolute;
            width: 0.4em;
            height: 0.4em;
            background: #00ffff;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .decoration-1 {
            top: 0.5em;
            left: 0.5em;
            animation: dot-pulse 2s ease-in-out infinite;
        }
        
        .decoration-2 {
            top: 0.5em;
            right: 0.5em;
            animation: dot-pulse 2s ease-in-out infinite 0.5s;
        }
        
        .decoration-3 {
            bottom: 0.5em;
            left: 0.5em;
            animation: dot-pulse 2s ease-in-out infinite 1s;
        }
        
        .decoration-4 {
            bottom: 0.5em;
            right: 0.5em;
            animation: dot-pulse 2s ease-in-out infinite 1.5s;
        }
        
        .dynamic-button:hover .button-decoration {
            opacity: 1;
        }
        
        @keyframes dot-pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* 文字发光效果 */
        .dynamic-button:hover {
            text-shadow: 
                0 0 0.5em rgba(0, 212, 255, 0.8),
                0 0 1em rgba(0, 212, 255, 0.6),
                0 0 1.5em rgba(0, 212, 255, 0.4);
        }
        
        /* 容器适配 */
        .button-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5%;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .dynamic-button {
                padding: 3% 6%;
                letter-spacing: 0.05em;
                min-height: 2.5em;
            }
            
            .button-decoration {
                width: 0.3em;
                height: 0.3em;
            }
            
            .decoration-1, .decoration-2 { top: 0.3em; }
            .decoration-3, .decoration-4 { bottom: 0.3em; }
            .decoration-1, .decoration-3 { left: 0.3em; }
            .decoration-2, .decoration-4 { right: 0.3em; }
        }
        
        @media (min-width: 1200px) {
            .dynamic-button {
                max-width: 400px;
                min-height: 4em;
            }
        }
    </style>
</head>
<body>
    <div class="button-container">
        <button class="dynamic-button">
            <span class="button-decoration decoration-1"></span>
            <span class="button-decoration decoration-2"></span>
            <span class="button-decoration decoration-3"></span>
            <span class="button-decoration decoration-4"></span>
            点击我
        </button>
    </div>
</body>
</html>
