/**
 * 数据源管理器
 * 负责数据源的增删改查和连接测试
 */
class DataSourceManager {
    constructor() {
        this.dataSources = [];
        this.dataSets = [];
        this.supportedTypes = [];
        this.currentEditingId = null;
        this.currentTab = 'dataSources';
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadSupportedTypes();
        this.loadStatistics();
        this.loadDataSources();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 新建数据源按钮
        document.getElementById('createDataSourceBtn').addEventListener('click', () => {
            this.showCreateModal();
        });

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadDataSources();
            this.loadStatistics();
        });

        // 保存数据源按钮
        document.getElementById('saveDataSourceBtn').addEventListener('click', () => {
            this.saveDataSource();
        });

        // 测试连接按钮
        document.getElementById('testConnectionBtn').addEventListener('click', () => {
            this.testConnection();
        });

        // 数据源类型变化
        document.getElementById('dataSourceType').addEventListener('change', (e) => {
            this.onDataSourceTypeChange(e.target.value);
        });

        // 新建数据集按钮
        document.getElementById('createDataSetBtn').addEventListener('click', () => {
            this.showCreateDataSetWizard();
        });

        // 标签页切换
        document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const targetTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.onTabChange(targetTab);
            });
        });
    }

    /**
     * 加载支持的数据源类型
     */
    async loadSupportedTypes() {
        try {
            const response = await fetch('/datasource/api/types');
            if (response.ok) {
                this.supportedTypes = await response.json();
                this.renderTypeOptions();
            }
        } catch (error) {
            console.error('加载数据源类型失败:', error);
        }
    }

    /**
     * 渲染数据源类型选项
     */
    renderTypeOptions() {
        const select = document.getElementById('dataSourceType');
        select.innerHTML = '<option value="">请选择数据源类型</option>';
        
        this.supportedTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type.type;
            option.textContent = `${type.name} - ${type.description}`;
            select.appendChild(option);
        });
    }

    /**
     * 加载统计信息
     */
    async loadStatistics() {
        try {
            const response = await fetch('/datasource/api/statistics');
            if (response.ok) {
                const stats = await response.json();
                this.renderStatistics(stats);
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    /**
     * 渲染统计信息
     */
    renderStatistics(stats) {
        const container = document.getElementById('statisticsCards');
        
        const cards = [
            { label: '总数据源', value: stats.total || 0, icon: 'database', color: '#495057' },
            { label: '已启用', value: stats.enabled || 0, icon: 'check-circle', color: '#198754' },
            { label: '已禁用', value: stats.disabled || 0, icon: 'x-circle', color: '#6c757d' },
            { label: '连接正常', value: (stats.byStatus && stats.byStatus.connected) || 0, icon: 'wifi', color: '#0d6efd' }
        ];

        container.innerHTML = cards.map(card => `
            <div class="stat-card">
                <div class="stat-number" style="color: ${card.color}">
                    <i class="bi bi-${card.icon}"></i> ${card.value}
                </div>
                <div class="stat-label">${card.label}</div>
            </div>
        `).join('');
    }

    /**
     * 加载数据源列表
     */
    async loadDataSources() {
        try {
            const response = await fetch('/datasource/api/list');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.dataSources = result.data || [];
                    this.renderDataSources();
                } else {
                    throw new Error(result.message || '加载数据源列表失败');
                }
            } else {
                throw new Error('加载数据源列表失败');
            }
        } catch (error) {
            console.error('加载数据源列表失败:', error);
            this.showError('加载数据源列表失败: ' + error.message);
        }
    }

    /**
     * 渲染数据源列表
     */
    renderDataSources() {
        const container = document.getElementById('dataSourceList');
        
        if (this.dataSources.length === 0) {
            container.innerHTML = `
                <div class="empty-message">
                    <i class="bi bi-database"></i>
                    <h5>暂无数据源</h5>
                    <p>点击"新建数据源"按钮创建第一个数据源</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.dataSources.map(ds => this.renderDataSourceCard(ds)).join('');
    }

    /**
     * 渲染数据源卡片
     */
    renderDataSourceCard(dataSource) {
        const statusClass = this.getStatusClass(dataSource.status);
        const typeClass = this.getTypeClass(dataSource.type);
        const typeName = this.getTypeName(dataSource.type);
        
        return `
            <div class="datasource-card">
                <div class="datasource-card-header">
                    <div class="datasource-info">
                        <div class="datasource-name">
                            <span class="type-badge ${typeClass}">${typeName}</span>
                            ${dataSource.name}
                            ${dataSource.enabled ? '' : '<span class="badge bg-secondary ms-2">已禁用</span>'}
                        </div>
                        <div class="datasource-description">${dataSource.description || '无描述'}</div>
                        <div class="datasource-meta">
                            <span><i class="bi bi-circle-fill ${statusClass}"></i> ${this.getStatusText(dataSource.status)}</span>
                            <span><i class="bi bi-calendar"></i> 创建于 ${this.formatDate(dataSource.createdAt)}</span>
                            ${dataSource.lastTestTime ? `<span><i class="bi bi-wifi"></i> 最后测试 ${this.formatDate(dataSource.lastTestTime)}</span>` : ''}
                        </div>
                    </div>
                    <div class="datasource-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="dataSourceManager.testDataSourceConnection('${dataSource.id}')" title="测试连接">
                            <i class="bi bi-wifi"></i>
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="dataSourceManager.viewMetadata('${dataSource.id}')" title="查看元数据">
                            <i class="bi bi-info-circle"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="dataSourceManager.editDataSource('${dataSource.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="dataSourceManager.deleteDataSource('${dataSource.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        switch (status) {
            case 'connected': return 'text-success';
            case 'error': return 'text-danger';
            default: return 'text-secondary';
        }
    }

    /**
     * 获取类型样式类
     */
    getTypeClass(type) {
        switch (type) {
            case 'database': return 'type-database';
            case 'api': return 'type-api';
            case 'file': return 'type-file';
            case 'mqtt': return 'type-mqtt';
            default: return 'type-database';
        }
    }

    /**
     * 获取类型显示名称
     */
    getTypeName(type) {
        const typeInfo = this.supportedTypes.find(t => t.type === type);
        return typeInfo ? typeInfo.name : type;
    }

    /**
     * 获取状态显示文本
     */
    getStatusText(status) {
        switch (status) {
            case 'connected': return '已连接';
            case 'error': return '连接错误';
            case 'disconnected': return '未连接';
            default: return '未知';
        }
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    /**
     * 显示创建模态框
     */
    showCreateModal() {
        this.currentEditingId = null;
        document.getElementById('dataSourceModalTitle').textContent = '新建数据源';
        document.getElementById('dataSourceForm').reset();
        document.getElementById('dataSourceId').value = '';
        document.getElementById('dataSourceEnabled').checked = true;
        this.hideAllConfigSections();
        
        const modal = new bootstrap.Modal(document.getElementById('dataSourceModal'));
        modal.show();
    }

    /**
     * 数据源类型变化处理
     */
    onDataSourceTypeChange(type) {
        this.hideAllConfigSections();

        if (type === 'database') {
            const databaseConfig = document.getElementById('databaseConfig');
            if (databaseConfig) {
                databaseConfig.style.display = 'block';
            }
        }
        // 其他类型的配置界面可以在这里添加
    }

    /**
     * 隐藏所有配置区域（仅限数据源模态框内）
     */
    hideAllConfigSections() {
        document.querySelectorAll('#dataSourceModal .config-section').forEach(section => {
            section.style.display = 'none';
        });
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 这里可以使用更好的错误提示组件
        alert('错误: ' + message);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        // 这里可以使用更好的成功提示组件
        alert('成功: ' + message);
    }

    /**
     * 保存数据源
     */
    async saveDataSource() {
        try {
            const formData = this.collectFormData();
            if (!this.validateFormData(formData)) {
                return;
            }

            const url = this.currentEditingId ?
                `/datasource/api/${this.currentEditingId}` :
                '/datasource/api/create';
            const method = this.currentEditingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            if (result.success) {
                this.showSuccess(result.message);
                bootstrap.Modal.getInstance(document.getElementById('dataSourceModal')).hide();
                this.loadDataSources();
                this.loadStatistics();

                // 通知数据集向导刷新数据源列表
                if (window.dataSetWizard && typeof window.dataSetWizard.refreshDataSources === 'function') {
                    window.dataSetWizard.refreshDataSources();
                }
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('保存数据源失败:', error);
            this.showError('保存数据源失败: ' + error.message);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = {
            name: document.getElementById('dataSourceName').value,
            description: document.getElementById('dataSourceDescription').value,
            type: document.getElementById('dataSourceType').value,
            enabled: document.getElementById('dataSourceEnabled').checked
        };

        // 根据类型收集连接配置
        if (formData.type === 'database') {
            const connectionConfig = {
                host: document.getElementById('dbHost').value,
                port: parseInt(document.getElementById('dbPort').value) || 3306,
                database: document.getElementById('dbDatabase').value,
                username: document.getElementById('dbUsername').value,
                password: document.getElementById('dbPassword').value,
                driverType: document.getElementById('dbDriverType').value
            };
            formData.connectionConfig = JSON.stringify(connectionConfig);
        }

        return formData;
    }

    /**
     * 验证表单数据
     */
    validateFormData(formData) {
        if (!formData.name.trim()) {
            this.showError('请输入数据源名称');
            return false;
        }

        if (!formData.type) {
            this.showError('请选择数据源类型');
            return false;
        }

        if (formData.type === 'database') {
            const config = JSON.parse(formData.connectionConfig);
            if (!config.host || !config.database || !config.username) {
                this.showError('请填写完整的数据库连接信息');
                return false;
            }
        }

        return true;
    }

    /**
     * 编辑数据源
     */
    async editDataSource(id) {
        try {
            const response = await fetch(`/datasource/api/${id}`);
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.populateForm(result.data);
                    this.currentEditingId = id;

                    document.getElementById('dataSourceModalTitle').textContent = '编辑数据源';
                    const modal = new bootstrap.Modal(document.getElementById('dataSourceModal'));
                    modal.show();
                } else {
                    this.showError(result.message || '获取数据源信息失败');
                }
            } else {
                this.showError('获取数据源信息失败');
            }
        } catch (error) {
            console.error('编辑数据源失败:', error);
            this.showError('编辑数据源失败: ' + error.message);
        }
    }

    /**
     * 填充表单
     */
    populateForm(dataSource) {
        document.getElementById('dataSourceId').value = dataSource.id;
        document.getElementById('dataSourceName').value = dataSource.name;
        document.getElementById('dataSourceDescription').value = dataSource.description || '';
        document.getElementById('dataSourceType').value = dataSource.type;
        document.getElementById('dataSourceEnabled').checked = dataSource.enabled;

        this.onDataSourceTypeChange(dataSource.type);

        // 填充连接配置
        if (dataSource.connectionConfig) {
            const config = JSON.parse(dataSource.connectionConfig);

            if (dataSource.type === 'database') {
                document.getElementById('dbHost').value = config.host || '';
                document.getElementById('dbPort').value = config.port || 3306;
                document.getElementById('dbDatabase').value = config.database || '';
                document.getElementById('dbUsername').value = config.username || '';
                document.getElementById('dbPassword').value = config.password || '';
                document.getElementById('dbDriverType').value = config.driverType || 'mysql';
            }
        }
    }

    /**
     * 删除数据源
     */
    async deleteDataSource(id) {
        if (!confirm('确定要删除这个数据源吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/datasource/api/${id}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            if (result.success) {
                this.showSuccess(result.message);
                this.loadDataSources();
                this.loadStatistics();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('删除数据源失败:', error);
            this.showError('删除数据源失败: ' + error.message);
        }
    }

    /**
     * 测试数据源连接
     */
    async testDataSourceConnection(id) {
        try {
            const response = await fetch(`/datasource/api/${id}/test`, {
                method: 'POST'
            });

            const result = await response.json();
            if (result.success) {
                this.showSuccess(`连接测试成功！响应时间: ${result.responseTime}ms`);
                this.loadDataSources(); // 刷新状态
            } else {
                this.showError(`连接测试失败: ${result.message}`);
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            this.showError('测试连接失败: ' + error.message);
        }
    }

    /**
     * 测试当前表单的连接
     */
    async testConnection() {
        try {
            const formData = this.collectFormData();
            if (!this.validateFormData(formData)) {
                return;
            }

            // 显示测试中状态
            const testBtn = document.getElementById('testConnectionBtn');
            const originalText = testBtn.innerHTML;
            testBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            testBtn.disabled = true;

            try {
                const response = await fetch('/datasource/api/test-temp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                if (result.success) {
                    this.showSuccess(`连接测试成功！响应时间: ${result.responseTime}ms`);
                } else {
                    this.showError(`连接测试失败: ${result.message}`);
                }
            } finally {
                // 恢复按钮状态
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            this.showError('测试连接失败: ' + error.message);
        }
    }

    /**
     * 查看元数据
     */
    async viewMetadata(id) {
        try {
            const response = await fetch(`/datasource/api/${id}/metadata`);
            const result = await response.json();

            if (result.success) {
                // 显示元数据信息
                this.showMetadataModal(result.metadata);
            } else {
                this.showError(`获取元数据失败: ${result.message}`);
            }
        } catch (error) {
            console.error('获取元数据失败:', error);
            this.showError('获取元数据失败: ' + error.message);
        }
    }

    /**
     * 显示元数据模态框
     */
    showMetadataModal(metadata) {
        // 这里可以创建一个专门的元数据显示模态框
        alert('元数据信息:\n' + JSON.stringify(metadata, null, 2));
    }

    /**
     * 标签页切换处理
     */
    onTabChange(tabName) {
        this.currentTab = tabName;

        // 更新按钮显示
        const createDataSourceBtn = document.getElementById('createDataSourceBtn');
        const createDataSetBtn = document.getElementById('createDataSetBtn');

        if (tabName === 'dataSources') {
            createDataSourceBtn.style.display = 'inline-block';
            createDataSetBtn.style.display = 'none';
        } else if (tabName === 'dataSets') {
            createDataSourceBtn.style.display = 'none';
            createDataSetBtn.style.display = 'inline-block';
            this.loadDataSets();
        }
    }

    /**
     * 显示数据集创建向导
     */
    showCreateDataSetWizard() {
        if (window.dataSetWizard) {
            window.dataSetWizard.show();
        }
    }

    /**
     * 加载数据集列表
     */
    async loadDataSets() {
        try {
            const response = await fetch('/api/dataset/list');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.dataSets = result.data || [];
                    this.renderDataSets();
                } else {
                    throw new Error(result.message || '加载数据集列表失败');
                }
            } else {
                throw new Error('加载数据集列表失败');
            }
        } catch (error) {
            console.error('加载数据集列表失败:', error);
            this.showError('加载数据集列表失败: ' + error.message);
        }
    }

    /**
     * 渲染数据集列表
     */
    renderDataSets() {
        const container = document.getElementById('dataSetList');

        if (this.dataSets.length === 0) {
            container.innerHTML = `
                <div class="empty-message">
                    <i class="bi bi-table"></i>
                    <h5>暂无数据集</h5>
                    <p>点击"新建数据集"按钮创建第一个数据集</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.dataSets.map(ds => this.renderDataSetCard(ds)).join('');
    }

    /**
     * 渲染数据集卡片
     */
    renderDataSetCard(dataSet) {
        const statusClass = dataSet.enabled ? 'text-success' : 'text-secondary';
        const statusText = dataSet.enabled ? '已启用' : '已禁用';

        return `
            <div class="datasource-card">
                <div class="datasource-card-header">
                    <div class="datasource-info">
                        <div class="datasource-name">
                            <span class="type-badge type-database">数据集</span>
                            ${dataSet.name}
                            ${dataSet.enabled ? '' : '<span class="badge bg-secondary ms-2">已禁用</span>'}
                        </div>
                        <div class="datasource-description">${dataSet.description || '无描述'}</div>
                        <div class="datasource-meta">
                            <span><i class="bi bi-circle-fill ${statusClass}"></i> ${statusText}</span>
                            <span><i class="bi bi-database"></i> ${dataSet.dataSourceName || '未知数据源'}</span>
                            <span><i class="bi bi-calendar"></i> 创建于 ${this.formatDate(dataSet.createdAt)}</span>
                            ${dataSet.lastExecuteTime ? `<span><i class="bi bi-play-circle"></i> 最后执行 ${this.formatDate(dataSet.lastExecuteTime)}</span>` : ''}
                            ${dataSet.dataRowCount ? `<span><i class="bi bi-list-ol"></i> ${dataSet.dataRowCount} 行数据</span>` : ''}
                        </div>
                    </div>
                    <div class="datasource-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="dataSourceManager.executeDataSet('${dataSet.id}')" title="执行查询">
                            <i class="bi bi-play-circle"></i>
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="dataSourceManager.previewDataSet('${dataSet.id}')" title="预览数据">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="dataSourceManager.editDataSet('${dataSet.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="dataSourceManager.deleteDataSet('${dataSet.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 执行数据集查询
     */
    async executeDataSet(id) {
        try {
            const response = await fetch(`/api/dataset/${id}/execute`, {
                method: 'POST'
            });

            const result = await response.json();
            if (result.success) {
                this.showSuccess(`查询执行成功！返回 ${result.rowCount} 行数据，耗时 ${result.executeTime}ms`);
                this.loadDataSets(); // 刷新状态
            } else {
                this.showError(`查询执行失败: ${result.message}`);
            }
        } catch (error) {
            console.error('执行数据集查询失败:', error);
            this.showError('执行查询失败: ' + error.message);
        }
    }

    /**
     * 预览数据集
     */
    async previewDataSet(id) {
        try {
            const response = await fetch(`/api/dataset/${id}/execute`, {
                method: 'POST'
            });

            const result = await response.json();
            if (result.success) {
                this.showDataPreviewModal(result.data);
            } else {
                this.showError(`预览失败: ${result.message}`);
            }
        } catch (error) {
            console.error('预览数据集失败:', error);
            this.showError('预览数据集失败: ' + error.message);
        }
    }

    /**
     * 显示数据预览模态框
     */
    showDataPreviewModal(data) {
        // 简单的数据预览，实际可以创建专门的模态框
        if (!data || data.length === 0) {
            alert('没有数据');
            return;
        }

        const fields = Object.keys(data[0]);
        const preview = data.slice(0, 5); // 只显示前5行

        let content = '数据预览（前5行）:\n\n';
        content += fields.join('\t') + '\n';
        content += '-'.repeat(50) + '\n';

        preview.forEach(row => {
            content += fields.map(field => row[field] || '').join('\t') + '\n';
        });

        alert(content);
    }

    /**
     * 编辑数据集
     */
    async editDataSet(id) {
        try {
            if (window.dataSetWizard && typeof window.dataSetWizard.showForEdit === 'function') {
                await window.dataSetWizard.showForEdit(id);
            } else {
                this.showError('数据集编辑功能未初始化，请刷新页面重试');
            }
        } catch (error) {
            console.error('打开数据集编辑失败:', error);
            this.showError('打开数据集编辑失败: ' + error.message);
        }
    }

    /**
     * 删除数据集
     */
    async deleteDataSet(id) {
        if (!confirm('确定要删除这个数据集吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/dataset/${id}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            if (result.success) {
                this.showSuccess(result.message);
                this.loadDataSets();
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('删除数据集失败:', error);
            this.showError('删除数据集失败: ' + error.message);
        }
    }
}
