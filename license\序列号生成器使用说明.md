# 🔑 SDPLC序列号生成器使用说明

## 📋 概述

SDPLC序列号生成器是一个**中文图形界面工具**，用于生成SHENGDA-PLC系统的加密序列号。该工具具有直观的Windows风格中文界面，支持可视化操作，并包含完整的输入验证功能。

## 🚀 快速开始

### 1. 启动程序

```bash
# 编译程序（首次使用，指定UTF-8编码）
javac -encoding UTF-8 SDPLCSerialGenerator.java

# 运行程序
java SDPLCSerialGenerator
```

### 2. 界面说明

程序启动后会显示一个图形界面，包含以下部分：

#### 🔧 序列号参数设置区域
- **用户ID**: 输入用户标识，如 `USER001`（**仅支持大写字母和数字**）
- **开始日期**: 序列号安装有效期开始日期，格式：`YYYYMMDD`
- **结束日期**: 序列号安装有效期结束日期，格式：`YYYYMMDD`
- **重要说明**: 开始和结束日期是指**序列号可以用于激活安装的时间窗口**，超出此时间范围序列号将失效无法激活

#### 🎯 快捷操作按钮
- **随机生成**: 随机生成用户ID
- **今天**: 设置开始日期为今天
- **明天**: 设置结束日期为明天
- **1天有效期 / 7天有效期 / 30天有效期 / 365天有效期**: 快速设置序列号安装有效期

#### 📄 生成结果区域
- **序列号显示框**: 显示生成的加密序列号
- **🔧 生成序列号**: 生成序列号按钮
- **📋 复制到剪贴板**: 复制到剪贴板按钮
- **🗑️ 清空**: 清空按钮

## 📝 详细使用步骤

### 步骤1：设置参数

1. **输入用户ID**
   - 手动输入，如：`USER001`（**注意：仅支持大写字母和数字，不支持小写字母**）
   - 或点击"随机生成"按钮随机生成
   - 程序会自动将输入转换为大写

2. **设置日期**
   - **方法一**：手动输入日期（格式：YYYYMMDD）
   - **方法二**：使用快捷按钮
     - 点击"今天"设置开始日期为今天
     - 点击"明天"设置结束日期为明天
   - **方法三**：使用有效期按钮
     - 点击"30天有效期"自动设置30天的序列号安装有效期

### 步骤2：生成序列号

1. 确认所有参数填写正确
2. 点击"🔧 生成序列号"按钮
3. 程序会显示成功消息并在下方显示生成的序列号

### 步骤3：复制序列号

1. 点击"📋 复制到剪贴板"按钮
2. 序列号会自动复制到系统剪贴板
3. 现在可以在SDPLC应用中粘贴使用

## 🎯 实际操作示例

### 示例1：生成30天有效期序列号

1. **启动程序**：`java SDPLCSerialGenerator`
2. **设置参数**：
   - 用户ID: `USER001`（默认已填写）
   - 点击"30天有效期"按钮（自动设置30天的序列号安装有效期）
3. **生成序列号**：点击"🔧 生成序列号"
4. **复制序列号**：点击"📋 复制到剪贴板"

### 示例2：生成自定义日期序列号

1. **启动程序**：`java SDPLCSerialGenerator`
2. **设置参数**：
   - 用户ID: `USER001`
   - 开始日期: `20250728`
   - 结束日期: `20250827`
3. **生成序列号**：点击"🔧 生成序列号"
4. **复制序列号**：点击"📋 复制到剪贴板"

## 🔒 技术规格

### 加密算法
- **算法**: AES/ECB/PKCS5Padding
- **密钥**: SDPLC2024LICENSE
- **编码**: Base64

### 序列号格式
```
SDPLC-{Base64加密数据}
```

### 数据结构（加密前）
```json
{
  "userId": "USER001",
  "startDate": "20250728",
  "endDate": "20250827",
  "checksum": "7E179F"
}
```

## ⚠️ 重要注意事项

### 1. 序列号有效期概念（重要！）
**序列号有效期 ≠ 软件使用期限**

- **序列号有效期**：指序列号可以用于激活安装的时间窗口
- **软件使用**：一旦激活成功，软件可以正常使用，没有时间限制
- **举例说明**：
  - 设置有效期：2025-07-28 至 2025-08-27（30天）
  - 含义：在这30天内可以使用序列号进行激活
  - 结果：2025-08-28之后序列号失效，无法再用于激活
  - 但是：已经激活的软件可以继续正常使用

### 2. 日期格式
- **正确格式**: `20250728`
- **错误格式**: `2025-07-28`, `28/07/2025`, `2025/7/28`

### 3. 用户ID规范（重要！）
- **仅支持大写字母和数字**，不支持小写字母
- **不支持的字符**：小写字母（a-z）、中文字符、特殊符号（除了_和-）
- **支持的格式**：
  - `USER001`, `USER002`, `USER999` ✅
  - `ADMIN`, `TEST123`, `LICENSE_001` ✅
- **不支持的格式**：
  - `user001`, `User001`, `用户001` ❌
  - `test@123`, `user.001` ❌
- **自动转换**：程序会自动将输入转换为大写

### 4. 序列号安装有效期设置
- 开始日期不能晚于结束日期
- 建议设置合理的安装有效期（如30天、365天）
- **重要理解**：这是序列号可以用于激活安装的时间窗口，不是软件使用时间限制
- **示例**：设置30天有效期意味着在这30天内可以使用序列号激活，超过30天序列号失效

### 5. 序列号使用
- 生成的序列号必须完整复制（包括`SDPLC-`前缀）
- 激活时用户ID必须与生成时一致
- 必须在序列号有效期内进行激活，超期序列号失效

## 🔍 故障排除

### 常见问题

1. **程序无法启动**
   - 确保Java环境正确安装
   - 检查文件路径是否正确
   - 尝试重新编译：`javac SDPLCSerialGenerator.java`

2. **界面显示异常**
   - 确保系统支持Swing GUI
   - 检查显示器分辨率设置

3. **生成序列号失败**
   - 检查日期格式是否正确（YYYYMMDD）
   - 确保所有字段都已填写
   - 检查日期逻辑（开始日期 ≤ 结束日期）

4. **序列号无法激活**
   - 确认序列号完整复制
   - 检查用户ID是否匹配
   - 验证日期是否在有效期内

### 错误消息说明

- **"请填写所有字段！"**: 请填写所有必填字段
- **"用户ID格式不正确！"**: 用户ID包含不支持的字符（如小写字母）
- **"日期格式不正确！"**: 日期格式错误，请使用YYYYMMDD格式
- **"生成失败"**: 生成失败，检查输入参数

## 📞 技术支持

如果遇到问题，请检查：
1. Java版本是否兼容（建议Java 8+）
2. 输入参数是否符合规范
3. 系统环境是否支持Swing GUI

## 🎉 使用技巧

1. **批量生成**: 可以多次运行程序生成不同用户的序列号
2. **快捷操作**: 善用快捷按钮提高效率
3. **参数保存**: 程序会记住上次的设置（在同一会话中）
4. **验证生成**: 生成后可以在SDPLC测试页面验证序列号

---

**版本**: v2.0  
**更新日期**: 2025-07-28  
**兼容性**: Windows系统，Java 8+
