package com.example.repository;

import com.example.entity.PublishedTopology;
import com.example.entity.Topology;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface PublishedTopologyRepository extends JpaRepository<PublishedTopology, Long> {
    
    Optional<PublishedTopology> findByAccessToken(String accessToken);
    
    List<PublishedTopology> findByTopology(Topology topology);
    
    List<PublishedTopology> findByStatus(String status);
    
    List<PublishedTopology> findByExpiryDateBefore(Date date);
    
    List<PublishedTopology> findByTopologyId(Long topologyId);
    
    List<PublishedTopology> findByTopologyIdAndStatus(Long topologyId, String status);
} 