#!/bin/bash

# SDPLC序列号生成器启动脚本

echo ""
echo "========================================"
echo "    🔑 SDPLC序列号生成器启动脚本"
echo "========================================"
echo ""

echo "正在检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 错误：未找到Java环境！"
    echo "请确保已安装Java 8或更高版本，并配置了PATH环境变量。"
    echo ""
    exit 1
fi

echo "✅ Java环境检查通过"
echo ""

echo "正在编译序列号生成器..."
if ! javac -encoding UTF-8 SDPLCSerialGenerator.java; then
    echo "❌ 编译失败！请检查Java环境和源代码。"
    echo ""
    exit 1
fi

echo "✅ 编译成功"
echo ""

echo "正在启动序列号生成器..."
echo "📝 使用说明："
echo "   - 用户ID仅支持大写字母和数字"
echo "   - 日期格式：YYYYMMDD（如：20250728）"
echo "   - 安装有效期是指序列号可用于激活的时间窗口"
echo ""

java SDPLCSerialGenerator &

echo "✅ 序列号生成器已启动！"
echo ""
echo "如果程序没有显示，请检查："
echo "1. Java版本是否为8或更高"
echo "2. 系统是否支持Swing GUI"
echo "3. X11转发是否正确配置（SSH连接时）"
echo ""
