import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 测试界面修复效果
 */
public class TestUIFixes {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            createTestFrame();
        });
    }
    
    private static void createTestFrame() {
        JFrame frame = new JFrame("界面修复测试");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(600, 400);
        frame.setLocationRelativeTo(null);
        
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 测试按钮样式
        gbc.gridx = 0; gbc.gridy = 0;
        JButton testButton1 = new JButton("生成序列号");
        testButton1.setFont(new Font("Microsoft YaHei", Font.BOLD, 14));
        testButton1.setBackground(new Color(0, 102, 204));
        testButton1.setForeground(Color.WHITE);
        testButton1.setPreferredSize(new Dimension(150, 40));
        testButton1.setBorder(BorderFactory.createRaisedBevelBorder());
        testButton1.setFocusPainted(false);
        panel.add(testButton1, gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        JButton testButton2 = new JButton("复制到剪贴板");
        testButton2.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        testButton2.setBackground(new Color(25, 135, 84));
        testButton2.setForeground(Color.WHITE);
        testButton2.setPreferredSize(new Dimension(150, 40));
        testButton2.setBorder(BorderFactory.createRaisedBevelBorder());
        testButton2.setFocusPainted(false);
        panel.add(testButton2, gbc);
        
        gbc.gridx = 2; gbc.gridy = 0;
        JButton testButton3 = new JButton("许可解析");
        testButton3.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        testButton3.setBackground(new Color(102, 16, 242));
        testButton3.setForeground(Color.WHITE);
        testButton3.setPreferredSize(new Dimension(120, 40));
        testButton3.setBorder(BorderFactory.createRaisedBevelBorder());
        testButton3.setFocusPainted(false);
        panel.add(testButton3, gbc);
        
        // 测试中文文本显示
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 3;
        JTextArea textArea = new JTextArea(10, 50);
        textArea.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        textArea.setEditable(false);
        textArea.setBackground(new Color(248, 249, 250));
        textArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        String testText = "=== 许可文件信息 ===\n" +
                         "用户ID: USER001\n" +
                         "安装时间: 2025-08-01T09:24:38\n" +
                         "硬件ID: 4A7C3B31E0D41232\n\n" +
                         "=== 许可使用期限 ===\n" +
                         "开始日期: 2025-08-01\n" +
                         "结束日期: 2026-08-01\n" +
                         "剩余天数: 365 天\n" +
                         "状态: ✅ 有效\n\n" +
                         "=== 硬件绑定验证 ===\n" +
                         "当前硬件ID: 4A7C3B31E0D41232\n" +
                         "许可硬件ID: 4A7C3B31E0D41232\n" +
                         "硬件绑定: ✅ 匹配\n\n" +
                         "=== 序列号信息 ===\n" +
                         "序列号: SDPLC-zymLUJoCsVDDMwEjeZRTPw...\n" +
                         "序列号用户ID: USER001\n" +
                         "激活时间窗口: 2025-08-01 至 2025-08-02\n" +
                         "序列号许可期限: 2025-08-01 至 2026-08-01";
        
        textArea.setText(testText);
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        panel.add(scrollPane, gbc);
        
        // 测试按钮点击事件
        testButton3.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JOptionPane.showMessageDialog(frame, 
                    "许可解析功能测试\n\n" +
                    "✅ 按钮样式：无图标，颜色对比度高\n" +
                    "✅ 中文显示：Microsoft YaHei字体\n" +
                    "✅ 界面美观：统一的视觉风格", 
                    "测试结果", 
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });
        
        frame.add(panel);
        frame.setVisible(true);
        
        System.out.println("界面修复测试程序已启动");
        System.out.println("✅ 移除了Unicode图标");
        System.out.println("✅ 优化了按钮颜色对比度");
        System.out.println("✅ 统一使用Microsoft YaHei字体");
        System.out.println("✅ 中文字符应该正确显示");
    }
}
