# BI大屏数据源配置调用文档

## 概述

本文档详细记录了BI大屏系统中所有组件对数据源配置的调用方式，包括配置收集、恢复、数据获取等各个环节的具体实现。

## 核心数据源管理器

### BiDataSourceManager 类
- **文件位置**: `src/main/resources/static/js/bi-data-source-manager.js`
- **全局实例**: `window.biDataSourceManager`
- **初始化位置**: 
  - 设计页面: `dashboard-designer.html`
  - 预览页面: `dashboard-preview.html` 
  - 发布页面: `published-dashboard.html`

### 支持的数据源类型
1. **dataItem** - 监控项数据源
2. **static** - 静态数据源
3. **multiData** - 多数据源
4. **externalData** - 外部数据源

## 组件配置映射

### 组件类型与支持的数据源
```javascript
// BiDataSourceManager.initComponentConfigs()
{
    'pie-chart': {
        supportedDataSources: ['static', 'multiData', 'externalData'],
        multiDataConfig: {
            containerId: 'pieDataSourceList',
            idPrefix: 'pieData',
            labelPlaceholder: '产品A',
            dataFormat: 'pie'
        }
    },
    'bar-chart': {
        supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData'],
        multiDataConfig: {
            containerId: 'barDataSourceList',
            idPrefix: 'barData',
            labelPlaceholder: '系列A',
            dataFormat: 'chart'
        }
    },
    'line-chart': {
        supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData']
    },
    'gauge-chart': {
        supportedDataSources: ['dataItem', 'static', 'externalData']
    },
    'data-table': {
        supportedDataSources: ['dataItem', 'static', 'multiData', 'externalData']
    }
}
```

## 主要调用场景

### 1. 组件配置保存
**调用位置**: `bi-dashboard-designer.js` 第4043行
```javascript
// 在applyWidgetConfig函数中
const dataSourceConfig = window.biDataSourceManager ?
    window.biDataSourceManager.collectDataSourceConfig(selectedWidget) :
    { dataSourceType: 'dataItem' };
```

**调用流程**:
1. 用户在属性面板修改数据源配置
2. 点击"应用配置"按钮
3. 调用`applyWidgetConfig()`函数
4. 函数内部调用`BiDataSourceManager.collectDataSourceConfig(widget)`
5. 根据数据源类型收集相应配置
6. 保存到组件的`dataSourceConfig`属性

### 2. 组件配置恢复
**调用位置**: `bi-dashboard-designer.js` 第2803行
```javascript
// 在updatePropertyPanel函数中
if (window.biDataSourceManager) {
    // 如果是外部数据源，先进行智能重置防止组件间配置污染
    if (dataSourceConfig.dataSourceType === 'externalData') {
        console.log(`组件 ${widget.id} (${widget.type}) 恢复外部数据源配置前进行智能重置`);
        resetExternalDataSourceUIStateSmartly(dataSourceConfig);
    }
    
    window.biDataSourceManager.restoreDataSourceConfig(widget, dataSourceConfig);
}
```

**调用流程**:
1. 用户选中组件
2. 调用`updatePropertyPanel(widget)`函数
3. 解析组件的`dataSourceConfig`属性
4. 调用`BiDataSourceManager.restoreDataSourceConfig(widget, dataSourceConfig)`
5. 根据数据源类型恢复相应的UI配置

### 3. 组件数据获取
**调用位置**: `bi-dashboard-designer.js` 第8546行
```javascript
// 在updateWidgetData函数中
const data = window.biDataSourceManager ?
    await window.biDataSourceManager.fetchWidgetData(widget) :
    { success: false, error: '数据源管理器未初始化' };
```

**调用流程**:
1. 组件需要更新数据时
2. 调用`updateWidgetData(widget)`函数
3. 函数内部调用`BiDataSourceManager.fetchWidgetData(widget)`
4. 根据组件的数据源配置获取数据
5. 将获取的数据应用到组件显示

## 不同页面的调用差异

### 设计页面 (dashboard-designer.html)
- **完整功能**: 支持配置收集、恢复、数据获取
- **实时更新**: 支持定时数据刷新
- **配置管理**: 支持配置保存和加载

### 预览页面 (dashboard-preview.html)
- **只读模式**: 仅支持数据获取，不支持配置修改
- **简化调用**: 
```javascript
const data = window.biDataSourceManager ?
    await window.biDataSourceManager.fetchWidgetData(widget) :
    { success: false, error: '数据源管理器未初始化' };
```

### 发布页面 (published-dashboard.html)
- **发布模式**: 与预览页面类似，仅支持数据获取
- **性能优化**: 针对发布环境进行了优化

## 重复配置问题

### 当前存在的重复配置
1. **bi-dashboard-designer.js** 中的 `getMultiDataSourceConfig()` 函数 (第5899行)
2. **BiDataSourceManager** 中的 `initComponentConfigs()` 方法

### 重复配置内容
```javascript
// bi-dashboard-designer.js 中的重复配置
function getMultiDataSourceConfig(componentType) {
    const configs = {
        'pie-chart': {
            containerId: 'pieDataSourceList',
            idPrefix: 'pieData',
            labelPlaceholder: '产品A',
            dataFormat: 'pie'
        },
        // ... 其他组件配置
    };
    return configs[componentType];
}
```

## 配置污染问题

### 外部数据源配置污染
- **问题**: 多个组件切换时，外部数据源配置相互影响
- **解决方案**: 在恢复配置前调用`resetExternalDataSourceUIStateSmartly()`进行智能重置

### 多数据源配置污染
- **问题**: 多数据源配置在组件间共享DOM元素
- **解决方案**: 使用组件特定的容器ID和前缀

## 调用时机总结

### 配置收集 (collectDataSourceConfig)
- 用户点击"应用配置"按钮时
- 组件配置保存时
- 布局保存时

### 配置恢复 (restoreDataSourceConfig)
- 组件被选中时
- 页面加载组件时
- 组件配置切换时

### 数据获取 (fetchWidgetData)
- 组件初始化时
- 定时数据刷新时
- 手动刷新数据时
- 数据源配置变更后

## 具体调用代码示例

### 监控项数据源配置收集
```javascript
// BiDataSourceManager.collectDataItemConfig()
collectDataItemConfig(config) {
    const deviceId = document.getElementById('deviceSelect').value;
    const dataItemId = document.getElementById('dataItemSelect').value;
    const dataMode = document.getElementById('dataMode').value;
    const historyCount = parseInt(document.getElementById('historyCount').value);
    const refreshInterval = parseInt(document.getElementById('refreshInterval').value);
    const timeFormat = document.getElementById('timeFormat').value;

    return {
        ...config,
        deviceId,
        dataItemId,
        dataMode,
        historyCount,
        refreshInterval,
        timeFormat
    };
}
```

### 静态数据源配置收集
```javascript
// BiDataSourceManager.collectStaticDataConfig()
collectStaticDataConfig(config) {
    const staticLabels = document.getElementById('staticLabels');
    const staticValues = document.getElementById('staticValues');

    return {
        ...config,
        staticLabels: staticLabels ? staticLabels.value : '',
        staticValues: staticValues ? staticValues.value : ''
    };
}
```

### 外部数据源配置收集
```javascript
// BiDataSourceManager.collectExternalDataConfig()
collectExternalDataConfig(config, widget) {
    const multiDataSetEnabled = document.getElementById('multiExternalDataSetEnabled')?.checked || false;

    if (multiDataSetEnabled) {
        return this.collectMultiExternalDataConfig(config, widget);
    } else {
        return this.collectSingleExternalDataConfig(config, widget);
    }
}
```

## 组件生命周期中的数据源调用

### 组件创建时
1. **renderWidget()** - 渲染组件DOM
2. **applyWidgetStyleConfig()** - 应用样式配置
3. **initializeEChart()** - 初始化图表（如果是图表组件）
4. **updateWidgetDataSafe()** - 安全更新组件数据

### 组件选中时
1. **updatePropertyPanel()** - 更新属性面板
2. **restoreDataSourceConfig()** - 恢复数据源配置
3. **onDataSourceTypeChange()** - 处理数据源类型变化

### 组件配置时
1. **applyWidgetConfig()** - 应用组件配置
2. **collectDataSourceConfig()** - 收集数据源配置
3. **markWidgetAsModified()** - 标记组件已修改
4. **updateWidgetData()** - 更新组件数据

### 组件保存时
1. **saveWidgetStandard()** - 标准化保存组件
2. **convertToStandardFormat()** - 转换为标准格式
3. **ensureConfigCompleteness()** - 确保配置完整性

## 数据流转过程

### 配置保存流程
```
用户修改配置 → 点击应用 → collectDataSourceConfig() →
保存到widget.dataSourceConfig → 标记组件已修改 →
触发数据更新 → fetchWidgetData() → 更新组件显示
```

### 配置恢复流程
```
选中组件 → updatePropertyPanel() → 解析dataSourceConfig →
restoreDataSourceConfig() → 恢复UI状态 →
触发数据源类型变化 → onDataSourceTypeChange()
```

### 数据获取流程
```
触发数据更新 → fetchWidgetData() → 解析数据源配置 →
根据类型获取数据 → 应用数据转换 → 更新组件显示
```

## 错误处理机制

### 数据源管理器未初始化
```javascript
const data = window.biDataSourceManager ?
    await window.biDataSourceManager.fetchWidgetData(widget) :
    { success: false, error: '数据源管理器未初始化' };
```

### 配置解析失败
```javascript
try {
    if (typeof widget.dataSourceConfig === 'string') {
        dataSourceConfig = JSON.parse(widget.dataSourceConfig);
    }
} catch (parseError) {
    console.error('解析数据源配置失败:', parseError);
    dataSourceConfig = {};
}
```

### 组件类型不支持
```javascript
const noDataSourceComponents = ['image-widget', 'decoration-widget', 'html-widget'];
if (noDataSourceComponents.includes(componentType)) {
    return {
        success: true,
        message: `${componentType} 组件无需数据源`,
        noDataRequired: true
    };
}
```

## 需要优化的问题

1. **代码重复**: 消除重复的配置映射
2. **配置污染**: 增强组件间配置隔离
3. **调用不统一**: 统一不同页面的调用方式
4. **接口复杂**: 简化组件调用接口
5. **错误处理**: 增强错误处理和容错机制

## 模块化重构建议

### 1. 统一配置管理
- 移除`bi-dashboard-designer.js`中的重复配置
- 所有配置统一由`BiDataSourceManager`管理

### 2. 增强配置隔离
- 为每个组件创建独立的配置上下文
- 防止组件间配置相互污染

### 3. 简化调用接口
- 提供更简洁的API接口
- 减少组件调用的复杂度

### 4. 标准化错误处理
- 统一错误处理机制
- 提供更好的错误提示和恢复机制
