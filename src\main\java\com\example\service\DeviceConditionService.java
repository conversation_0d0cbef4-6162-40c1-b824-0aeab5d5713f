package com.example.service;

import com.example.model.Device;
import com.example.model.DeviceCondition;
import com.example.model.AddressAlias;
import com.example.model.DataItem;
import com.example.repository.DeviceConditionRepository;
import com.example.repository.OperationLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.scheduling.annotation.Scheduled;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.time.LocalDateTime;

@Slf4j
@Service
public class DeviceConditionService {
    
    @Autowired
    private DeviceConditionRepository deviceConditionRepository;
    
    @Autowired
    private OperationLogService operationLogService;
    
    @Autowired
    private ModbusService modbusService;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Transactional(readOnly = true)
    public List<DeviceCondition> getDeviceConditions(Device device) {
        return deviceConditionRepository.findByDevice(device);
    }
    
    @Transactional(readOnly = true)
    public List<DeviceCondition> getEnabledConditions(Device device) {
        return deviceConditionRepository.findByDeviceAndEnabled(device, true);
    }
    
    @Transactional
    public DeviceCondition createCondition(Device device, DataItem sourceItem, 
            String operator, Integer compareValue, DataItem targetItem, Integer writeValue,
            Integer writeInterval, String remark) {
        validateOperator(operator);
        
        // 检查源地址和目标地址是否为同一个数据项
        if (sourceItem.getId().equals(targetItem.getId())) {
            throw new IllegalArgumentException("源地址和目标地址不能为同一个数据项");
        }
        
        // 检查写入间隔
        if (writeInterval == null || writeInterval < 100) {
            throw new IllegalArgumentException("写入间隔不能小于100毫秒");
        }
        
        // 检查是否存在完全相同的条件（包括比较值）
        boolean exists = deviceConditionRepository.findByDevice(device).stream()
            .anyMatch(c -> c.getSourceItem().getId().equals(sourceItem.getId()) &&
                         c.getTargetItem().getId().equals(targetItem.getId()) &&
                         c.getOperator().equals(operator) &&
                         c.getCompareValue().equals(compareValue) &&
                         c.getWriteValue().equals(writeValue));
        
        if (exists) {
            throw new IllegalArgumentException("已存在完全相同的条件配置");
        }
        
        DeviceCondition condition = new DeviceCondition();
        condition.setDevice(device);
        condition.setSourceItem(sourceItem);
        condition.setOperator(operator);
        condition.setCompareValue(compareValue);
        condition.setTargetItem(targetItem);
        condition.setWriteValue(writeValue);
        condition.setWriteInterval(writeInterval);
        condition.setEnabled(false);
        condition.setRemark(remark);
        
        return deviceConditionRepository.save(condition);
    }
    
    @Transactional
    public DeviceCondition updateCondition(Long conditionId, DataItem sourceItem, 
            String operator, Integer compareValue, DataItem targetItem, Integer writeValue,
            Integer writeInterval, String remark) {
        validateOperator(operator);
        
        DeviceCondition condition = deviceConditionRepository.findById(conditionId)
            .orElseThrow(() -> new IllegalArgumentException("条件不存在: " + conditionId));
        
        // 检查源地址和目标地址是否为同一个数据项
        if (sourceItem.getId().equals(targetItem.getId())) {
            throw new IllegalArgumentException("源地址和目标地址不能为同一个数据项");
        }
        
        // 检查写入间隔
        if (writeInterval == null || writeInterval < 100) {
            throw new IllegalArgumentException("写入间隔不能小于100毫秒");
        }
        
        condition.setSourceItem(sourceItem);
        condition.setOperator(operator);
        condition.setCompareValue(compareValue);
        condition.setTargetItem(targetItem);
        condition.setWriteValue(writeValue);
        condition.setWriteInterval(writeInterval);
        condition.setRemark(remark);
        
        return deviceConditionRepository.save(condition);
    }
    
    @Transactional
    public void deleteCondition(Long conditionId) {
        try {
            // 先删除关联的操作日志
            operationLogRepository.deleteByConditionId(conditionId);
            
            // 再删除条件本身
            deviceConditionRepository.deleteById(conditionId);
            
            log.info("成功删除条件及其关联日志: {}", conditionId);
        } catch (Exception e) {
            log.error("删除条件失败: {}", e.getMessage());
            throw new RuntimeException("删除条件失败: " + e.getMessage());
        }
    }
    
    @Transactional(readOnly = true)
    public Optional<DeviceCondition> getCondition(Long conditionId) {
        return deviceConditionRepository.findById(conditionId);
    }
    
    @Transactional
    public void toggleCondition(Long conditionId, boolean enabled) {
        DeviceCondition condition = deviceConditionRepository.findById(conditionId)
            .orElseThrow(() -> new IllegalArgumentException("条件不存在: " + conditionId));
        
        condition.setEnabled(enabled);
        deviceConditionRepository.save(condition);
    }
    
    @Scheduled(fixedDelay = 1000) // 每秒执行一次
    public void evaluateAllConditions() {
        List<Device> devices = deviceConditionRepository.findDistinctDevices();
        for (Device device : devices) {
            if (!device.getConnected()) {
                continue;
            }
            try {
                List<DeviceCondition> conditions = getEnabledConditions(device);
                for (DeviceCondition condition : conditions) {
                    try {
                        // 读取源地址的值
                        String sourceAddress = condition.getSourceItem().getAddress();
                        Map<String, Object> result = modbusService.readRegister(device.getId(), sourceAddress);
                        Integer sourceValue = (Integer) result.get("value");
                        
                        // 评估条件
                        evaluateCondition(condition, sourceValue);
                    } catch (Exception e) {
                        log.error("评估条件失败: {}", e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("处理设备条件失败: {}", e.getMessage());
            }
        }
    }
    
    @Transactional
    public void evaluateCondition(DeviceCondition condition, Integer currentValue) {
        if (!condition.getEnabled()) {
            return;
        }
        
        // 检查写入间隔
        LocalDateTime now = LocalDateTime.now();
        if (condition.getLastWriteTime() != null) {
            long millisSinceLastWrite = java.time.Duration.between(condition.getLastWriteTime(), now).toMillis();
            if (millisSinceLastWrite < condition.getWriteInterval()) {
                // 未达到写入间隔，跳过本次写入
                return;
            }
        }
        
        boolean shouldWrite = false;
        switch (condition.getOperator()) {
            case ">":
                shouldWrite = currentValue > condition.getCompareValue();
                break;
            case "<":
                shouldWrite = currentValue < condition.getCompareValue();
                break;
            case "=":
                shouldWrite = currentValue.equals(condition.getCompareValue());
                break;
            case ">=":
                shouldWrite = currentValue >= condition.getCompareValue();
                break;
            case "<=":
                shouldWrite = currentValue <= condition.getCompareValue();
                break;
            default:
                log.error("不支持的操作符: {}", condition.getOperator());
                return;
        }
        
        if (shouldWrite) {
            try {
                // 写入值
                modbusService.writeRegister(condition.getDevice().getId(), 
                    condition.getTargetItem().getAddress(), 
                    new int[]{condition.getWriteValue()});
                // 更新最后写入时间
                condition.setLastWriteTime(now);
                deviceConditionRepository.save(condition);
                // 记录成功日志
                operationLogService.logOperation(condition, currentValue, condition.getWriteValue(), true, "写入成功");
            } catch (Exception e) {
                log.error("写入失败: {}", e.getMessage());
                // 记录失败日志
                operationLogService.logOperation(condition, currentValue, condition.getWriteValue(), false, "写入失败: " + e.getMessage());
            }
        }
    }
    
    private void validateOperator(String operator) {
        if (!operator.matches("^[><=]{1,2}$")) {
            throw new IllegalArgumentException("无效的操作符: " + operator);
        }
    }
} 