package com.example.service;

import com.example.entity.Topology;
import com.example.entity.BiWidget;
import com.example.entity.BiDashboard;
import com.example.repository.TopologyRepository;
import com.example.repository.BiWidgetRepository;
import com.example.repository.BiDashboardRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VideoReferenceService {
    private static final Logger logger = LoggerFactory.getLogger(VideoReferenceService.class);

    private final TopologyRepository topologyRepository;
    private final BiWidgetRepository biWidgetRepository;
    private final BiDashboardRepository biDashboardRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${server.external-url}")
    private String serverExternalUrl;
    
    @Value("${upload.video.url-prefix}")
    private String videoUrlPrefix;

    @Autowired
    public VideoReferenceService(TopologyRepository topologyRepository,
                               BiWidgetRepository biWidgetRepository, 
                               BiDashboardRepository biDashboardRepository) {
        this.topologyRepository = topologyRepository;
        this.biWidgetRepository = biWidgetRepository;
        this.biDashboardRepository = biDashboardRepository;
    }

    /**
     * 检查视频是否被引用并返回引用信息
     * @param fileName 视频文件名
     * @return 包含使用状态和引用列表的Map
     */
    public Map<String, Object> checkVideoUsage(String fileName) {
        Map<String, Object> result = new HashMap<>();
        boolean isUsed = false;
        List<Map<String, String>> references = new ArrayList<>();

        try {
            // 构建可能的视频URL（用于和组件中保存的URL进行比较）
            String possibleVideoUrl = "http://" + serverExternalUrl + videoUrlPrefix + "/" + fileName;
            
            // 1. 检查拓扑引用
            List<Topology> topologies = topologyRepository.findAll();
            for (Topology topology : topologies) {
                String data = topology.getData();
                if (data != null && !data.isEmpty()) {
                    try {
                        // 简单检查：如果JSON字符串中包含文件名，再进行详细解析
                        if (data.contains(fileName)) {
                            JsonNode jsonNode = objectMapper.readTree(data);
                            
                            // 检查视频节点（如果拓扑图支持视频节点）
                            if (jsonNode.has("videoNodes")) {
                                JsonNode videoNodes = jsonNode.get("videoNodes");
                                for (JsonNode node : videoNodes) {
                                    if (node.has("videoUrl") && 
                                        node.get("videoUrl").asText().contains(fileName)) {
                                        isUsed = true;
                                        Map<String, String> reference = new HashMap<>();
                                        reference.put("type", "topology_video");
                                        reference.put("topology", topology.getName());
                                        reference.put("title", node.has("title") ? 
                                                           node.get("title").asText() : "未命名视频");
                                        references.add(reference);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析拓扑JSON数据失败: {}", e.getMessage());
                        // 解析JSON失败，继续检查下一个拓扑
                        continue;
                    }
                }
            }

            // 2. 检查BI仪表盘视频组件引用
            List<BiWidget> biWidgets = biWidgetRepository.findAll();
            for (BiWidget widget : biWidgets) {
                if ("video-widget".equals(widget.getWidgetType()) && widget.getConfig() != null) {
                    try {
                        JsonNode configNode = objectMapper.readTree(widget.getConfig());
                        if (configNode.has("videoUrl")) {
                            String videoUrl = configNode.get("videoUrl").asText();
                            if (videoUrl.contains(fileName) || videoUrl.equals(possibleVideoUrl)) {
                                isUsed = true;
                                Map<String, String> reference = new HashMap<>();
                                reference.put("type", "bi_dashboard");
                                
                                // 获取仪表盘名称
                                String dashboardName = "未知仪表盘";
                                try {
                                    BiDashboard dashboard = biDashboardRepository.findById(widget.getDashboardId()).orElse(null);
                                    if (dashboard != null) {
                                        dashboardName = dashboard.getName();
                                    }
                                } catch (Exception e) {
                                    logger.warn("获取仪表盘名称失败: {}", e.getMessage());
                                }
                                
                                reference.put("dashboardId", widget.getDashboardId().toString());
                                reference.put("dashboardName", dashboardName);
                                reference.put("widgetId", widget.getId().toString());
                                reference.put("description", dashboardName + " (ID: " + widget.getDashboardId() + ") 中的视频组件");
                                references.add(reference);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析BI组件配置失败: {}", e.getMessage());
                        // 继续检查下一个组件
                        continue;
                    }
                }
            }

            // 3. 检查BI仪表盘画布背景视频引用（如果支持视频背景）
            List<BiDashboard> biDashboards = biDashboardRepository.findAll();
            for (BiDashboard dashboard : biDashboards) {
                if (dashboard.getCanvasConfig() != null && !dashboard.getCanvasConfig().isEmpty()) {
                    try {
                        JsonNode canvasConfigNode = objectMapper.readTree(dashboard.getCanvasConfig());

                        // 检查背景视频
                        if (canvasConfigNode.has("backgroundType") && canvasConfigNode.has("backgroundVideo")) {
                            String backgroundType = canvasConfigNode.get("backgroundType").asText();
                            String backgroundVideoUrl = canvasConfigNode.get("backgroundVideo").asText();

                            // 只有背景类型为'video'且背景视频URL不为空时，才认为视频被使用
                            if ("video".equals(backgroundType) &&
                                backgroundVideoUrl != null && !backgroundVideoUrl.isEmpty() &&
                                (backgroundVideoUrl.contains(fileName) || backgroundVideoUrl.equals(possibleVideoUrl))) {
                                isUsed = true;
                                Map<String, String> reference = new HashMap<>();
                                reference.put("type", "bi_dashboard_background");
                                reference.put("dashboardId", dashboard.getId().toString());
                                reference.put("dashboardName", dashboard.getName());
                                reference.put("description", dashboard.getName() + " (ID: " + dashboard.getId() + ") 中的背景视频");
                                references.add(reference);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析BI仪表盘画布配置失败: {}", e.getMessage());
                        // 继续检查下一个仪表盘
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("检查视频使用状态时出错: {}", e.getMessage());
        }

        result.put("isUsed", isUsed);
        result.put("references", references);
        return result;
    }
}
