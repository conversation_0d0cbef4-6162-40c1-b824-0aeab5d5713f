<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态按钮效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            font-family: 'Arial', sans-serif;
            padding: 50px;
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        /* 基础按钮样式 */
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            position: relative;
            overflow: hidden;
            background: transparent;
            color: #fff;
        }
        
        /* 发光按钮 */
        .btn-glow {
            background: linear-gradient(45deg, #00d4ff, #0099ff);
            border: 2px solid #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            animation: glow-pulse 2s ease-in-out infinite alternate;
        }
        
        .btn-glow:hover {
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
            transform: translateY(-2px);
        }
        
        .btn-glow:active {
            transform: translateY(0);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
        }
        
        @keyframes glow-pulse {
            0% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
            100% { box-shadow: 0 0 25px rgba(0, 212, 255, 0.5); }
        }
        
        /* 边框动画按钮 */
        .btn-border {
            background: transparent;
            border: 2px solid #ff00cc;
            color: #ff00cc;
            position: relative;
        }
        
        .btn-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 204, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-border:hover::before {
            left: 100%;
        }
        
        .btn-border:hover {
            color: #fff;
            background: rgba(255, 0, 204, 0.1);
            box-shadow: 0 0 20px rgba(255, 0, 204, 0.4);
        }
        
        /* 渐变动画按钮 */
        .btn-gradient {
            background: linear-gradient(45deg, #ff00cc, #00ffff, #00ff88, #ffaa00);
            background-size: 400%;
            border: none;
            animation: gradient-shift 3s ease infinite;
        }
        
        .btn-gradient:hover {
            animation-duration: 1s;
            transform: scale(1.05);
        }
        
        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 波纹按钮 */
        .btn-ripple {
            background: rgba(0, 255, 136, 0.2);
            border: 2px solid #00ff88;
            color: #00ff88;
            position: relative;
            overflow: hidden;
        }
        
        .btn-ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(0, 255, 136, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        
        .btn-ripple:hover::after {
            width: 300px;
            height: 300px;
        }
        
        .btn-ripple:hover {
            color: #fff;
            border-color: #66ff99;
        }
        
        /* 粒子效果按钮 */
        .btn-particle {
            background: rgba(255, 170, 0, 0.1);
            border: 2px solid #ffaa00;
            color: #ffaa00;
            position: relative;
        }
        
        .btn-particle::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffaa00, #ff6600, #ffaa00);
            border-radius: 8px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .btn-particle:hover::before {
            opacity: 1;
            animation: particle-glow 0.5s ease;
        }
        
        .btn-particle:hover {
            color: #fff;
            background: rgba(255, 170, 0, 0.2);
        }
        
        @keyframes particle-glow {
            0% { transform: scale(0.8); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        /* 霓虹按钮 */
        .btn-neon {
            background: transparent;
            border: 2px solid #00ffff;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            box-shadow: 
                0 0 10px rgba(0, 255, 255, 0.3),
                inset 0 0 10px rgba(0, 255, 255, 0.1);
        }
        
        .btn-neon:hover {
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.6),
                0 0 40px rgba(0, 255, 255, 0.4),
                inset 0 0 20px rgba(0, 255, 255, 0.2);
            text-shadow: 0 0 15px #00ffff;
        }
        
        /* 3D按钮 */
        .btn-3d {
            background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 
                0 8px 15px rgba(0, 0, 0, 0.1),
                0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(0);
        }
        
        .btn-3d:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 12px 20px rgba(0, 0, 0, 0.15),
                0 6px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-3d:active {
            transform: translateY(-1px);
            box-shadow: 
                0 6px 10px rgba(0, 0, 0, 0.1),
                0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 加载按钮 */
        .btn-loading {
            background: rgba(102, 221, 255, 0.2);
            border: 2px solid #66ddff;
            color: #66ddff;
            position: relative;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid #66ddff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }
        
        .btn-loading:hover::after {
            opacity: 1;
        }
        
        .btn-loading:hover {
            color: transparent;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 20px;
                gap: 20px;
            }
            
            .btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-glow">发光按钮</button>
    <button class="btn btn-border">边框动画</button>
    <button class="btn btn-gradient">渐变按钮</button>
    <button class="btn btn-ripple">波纹效果</button>
    <button class="btn btn-particle">粒子效果</button>
    <button class="btn btn-neon">霓虹按钮</button>
    <button class="btn btn-3d">3D按钮</button>
    <button class="btn btn-loading">加载按钮</button>
</body>
</html>
