# BI大屏多数据集组件类型获取修复验证

## 问题诊断

### 原始错误
根据rz.txt日志文件分析，发现关键错误：
```
bi-data-source-manager.js?v=20250127:1113  组件不支持多外部数据源: undefined
```

### 根本原因
- `window.selectedWidget?.type`在配置恢复时返回`undefined`
- 配置恢复函数依赖全局状态`window.selectedWidget`，但在属性面板加载过程中该状态不稳定
- 导致无法获取正确的组件类型，进而无法找到对应的多数据集配置

## 修复方案

### 核心思路
将组件类型的获取从依赖全局状态改为直接从函数参数传递，确保配置恢复过程中有可靠的组件信息来源。

### 具体修改

#### 1. 修改`restoreDataSourceConfig`函数
- 添加组件信息日志记录
- 将widget参数传递给`restoreExternalDataConfig`

#### 2. 修改`restoreExternalDataConfig`函数
- 接受widget参数
- 添加详细的组件信息日志
- 将widget参数传递给多数据集配置恢复函数

#### 3. 修改`restoreMultiExternalDataConfig`函数
- 接受widget参数
- 添加详细的组件和配置信息日志
- 将widget参数传递给后续函数

#### 4. 修改`waitForMultiDataSetInterfaceReady`函数
- 从传入的widget参数获取组件类型
- 移除对`window.selectedWidget?.type`的依赖
- 添加组件类型和可用配置的日志记录

#### 5. 修改`restoreDataSetsConfigWithCallback`函数
- 接受widget参数
- 从widget参数获取组件类型
- 添加组件信息的错误日志

#### 6. 修改`restoreDataSetsConfig`函数
- 提供向后兼容性
- 从全局状态获取widget作为备用

## 修复效果预期

### 解决的问题
1. **组件类型获取失败** - 不再依赖不稳定的全局状态
2. **"组件不支持多外部数据源: undefined"错误** - 能够正确获取组件类型
3. **多数据集配置恢复失败** - 配置恢复流程能够正常执行

### 增强的功能
1. **详细的日志记录** - 便于问题诊断和调试
2. **更好的错误处理** - 提供更多上下文信息
3. **向后兼容性** - 保持现有API的兼容性

## 测试验证要点

### 1. 基础功能验证
- 选中配置了多数据集的组件
- 检查控制台是否还有"组件不支持多外部数据源: undefined"错误
- 验证组件类型是否正确识别

### 2. 配置恢复验证
- 页面刷新后选中组件
- 检查多数据集模式是否正确启用
- 验证数据集配置是否正确恢复
- 确认字段配置是否正确恢复

### 3. 日志验证
- 检查控制台日志是否显示正确的组件信息
- 验证配置恢复过程的日志记录
- 确认错误日志提供足够的诊断信息

### 4. 数据加载验证
- 配置恢复后组件是否正常加载数据
- 组件不再一直显示"加载中"状态
- 数据正确合并和显示

## 关键改进点

### 1. 参数传递链路完整
```
updatePropertyPanel(widget) 
  -> restoreDataSourceConfig(widget, config)
    -> restoreExternalDataConfig(widget, config)
      -> restoreMultiExternalDataConfig(widget, config)
        -> waitForMultiDataSetInterfaceReady(widget, callback)
          -> restoreDataSetsConfigWithCallback(widget, dataSets)
```

### 2. 组件类型获取策略
```javascript
// 修复前（不可靠）
const componentType = window.selectedWidget?.type;

// 修复后（可靠）
const componentType = widget?.type || widget?.widgetType;
```

### 3. 错误诊断增强
- 添加组件信息的详细日志
- 提供可用配置的列表
- 记录配置恢复的完整过程

这次修复应该彻底解决"组件不支持多外部数据源: undefined"的问题，让多数据集配置能够正确恢复和工作。
