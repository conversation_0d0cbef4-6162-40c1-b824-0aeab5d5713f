<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>操作日志</title>
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            padding: 20px;
        }
        
        /* 容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        /* 卡片样式 */
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            background: #fff;
        }
        
        .card-body {
            padding: 15px;
        }
        
        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .table th,
        .table td {
            padding: 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        /* 表单样式 */
        .filter-form {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: -8px;
        }
        
        .form-col {
            flex: 1;
            padding: 8px;
            min-width: 200px;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
        }
        
        .form-select {
            display: block;
            width: 100%;
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            appearance: none;
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 6px 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            border-radius: 4px;
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        
        .btn-outline {
            color: #0d6efd;
            background-color: transparent;
            border-color: #0d6efd;
        }
        
        .btn-outline:hover {
            color: #fff;
            background-color: #0d6efd;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            padding-left: 0;
            list-style: none;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .page-item {
            margin: 0 2px;
        }
        
        .page-link {
            display: block;
            padding: 0.5rem 0.75rem;
            color: #0d6efd;
            background-color: #fff;
            border: 1px solid #dee2e6;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .page-item.active .page-link {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        
        /* 状态标签 */
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #198754;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 style="margin-bottom: 20px;">操作日志</h2>
        
        <!-- 筛选表单 -->
        <div class="filter-form">
            <form id="filterForm">
                <div class="form-row">
                    <div class="form-col">
                        <label class="form-label">条件</label>
                        <select class="form-select" name="conditionId">
                            <option value="">全部</option>
                            <!-- 动态加载条件选项 -->
                        </select>
                    </div>
                    <div class="form-col">
                        <label class="form-label">时间范围</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="datetime-local" class="form-control" name="startTime">
                            <input type="datetime-local" class="form-control" name="endTime">
                        </div>
                    </div>
                    <div class="form-col" style="flex: 0 0 auto; align-self: flex-end;">
                        <button type="submit" class="btn">查询</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 日志列表 -->
        <div class="card">
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>条件</th>
                            <th>源值</th>
                            <th>目标值</th>
                            <th>状态</th>
                            <th>消息</th>
                        </tr>
                    </thead>
                    <tbody id="logTableBody">
                        <!-- 动态加载日志数据 -->
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <nav>
                    <ul class="pagination" id="pagination">
                        <!-- 动态加载分页 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <script th:inline="javascript">
        const deviceId = [[${deviceId}]];
        let currentPage = 0;
        let pageSize = 10;
        
        // API 请求函数
        async function fetchApi(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '请求失败');
                }
                
                return await response.json();
            } catch (error) {
                alert(error.message || '操作失败');
                throw error;
            }
        }
        
        // 加载数据
        async function loadData() {
            await Promise.all([
                loadConditions(),
                loadLogs()
            ]);
        }
        
        // 加载条件列表
        async function loadConditions() {
            try {
                const conditions = await fetchApi(`/api/conditions/device/${deviceId}`);
                const select = document.querySelector('select[name="conditionId"]');
                select.innerHTML = '<option value="">全部</option>' + conditions.map(condition => 
                    `<option value="${condition.id}">${condition.sourceAlias.aliasName} ${condition.operator} ${condition.compareValue}</option>`
                ).join('');
            } catch (error) {
                console.error('加载条件失败:', error);
            }
        }
        
        // 加载日志
        async function loadLogs(page = 0) {
            try {
                const form = document.getElementById('filterForm');
                const formData = new FormData(form);
                const params = new URLSearchParams();
                
                params.append('page', page);
                params.append('size', pageSize);
                
                const conditionId = formData.get('conditionId');
                if (conditionId) {
                    params.append('conditionId', conditionId);
                }
                
                const startTime = formData.get('startTime');
                const endTime = formData.get('endTime');
                if (startTime && endTime) {
                    params.append('start', startTime);
                    params.append('end', endTime);
                }
                
                const response = await fetchApi(`/api/logs/condition/${conditionId}/page?${params}`);
                const { content, totalPages, number } = response;
                currentPage = number;
                
                // 更新日志表格
                const tbody = document.getElementById('logTableBody');
                tbody.innerHTML = content.map(log => `
                    <tr>
                        <td>${formatDateTime(log.createdAt)}</td>
                        <td>${log.condition.sourceAlias.aliasName} ${log.condition.operator} ${log.condition.compareValue}</td>
                        <td>${log.sourceValue}</td>
                        <td>${log.targetValue}</td>
                        <td>
                            <span class="badge ${log.success ? 'badge-success' : 'badge-danger'}">
                                ${log.success ? '成功' : '失败'}
                            </span>
                        </td>
                        <td>${log.message}</td>
                    </tr>
                `).join('');
                
                // 更新分页
                updatePagination(totalPages, number);
            } catch (error) {
                console.error('加载日志失败:', error);
            }
        }
        
        // 更新分页
        function updatePagination(totalPages, currentPage) {
            const pagination = document.getElementById('pagination');
            let html = '';
            
            // 上一页
            html += `
                <li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${currentPage - 1})">上一页</a>
                </li>
            `;
            
            // 页码
            for (let i = 0; i < totalPages; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadLogs(${i})">${i + 1}</a>
                    </li>
                `;
            }
            
            // 下一页
            html += `
                <li class="page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${currentPage + 1})">下一页</a>
                </li>
            `;
            
            pagination.innerHTML = html;
        }
        
        // 格式化日期时间
        function formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        }
        
        // 监听表单提交
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            loadLogs();
        });
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html> 