package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.entity.PublishedBiDashboard;
import com.example.repository.BiDashboardRepository;
import com.example.repository.BiWidgetRepository;
import com.example.repository.PublishedBiDashboardRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class BiDashboardService {

    @Autowired
    private BiDashboardRepository dashboardRepository;

    @Autowired
    private BiWidgetRepository widgetRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PublishedBiDashboardRepository publishedBiDashboardRepository;
    
    /**
     * 创建新的大屏
     */
    @Transactional
    public BiDashboard createDashboard(String name, String description) {
        BiDashboard dashboard = new BiDashboard();
        dashboard.setName(name);
        dashboard.setDescription(description);
        dashboard.setCanvasConfig("{\"width\":1920,\"height\":1080,\"backgroundColor\":\"#ffffff\"}");
        
        BiDashboard saved = dashboardRepository.save(dashboard);
        log.info("创建新大屏: {}, ID: {}", name, saved.getId());
        return saved;
    }
    
    /**
     * 获取大屏数量
     */
    @Transactional(readOnly = true)
    public long getDashboardCount() {
        return dashboardRepository.count();
    }

    /**
     * 获取所有大屏列表
     */
    @Transactional(readOnly = true)
    public List<BiDashboard> getAllDashboards() {
        try {
            List<BiDashboard> dashboards = dashboardRepository.findAll();
            log.info("成功获取 {} 个大屏", dashboards.size());
            return dashboards;
        } catch (Exception e) {
            log.error("获取大屏列表时发生错误", e);
            throw e;
        }
    }
    
    /**
     * 根据ID获取大屏
     */
    @Transactional(readOnly = true)
    public Optional<BiDashboard> getDashboard(Long id) {
        return dashboardRepository.findById(id);
    }
    
    /**
     * 获取大屏及其组件
     */
    @Transactional(readOnly = true)
    public Optional<BiDashboard> getDashboardWithWidgets(Long id) {
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(id);
        if (dashboardOpt.isPresent()) {
            BiDashboard dashboard = dashboardOpt.get();
            List<BiWidget> widgets = getDashboardWidgets(id);
            dashboard.setWidgets(widgets);
        }
        return dashboardOpt;
    }
    
    /**
     * 保存大屏布局配置
     */
    @Transactional
    public void saveDashboardLayout(Long dashboardId, String layoutData) {
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
        if (dashboardOpt.isPresent()) {
            BiDashboard dashboard = dashboardOpt.get();
            dashboard.setCanvasConfig(layoutData);
            dashboardRepository.save(dashboard);
            log.info("保存大屏布局: {}", dashboardId);
        } else {
            throw new RuntimeException("大屏不存在: " + dashboardId);
        }
    }

    /**
     * 完善的大屏布局保存（参考report项目）
     */
    @Transactional
    public Map<String, Object> saveDashboardLayoutComplete(Long dashboardId, Map<String, Object> layoutData) {
        Map<String, Object> result = new HashMap<>();

        // 验证大屏是否存在
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
        if (!dashboardOpt.isPresent()) {
            throw new RuntimeException("大屏不存在: " + dashboardId);
        }

        BiDashboard dashboard = dashboardOpt.get();

        try {
            // 更新画布配置
            boolean canvasUpdated = false;
            if (layoutData.containsKey("canvasConfig")) {
                String canvasConfigJson = objectMapper.writeValueAsString(layoutData.get("canvasConfig"));
                dashboard.setCanvasConfig(canvasConfigJson);
                canvasUpdated = true;
                log.info("更新画布配置: {}", dashboardId);
            }

            // 删除现有组件
            widgetRepository.deleteByDashboardId(dashboardId);
            log.info("删除现有组件: {}", dashboardId);

            // 保存新组件（使用标准化格式）
            int savedWidgetCount = 0;
            if (layoutData.containsKey("widgets")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> widgets = (List<Map<String, Object>>) layoutData.get("widgets");

                for (Map<String, Object> widgetData : widgets) {
                    try {
                        BiWidget widget = createWidgetFromStandardFormat(dashboardId, widgetData);
                        widgetRepository.save(widget);
                        savedWidgetCount++;
                        log.debug("保存组件: {} - {}", widget.getWidgetType(), widget.getId());
                    } catch (Exception e) {
                        log.error("保存组件失败: {}", widgetData, e);
                        throw new RuntimeException("保存组件失败: " + e.getMessage());
                    }
                }
            }

            // 更新大屏修改时间
            dashboard.setUpdatedAt(LocalDateTime.now());
            dashboardRepository.save(dashboard);

            result.put("savedWidgets", savedWidgetCount);
            result.put("updatedCanvas", canvasUpdated);

            log.info("完成大屏布局保存: {}, 组件数: {}, 画布更新: {}",
                dashboardId, savedWidgetCount, canvasUpdated);

        } catch (Exception e) {
            log.error("保存大屏布局失败: {}", dashboardId, e);
            throw new RuntimeException("保存大屏布局失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从标准化格式创建组件（增强版，支持修改状态保存）
     */
    private BiWidget createWidgetFromStandardFormat(Long dashboardId, Map<String, Object> widgetData) throws Exception {
        BiWidget widget = new BiWidget();
        widget.setDashboardId(dashboardId);

        // 解析标准化格式
        if (!widgetData.containsKey("type") || !widgetData.containsKey("value")) {
            throw new IllegalArgumentException("组件数据格式错误，缺少type或value字段");
        }

        widget.setWidgetType((String) widgetData.get("type"));

        @SuppressWarnings("unchecked")
        Map<String, Object> value = (Map<String, Object>) widgetData.get("value");

        // 保存组件修改状态信息（如果存在）
        if (widgetData.containsKey("isModified")) {
            // 将修改状态保存到备注字段或扩展字段
            Map<String, Object> stateInfo = new HashMap<>();
            stateInfo.put("isModified", widgetData.get("isModified"));
            stateInfo.put("lastModified", widgetData.get("lastModified"));
            stateInfo.put("modifiedPaths", widgetData.get("modifiedPaths"));
            widget.setRemark(objectMapper.writeValueAsString(stateInfo));
            log.debug("保存组件修改状态: {}", stateInfo);
        }

        // 解析位置信息（修复：使用left/top而不是x/y）
        if (value.containsKey("position")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> position = (Map<String, Object>) value.get("position");

            // 正确解析left/top字段，与前端标准化格式一致
            widget.setPositionX(getIntValue(position, "left", 0));   // 使用left而不是x
            widget.setPositionY(getIntValue(position, "top", 0));    // 使用top而不是y
            widget.setWidth(getIntValue(position, "width", 300));
            widget.setHeight(getIntValue(position, "height", 200));
            widget.setZIndex(getIntValue(position, "zIndex", 1000));
            widget.setSort(getLongValue(position, "sort", 1000L));

            // 保存完整的位置配置JSON
            widget.setPosition(objectMapper.writeValueAsString(position));

            log.debug("解析组件位置: left={}, top={}, width={}, height={}, zIndex={}",
                widget.getPositionX(), widget.getPositionY(), widget.getWidth(), widget.getHeight(), widget.getZIndex());
        }

        // 保存样式配置（setup）
        if (value.containsKey("setup")) {
            String setupJson = objectMapper.writeValueAsString(value.get("setup"));
            widget.setSetup(setupJson);
            widget.setConfig(setupJson); // 保持向后兼容
        }

        // 保存数据配置（data）
        if (value.containsKey("data")) {
            String dataJson = objectMapper.writeValueAsString(value.get("data"));
            widget.setData(dataJson);
            widget.setDataSourceConfig(dataJson); // 保持向后兼容
        }

        // 保存选项配置（options）
        if (value.containsKey("options")) {
            widget.setOptions(objectMapper.writeValueAsString(value.get("options")));
        }

        // 设置其他属性
        widget.setRefreshSeconds(getIntValue(value, "refreshSeconds", null));
        widget.setEnableFlag(1);
        widget.setDeleteFlag(0);
        widget.setCreatedAt(LocalDateTime.now());
        widget.setUpdatedAt(LocalDateTime.now());

        return widget;
    }

    /**
     * 安全获取整数值
     */
    private Integer getIntValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    /**
     * 安全获取长整数值
     */
    private Long getLongValue(Map<String, Object> map, String key, Long defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return defaultValue;
    }
    
    /**
     * 检查大屏的发布记录
     */
    public List<PublishedBiDashboard> checkPublishedRecords(Long dashboardId) {
        return publishedBiDashboardRepository.findByDashboardId(dashboardId);
    }

    /**
     * 删除大屏（检查发布记录约束）
     */
    @Transactional
    public void deleteDashboard(Long id) {
        if (!dashboardRepository.existsById(id)) {
            throw new RuntimeException("大屏不存在: " + id);
        }

        // 检查是否有发布记录
        List<PublishedBiDashboard> publishedRecords = checkPublishedRecords(id);
        if (!publishedRecords.isEmpty()) {
            throw new RuntimeException("无法删除大屏：该大屏存在 " + publishedRecords.size() + " 条发布记录，请先处理发布记录或使用强制删除");
        }

        // 先删除相关组件
        widgetRepository.deleteByDashboardId(id);
        // 再删除大屏
        dashboardRepository.deleteById(id);
        log.info("删除大屏: {}", id);
    }

    /**
     * 强制删除大屏（包含发布记录）
     */
    @Transactional
    public void deleteDashboardWithPublished(Long id) {
        if (!dashboardRepository.existsById(id)) {
            throw new RuntimeException("大屏不存在: " + id);
        }

        // 先删除所有发布记录
        List<PublishedBiDashboard> publishedRecords = checkPublishedRecords(id);
        if (!publishedRecords.isEmpty()) {
            publishedBiDashboardRepository.deleteAll(publishedRecords);
            log.info("删除大屏 {} 的 {} 条发布记录", id, publishedRecords.size());
        }

        // 再删除相关组件
        widgetRepository.deleteByDashboardId(id);
        // 最后删除大屏
        dashboardRepository.deleteById(id);
        log.info("强制删除大屏: {}", id);
    }
    
    /**
     * 添加组件到大屏
     */
    @Transactional
    public BiWidget addWidget(Long dashboardId, String widgetType, Integer x, Integer y, Integer width, Integer height) {
        if (!dashboardRepository.existsById(dashboardId)) {
            throw new RuntimeException("大屏不存在: " + dashboardId);
        }

        BiWidget widget = new BiWidget();
        widget.setDashboardId(dashboardId);
        widget.setWidgetType(widgetType);
        widget.setPositionX(x);
        widget.setPositionY(y);
        widget.setWidth(width);
        widget.setHeight(height);
        widget.setConfig("{}");
        widget.setDataSourceConfig("{}");

        BiWidget saved = widgetRepository.save(widget);
        log.info("添加组件到大屏 {}: 类型={}, 位置=({},{})", dashboardId, widgetType, x, y);
        return saved;
    }
    
    /**
     * 更新组件配置
     */
    @Transactional
    public void updateWidget(Long widgetId, String config, String dataSourceConfig) {
        Optional<BiWidget> widgetOpt = widgetRepository.findById(widgetId);
        if (widgetOpt.isPresent()) {
            BiWidget widget = widgetOpt.get();
            if (config != null) {
                widget.setConfig(config);
            }
            if (dataSourceConfig != null) {
                widget.setDataSourceConfig(dataSourceConfig);
            }
            widgetRepository.save(widget);
            log.info("更新组件配置: {}", widgetId);
        } else {
            throw new RuntimeException("组件不存在: " + widgetId);
        }
    }
    
    /**
     * 删除组件
     */
    @Transactional
    public void deleteWidget(Long widgetId) {
        if (widgetRepository.existsById(widgetId)) {
            widgetRepository.deleteById(widgetId);
            log.info("删除组件: {}", widgetId);
        } else {
            throw new RuntimeException("组件不存在: " + widgetId);
        }
    }
    
    /**
     * 获取大屏的所有组件（增强版，恢复修改状态）
     */
    @Transactional(readOnly = true)
    public List<BiWidget> getDashboardWidgets(Long dashboardId) {
        List<BiWidget> widgets = widgetRepository.findByDashboardIdOrderByCreatedAt(dashboardId);

        // 为每个组件恢复修改状态信息
        for (BiWidget widget : widgets) {
            restoreWidgetModifiedState(widget);
        }

        return widgets;
    }

    /**
     * 恢复组件修改状态
     */
    private void restoreWidgetModifiedState(BiWidget widget) {
        try {
            if (widget.getRemark() != null && !widget.getRemark().isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stateInfo = objectMapper.readValue(widget.getRemark(), Map.class);

                if (stateInfo.containsKey("isModified")) {
                    // 将状态信息添加到组件的配置中，供前端使用
                    Map<String, Object> configMap = new HashMap<>();
                    if (widget.getConfig() != null && !widget.getConfig().isEmpty()) {
                        configMap = objectMapper.readValue(widget.getConfig(), Map.class);
                    }

                    configMap.put("_stateInfo", stateInfo);
                    widget.setConfig(objectMapper.writeValueAsString(configMap));

                    log.debug("恢复组件 {} 修改状态: {}", widget.getId(), stateInfo);
                }
            }
        } catch (Exception e) {
            log.warn("恢复组件 {} 修改状态失败: {}", widget.getId(), e.getMessage());
        }
    }
}
