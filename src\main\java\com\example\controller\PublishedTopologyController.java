package com.example.controller;

import com.example.entity.PublishedTopology;
import com.example.entity.Topology;
import com.example.service.DeviceService;
import com.example.service.PublishedTopologyService;
import com.example.service.TopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@Slf4j
public class PublishedTopologyController {
    
    @Autowired
    private PublishedTopologyService publishedTopologyService;
    
    @Autowired
    private TopologyService topologyService;
    
    @Autowired
    private DeviceService deviceService;
    
    /**
     * 访问已发布的组态布局
     */
    @GetMapping("/published/{token}")
    public String viewPublishedTopology(@PathVariable String token, Model model) {
        log.info("访问已发布的组态布局，令牌: {}", token);
        
        return publishedTopologyService.getPublishedTopologyByToken(token)
                .map(publishedTopology -> {
                    // 获取关联的原始拓扑布局，以确保显示最新内容
                    Topology originalTopology = publishedTopology.getTopology();
                    
                    // 使用原始拓扑布局的最新数据，而不是发布时的副本
                    model.addAttribute("topologyName", originalTopology.getName());
                    model.addAttribute("topologyData", originalTopology.getData());
                    model.addAttribute("devices", deviceService.getAllDevices());
                    model.addAttribute("publishedInfo", publishedTopology);
                    return "topology/published";
                })
                .orElse("error/expired");
    }
    
    /**
     * 显示组态布局发布管理页面
     */
    @GetMapping("/topology/publish-management")
    public String showPublishManagementPage(Model model) {
        log.info("显示组态布局发布管理页面");

        List<Topology> topologies = topologyService.getAllTopologies();
        model.addAttribute("topologies", topologies);

        // 获取所有发布记录（包括所有状态）
        List<PublishedTopology> allPublished = publishedTopologyService.getAllPublishedTopologies();

        // 为每个发布记录生成访问URL并进行实时状态检查
        List<Map<String, Object>> publishedList = allPublished.stream()
                .map(published -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", published.getId());
                    item.put("name", published.getName());
                    item.put("topologyId", published.getTopology().getId());
                    item.put("publishedAt", published.getPublishedAt());
                    item.put("expiryDate", published.getExpiryDate());

                    // 实时检查状态：如果数据库状态是ACTIVE但实际已过期，则显示为EXPIRED
                    String actualStatus = published.getStatus();
                    if ("ACTIVE".equals(actualStatus) && !published.isValid()) {
                        actualStatus = "EXPIRED";
                    }
                    item.put("status", actualStatus);

                    item.put("accessUrl", publishedTopologyService.generateAccessUrl(published));
                    return item;
                })
                .collect(Collectors.toList());

        model.addAttribute("publishedList", publishedList);
        return "topology/publish-management";
    }
    
    /**
     * 发布组态布局
     */
    @PostMapping("/api/topology/publish")
    @ResponseBody
    public ResponseEntity<?> publishTopology(@RequestBody Map<String, Object> request) {
        try {
            Long topologyId = Long.valueOf(request.get("topologyId").toString());
            Integer expiryDays = request.get("expiryDays") != null ? 
                    Integer.valueOf(request.get("expiryDays").toString()) : null;
            
            PublishedTopology published = publishedTopologyService.publishTopology(topologyId, expiryDays);
            
            Map<String, Object> response = new HashMap<>();
            response.put("id", published.getId());
            response.put("name", published.getName());
            response.put("accessToken", published.getAccessToken());
            response.put("accessUrl", publishedTopologyService.generateAccessUrl(published));
            response.put("expiryDate", published.getExpiryDate());
            response.put("status", published.getStatus());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("发布组态布局失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("error", "发布失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除已发布的组态布局记录
     */
    @PostMapping("/api/topology/revoke/{id}")
    @ResponseBody
    public ResponseEntity<?> deletePublishedTopology(@PathVariable Long id) {
        try {
            publishedTopologyService.deletePublishedTopology(id);

            Map<String, String> response = new HashMap<>();
            response.put("message", "已成功删除发布记录");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除发布记录失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("error", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取组态布局的发布记录
     */
    @GetMapping("/api/topology/{id}/published")
    @ResponseBody
    public ResponseEntity<?> getTopologyPublishRecords(@PathVariable Long id) {
        try {
            List<PublishedTopology> publishedList = publishedTopologyService.getPublishedTopologiesByTopologyId(id);
            
            List<Map<String, Object>> result = publishedList.stream()
                    .map(published -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("id", published.getId());
                        item.put("name", published.getName());
                        item.put("publishedAt", published.getPublishedAt());
                        item.put("expiryDate", published.getExpiryDate());

                        // 实时检查状态：如果数据库状态是ACTIVE但实际已过期，则显示为EXPIRED
                        String actualStatus = published.getStatus();
                        if ("ACTIVE".equals(actualStatus) && !published.isValid()) {
                            actualStatus = "EXPIRED";
                        }
                        item.put("status", actualStatus);

                        item.put("accessUrl", publishedTopologyService.generateAccessUrl(published));
                        return item;
                    })
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取发布记录失败", e);
            Map<String, String> response = new HashMap<>();
            response.put("error", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
} 