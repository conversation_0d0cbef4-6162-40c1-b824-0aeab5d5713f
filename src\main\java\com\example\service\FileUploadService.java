package com.example.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Service
public class FileUploadService {
    private static final Logger logger = LoggerFactory.getLogger(FileUploadService.class);

    @Value("${upload.image.path}")
    private String uploadPath;

    @Value("${upload.image.allowed-types}")
    private String[] allowedTypes;
    
    @Value("${upload.image.url-prefix}")
    private String urlPrefix;

    @Value("${upload.video.path}")
    private String videoUploadPath;

    @Value("${upload.video.url-prefix}")
    private String videoUrlPrefix;

    @Value("${server.external-url}")
    private String serverExternalUrl;

    public String uploadImage(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            logger.error("上传的文件为空");
            throw new IllegalArgumentException("请选择要上传的文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedFileType(contentType)) {
            logger.error("不支持的文件类型: {}", contentType);
            throw new IllegalArgumentException("不支持的文件类型: " + contentType);
        }

        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(uploadPath).isAbsolute()) {
                // 如果是绝对路径，直接使用
                uploadDir = Paths.get(uploadPath).normalize();
                logger.info("使用绝对路径: {}", uploadDir);
            } else {
                // 如果是相对路径，使用项目根目录 + 相对路径
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, uploadPath).toAbsolutePath().normalize();
                logger.info("使用相对路径: {}", uploadDir);
            }
            
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                logger.info("创建上传目录: {}", uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
            String filename = UUID.randomUUID() + extension;

            // 保存文件
            Path filePath = uploadDir.resolve(filename).normalize();
            logger.info("保存文件到: {}", filePath);
            
            // 使用REPLACE_EXISTING选项，如果文件已存在则替换
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 返回文件的完整URL
            String fileUrl = "http://" + serverExternalUrl + urlPrefix + "/" + filename;
            logger.info("文件上传成功，文件URL: {}", fileUrl);
            return fileUrl;
            
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            throw new IOException("文件上传失败: " + e.getMessage());
        }
    }

    private boolean isAllowedFileType(String contentType) {
        for (String allowedType : allowedTypes) {
            if (allowedType.equals(contentType)) {
                return true;
            }
        }
        logger.warn("不支持的文件类型: {}", contentType);
        return false;
    }

    public String uploadVideo(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            logger.error("上传的视频文件为空");
            throw new IllegalArgumentException("请选择要上传的视频文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedVideoType(contentType)) {
            logger.error("不支持的视频文件类型: {}", contentType);
            throw new IllegalArgumentException("不支持的视频文件类型: " + contentType);
        }

        // 检查文件大小（100MB限制）
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            logger.error("视频文件过大: {} bytes", file.getSize());
            throw new IllegalArgumentException("视频文件大小不能超过100MB");
        }

        try {
            // 处理上传路径
            Path uploadDir;
            if (Paths.get(videoUploadPath).isAbsolute()) {
                uploadDir = Paths.get(videoUploadPath).normalize();
            } else {
                String userDir = System.getProperty("user.dir");
                uploadDir = Paths.get(userDir, videoUploadPath).toAbsolutePath().normalize();
            }

            // 创建目录（如果不存在）
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                logger.info("创建视频上传目录: {}", uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".mp4";
            String filename = UUID.randomUUID() + extension;

            // 保存文件
            Path filePath = uploadDir.resolve(filename).normalize();
            logger.info("保存视频文件到: {}", filePath);

            // 使用REPLACE_EXISTING选项，如果文件已存在则替换
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 返回文件的完整URL
            String fileUrl = "http://" + serverExternalUrl + videoUrlPrefix + "/" + filename;
            logger.info("视频文件上传成功，文件URL: {}", fileUrl);
            return fileUrl;

        } catch (IOException e) {
            logger.error("视频文件上传失败", e);
            throw new IOException("视频文件上传失败: " + e.getMessage());
        }
    }

    private boolean isAllowedVideoType(String contentType) {
        return contentType.equals("video/mp4") ||
               contentType.equals("video/webm") ||
               contentType.equals("video/ogg") ||
               contentType.equals("video/avi") ||
               contentType.equals("video/mov") ||
               contentType.equals("video/wmv") ||
               contentType.equals("video/flv") ||
               contentType.equals("video/quicktime");
    }
}