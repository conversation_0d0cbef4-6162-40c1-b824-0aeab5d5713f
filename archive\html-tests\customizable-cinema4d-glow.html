<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可自定义CINEMA 4D文字发光特效</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
        }

        .controls input, .controls select {
            margin: 5px;
            padding: 8px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #00ffff;
            color: #00ffff;
            border-radius: 5px;
        }

        .controls label {
            color: #00ffff;
            font-size: 12px;
            display: block;
            margin-top: 10px;
        }

        .text-container {
            position: relative;
            display: inline-block;
        }

        .cinema4d-text {
            font-size: 8rem;
            font-weight: 900;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
            
            /* 基础发光效果 */
            text-shadow: 
                0 0 10px var(--glow-color-1, rgba(0, 212, 255, 0.3)),
                0 0 20px var(--glow-color-1, rgba(0, 212, 255, 0.2)),
                0 0 40px var(--glow-color-1, rgba(0, 212, 255, 0.1));
        }

        /* 上半部分发光遮罩层 */
        .glow-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: var(--mask-height, 50%);
            background: linear-gradient(
                90deg,
                transparent 0%,
                var(--glow-color-2, rgba(0, 255, 255, 0.8)) 20%,
                var(--glow-color-3, rgba(255, 0, 255, 0.8)) 50%,
                var(--glow-color-1, rgba(0, 212, 255, 0.8)) 80%,
                transparent 100%
            );
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
            z-index: 2;
            mix-blend-mode: screen;
            animation: glow-sweep var(--animation-speed, 3s) ease-in-out infinite;
        }

        /* 动态扫描光束 */
        .scan-beam {
            position: absolute;
            top: 0;
            left: -100%;
            width: 30%;
            height: var(--mask-height, 50%);
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.9) 50%,
                transparent
            );
            z-index: 3;
            animation: scan-move calc(var(--animation-speed, 3s) * 1.3) ease-in-out infinite;
            mix-blend-mode: overlay;
        }

        /* 脉冲发光效果 */
        .pulse-glow {
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(
                ellipse at center,
                var(--glow-color-2, rgba(0, 255, 255, 0.3)) 0%,
                var(--glow-color-3, rgba(255, 0, 255, 0.2)) 30%,
                transparent 70%
            );
            z-index: 0;
            animation: pulse-effect 2s ease-in-out infinite alternate;
            filter: blur(10px);
        }

        /* 3D立体效果 */
        .cinema4d-text::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            color: var(--glow-color-1, rgba(0, 212, 255, 0.5));
            z-index: -1;
            transform: translate(2px, 2px);
            text-shadow: 
                0 0 5px var(--glow-color-1, rgba(0, 212, 255, 0.8)),
                0 0 10px var(--glow-color-1, rgba(0, 212, 255, 0.6)),
                0 0 15px var(--glow-color-1, rgba(0, 212, 255, 0.4));
        }

        /* 动画定义 */
        @keyframes glow-sweep {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        @keyframes scan-move {
            0% { left: -100%; opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { left: 100%; opacity: 0; }
        }

        @keyframes pulse-effect {
            0% { transform: scale(1); opacity: 0.3; }
            100% { transform: scale(1.1); opacity: 0.6; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cinema4d-text { font-size: 4rem; }
            .controls { font-size: 12px; padding: 10px; }
        }

        @media (max-width: 480px) {
            .cinema4d-text { font-size: 2.5rem; }
            .controls { position: relative; top: auto; left: auto; margin-bottom: 20px; }
        }
    </style>
</head>
<body>
    <div class="controls">
        <label>文字内容:</label>
        <input type="text" id="textInput" value="CINEMA 4D" maxlength="20">
        
        <label>发光高度 (%):</label>
        <input type="range" id="heightSlider" min="30" max="100" value="50">
        
        <label>动画速度:</label>
        <select id="speedSelect">
            <option value="2s">快速</option>
            <option value="3s" selected>正常</option>
            <option value="5s">慢速</option>
        </select>
        
        <label>发光颜色:</label>
        <select id="colorSelect">
            <option value="cyan" selected>青色</option>
            <option value="purple">紫色</option>
            <option value="gold">金色</option>
            <option value="red">红色</option>
            <option value="green">绿色</option>
        </select>
    </div>

    <div class="text-container">
        <!-- 脉冲发光背景 -->
        <div class="pulse-glow"></div>
        
        <!-- 主文字 -->
        <div class="cinema4d-text" data-text="CINEMA 4D">CINEMA 4D</div>
        
        <!-- 上半部分发光遮罩 -->
        <div class="glow-mask"></div>
        
        <!-- 动态扫描光束 -->
        <div class="scan-beam"></div>
    </div>

    <script>
        const textElement = document.querySelector('.cinema4d-text');
        const textInput = document.getElementById('textInput');
        const heightSlider = document.getElementById('heightSlider');
        const speedSelect = document.getElementById('speedSelect');
        const colorSelect = document.getElementById('colorSelect');
        
        const colorSchemes = {
            cyan: ['rgba(0, 212, 255, 0.8)', 'rgba(0, 255, 255, 0.8)', 'rgba(255, 0, 255, 0.8)'],
            purple: ['rgba(138, 43, 226, 0.8)', 'rgba(255, 0, 255, 0.8)', 'rgba(75, 0, 130, 0.8)'],
            gold: ['rgba(255, 215, 0, 0.8)', 'rgba(255, 165, 0, 0.8)', 'rgba(255, 140, 0, 0.8)'],
            red: ['rgba(255, 0, 0, 0.8)', 'rgba(255, 69, 0, 0.8)', 'rgba(220, 20, 60, 0.8)'],
            green: ['rgba(0, 255, 0, 0.8)', 'rgba(50, 205, 50, 0.8)', 'rgba(0, 255, 127, 0.8)']
        };

        function updateText() {
            const text = textInput.value || 'CINEMA 4D';
            textElement.textContent = text;
            textElement.setAttribute('data-text', text);
        }

        function updateHeight() {
            const height = heightSlider.value + '%';
            document.documentElement.style.setProperty('--mask-height', height);
        }

        function updateSpeed() {
            const speed = speedSelect.value;
            document.documentElement.style.setProperty('--animation-speed', speed);
        }

        function updateColor() {
            const colors = colorSchemes[colorSelect.value];
            document.documentElement.style.setProperty('--glow-color-1', colors[0]);
            document.documentElement.style.setProperty('--glow-color-2', colors[1]);
            document.documentElement.style.setProperty('--glow-color-3', colors[2]);
        }

        // 事件监听
        textInput.addEventListener('input', updateText);
        heightSlider.addEventListener('input', updateHeight);
        speedSelect.addEventListener('change', updateSpeed);
        colorSelect.addEventListener('change', updateColor);

        // 初始化
        updateColor();
    </script>
</body>
</html>
