<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技感动态边框</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: transparent;
            font-family: 'Arial', sans-serif;
        }
        
        .tech-border-box {
            position: relative;
            width: 400px;
            height: 280px;
            background: transparent;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            overflow: hidden;
        }
        
        /* 角落装饰 */
        .tech-border-box::before,
        .tech-border-box::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid #00ffff;
            z-index: 2;
        }
        
        .tech-border-box::before {
            top: 10px;
            left: 10px;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }
        
        .tech-border-box::after {
            bottom: 10px;
            right: 10px;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }
        
        /* 扫描线效果 */
        .scan-line {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(0, 255, 255, 0.1) 20%, 
                rgba(0, 255, 255, 0.3) 50%, 
                rgba(0, 255, 255, 0.1) 80%, 
                transparent 100%);
            animation: scan 3s linear infinite;
            z-index: 1;
        }
        
        /* 边框发光脉冲 */
        .glow-border {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px solid #00d4ff;
            border-radius: 8px;
            animation: border-pulse 2s ease-in-out infinite;
            z-index: 0;
        }
        
        /* 数据流线条 */
        .data-line {
            position: absolute;
            width: 2px;
            height: 30px;
            background: linear-gradient(to bottom, transparent, #00ff88, transparent);
            animation: data-flow 4s linear infinite;
        }
        
        .data-line:nth-child(1) {
            top: 20px;
            right: 20px;
            animation-delay: 0s;
        }
        
        .data-line:nth-child(2) {
            bottom: 20px;
            left: 20px;
            animation-delay: 2s;
        }
        
        .content {
            background: transparent;
            border: none;
            border-radius: 6px;
            padding: 30px;
            text-align: center;
            color: #00d4ff;
            width: calc(100% - 40px);
            height: calc(100% - 40px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 3;
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            background: linear-gradient(90deg, #00d4ff, #00ffff, #00ff88);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            animation: text-glow 3s ease-in-out infinite alternate;
        }
        
        p {
            color: #66ddff;
            line-height: 1.6;
            font-size: 0.95rem;
            opacity: 1;
            text-shadow: 0 0 8px rgba(102, 221, 255, 0.6);
        }
        
        /* 动画定义 */
        @keyframes corner-glow {
            0% { 
                box-shadow: 0 0 5px #00ffff;
                opacity: 0.7;
            }
            100% { 
                box-shadow: 0 0 15px #00ffff, 0 0 25px #00ffff;
                opacity: 1;
            }
        }
        
        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        @keyframes border-pulse {
            0%, 100% { 
                opacity: 0.5;
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
            }
            50% { 
                opacity: 1;
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
            }
        }
        
        @keyframes data-flow {
            0% { opacity: 0; transform: translateY(-10px); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateY(10px); }
        }
        
        @keyframes text-glow {
            0% { filter: brightness(1); }
            100% { filter: brightness(1.2); }
        }
        
        /* 悬停效果 */
        .tech-border-box:hover {
            box-shadow: 
                0 0 30px rgba(0, 212, 255, 0.5),
                inset 0 0 30px rgba(0, 212, 255, 0.2);
        }
        
        .tech-border-box:hover .scan-line {
            animation-duration: 1.5s;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .tech-border-box {
                width: 350px;
                height: 250px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="tech-border-box">
        <div class="scan-line"></div>
        <div class="glow-border"></div>
        <div class="data-line"></div>
        <div class="data-line"></div>
        
        <div class="content">
            <h1>科技数据面板</h1>
            <p>这是一个科技感十足的动态边框组件，具有扫描线、发光效果和数据流动画，适用于数据可视化界面。</p>
        </div>
    </div>
</body>
</html>
