package com.example.controller;

import com.example.model.DataHourlyStats;
import com.example.service.DataAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/data-analysis")
@RequiredArgsConstructor
@Slf4j
public class DataAnalysisController {
    private final DataAnalysisService dataAnalysisService;
    
    @GetMapping("/hourly-stats/{dataItemId}")
    public ResponseEntity<?> getHourlyStats(
            @PathVariable String dataItemId,
            @RequestParam(defaultValue = "24") int hours) {
        try {
            List<DataHourlyStats> stats = dataAnalysisService.getHourlyStats(dataItemId, hours);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", stats.stream().map(stat -> {
                Map<String, Object> statMap = new HashMap<>();
                statMap.put("hourTimestamp", stat.getHourTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                statMap.put("maxValue", stat.getMaxValue());
                statMap.put("minValue", stat.getMinValue());
                statMap.put("avgValue", stat.getAvgValue());
                statMap.put("diffValue", stat.getDiffValue());
                statMap.put("sampleCount", stat.getSampleCount());
                return statMap;
            }).collect(Collectors.toList()));
            
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        } catch (Exception e) {
            log.error("获取小时统计数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    @PostMapping("/calculate-stats")
    public ResponseEntity<?> manualCalculateStats() {
        try {
            dataAnalysisService.calculateHourlyStats();
            Map<String, Object> successResult = new HashMap<>();
            successResult.put("success", true);
            successResult.put("message", "统计数据计算完成");
            return ResponseEntity.ok(successResult);
        } catch (Exception e) {
            log.error("手动计算统计数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "计算失败");
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    @PostMapping("/recalculate-stats/{dataItemId}")
    public ResponseEntity<?> recalculateStatsByTimeRange(
            @PathVariable String dataItemId,
            @RequestParam(defaultValue = "24") int hours) {
        try {
            dataAnalysisService.recalculateStatsByTimeRange(dataItemId, hours);
            Map<String, Object> successResult = new HashMap<>();
            successResult.put("success", true);
            successResult.put("message", String.format("已重新计算最近%d小时的统计数据", hours));
            return ResponseEntity.ok(successResult);
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        } catch (Exception e) {
            log.error("重新计算统计数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "重新计算失败");
            return ResponseEntity.status(500).body(errorResult);
        }
    }
}
