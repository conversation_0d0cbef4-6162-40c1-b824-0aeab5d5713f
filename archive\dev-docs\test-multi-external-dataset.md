# BI大屏多外部数据集功能测试指南

## 功能概述
为BI大屏中的外部数据源增加了多数据集功能，支持同时使用多个外部数据集，并提供数据合并显示。

## 主要功能特性

### 1. 多数据集模式开关
- 在外部数据源配置中添加了"启用多数据集模式"开关
- 可以在单数据集和多数据集模式之间切换

### 2. 支持的组件类型
- 饼图 (pie-chart)
- 柱状图 (bar-chart) 
- 水平柱状图 (horizontal-bar-chart)
- 折线图 (line-chart)
- 数据表格 (data-table)

### 3. 数据合并策略
- **联合模式 (union)**: 合并所有数据集的数据，添加数据源标识
- **分离模式 (separate)**: 保持各数据集独立显示，用分隔符区分

### 4. 字段配置
- **图表组件**: 每个数据集可独立配置标签字段和数值字段
- **表格组件**: 每个数据集可配置多个表格字段映射

## 测试步骤

### 前置条件
1. 确保系统中已创建多个外部数据集
2. 数据集包含不同的字段结构用于测试

### 测试用例1: 饼图多数据集
1. 创建新的BI仪表盘
2. 添加饼图组件
3. 选择"外部数据源"
4. 启用"多数据集模式"
5. 添加2-3个数据集配置
6. 为每个数据集配置标签字段和数值字段
7. 选择合并策略（联合/分离）
8. 保存并预览效果

### 测试用例2: 表格多数据集
1. 添加数据表格组件
2. 选择"外部数据源"并启用多数据集模式
3. 添加多个数据集
4. 为每个数据集配置表格字段映射
5. 测试不同的合并策略
6. 验证数据显示效果

### 测试用例3: 错误处理
1. 配置无效的数据集ID
2. 配置空的字段映射
3. 测试网络错误情况
4. 验证错误信息显示

## 预期结果

### 联合模式
- 所有数据集的数据合并显示
- 每条数据带有数据源标识
- 图表中标签包含数据集别名

### 分离模式  
- 各数据集数据独立显示
- 数据集之间有明显分隔
- 保持原始数据结构

### 错误处理
- 部分数据集失败时，成功的数据集仍能正常显示
- 显示详细的错误信息
- 提供重试机制

## 技术实现要点

### 前端改动
1. `bi-data-source-manager.js`: 添加多数据集配置收集和数据处理逻辑
2. `bi-dashboard-designer.js`: 添加多数据集UI管理功能
3. `dashboard-designer.html`: 添加多数据集配置界面

### 后端改动
1. `BiDataService.java`: 添加多数据集数据处理逻辑
2. `BiDashboardController.java`: 添加批量数据集查询API

### 数据流程
1. 前端收集多数据集配置
2. 并行查询所有数据集
3. 根据合并策略处理数据
4. 返回格式化的合并结果

## 注意事项
1. 多数据集查询采用并行处理，提高性能
2. 支持部分失败容错，不会因单个数据集失败而影响整体
3. 提供详细的错误信息和警告提示
4. 保持向后兼容，不影响现有单数据集功能
