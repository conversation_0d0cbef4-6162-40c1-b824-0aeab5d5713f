package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Calendar;
import java.util.Date;

@Entity
@Table(name = "published_bi_dashboards")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PublishedBiDashboard {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "access_token", nullable = false, unique = true)
    private String accessToken;
    
    @Column(name = "expiry_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiryDate;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    
    @Column(name = "published_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date publishedAt;
    
    @Column(name = "status", nullable = false)
    private String status; // ACTIVE, EXPIRED, REVOKED
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "dashboard_id", nullable = false)
    private BiDashboard dashboard;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
        publishedAt = new Date();
        if (status == null) {
            status = "ACTIVE";
        }
    }
    
    // 检查发布是否有效
    @Transient
    public boolean isValid() {
        if (!"ACTIVE".equals(status)) {
            return false;
        }
        
        if (expiryDate == null) {
            return true; // 永久有效
        }
        
        return expiryDate.after(new Date());
    }
    
    // 获取剩余有效天数
    @Transient
    public long getRemainingDays() {
        if (expiryDate == null) {
            return -1; // 永久有效
        }
        
        long diffInMillies = expiryDate.getTime() - new Date().getTime();
        return diffInMillies / (24 * 60 * 60 * 1000);
    }
}
