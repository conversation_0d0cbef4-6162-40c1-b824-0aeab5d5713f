# 📁 项目根目录文件清理分析

## 🎯 文件分类

### ✅ 必须保留的核心文件
- `README.md` - 项目说明文档
- `pom.xml` - Maven项目配置文件
- `src/` - 源代码目录
- `target/` - 编译输出目录
- `license/` - 许可证管理系统
- `docs/` - 项目文档目录
- `upload/` - 上传文件目录
- `sdplc.sql` - 数据库脚本

### 🔧 可能有用的工具文件
- `run.bat` - 项目启动脚本
- `test-activation.ps1` - 激活测试脚本

### 🗑️ 建议清理的文件类型

#### 1. HTML测试文件（约40个）
- `3D立方体科技感动画.html`
- `3D网格轨迹效果.html`
- `Portal边框动态效果.html`
- `browser_test.html`
- `cinema4d-text-glow-effect.html`
- `clean-border-effect.html`
- `customizable-cinema4d-glow.html`
- `datav-style-border.html`
- `decorative-effects.html`
- `dynamic-buttons.html`
- `fixed-border.html`
- `fixed-button.html`
- `fixed-decoration.html`
- `fixed-dynamic-border.html`
- `fixed-dynamic-button.html`
- `fixed-responsive-decoration.html`
- `fully-responsive-button.html`
- `html-widget-test.html`
- `optimized-border.html`
- `responsive-border.html`
- `responsive-decoration.html`
- `responsive-dynamic-button.html`
- `simple-decoration.html`
- `single-dynamic-button.html`
- `tech-border.html`
- `test-aggregation.html`
- `test-alias-fix.html`
- `test-complete-flow.html`
- `test-config-restore-fix.html`
- `test-config-restore.html`
- `test-date-format-options.html`
- `test-date-format-sorting.html`
- `test-date-format.html`
- `test-dom-conflict-fix.html`
- `test-field-options-fix.html`
- `test-html-widget.html`
- `test-layout-adjustment.html`
- `test-layout-fix.html`
- `test-output-limit.html`
- `test-smart-limit.html`
- `test-sql-consistency.html`
- `test-time-filter.html`
- `test_multi_line_chart.html`
- `transparent-colorful-border.html`
- `transparent-tech-border.html`
- `Untitled-1.html`
- `HTML代码组件模板.html`
- `优化后的科技感动态边框.html`
- `优化后的透明科技边框.html`
- `动态粒子点线效果.html`
- `霓虹玻璃边框效果.html`

#### 2. 开发过程文档（约60个）
- 各种修复报告和完成文档
- 任务分析文档
- 测试报告
- 问题分析文档

#### 3. 测试脚本文件
- `basic_test.ps1`
- `create_test_dashboard.ps1`
- `simple_test.ps1`
- `test_api.ps1`
- `component_state_test.js`
- `test_time_format_fix.js`
- `verify-dom-fix.js`
- `verify-sql-output.js`
- `verify_multi_line_chart.js`
- `快速检查脚本.js`

#### 4. 临时文件
- `gg.txt`
- `rz.txt`
- `rz2.txt`

## 📊 清理建议

### 🎯 清理策略
1. **创建archive目录** - 将开发过程文件归档
2. **删除临时文件** - 删除明显的临时文件
3. **保留核心功能** - 保留项目运行必需的文件
4. **整理文档** - 将重要文档移到docs目录

### 📁 建议的目录结构
```
sdplc/
├── src/                    # 源代码
├── target/                 # 编译输出
├── docs/                   # 项目文档
├── license/                # 许可证系统
├── upload/                 # 上传文件
├── archive/                # 开发过程归档
│   ├── html-tests/         # HTML测试文件
│   ├── dev-docs/           # 开发文档
│   └── scripts/            # 测试脚本
├── README.md               # 项目说明
├── pom.xml                 # Maven配置
├── sdplc.sql              # 数据库脚本
└── run.bat                # 启动脚本
```
