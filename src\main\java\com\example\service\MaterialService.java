package com.example.service;

import com.example.entity.MaterialCategory;
import com.example.entity.MaterialFile;
import com.example.repository.MaterialCategoryRepository;
import com.example.repository.MaterialFileRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Service
@Slf4j
public class MaterialService {
    
    @Autowired
    private MaterialCategoryRepository categoryRepository;
    
    @Autowired
    private MaterialFileRepository fileRepository;
    
    @Value("${upload.material.path}")
    private String materialUploadPath;

    @Value("${server.external-url}")
    private String serverExternalUrl;

    @Value("${upload.material.url-prefix}")
    private String materialUrlPrefix;
    
    /**
     * 获取所有分类
     */
    @Transactional(readOnly = true)
    public List<MaterialCategory> getAllCategories() {
        return categoryRepository.findAllOrderByTypeAndSortOrder();
    }
    
    /**
     * 根据类型获取分类
     */
    @Transactional(readOnly = true)
    public List<MaterialCategory> getCategoriesByType(MaterialCategory.MaterialType type) {
        return categoryRepository.findByTypeOrderBySortOrderAsc(type);
    }
    
    /**
     * 获取分类详情
     */
    @Transactional(readOnly = true)
    public Optional<MaterialCategory> getCategoryById(Long id) {
        return categoryRepository.findById(id);
    }
    
    /**
     * 创建新分类
     */
    @Transactional
    public MaterialCategory createCategory(String name, MaterialCategory.MaterialType type, String description) {
        // 检查名称是否重复
        if (categoryRepository.existsByNameAndType(name, type)) {
            throw new RuntimeException("该类型下已存在同名分类");
        }
        
        MaterialCategory category = new MaterialCategory();
        category.setName(name);
        category.setType(type);
        category.setDescription(description);
        
        // 设置排序顺序为该类型下的最大值+1
        Integer maxSortOrder = categoryRepository.findMaxSortOrderByType(type);
        category.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        
        MaterialCategory saved = categoryRepository.save(category);
        log.info("创建素材分类: {}, 类型: {}", name, type);
        return saved;
    }
    
    /**
     * 更新分类
     */
    @Transactional
    public MaterialCategory updateCategory(Long id, String name, String description) {
        MaterialCategory category = categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        // 检查名称是否与其他分类重复
        if (!category.getName().equals(name) && 
            categoryRepository.existsByNameAndType(name, category.getType())) {
            throw new RuntimeException("该类型下已存在同名分类");
        }
        
        category.setName(name);
        category.setDescription(description);
        
        MaterialCategory saved = categoryRepository.save(category);
        log.info("更新素材分类: {}", id);
        return saved;
    }
    
    /**
     * 删除分类
     */
    @Transactional
    public void deleteCategory(Long id) {
        MaterialCategory category = categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        // 检查是否有素材文件
        long fileCount = fileRepository.countByCategory(category);
        if (fileCount > 0) {
            throw new RuntimeException("该分类下还有素材文件，无法删除");
        }
        
        categoryRepository.delete(category);
        log.info("删除素材分类: {}", id);
    }
    
    /**
     * 根据分类获取素材文件
     */
    @Transactional(readOnly = true)
    public List<MaterialFile> getMaterialsByCategory(Long categoryId) {
        return fileRepository.findByCategoryIdOrderBySortOrderAsc(categoryId);
    }
    
    /**
     * 根据类型获取所有素材文件
     */
    @Transactional(readOnly = true)
    public List<MaterialFile> getMaterialsByType(MaterialCategory.MaterialType type) {
        return fileRepository.findByMaterialTypeOrderBySortOrder(type);
    }
    
    /**
     * 获取所有素材文件
     */
    @Transactional(readOnly = true)
    public List<MaterialFile> getAllMaterials() {
        return fileRepository.findAllByOrderByCreatedAtDesc();
    }

    /**
     * 获取默认素材文件
     */
    @Transactional(readOnly = true)
    public List<MaterialFile> getDefaultMaterials() {
        return fileRepository.findByIsDefaultTrueOrderBySortOrderAsc();
    }

    /**
     * 上传素材文件
     */
    @Transactional
    public MaterialFile uploadMaterial(MultipartFile file, Long categoryId, boolean isDefault) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("请选择要上传的文件");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedMaterialType(contentType)) {
            throw new IllegalArgumentException("不支持的文件类型: " + contentType);
        }

        // 检查分类是否存在
        MaterialCategory category = null;
        if (categoryId != null) {
            category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        }

        // 处理上传路径
        Path uploadDir = getUploadDirectory();
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null ?
            originalFilename.substring(originalFilename.lastIndexOf(".")) : ".png";
        String filename = UUID.randomUUID() + extension;

        // 保存文件
        Path filePath = uploadDir.resolve(filename).normalize();
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

        // 创建素材文件记录
        MaterialFile materialFile = new MaterialFile();
        materialFile.setFilename(filename);
        materialFile.setOriginalName(originalFilename);
        materialFile.setFileUrl("http://" + serverExternalUrl + materialUrlPrefix + "/" + filename);
        materialFile.setFileSize(file.getSize());
        materialFile.setFileType(contentType);
        materialFile.setCategory(category);
        materialFile.setIsDefault(isDefault);

        // 设置排序顺序
        if (category != null) {
            Integer maxSortOrder = fileRepository.findMaxSortOrderByCategoryId(categoryId);
            materialFile.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        }

        MaterialFile saved = fileRepository.save(materialFile);
        log.info("上传素材文件: {}, 分类: {}", filename, categoryId);
        return saved;
    }

    /**
     * 删除素材文件
     */
    @Transactional
    public void deleteMaterial(Long id) throws IOException {
        MaterialFile materialFile = fileRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("素材文件不存在"));

        // 删除物理文件
        Path uploadDir = getUploadDirectory();
        Path filePath = uploadDir.resolve(materialFile.getFilename()).normalize();
        if (Files.exists(filePath)) {
            Files.delete(filePath);
        }

        // 删除数据库记录
        fileRepository.delete(materialFile);
        log.info("删除素材文件: {}", id);
    }

    /**
     * 更新素材文件信息
     */
    @Transactional
    public MaterialFile updateMaterial(Long id, String originalName, Long categoryId, boolean isDefault) {
        MaterialFile materialFile = fileRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("素材文件不存在"));

        MaterialCategory category = null;
        if (categoryId != null) {
            category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        }

        materialFile.setOriginalName(originalName);
        materialFile.setCategory(category);
        materialFile.setIsDefault(isDefault);

        MaterialFile saved = fileRepository.save(materialFile);
        log.info("更新素材文件: {}", id);
        return saved;
    }

    /**
     * 获取素材上传目录
     */
    private Path getUploadDirectory() {
        if (Paths.get(materialUploadPath).isAbsolute()) {
            return Paths.get(materialUploadPath).normalize();
        } else {
            String userDir = System.getProperty("user.dir");
            return Paths.get(userDir, materialUploadPath).toAbsolutePath().normalize();
        }
    }

    /**
     * 检查是否为允许的素材文件类型
     */
    private boolean isAllowedMaterialType(String contentType) {
        return contentType.equals("image/jpeg") ||
               contentType.equals("image/png") ||
               contentType.equals("image/gif") ||
               contentType.equals("image/webp") ||
               contentType.equals("image/apng");
    }
}
