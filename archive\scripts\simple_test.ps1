# 简化的API测试脚本

Write-Host "=== 大屏设计器API测试 ===" -ForegroundColor Green

# 测试基本连接
Write-Host "`n测试基本连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8080' -Method Get -TimeoutSec 5
    Write-Host "✅ 应用运行正常，状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ 应用连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试API端点（不需要登录的）
Write-Host "`n测试API端点..." -ForegroundColor Yellow
try {
    # 尝试访问大屏列表API
    $dashboardResponse = Invoke-RestMethod -Uri 'http://localhost:8080/api/bi/dashboards' -Method Get -ErrorAction Stop
    Write-Host "✅ 大屏API访问成功" -ForegroundColor Green
    
    if ($dashboardResponse.success -and $dashboardResponse.data) {
        Write-Host "大屏数量: $($dashboardResponse.data.Count)" -ForegroundColor Cyan
        
        if ($dashboardResponse.data.Count -gt 0) {
            $firstDashboard = $dashboardResponse.data[0]
            Write-Host "第一个大屏: $($firstDashboard.name) (ID: $($firstDashboard.id))" -ForegroundColor Cyan
            
            # 测试获取组件
            try {
                $widgetResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/bi/dashboard/$($firstDashboard.id)/widgets" -Method Get
                Write-Host "✅ 组件API访问成功" -ForegroundColor Green
                Write-Host "组件数量: $($widgetResponse.data.Count)" -ForegroundColor Cyan
                
                # 检查组件配置
                if ($widgetResponse.data.Count -gt 0) {
                    $widget = $widgetResponse.data[0]
                    Write-Host "第一个组件类型: $($widget.widgetType)" -ForegroundColor Cyan
                    
                    if ($widget.config) {
                        Write-Host "组件配置长度: $($widget.config.Length) 字符" -ForegroundColor Cyan
                        
                        # 检查是否包含状态信息
                        if ($widget.config.Contains('_stateInfo')) {
                            Write-Host "✅ 发现组件状态信息" -ForegroundColor Green
                        } else {
                            Write-Host "⚠️ 未发现组件状态信息" -ForegroundColor Yellow
                        }
                    }
                }
            } catch {
                Write-Host "❌ 组件API访问失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
} catch {
    Write-Host "❌ API访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
