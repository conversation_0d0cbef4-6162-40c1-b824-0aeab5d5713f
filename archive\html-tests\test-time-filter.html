<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间筛选功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .filter-condition {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
        }
        .filter-condition-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .filter-condition-content {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }
        .logic-operator {
            text-align: center;
            font-weight: bold;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .sql-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>时间筛选功能测试</h2>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>筛选条件配置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 字段配置区域 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">字段配置</label>
                            <div id="fieldConfigList">
                                <!-- 字段列表将动态加载 -->
                            </div>
                        </div>
                        
                        <!-- 筛选条件构建器 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">筛选条件</label>
                            <div class="filter-builder">
                                <div class="filter-conditions" id="filterConditions">
                                    <!-- 筛选条件将动态加载 -->
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addFilterBtn">
                                    <i class="bi bi-plus"></i> 添加条件
                                </button>
                            </div>
                        </div>

                        <!-- 聚合配置 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">聚合配置</label>
                            <div class="aggregation-config">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAggregation">
                                    <label class="form-check-label" for="enableAggregation">
                                        启用聚合查询
                                    </label>
                                </div>
                                <div id="aggregationOptions" style="display: none;">
                                    <div class="mb-2">
                                        <label for="aggregationType" class="form-label form-label-sm">聚合类型</label>
                                        <select class="form-select form-select-sm" id="aggregationType">
                                            <option value="MAX">最大值</option>
                                            <option value="MIN">最小值</option>
                                            <option value="AVG">平均值</option>
                                            <option value="SUM">求和</option>
                                            <option value="COUNT">计数</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="timeField" class="form-label form-label-sm">时间字段</label>
                                        <select class="form-select form-select-sm" id="timeField">
                                            <option value="">选择时间字段（用于排序）</option>
                                            <!-- 时间字段选项将动态加载 -->
                                        </select>
                                        <div class="form-text small">当有多个相同聚合值时，按此字段排序取最新记录</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm" id="refreshSqlBtn">
                                <i class="bi bi-arrow-clockwise"></i> 重新生成SQL
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" id="validateSqlBtn">
                                <i class="bi bi-check-circle"></i> 验证SQL
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="resetFieldsBtn">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置字段
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>生成的SQL</h5>
                    </div>
                    <div class="card-body">
                        <div class="sql-output" id="customSql">
                            <!-- SQL将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="src/main/resources/static/js/dataset-field-configurator.js"></script>
    <script>
        // 模拟测试数据
        const mockTable = {
            name: 'test_table',
            comment: '测试表'
        };
        
        const mockFields = [
            { name: 'id', type: 'INT', size: 11, remarks: '主键ID' },
            { name: 'name', type: 'VARCHAR', size: 100, remarks: '姓名' },
            { name: 'create_date', type: 'DATE', remarks: '创建日期' },
            { name: 'update_time', type: 'DATETIME', remarks: '更新时间' },
            { name: 'birth_timestamp', type: 'TIMESTAMP', remarks: '出生时间戳' },
            { name: 'age', type: 'INT', size: 3, remarks: '年龄' },
            { name: 'status', type: 'VARCHAR', size: 20, remarks: '状态' }
        ];
        
        // 初始化字段配置器
        document.addEventListener('DOMContentLoaded', function() {
            window.fieldConfigurator = new DataSetFieldConfigurator();
            window.fieldConfigurator.init();
            window.fieldConfigurator.setTableAndFields(mockTable, mockFields);
            
            console.log('时间筛选功能测试页面已加载');
            console.log('可用字段:', mockFields);
        });
    </script>
</body>
</html>
