package com.example.config;

import io.vertx.core.Vertx;
import io.vertx.mqtt.MqttServer;
import io.vertx.mqtt.MqttServerOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Configuration
public class MqttBrokerConfig {

    private Vertx vertx;
    private MqttServer mqttServer;
    private final ConcurrentHashMap<String, String> lastMessages = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<String>> subscriptions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, io.vertx.mqtt.MqttEndpoint> connectedClients = new ConcurrentHashMap<>();

    @Bean
    public MqttServer mqttServer() {
        try {
            vertx = Vertx.vertx();
            MqttServerOptions options = new MqttServerOptions()
                .setPort(1883)
                .setHost("0.0.0.0");

            mqttServer = MqttServer.create(vertx, options);

            // 处理客户端连接
            mqttServer.endpointHandler(endpoint -> {
                String clientId = endpoint.clientIdentifier();
                log.info("MQTT Client [{}] requesting connection", clientId);

                // 接受连接
                endpoint.accept(false);
                connectedClients.put(clientId, endpoint);

                // 处理订阅请求
                endpoint.subscribeHandler(subscribe -> {
                    subscribe.topicSubscriptions().forEach(s -> {
                        String topic = s.topicName();
                        log.info("Client [{}] subscription for topic [{}] with QoS [{}]", 
                            clientId, topic, s.qualityOfService());
                        
                        // 添加订阅
                        subscriptions.computeIfAbsent(topic, k -> new CopyOnWriteArrayList<>())
                            .addIfAbsent(clientId);

                        // 如果有保留的消息，发送给新订阅者
                        String lastMessage = lastMessages.get(topic);
                        if (lastMessage != null) {
                            endpoint.publish(topic, io.vertx.core.buffer.Buffer.buffer(lastMessage), 
                                s.qualityOfService(), false, true);
                        }
                    });
                });

                // 处理取消订阅请求
                endpoint.unsubscribeHandler(unsubscribe -> {
                    unsubscribe.topics().forEach(topic -> {
                        log.info("Client [{}] unsubscription from topic [{}]", clientId, topic);
                        // 移除订阅
                        CopyOnWriteArrayList<String> subscribers = subscriptions.get(topic);
                        if (subscribers != null) {
                            subscribers.remove(clientId);
                            if (subscribers.isEmpty()) {
                                subscriptions.remove(topic);
                            }
                        }
                    });
                });

                // 处理发布消息
                endpoint.publishHandler(message -> {
                    String topic = message.topicName();
                    String payload = message.payload().toString();
                    log.debug("Received message from client [{}]: topic=[{}], payload=[{}]", 
                        clientId, topic, payload);
                    
                    // 存储保留的消息
                    if (message.isRetain()) {
                        lastMessages.put(topic, payload);
                    }
                    
                    // 转发消息给所有订阅者
                    CopyOnWriteArrayList<String> subscribers = subscriptions.get(topic);
                    if (subscribers != null) {
                        subscribers.forEach(subscriberId -> {
                            if (!subscriberId.equals(clientId)) {
                                io.vertx.mqtt.MqttEndpoint subscriberEndpoint = connectedClients.get(subscriberId);
                                if (subscriberEndpoint != null) {
                                    subscriberEndpoint.publish(topic, message.payload(), 
                                        message.qosLevel(), message.isDup(), message.isRetain());
                                }
                            }
                        });
                    }
                });

                // 处理断开连接
                endpoint.disconnectHandler(v -> {
                    log.info("MQTT Client [{}] disconnected", clientId);
                    connectedClients.remove(clientId);
                    // 清理该客户端的所有订阅
                    subscriptions.values().forEach(subscribers -> subscribers.remove(clientId));
                });

                // 处理连接关闭
                endpoint.closeHandler(v -> {
                    log.info("MQTT Client [{}] connection closed", clientId);
                    connectedClients.remove(clientId);
                    // 清理该客户端的所有订阅
                    subscriptions.values().forEach(subscribers -> subscribers.remove(clientId));
                });
            });

            // 启动服务器
            mqttServer.listen(ar -> {
                if (ar.succeeded()) {
                    log.info("MQTT Broker started successfully");
                    log.info("TCP endpoint: tcp://localhost:1883");
                } else {
                    log.error("Failed to start MQTT Broker", ar.cause());
                }
            });

            return mqttServer;
        } catch (Exception e) {
            log.error("Failed to create MQTT Broker", e);
            throw new RuntimeException("Failed to create MQTT Broker", e);
        }
    }

    @PreDestroy
    public void stopBroker() {
        if (mqttServer != null) {
            try {
                mqttServer.close();
                log.info("MQTT Server stopped");
            } catch (Exception e) {
                log.error("Error stopping MQTT Server", e);
            }
        }
        if (vertx != null) {
            try {
                vertx.close();
                log.info("Vertx instance stopped");
            } catch (Exception e) {
                log.error("Error stopping Vertx instance", e);
            }
        }
    }

    // 获取主题的最后一条消息
    public String getLastMessage(String topic) {
        return lastMessages.get(topic);
    }
} 