# 序列号许可系统测试指南

## 测试环境
- 应用地址：http://localhost:8080
- 序列号生成器：SimpleSerialGenerator.java

## 测试步骤

### 1. 生成测试序列号
运行序列号生成器：
```bash
java SimpleSerialGenerator
```

输入测试数据：
- 用户ID：USER001
- 开始日期：20250726
- 结束日期：20250727

生成的序列号：
```
SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP
```

### 2. 测试许可检查
1. 访问 http://localhost:8080
2. 应该自动重定向到激活页面：http://localhost:8080/license/activation
3. 页面显示硬件标识和激活表单

### 3. 测试激活功能
在激活页面输入：
- 用户ID：USER001
- 序列号：SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP

点击"激活软件"按钮

### 4. 验证激活结果
激活成功后：
1. 显示"激活成功"消息
2. 自动跳转到登录页面
3. 在应用根目录生成 license.json 文件

### 5. 测试许可验证
重新访问 http://localhost:8080
应该直接进入登录页面，不再显示激活页面

## 预期结果

### 激活前
- 访问任何页面都重定向到激活页面
- 无 license.json 文件

### 激活后
- 生成 license.json 文件，包含：
  - userId: "USER001"
  - serialNumber: "SDPLC-..."
  - installTime: "2025-07-26T..."
  - hardwareId: "..."
- 访问应用正常进入登录页面

### 许可文件示例
```json
{
  "userId": "USER001",
  "serialNumber": "SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP",
  "installTime": "2025-07-26T14:45:00",
  "hardwareId": "ABC123DEF456"
}
```

## 错误测试

### 1. 无效序列号
输入错误的序列号，应显示"序列号无效"

### 2. 用户ID不匹配
输入不匹配的用户ID，应显示"序列号无效或用户ID不匹配"

### 3. 过期序列号
使用过期日期的序列号，应显示相应错误信息

### 4. 硬件变更
删除 `C:\ProgramData\SHENGDA-PLC\license.json` 文件，模拟硬件变更，应重新要求激活

**注意**：重新激活按钮已在生产版本中移除，如需重新激活请手动删除许可证文件

## API测试

⚠️ **注意：以下API接口已在生产版本中移除，仅供开发参考**

### ~~许可信息查询~~ (已移除)
```bash
# curl http://localhost:8080/license/info
# 此接口已移除，避免许可证信息泄露
```

### ~~序列号验证~~ (已移除)
```bash
# curl -X POST http://localhost:8080/license/validate \
#   -d "userId=USER001&serialNumber=SDPLC-..."
# 此接口已移除，避免调试接口暴露
```

## 故障排除

### 常见问题
1. **激活页面不显示**：检查拦截器配置和许可检查逻辑
2. **序列号验证失败**：检查加密密钥一致性
3. **硬件ID不匹配**：检查硬件信息获取逻辑
4. **许可文件无法生成**：检查文件写入权限
5. **需要重新激活**：手动删除 `C:\ProgramData\SHENGDA-PLC\license.json` 文件后重新访问应用

### 调试信息
- 查看应用日志中的许可相关信息
- 检查 license.json 文件内容
- 使用序列号生成器的验证功能

## 成功标准
- ✅ 无许可时自动重定向到激活页面
- ✅ 序列号生成器正常工作
- ✅ 激活功能正常
- ✅ 许可文件正确生成
- ✅ 激活后正常访问应用
- ✅ 硬件绑定有效
