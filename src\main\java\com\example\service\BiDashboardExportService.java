package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.entity.HtmlCodeSnippet;
import com.example.repository.BiDashboardRepository;
import com.example.repository.BiWidgetRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class BiDashboardExportService {

    @Autowired
    private BiDashboardRepository dashboardRepository;

    @Autowired
    private BiWidgetRepository widgetRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BiDashboardResourceAnalyzer resourceAnalyzer;

    @Autowired
    private BiDashboardResourceCollector resourceCollector;

    @Autowired
    private HtmlCodeExportService htmlCodeExportService;

    /**
     * 导出大屏模板（不包含数据源配置）
     */
    public Map<String, Object> exportDashboardTemplate(Long dashboardId) {
        log.info("开始导出大屏模板，ID: {}", dashboardId);

        // 1. 获取大屏基础信息
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
        if (!dashboardOpt.isPresent()) {
            throw new RuntimeException("大屏不存在: " + dashboardId);
        }

        BiDashboard dashboard = dashboardOpt.get();

        // 2. 获取组件列表
        List<BiWidget> widgets = widgetRepository.findByDashboardIdOrderByCreatedAt(dashboardId);

        try {
            // 3. 构建导出数据
            Map<String, Object> exportData = new HashMap<>();

            // 3.1 添加元数据
            exportData.put("metadata", createMetadata(dashboard));

            // 3.2 添加画布配置
            exportData.put("canvasConfig", processCanvasConfig(dashboard.getCanvasConfig()));

            // 3.3 添加组件配置（过滤数据源）
            exportData.put("widgets", processWidgets(widgets));

            log.info("大屏模板导出成功，ID: {}, 组件数: {}", dashboardId, widgets.size());
            return exportData;

        } catch (Exception e) {
            log.error("导出大屏模板失败，ID: {}", dashboardId, e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 创建导出元数据
     */
    private Map<String, Object> createMetadata(BiDashboard dashboard) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("version", "1.0");
        metadata.put("exportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        metadata.put("templateName", dashboard.getName());
        metadata.put("description", dashboard.getDescription());
        metadata.put("originalId", dashboard.getId());
        metadata.put("createdAt", dashboard.getCreatedAt() != null ? 
                    dashboard.getCreatedAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) : null);
        return metadata;
    }

    /**
     * 处理画布配置
     */
    private Map<String, Object> processCanvasConfig(String canvasConfigJson) {
        try {
            if (canvasConfigJson == null || canvasConfigJson.trim().isEmpty()) {
                // 返回默认画布配置
                Map<String, Object> defaultConfig = new HashMap<>();
                defaultConfig.put("width", 1920);
                defaultConfig.put("height", 1080);
                defaultConfig.put("backgroundColor", "#ffffff");
                defaultConfig.put("backgroundType", "color");
                defaultConfig.put("showGrid", true);
                defaultConfig.put("gridColor", "#e0e0e0");
                defaultConfig.put("gridSize", 20);
                return defaultConfig;
            }

            JsonNode canvasNode = objectMapper.readTree(canvasConfigJson);
            Map<String, Object> canvasConfig = objectMapper.convertValue(canvasNode, Map.class);

            log.debug("处理画布配置: {}", canvasConfig);
            return canvasConfig;

        } catch (Exception e) {
            log.error("处理画布配置失败: {}", canvasConfigJson, e);
            throw new RuntimeException("画布配置处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理组件列表，移除数据源配置
     */
    private List<Map<String, Object>> processWidgets(List<BiWidget> widgets) {
        List<Map<String, Object>> processedWidgets = new ArrayList<>();

        for (BiWidget widget : widgets) {
            try {
                Map<String, Object> widgetData = processWidget(widget);
                processedWidgets.add(widgetData);
            } catch (Exception e) {
                log.error("处理组件失败，ID: {}, Type: {}", widget.getId(), widget.getWidgetType(), e);
                // 继续处理其他组件，不因单个组件失败而中断
            }
        }

        // 按zIndex排序，确保导入时层级正确
        processedWidgets.sort((w1, w2) -> {
            Map<String, Object> pos1 = (Map<String, Object>) w1.get("position");
            Map<String, Object> pos2 = (Map<String, Object>) w2.get("position");
            Integer z1 = (Integer) pos1.getOrDefault("zIndex", 1000);
            Integer z2 = (Integer) pos2.getOrDefault("zIndex", 1000);
            return z1.compareTo(z2);
        });

        log.info("处理组件完成，总数: {}", processedWidgets.size());
        return processedWidgets;
    }

    /**
     * 处理单个组件，移除数据源配置
     */
    private Map<String, Object> processWidget(BiWidget widget) throws Exception {
        Map<String, Object> widgetData = new HashMap<>();

        // 1. 基础信息
        widgetData.put("type", widget.getWidgetType());

        // 2. 处理位置配置
        widgetData.put("position", processPosition(widget));

        // 3. 处理样式配置（保留，但移除数据源相关配置）
        widgetData.put("setup", processSetup(widget));

        // 4. 不包含数据源配置，但提供默认的空配置结构
        widgetData.put("data", createDefaultDataConfig());

        log.debug("处理组件: ID={}, Type={}", widget.getId(), widget.getWidgetType());
        return widgetData;
    }

    /**
     * 处理位置配置
     */
    private Map<String, Object> processPosition(BiWidget widget) throws Exception {
        Map<String, Object> position = new HashMap<>();

        // 优先使用position字段
        if (widget.getPosition() != null && !widget.getPosition().trim().isEmpty()) {
            JsonNode positionNode = objectMapper.readTree(widget.getPosition());
            position = objectMapper.convertValue(positionNode, Map.class);
        } else {
            // 回退到兼容性字段
            position.put("left", widget.getPositionX() != null ? widget.getPositionX() : 0);
            position.put("top", widget.getPositionY() != null ? widget.getPositionY() : 0);
            position.put("width", widget.getWidth() != null ? widget.getWidth() : 300);
            position.put("height", widget.getHeight() != null ? widget.getHeight() : 200);
            position.put("zIndex", widget.getZIndex() != null ? widget.getZIndex() : 1000);
        }

        return position;
    }

    /**
     * 处理样式配置，移除数据源相关配置
     */
    private Map<String, Object> processSetup(BiWidget widget) throws Exception {
        Map<String, Object> setup = new HashMap<>();

        // 优先使用setup字段
        if (widget.getSetup() != null && !widget.getSetup().trim().isEmpty()) {
            JsonNode setupNode = objectMapper.readTree(widget.getSetup());
            setup = objectMapper.convertValue(setupNode, Map.class);
        } else if (widget.getConfig() != null && !widget.getConfig().trim().isEmpty()) {
            // 回退到config字段
            JsonNode configNode = objectMapper.readTree(widget.getConfig());
            setup = objectMapper.convertValue(configNode, Map.class);
        }

        // 移除数据源相关的配置项
        setup = filterDataSourceConfig(setup);

        // 确保基础配置存在
        if (!setup.containsKey("title")) {
            setup.put("title", getDefaultTitle(widget.getWidgetType()));
        }
        if (!setup.containsKey("chartType")) {
            setup.put("chartType", widget.getWidgetType());
        }

        return setup;
    }

    /**
     * 过滤数据源相关配置
     */
    private Map<String, Object> filterDataSourceConfig(Map<String, Object> setup) {
        Map<String, Object> filtered = new HashMap<>(setup);

        // 移除数据源相关的配置项
        String[] dataSourceKeys = {
            "deviceId", "dataItemId", "dataMode", "historyCount", "refreshInterval",
            "staticLabels", "staticValues", "externalDataSetId", "labelField", "valueField",
            "enableDataTransform", "transformOperation", "transformValue", "decimalPlaces", "dataSuffix",
            "enableWaterTarget", "waterTargetSource", "waterTargetValue", "waterTargetDevice", "waterTargetDataItem",
            "enableColumnTarget", "columnTargetSource", "columnTargetValue", "columnTargetDevice", "columnTargetDataItem"
        };

        for (String key : dataSourceKeys) {
            filtered.remove(key);
        }

        log.debug("过滤数据源配置，原始配置项: {}, 过滤后: {}", setup.size(), filtered.size());
        return filtered;
    }

    /**
     * 创建默认的空数据配置
     */
    private Map<String, Object> createDefaultDataConfig() {
        Map<String, Object> dataConfig = new HashMap<>();
        dataConfig.put("dataSourceType", "dataItem");
        dataConfig.put("deviceId", "");
        dataConfig.put("dataItemId", "");
        dataConfig.put("refreshInterval", 5);
        return dataConfig;
    }

    /**
     * 获取组件类型的默认标题
     */
    private String getDefaultTitle(String widgetType) {
        switch (widgetType) {
            case "line-chart": return "折线图";
            case "bar-chart": return "柱状图";
            case "pie-chart": return "饼图";
            case "gauge-chart": return "仪表盘";
            case "water-chart": return "水波图";
            case "text-component": return "文本组件";
            case "image-component": return "图片组件";
            case "table-component": return "表格组件";
            case "status-indicator": return "状态指示器";
            default: return widgetType;
        }
    }

    /**
     * 生成导出文件名
     */
    public String generateExportFileName(BiDashboard dashboard) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String safeName = dashboard.getName().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]", "_");
        return String.format("bi_template_%s_%s.json", safeName, timestamp);
    }

    /**
     * 导出完整资源包（包含模板配置和所有资源文件）
     */
    public ResponseEntity<ByteArrayResource> exportDashboardWithResources(Long dashboardId) {
        log.info("开始导出完整资源包，大屏ID: {}", dashboardId);

        Path tempDir = null;
        try {
            // 1. 获取大屏信息
            Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
            if (!dashboardOpt.isPresent()) {
                throw new RuntimeException("大屏不存在: " + dashboardId);
            }

            BiDashboard dashboard = dashboardOpt.get();

            // 2. 创建临时目录
            tempDir = resourceCollector.createTempDirectory();
            log.info("创建临时目录: {}", tempDir);

            // 3. 分析资源文件
            Map<String, Object> resourceAnalysis = resourceAnalyzer.analyzeResources(dashboardId);
            List<String> imageUrls = (List<String>) resourceAnalysis.get("imageUrls");
            List<String> materialUrls = (List<String>) resourceAnalysis.get("materialUrls");
            List<String> videoUrls = (List<String>) resourceAnalysis.get("videoUrls");
            List<HtmlCodeSnippet> htmlSnippets = (List<HtmlCodeSnippet>) resourceAnalysis.get("htmlSnippets");

            log.info("资源分析结果 - 图片: {}, 素材: {}, 视频: {}, HTML片段: {}",
                    imageUrls.size(), materialUrls.size(), videoUrls.size(), htmlSnippets.size());

            // 4. 收集资源文件
            Map<String, Object> collectionResult = resourceCollector.collectResources(
                    imageUrls, materialUrls, videoUrls, tempDir);

            // 5. 导出HTML代码片段
            if (!htmlSnippets.isEmpty()) {
                htmlCodeExportService.exportHtmlSnippets(htmlSnippets, tempDir);
            }

            // 6. 生成模板配置文件
            Map<String, Object> templateData = exportDashboardTemplate(dashboardId);
            String templateJson = objectMapper.writerWithDefaultPrettyPrinter()
                                            .writeValueAsString(templateData);
            Path templateFile = tempDir.resolve("template.json");
            Files.write(templateFile, templateJson.getBytes(StandardCharsets.UTF_8));

            // 7. 生成资源清单
            generateResourceManifest(resourceAnalysis, collectionResult, tempDir);

            // 8. 生成使用说明
            generateReadmeFile(dashboard, resourceAnalysis, tempDir);

            // 9. 创建ZIP包
            byte[] zipData = createZipPackage(tempDir);

            // 10. 生成文件名
            String filename = generateResourcePackageFileName(dashboard);

            // 11. 返回响应
            ByteArrayResource resource = new ByteArrayResource(zipData);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentLength(zipData.length)
                    .body(resource);

        } catch (Exception e) {
            log.error("导出完整资源包失败，大屏ID: {}", dashboardId, e);
            throw new RuntimeException("导出完整资源包失败: " + e.getMessage());
        } finally {
            // 清理临时目录
            if (tempDir != null) {
                resourceCollector.cleanupTempDirectory(tempDir);
            }
        }
    }

    /**
     * 生成资源清单文件
     */
    private void generateResourceManifest(Map<String, Object> resourceAnalysis,
                                        Map<String, Object> collectionResult, Path tempDir) {
        try {
            Map<String, Object> manifest = new HashMap<>();
            manifest.put("version", "1.0");
            manifest.put("exportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            manifest.put("resourceAnalysis", resourceAnalysis);
            manifest.put("collectionResult", collectionResult);

            String manifestJson = objectMapper.writerWithDefaultPrettyPrinter()
                                            .writeValueAsString(manifest);
            Path manifestFile = tempDir.resolve("manifest.json");
            Files.write(manifestFile, manifestJson.getBytes(StandardCharsets.UTF_8));

            log.info("生成资源清单文件: {}", manifestFile);

        } catch (Exception e) {
            log.error("生成资源清单失败", e);
        }
    }

    /**
     * 生成使用说明文件
     */
    private void generateReadmeFile(BiDashboard dashboard, Map<String, Object> resourceAnalysis, Path tempDir) {
        try {
            StringBuilder readme = new StringBuilder();
            readme.append("BI大屏完整资源包\n");
            readme.append("==================\n\n");
            readme.append("大屏名称: ").append(dashboard.getName()).append("\n");
            readme.append("导出时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            readme.append("大屏ID: ").append(dashboard.getId()).append("\n\n");

            readme.append("包含内容:\n");
            readme.append("---------\n");
            readme.append("1. template.json - 大屏模板配置文件\n");
            readme.append("2. resources/ - 资源文件目录\n");
            readme.append("   ├── images/ - 图片文件\n");
            readme.append("   ├── materials/ - 素材文件\n");
            readme.append("   ├── videos/ - 视频文件\n");
            readme.append("   └── html_codes/ - HTML代码片段\n");
            readme.append("3. manifest.json - 资源清单文件\n");
            readme.append("4. README.txt - 本说明文件\n\n");

            readme.append("资源统计:\n");
            readme.append("---------\n");
            readme.append("图片文件: ").append(((List<?>) resourceAnalysis.get("imageUrls")).size()).append(" 个\n");
            readme.append("素材文件: ").append(((List<?>) resourceAnalysis.get("materialUrls")).size()).append(" 个\n");
            readme.append("视频文件: ").append(((List<?>) resourceAnalysis.get("videoUrls")).size()).append(" 个\n");
            readme.append("HTML代码片段: ").append(((List<?>) resourceAnalysis.get("htmlSnippets")).size()).append(" 个\n\n");

            readme.append("使用说明:\n");
            readme.append("---------\n");
            readme.append("1. 模板导入: 使用 template.json 文件导入大屏模板\n");
            readme.append("2. 资源部署:\n");
            readme.append("   - images/ 目录: 可直接复制到服务器的图片目录 (如: D:/images/)\n");
            readme.append("   - videos/ 目录: 可直接复制到服务器的视频目录 (如: D:/videos/)\n");
            readme.append("   - materials/ 目录: 装饰元素文件，需要通过系统界面手动上传添加\n");
            readme.append("     * 不能直接复制到服务器目录\n");
            readme.append("     * 需要在系统的素材管理界面逐个上传\n");
            readme.append("     * 上传后系统会自动分配新的文件名和ID\n");
            readme.append("3. HTML代码: html_codes/ 目录包含所有HTML代码片段\n");
            readme.append("   - 需要在系统的HTML代码管理界面手动添加\n");
            readme.append("   - 可以复制文件内容后在系统中创建新的代码片段\n");
            readme.append("4. 清单检查: 使用 manifest.json 验证资源完整性\n\n");

            readme.append("注意事项:\n");
            readme.append("---------\n");
            readme.append("1. 图片和视频文件:\n");
            readme.append("   - 可以直接复制到服务器对应目录\n");
            readme.append("   - 请确保目标服务器的目录结构与源服务器一致\n");
            readme.append("   - 如有文件名冲突，请手动处理\n");
            readme.append("2. 装饰元素文件 (materials/):\n");
            readme.append("   - 不能直接复制文件到服务器目录\n");
            readme.append("   - 必须通过系统界面逐个手动上传\n");
            readme.append("   - 上传后文件名和引用路径会发生变化\n");
            readme.append("   - 需要在大屏设计器中重新配置装饰元素的引用\n");
            readme.append("3. HTML代码片段:\n");
            readme.append("   - 需要在系统的HTML代码管理界面手动创建\n");
            readme.append("   - 复制文件内容后在系统中添加为新的代码片段\n");
            readme.append("   - 添加后需要在大屏设计器中重新选择HTML代码片段\n");
            readme.append("4. 导入前请备份现有数据\n");
            readme.append("5. 建议先在测试环境验证完整性后再部署到生产环境\n");

            Path readmeFile = tempDir.resolve("README.txt");
            Files.write(readmeFile, readme.toString().getBytes(StandardCharsets.UTF_8));

            log.info("生成使用说明文件: {}", readmeFile);

        } catch (Exception e) {
            log.error("生成使用说明失败", e);
        }
    }

    /**
     * 创建ZIP压缩包
     */
    private byte[] createZipPackage(Path sourceDir) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            Files.walk(sourceDir)
                 .filter(path -> !Files.isDirectory(path))
                 .forEach(path -> {
                     try {
                         Path relativePath = sourceDir.relativize(path);
                         ZipEntry zipEntry = new ZipEntry(relativePath.toString().replace('\\', '/'));
                         zos.putNextEntry(zipEntry);
                         Files.copy(path, zos);
                         zos.closeEntry();
                     } catch (IOException e) {
                         log.error("添加文件到ZIP失败: {}", path, e);
                     }
                 });
        }

        byte[] zipData = baos.toByteArray();
        log.info("创建ZIP包完成，大小: {}", resourceCollector.formatFileSize(zipData.length));
        return zipData;
    }

    /**
     * 生成资源包文件名
     */
    private String generateResourcePackageFileName(BiDashboard dashboard) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String safeName = dashboard.getName().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]", "_");
        return String.format("bi_dashboard_resources_%s_%s.zip", safeName, timestamp);
    }

    /**
     * 获取资源包预览信息
     */
    public Map<String, Object> getResourcePackagePreview(Long dashboardId) {
        log.info("获取资源包预览信息，大屏ID: {}", dashboardId);

        try {
            // 1. 获取大屏信息
            Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(dashboardId);
            if (!dashboardOpt.isPresent()) {
                throw new RuntimeException("大屏不存在: " + dashboardId);
            }

            BiDashboard dashboard = dashboardOpt.get();

            // 2. 分析资源文件
            Map<String, Object> resourceAnalysis = resourceAnalyzer.analyzeResources(dashboardId);
            List<String> imageUrls = (List<String>) resourceAnalysis.get("imageUrls");
            List<String> materialUrls = (List<String>) resourceAnalysis.get("materialUrls");
            List<String> videoUrls = (List<String>) resourceAnalysis.get("videoUrls");

            // 3. 计算文件大小
            long totalSize = resourceCollector.calculateTotalSize(imageUrls, materialUrls, videoUrls);

            // 4. 构建预览信息
            Map<String, Object> preview = new HashMap<>();
            preview.put("dashboardName", dashboard.getName());
            preview.put("dashboardId", dashboardId);
            preview.put("resourceAnalysis", resourceAnalysis);
            preview.put("totalSize", totalSize);
            preview.put("formattedSize", resourceCollector.formatFileSize(totalSize));
            preview.put("estimatedFileName", generateResourcePackageFileName(dashboard));

            log.info("资源包预览信息获取完成，总大小: {}", resourceCollector.formatFileSize(totalSize));
            return preview;

        } catch (Exception e) {
            log.error("获取资源包预览信息失败，大屏ID: {}", dashboardId, e);
            throw new RuntimeException("获取预览信息失败: " + e.getMessage());
        }
    }
}
