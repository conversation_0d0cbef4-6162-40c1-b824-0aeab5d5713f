<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>别名冲突修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .sql-comparison {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .sql-before, .sql-after {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 0.5rem 0;
        }
        
        .sql-before {
            border-left: 4px solid #dc3545;
        }
        
        .sql-after {
            border-left: 4px solid #28a745;
        }
        
        .issue-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .issue-conflict {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .issue-resolved {
            background-color: #d4edda;
            color: #155724;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="bi bi-exclamation-triangle"></i>
                别名冲突修复验证
            </h2>
            
            <!-- 问题说明 -->
            <div class="alert alert-danger">
                <h6><i class="bi bi-bug"></i> 问题描述</h6>
                <p class="mb-0">月日时分、月日、时分秒、时分这几个日期格式化选项时，输出的数据记录不是最新的。根本原因是字段名冲突导致ORDER BY引用了格式化后的别名。</p>
            </div>
            
            <!-- 问题分析 -->
            <div class="test-section">
                <h5><i class="bi bi-search"></i> 问题根因分析</h5>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-x-circle text-danger"></i> 字段名冲突问题</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复前的SQL：</strong>
                            <div class="sql-before">SELECT <span class="highlight">DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp</span>, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY <span class="highlight">timestamp</span> DESC
LIMIT 5</div>
                            <span class="issue-badge issue-conflict">字段名冲突</span>
                        </div>
                        <div class="col-md-6">
                            <strong>MySQL解析逻辑：</strong>
                            <div class="alert alert-warning small">
                                <p><strong>ORDER BY timestamp 引用的是：</strong></p>
                                <ul class="mb-0">
                                    <li>❌ 不是原始表中的timestamp字段</li>
                                    <li>❌ 而是SELECT子句中的别名timestamp</li>
                                    <li>❌ 即格式化后的字符串：'07-22 17:35'</li>
                                    <li>❌ 导致按字符串排序而不是时间排序</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="sql-comparison">
                    <h6><i class="bi bi-check-circle text-success"></i> 别名冲突解决</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复后的SQL：</strong>
                            <div class="sql-after">SELECT <span class="highlight">DATE_FORMAT(timestamp, '%m-%d %H:%i') as formatted_timestamp</span>, value
FROM data_history
WHERE data_item_name LIKE '%1%'
ORDER BY <span class="highlight">timestamp</span> DESC
LIMIT 5</div>
                            <span class="issue-badge issue-resolved">别名冲突已解决</span>
                        </div>
                        <div class="col-md-6">
                            <strong>MySQL解析逻辑：</strong>
                            <div class="alert alert-success small">
                                <p><strong>ORDER BY timestamp 引用的是：</strong></p>
                                <ul class="mb-0">
                                    <li>✅ 原始表中的timestamp字段</li>
                                    <li>✅ 不会与别名formatted_timestamp冲突</li>
                                    <li>✅ 按原始时间戳排序</li>
                                    <li>✅ 获取最新的数据记录</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 各格式化选项对比 -->
            <div class="test-section">
                <h5><i class="bi bi-list-ul"></i> 各格式化选项修复对比</h5>
                
                <!-- 月日时分 -->
                <div class="sql-comparison">
                    <h6>%m-%d %H:%i (月日时分)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复前：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as timestamp, value
FROM data_history
ORDER BY timestamp DESC  -- 引用格式化字符串
LIMIT 5

-- 结果：12-25 14:30, 07-23 16:45, 07-22 17:35
-- 问题：'12-25' > '07-23' (字符串排序)</div>
                        </div>
                        <div class="col-md-6">
                            <strong>修复后：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%m-%d %H:%i') as formatted_timestamp, value
FROM data_history
ORDER BY timestamp DESC  -- 引用原始时间字段
LIMIT 5

-- 结果：07-22 17:35, 07-23 16:45, 12-25 14:30
-- 正确：按原始时间戳排序</div>
                        </div>
                    </div>
                </div>
                
                <!-- 月日 -->
                <div class="sql-comparison">
                    <h6>%m-%d (月日)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复前：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%m-%d') as timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 问题：按月日字符串排序</div>
                        </div>
                        <div class="col-md-6">
                            <strong>修复后：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%m-%d') as formatted_timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 正确：按原始时间戳排序</div>
                        </div>
                    </div>
                </div>
                
                <!-- 时分秒 -->
                <div class="sql-comparison">
                    <h6>%H:%i:%s (时分秒)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复前：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%H:%i:%s') as timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 问题：按时分秒字符串排序</div>
                        </div>
                        <div class="col-md-6">
                            <strong>修复后：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%H:%i:%s') as formatted_timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 正确：按原始时间戳排序</div>
                        </div>
                    </div>
                </div>
                
                <!-- 时分 -->
                <div class="sql-comparison">
                    <h6>%H:%i (时分)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>修复前：</strong>
                            <div class="sql-before">SELECT DATE_FORMAT(timestamp, '%H:%i') as timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 问题：按时分字符串排序</div>
                        </div>
                        <div class="col-md-6">
                            <strong>修复后：</strong>
                            <div class="sql-after">SELECT DATE_FORMAT(timestamp, '%H:%i') as formatted_timestamp, value
FROM data_history
ORDER BY timestamp DESC
LIMIT 5

-- 正确：按原始时间戳排序</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修复详情 -->
            <div class="test-section">
                <h5><i class="bi bi-code-slash"></i> 修复详情</h5>
                
                <div class="sql-comparison">
                    <h6>修改formatFieldForSelect方法</h6>
                    <div class="sql-after">formatFieldForSelect(fieldName) {
    const dateField = document.getElementById('dateField')?.value;
    const dateFormat = document.getElementById('dateFormat')?.value;

    if (dateField && dateFormat && fieldName === dateField) {
        // 使用不同的别名避免与原始字段名冲突
        // 这样ORDER BY可以明确引用原始字段，而不是格式化后的别名
        return `DATE_FORMAT(${fieldName}, '${dateFormat}') as formatted_${fieldName}`;
    }

    return fieldName;
}</div>
                </div>
                
                <div class="sql-comparison">
                    <h6>修复原理</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-info small">
                                <h6>问题原理</h6>
                                <ul class="mb-0">
                                    <li>SELECT子句：<code>DATE_FORMAT(...) as timestamp</code></li>
                                    <li>ORDER BY子句：<code>ORDER BY timestamp</code></li>
                                    <li>MySQL优先引用SELECT中的别名</li>
                                    <li>导致按格式化字符串排序</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success small">
                                <h6>修复原理</h6>
                                <ul class="mb-0">
                                    <li>SELECT子句：<code>DATE_FORMAT(...) as formatted_timestamp</code></li>
                                    <li>ORDER BY子句：<code>ORDER BY timestamp</code></li>
                                    <li>没有别名冲突，引用原始字段</li>
                                    <li>按原始时间戳正确排序</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 验证结果 -->
            <div class="test-section">
                <h5><i class="bi bi-check-circle"></i> 验证结果</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="sql-comparison text-center">
                            <h6>%m-%d %H:%i</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">排序已修复</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="sql-comparison text-center">
                            <h6>%m-%d</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">排序已修复</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="sql-comparison text-center">
                            <h6>%H:%i:%s</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">排序已修复</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="sql-comparison text-center">
                            <h6>%H:%i</h6>
                            <div class="display-6 text-success">✓</div>
                            <p class="small mb-0">排序已修复</p>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="bi bi-check-circle"></i> 修复总结</h6>
                    <p class="mb-0">通过使用<code>formatted_字段名</code>别名避免字段名冲突，现在所有日期格式化选项都能正确按原始时间戳排序，确保获取最新的数据记录。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
