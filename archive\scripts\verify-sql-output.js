// 验证SQL输出修改的脚本
// 模拟DataSetFieldConfigurator类的关键方法

class TestFieldConfigurator {
    constructor() {
        this.selectedTable = { name: 'sales_data' };
        this.fieldRoles = {
            'product_name': 'label',
            'sales_amount': 'value'
        };
        this.filterConditions = [{
            field: 'sale_date',
            operator: 'TODAY',
            value: '',
            logicOperator: null
        }];
        this.aggregationConfig = {
            enabled: true,
            type: 'MAX',
            timeField: 'created_time',
            groupByLabel: true
        };
    }

    isTimePresetOperator(operator) {
        const timePresetOperators = ['TODAY', 'THIS_WEEK', 'THIS_MONTH', 'THIS_QUARTER', 'THIS_YEAR'];
        return timePresetOperators.includes(operator);
    }

    generateConditionSQL(condition) {
        const { field, operator, value } = condition;
        
        if (operator === 'TODAY') {
            return `DATE(${field}) = CURDATE()`;
        }
        
        return `${field} ${operator} '${value}'`;
    }

    generateWhereClause() {
        const validConditions = this.filterConditions.filter(c => 
            c.field && c.operator && (c.value || c.operator.includes('NULL') || this.isTimePresetOperator(c.operator))
        );

        if (validConditions.length === 0) {
            return '';
        }

        let whereClause = 'WHERE ';
        validConditions.forEach((condition, index) => {
            if (index > 0 && condition.logicOperator) {
                whereClause += ` ${condition.logicOperator} `;
            }
            whereClause += this.generateConditionSQL(condition);
        });

        return whereClause;
    }

    generateWindowFunctionSQL(labelField, valueField, timeField, aggregationType, whereClause) {
        const orderDirection = aggregationType === 'MAX' ? 'DESC' : 'ASC';
        
        let sql = `SELECT ${labelField}, ${valueField}\n`;
        sql += `FROM (\n`;
        sql += `    SELECT *, ROW_NUMBER() OVER (\n`;
        sql += `        PARTITION BY ${labelField} \n`;
        sql += `        ORDER BY ${valueField} ${orderDirection}, ${timeField} DESC\n`;
        sql += `    ) as rn\n`;
        sql += `    FROM ${this.selectedTable.name}`;
        
        if (whereClause) {
            sql += `\n    ${whereClause}`;
        }
        
        sql += `\n) t\n`;
        sql += `WHERE rn = 1`;
        
        return sql;
    }

    generateAggregationSQL() {
        const labelFields = Object.keys(this.fieldRoles).filter(fieldName => 
            this.fieldRoles[fieldName] === 'label'
        );
        const valueFields = Object.keys(this.fieldRoles).filter(fieldName => 
            this.fieldRoles[fieldName] === 'value'
        );

        if (labelFields.length === 0 || valueFields.length === 0) {
            return 'SELECT * FROM ' + this.selectedTable.name;
        }

        const labelField = labelFields[0];
        const valueField = valueFields[0];
        const aggregationType = this.aggregationConfig.type;
        const timeField = this.aggregationConfig.timeField;
        const whereClause = this.generateWhereClause();

        if (timeField && (aggregationType === 'MAX' || aggregationType === 'MIN')) {
            return this.generateWindowFunctionSQL(labelField, valueField, timeField, aggregationType, whereClause);
        } else {
            return this.generateSimpleAggregationSQL(labelField, valueField, aggregationType, whereClause);
        }
    }

    generateSimpleAggregationSQL(labelField, valueField, aggregationType, whereClause) {
        let sql = `SELECT ${labelField}, ${aggregationType}(${valueField}) as ${valueField}\n`;
        sql += `FROM ${this.selectedTable.name}`;
        
        if (whereClause) {
            sql += `\n${whereClause}`;
        }
        
        sql += `\nGROUP BY ${labelField}`;
        
        return sql;
    }
}

// 测试函数
function testSQLGeneration() {
    console.log('=== SQL输出验证测试 ===\n');
    
    const configurator = new TestFieldConfigurator();
    const sql = configurator.generateAggregationSQL();
    
    console.log('生成的SQL:');
    console.log(sql);
    console.log('\n验证结果:');
    
    // 验证外层SELECT不包含时间字段
    const hasTimeInSelect = sql.includes('SELECT product_name, sales_amount, created_time');
    const hasCorrectSelect = sql.includes('SELECT product_name, sales_amount\n');
    const hasTimeInOrderBy = sql.includes('ORDER BY sales_amount DESC, created_time DESC');
    
    console.log(`✓ 外层SELECT不包含时间字段: ${!hasTimeInSelect ? '✅ 通过' : '❌ 失败'}`);
    console.log(`✓ 外层SELECT只包含标签和数值: ${hasCorrectSelect ? '✅ 通过' : '❌ 失败'}`);
    console.log(`✓ 内层仍使用时间字段排序: ${hasTimeInOrderBy ? '✅ 通过' : '❌ 失败'}`);
    
    // 预期的SQL结构
    const expectedSQL = `SELECT product_name, sales_amount
FROM (
    SELECT *, ROW_NUMBER() OVER (
        PARTITION BY product_name 
        ORDER BY sales_amount DESC, created_time DESC
    ) as rn
    FROM sales_data
    WHERE DATE(sale_date) = CURDATE()
) t
WHERE rn = 1`;

    console.log('\n预期的SQL结构:');
    console.log(expectedSQL);
    
    console.log('\n=== 测试完成 ===');
}

// 运行测试
testSQLGeneration();
